#!/usr/bin/env python3

import PyPDF2
import re
import json

def extract_pdf_text(pdf_path):
    """Extract text content from a PDF file."""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
            return text
    except Exception as e:
        return f"Error reading PDF: {str(e)}"

def analyze_ticket_quality(text, ticket_id):
    """Analyze ticket quality based on troubleshooting best practices."""
    
    analysis = {
        "ticket_id": ticket_id,
        "quality_score": 0,
        "has_problem_statement": False,
        "has_resolution": False,
        "has_technical_details": False,
        "has_troubleshooting_steps": False,
        "has_root_cause": False,
        "contains_sensitive_info": False,
        "educational_value": "Low",
        "recommendation": "Remove",
        "summary": "",
        "reasons": []
    }
    
    # Check for problem statement
    problem_patterns = [
        r"customer.*(?:is|are).*(?:not|unable|cannot|failed|experiencing)",
        r"issue.*(?:with|regarding)",
        r"problem.*(?:with|is)",
        r"error.*(?:message|occurred|happens)"
    ]
    
    for pattern in problem_patterns:
        if re.search(pattern, text.lower()):
            analysis["has_problem_statement"] = True
            analysis["quality_score"] += 20
            break
    
    # Check for resolution
    resolution_patterns = [
        r"resolved|fixed|solution|workaround",
        r"issue.*(?:was|has been).*(?:resolved|fixed)",
        r"corrected|addressed"
    ]
    
    for pattern in resolution_patterns:
        if re.search(pattern, text.lower()):
            analysis["has_resolution"] = True
            analysis["quality_score"] += 25
            break
    
    # Check for technical details
    tech_patterns = [
        r"EDI\s+\d{3}",  # EDI transaction codes
        r"X12|EDIFACT|AS2|FTP|SFTP",
        r"error\s+code|error\s+message",
        r"trading\s+partner",
        r"mailbox|queue|batch"
    ]
    
    tech_count = 0
    for pattern in tech_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            tech_count += 1
    
    if tech_count >= 2:
        analysis["has_technical_details"] = True
        analysis["quality_score"] += 15
    
    # Check for troubleshooting steps
    troubleshooting_patterns = [
        r"step\s+\d+|first.*then|checked.*found",
        r"verified|confirmed|tested",
        r"root cause|investigation|analysis"
    ]
    
    for pattern in troubleshooting_patterns:
        if re.search(pattern, text.lower()):
            analysis["has_troubleshooting_steps"] = True
            analysis["quality_score"] += 20
            break
    
    # Check for root cause analysis
    if re.search(r"root cause|caused by|reason.*was|due to", text.lower()):
        analysis["has_root_cause"] = True
        analysis["quality_score"] += 20
    
    # Check for sensitive information
    sensitive_patterns = [
        r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",  # Email
        r"\b\d{3}[-.]?\d{3}[-.]?\d{4}\b",  # Phone
        r"\b(?:company|customer)\s*(?:name|:)\s*[A-Za-z\s]+",  # Company names
    ]
    
    for pattern in sensitive_patterns:
        if re.search(pattern, text):
            analysis["contains_sensitive_info"] = True
            break
    
    # Determine educational value
    if analysis["quality_score"] >= 80:
        analysis["educational_value"] = "High"
        analysis["recommendation"] = "Keep - Excellent example"
    elif analysis["quality_score"] >= 60:
        analysis["educational_value"] = "Medium"
        analysis["recommendation"] = "Keep - Good reference"
    elif analysis["quality_score"] >= 40:
        analysis["educational_value"] = "Low-Medium"
        analysis["recommendation"] = "Consider keeping with improvements"
    else:
        analysis["educational_value"] = "Low"
        analysis["recommendation"] = "Remove - Limited value"
    
    # Generate summary
    if "duplicate 855" in text.lower() or "duplicate" in text.lower():
        analysis["summary"] = "Duplicate transaction issue"
    elif "notification" in text.lower() and "trigger" in text.lower():
        analysis["summary"] = "Email notification failure"
    elif "status" in text.lower() and "updating" in text.lower():
        analysis["summary"] = "Dashboard status update issue"
    elif "not receiving" in text.lower() and "edi" in text.lower():
        analysis["summary"] = "Missing EDI documents issue"
    elif "voicemail" in text.lower():
        analysis["summary"] = "Voicemail/callback request"
    else:
        analysis["summary"] = "General support inquiry"
    
    # Add specific reasons
    if analysis["has_problem_statement"]:
        analysis["reasons"].append("Clear problem statement")
    if analysis["has_resolution"]:
        analysis["reasons"].append("Contains resolution")
    if analysis["has_technical_details"]:
        analysis["reasons"].append("Good technical details")
    if analysis["has_troubleshooting_steps"]:
        analysis["reasons"].append("Shows troubleshooting process")
    if analysis["has_root_cause"]:
        analysis["reasons"].append("Includes root cause analysis")
    if analysis["contains_sensitive_info"]:
        analysis["reasons"].append("Contains sensitive info (needs redaction)")
    
    if not analysis["reasons"]:
        analysis["reasons"].append("Limited troubleshooting value")
    
    return analysis

def main():
    tickets = [
        ("/home/<USER>/edi_knowledge_base/support_tickets/cleo.zendesk.com_tickets_13113892_print.pdf", "13113892"),
        ("/home/<USER>/edi_knowledge_base/support_tickets/cleo.zendesk.com_tickets_13138304_print.pdf", "13138304"),
        ("/home/<USER>/edi_knowledge_base/support_tickets/cleo.zendesk.com_tickets_13160257_print.pdf", "13160257"),
        ("/home/<USER>/edi_knowledge_base/support_tickets/cleo.zendesk.com_tickets_13126051_print.pdf", "13126051"),
        ("/home/<USER>/edi_knowledge_base/support_tickets/cleo.zendesk.com_tickets_13137226_print.pdf", "13137226")
    ]
    
    results = []
    
    for pdf_path, ticket_id in tickets:
        text = extract_pdf_text(pdf_path)
        if not text.startswith("Error"):
            analysis = analyze_ticket_quality(text, ticket_id)
            results.append(analysis)
            
            print(f"\n{'='*80}")
            print(f"TICKET #{ticket_id} ANALYSIS")
            print('='*80)
            print(f"Summary: {analysis['summary']}")
            print(f"Quality Score: {analysis['quality_score']}/100")
            print(f"Educational Value: {analysis['educational_value']}")
            print(f"Recommendation: {analysis['recommendation']}")
            print(f"\nQuality Indicators:")
            print(f"  ✓ Problem Statement: {analysis['has_problem_statement']}")
            print(f"  ✓ Resolution: {analysis['has_resolution']}")
            print(f"  ✓ Technical Details: {analysis['has_technical_details']}")
            print(f"  ✓ Troubleshooting Steps: {analysis['has_troubleshooting_steps']}")
            print(f"  ✓ Root Cause: {analysis['has_root_cause']}")
            print(f"  ⚠ Sensitive Info: {analysis['contains_sensitive_info']}")
            print(f"\nReasons: {', '.join(analysis['reasons'])}")
    
    # Summary
    print(f"\n{'='*80}")
    print("OVERALL RECOMMENDATIONS")
    print('='*80)
    
    keep_count = sum(1 for r in results if "Keep" in r["recommendation"])
    print(f"\nTickets to Keep: {keep_count}/{len(results)}")
    print("\nDetailed Recommendations:")
    
    for result in results:
        status = "✓ KEEP" if "Keep" in result["recommendation"] else "✗ REMOVE"
        print(f"\n{status} Ticket #{result['ticket_id']} - {result['summary']}")
        print(f"   Reason: {result['recommendation']}")
        if result["contains_sensitive_info"] and "Keep" in result["recommendation"]:
            print("   ⚠ Note: Redact sensitive information before using")

if __name__ == "__main__":
    main()