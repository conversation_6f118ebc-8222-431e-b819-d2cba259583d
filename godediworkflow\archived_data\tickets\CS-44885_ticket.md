# CS-44885: Fwd: Notification from Integration Import for Dream Team Americas - CTC

## Ticket Information
- **Key**: CS-44885
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-06-03T10:29:04.042-0600
- **Updated**: 2025-06-09T09:12:09.068-0600
- **Customer**: Order

## Description
Hello Support 
  
This is in regards to Canadian Tire PO ******** that we submitted PO acceptance, and then need to also send 870 for update as merchant requested to cancel.  
  
See case/email {color:rgba(0, 0, 0, 0.85)}CS-44821 Fwd: VENDOR ACTION REQUIRED{color}{color:rgba(0, 0, 0, 0.85)}{color} {color:rgba(0, 0, 0, 0.85)}> Online Only SKUs > Cancelled Customer Order{color}{color:rgba(0, 0, 0, 0.85)}{color} {color:rgba(0, 0, 0, 0.85)}V# G7D5 OA DREAM TEAM AMER{color}{color:rgba(0, 0, 0, 0.85)}{color}{color:rgba(0, 0, 0, 0.85)}{color} 

  

  
{color:rgba(0, 0, 0, 0.85)}Can you advise how we fix this error ?{color} 
  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
{color:#172B4D}{color}{color:#aa7900}{color}{color:#172B4D}{color}{color:#aa7900}{color}{color:#ffffff}{color}{color:#172B4D}{color}{color:#aa7900}{color}{color:#172B4D}{color}{color:#aa7900} DTA Orders{color}{color:#172B4D} 
 {color}{color:#aa7900}Owner Of The Future{color}{color:#172B4D}{color}{color:#797979}{color} 
 
 {color:#172B4D} 
 {color}{color:#797979}Toll Free: 1-844-DRM-TEAM{color}{color:#172B4D} 
 {color}{color:#797979}Phone: 844-DRMTEAM{color}{color:#172B4D} 
 {color}{color:#797979}Email: <EMAIL>{color}{color:#172B4D} 
 
  
{color}{color:#aa7900}www{color} {color:#172B4D}{color}{color:#aa7900}DreamTeamAmericas{color}{color:#172B4D}{color}{color:#aa7900}{color} {color:#172B4D}{color}{color:#aa7900}com{color}{color:#172B4D} 
{color}{color:#aa7900}BELIEVE{color}{color:#172B4D}{color}{color:#797979} in the{color}{color:#172B4D}{color}{color:#aa7900} DREAM TRUST{color} {color:#172B4D}{color}{color:#797979}in the{color} {color:#172B4D}{color}{color:#aa7900}TEAM{color}{color:#172B4D}{color} 
    {color:#aa7900}{color}{color:#172B4D}{color} 
{color:#aa7900} 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 {color}{color:#172B4D}{color}{color:#666666}{color}{color:#172B4D}{color}{color:#666666}{color}{color:#996604}CONFIDENTIALITY NOTICE:{color} {color:#666666}This email may contain privileged and or confidential information for the sole use of the intended recipient(s). Any unauthorized use, distribution or disclosure by others is strictly prohibited.{color}{color:#172B4D}{color} {color:#666666}{color}{color:#996604}Dream Team Americas INC & LLC{color}{color:#666666}{color}{color:#996604}{color} {color:#666666}2019/2022.{color}{color:#172B4D}{color}                   {color:#666666} 
{color}{color:#172B4D}{color}{color:#666666}Live Life #{color}{color:#172B4D}{color}{color:#aa7900}DreamTeaming{color}{color:#172B4D}{color}{color:#666666}{color}{color:#aa7900}{color} 

 {color:#666666}{color} {color:#172B4D}{color} {color:#aa7900}{color} {color:#172B4D}{color}                      {color:#ffffff}{color} {color:#aa7900}{color} {color:#172B4D}{color}   {color:#aa7900}{color} {color:#172B4D}{color}{color:#172B4D}{color}                         
   
 
  
 
{quote} 
Begin forwarded message: 
 
  {color:rgba(0, 0, 0, 1.0)}*From:*{color} "Dsco Support" <<EMAIL>>
  
  {color:rgba(0, 0, 0, 1.0)}*Subject:*{color}  *Notification from Integration Import for Dream Team Americas - CTC*
  
  {color:rgba(0, 0, 0, 1.0)}*Date:*{color} June 3, 2025 at 12:21:40 EDT
  
  {color:rgba(0, 0, 0, 1.0)}*To:*{color} <EMAIL>
  
 
  

There was a problem with your "Import" integration. We received the following message: 

Finished successfully, but issues were detected: 

Please follow these steps to review the logs for the job in question: 

1. Log into your Dsco account. 

2. Click the following link to view the logs: [https://app.dsco.io/automation/view-log/process_id/p683f20bce60d1214004340|https://mandrillapp.com/track/click/********/app.dsco.io?p=*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************] 

For a list of common error messages, please review this support article: [https://support.dsco.io/hc/en-us/articles/************-Common-Error-Messages|https://mandrillapp.com/track/click/********/support.dsco.io?p=**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************] 

==========================> SUMMARY <========================== 

* Failed records: 1. 

Please review the encountered errors in the failed records:  

- po_number ******** failed with message 'Sku 1749921 (line #1) has been over-accounted by quantity 1, update rejected'. 

- Dsco Support !https://mandrillapp.com/track/open.php?u=********&id=7d829f5c2eef446784a112bf9c24f133! 
| 
| {color:#606060}This email was sent to {color}[{color:#606060}{color}{color:rgb(64, 64, 64) !important}<EMAIL>{color}|mailto:<EMAIL>]{color:rgb(64, 64, 64) !important}{color}{color:#606060} {color}[{color:#606060}{color}{color:rgb(64, 64, 64) !important}unsubscribe from this list{color}|https://mandrillapp.com/track/click/********/mandrillapp.com?p=*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************]{color:rgb(64, 64, 64) !important}{color}{color:#606060}{color}{color:#606060}{color} | 
    {quote}

## Components


## Labels

