# EDI 846 Catalog Setup Guide

## Overview
The EDI 846 Inventory Inquiry/Advice transaction is used to communicate product catalog information and inventory levels between trading partners. This guide covers common setup scenarios and troubleshooting.

## Document Structure

### Basic 846 Segments
```
ISA - Interchange Control Header
  GS - Functional Group Header
    ST - Transaction Set Header (846)
      BIA - Beginning Segment for Inventory Inquiry/Advice
      CUR - Currency (if needed)
      DTM - Date/Time Reference
      REF - Reference Numbers
      N1 Loop - Name/Address Information
      LIN Loop - Item Identification
        LIN - Item Identification
        PID - Product/Item Description
        QTY - Quantity
        UIT - Unit Detail
        DTM - Date/Time (item level)
    SE - Transaction Set Trailer
  GE - Functional Group Trailer
IEA - Interchange Control Trailer
```

## Common Setup Scenarios

### 1. Basic Inventory Update
Used for regular inventory level updates to trading partners.

**Key Configuration**:
- BIA01 = "00" (Original)
- BIA02 = "SQ" (Stock Query)
- Include current on-hand quantities
- Update frequency per partner requirements

### 2. Product Catalog Distribution
Sharing complete product catalog with descriptions and attributes.

**Key Configuration**:
- Include detailed PID segments
- Multiple PID loops for different description types
- UPC/GTIN in appropriate LIN qualifiers
- Pricing information if required

### 3. Available to Promise (ATP)
Communicating future inventory availability.

**Key Elements**:
- Multiple QTY segments with different qualifiers
- DTM segments for availability dates
- Location-specific inventory if multi-warehouse

## Field Mapping Considerations

### LIN Segment Qualifiers
- UP = UPC/EAN Case Code (most common)
- UK = UPC/EAN Consumer Package Code
- VN = Vendor Item Number
- BP = Buyer's Part Number

### QTY Segment Qualifiers
- QH = Quantity on Hand
- QA = Quantity Available
- QC = Quantity Committed
- QP = Quantity Pending

### Common Mapping Issues
1. **Decimal Places**: Some partners require whole numbers only
2. **UOM Conversion**: Cases vs. Eaches vs. Pallets
3. **Date Formats**: CCYYMMDD vs. YYMMDD
4. **Location Codes**: Must match partner's system

## Implementation Best Practices

### 1. Data Validation
- Verify all product codes exist in partner's system
- Validate quantity calculations
- Check for negative inventory
- Ensure date logic is correct

### 2. Testing Approach
- Start with single item
- Test boundary conditions (zero inventory, large quantities)
- Verify all required fields populated
- Check partner's acknowledgment

### 3. Performance Considerations
- Batch updates efficiently
- Consider file size limits
- Implement incremental updates where possible
- Monitor processing times

## Troubleshooting Common Issues

### Issue: Partner Not Receiving Updates
**Check**:
- Communication logs for transmission errors
- 997 Functional Acknowledgments
- File naming conventions
- Timing of transmissions

### Issue: Quantity Mismatches
**Verify**:
- Unit of measure alignment
- Decimal handling
- Pack size conversions
- Aggregation logic

### Issue: Product Not Found Errors
**Review**:
- Product identifier qualifiers
- Leading zeros in codes
- Check digits on UPCs
- Case sensitivity

### Issue: Date/Time Errors
**Confirm**:
- Timezone handling
- Date format requirements
- Effective date logic
- Cutoff time processing

## Partner-Specific Requirements

### Retail Partners
- Often require location-level inventory
- May need promotional pricing flags
- Seasonal item indicators
- Discontinuation dates

### Distribution Partners
- Focus on available quantities
- Lead time requirements
- Minimum order quantities
- Pallet configurations

### Manufacturing Partners
- Component level inventory
- Lot tracking information
- Expiration dates
- Quality status codes

## Monitoring and Maintenance

### Regular Checks
- Monitor 997 acknowledgments
- Verify inventory accuracy
- Check for stuck/pending files
- Review error logs

### Optimization Opportunities
- Reduce file frequency if over-sending
- Combine multiple updates
- Implement delta processing
- Archive old transmissions

---
*This guide is based on real-world implementations. Always consult specific partner documentation for their unique requirements.*