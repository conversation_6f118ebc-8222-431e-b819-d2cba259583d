<h3 id="meeting-transcription">Meeting Transcription</h3>
<p>Meeting started: May 19, 2025, 2:52:43 PM Meeting duration: 58 minutes Meeting participants: <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="generated-content">Generated Content</h4>
<h3 id="current-problems-faced-by-darian">Current Problems Faced by <PERSON><PERSON></h3>
<ol>
<li><strong>Service Issues</strong>    - <PERSON><PERSON> is dealing with issues related to the <strong>Universal Metal Server</strong>.    - There are concerns about whether the services are properly set up and running.</li>
<li><strong>Connection Problems</strong>    - <PERSON><PERSON> is uncertain about the connection status and needs to test it.    - There is a need to verify if the <strong>DTS (Data Transfer Service)</strong> and <strong>FTTS (File Transfer Task Service)</strong> are functioning correctly.</li>
<li><strong>Script and Scheduler Verification</strong>    - <PERSON><PERSON> needs assistance in checking the <strong>scheduler</strong> to ensure it is operational.    - There is a requirement to review the script for the correct credentials and configurations.</li>
<li><strong>File Staging</strong>    - <PERSON><PERSON> is waiting for files to be staged correctly and needs confirmation that the process is working as intended.</li>
</ol>
<h3 id="support-needed-from-nicola<PERSON>-and-maria">Support Needed from <PERSON> and <PERSON></h3>
<ul>
<li><strong>Testing Connections</strong>: Help in testing the connection to ensure that the services are operational. - <strong>Service Management</strong>: Assistance in managing and verifying the status of the services. - <strong>Script Review</strong>: Collaborate on reviewing the script to ensure all parameters are set correctly. - <strong>File Handling</strong>: Support in staging files and checking their locations to ensure they are processed correctly.</li>
</ul>
<h3 id="summary-darian-is-facing-multiple-technical-issues-related-to-server-services-connection-testing-script-verification-and-file-staging-requiring-collaborative-support-from-nicolas-and-maria-to-resolve-these-problems-efficiently">Summary Darian is facing multiple technical issues related to server services, connection testing, script verification, and file staging, requiring collaborative support from Nicolas and Maria to resolve these problems efficiently.</h3>
<h4 id="generated-content-2">Generated Content</h4>
<p>The problem with Darian appears to be related to issues with file transfers and the configuration of the DTS (Data Transfer Service). Here are the key points regarding the situation:</p>
<ol>
<li><strong>Connection Issues</strong>:    - There were concerns about whether the services were properly set up and running, particularly the DTS and FD services.</li>
<li><strong>File Staging</strong>:    - Maria mentioned that files she staged were not moving into the designated folder, indicating a potential issue with the file transfer process.</li>
<li><strong>Script Configuration</strong>:    - There was confusion regarding the script being used, particularly the file
paths and whether they were correctly pointing to the necessary directories.</li>
<li><strong>Path Issues</strong>:    - It was discovered that the target path for the file transfers was not correctly set, leading to files not being picked up as expected.</li>
<li><strong>Testing and Troubleshooting</strong>:    - The team was engaged in testing the connection and checking various configurations to identify where the breakdown was occurring.
Overall, the problem seems to stem from a combination of configuration errors, potential service issues, and file path discrepancies that are affecting the file transfer process.</li>
</ol>
<h4 id="generated-content-3">Generated Content</h4>
<p>The meeting involved a technical discussion among team members, including Nicolas, Maria, Michael, and Darian, regarding issues with file transfers and server connections. Here are the key details:</p>
<h3 id="participants-nicolas-maria-michael-darian">Participants: - <strong>Nicolas</strong> - <strong>Maria</strong> - <strong>Michael</strong> - <strong>Darian</strong></h3>
<h3 id="main-topics-discussed-1-file-transfer-issues-file-pickup-the-team-discussed-that-files-are-picked-up-from-their-end-rather-than-pushed-on-a-scheduled-basis-testing-file-transfers-maria-suggested-dropping-a-backup-file-to-see-if-it-shows-up-in-the-system">Main Topics Discussed: 1. <strong>File Transfer Issues</strong>    - <strong>File Pickup</strong>: The team discussed that files are picked up from their end rather than pushed on a scheduled basis.    - <strong>Testing File Transfers</strong>: Maria suggested dropping a backup file to see if it shows up in the system.</h3>
<ol start="2">
<li><strong>Server Connection and Configuration</strong>    - <strong>Universal Metal Server</strong>: Darian mentioned restarting the service but was unsure of the background issues.    - <strong>Service Status</strong>: Nicolas inquired about the status of the DTS (Data Transfer Service) and FTTS (File Transfer Service), confirming that they were disabled as expected.</li>
<li><strong>Testing Connections</strong>    - <strong>Connection Testing</strong>: The team tested the connection and confirmed that it was successful.    - <strong>Script Verification</strong>: They reviewed the script for file paths and credentials, ensuring they matched the expected configurations.</li>
<li><strong>File Staging Issues</strong>    - <strong>Staging Files</strong>: Maria mentioned that after restaging files, they were not moving into the designated folder, indicating a potential issue on their end.    - <strong>Path Verification</strong>: There was confusion regarding the target path for file transfers, with Maria identifying that the path was not pointing to the correct drive.</li>
<li><strong>Next Steps</strong>    - <strong>Further Investigation</strong>: The team decided to check the client and the D drive for any files that may not have been processed correctly.    - <strong>Account Verification</strong>: They confirmed that the account credentials were correct and that the connection to the SAP system was functioning.</li>
</ol>
<h3 id="summary-the-meeting-focused-on-troubleshooting-file-transfer-issues-related-to-the-universal-metal-server-verifying-server-configurations-and-ensuring-that-file-paths-were-correctly-set-up-for-successful-transfers-the-team-planned-to-investigate-further-to-resolve-the-issues-identified">Summary: The meeting focused on troubleshooting file transfer issues related to the Universal Metal Server, verifying server configurations, and ensuring that file paths were correctly set up for successful transfers. The team planned to investigate further to resolve the issues identified.</h3>
<h4 id="generated-content-4">Generated Content</h4>
<p>The solution involved several troubleshooting steps to address the file transfer issues between the systems. Here are the key points of the solution:</p>
<ol>
<li><strong>Connection Verification</strong>:    - Confirmed that the connection between the systems was functioning correctly.    - Tested the credentials and ensured that the scheduled tasks were running.</li>
<li><strong>File Staging</strong>:    - Rescheduled and restaged files to ensure they were being picked up correctly.    - Dropped test files manually to check if they were processed.</li>
<li><strong>Script Review</strong>:    - Reviewed the scripts for any discrepancies in file paths and configurations.    - Corrected the host name and file paths in the scripts to ensure they matched the expected locations.</li>
<li><strong>Scheduled Task Adjustment</strong>:    - Identified that the scheduled tasks were not functioning as intended and needed to be run manually.    - Suggested reconfiguring the scheduled tasks to ensure they operate automatically without manual intervention.</li>
<li><strong>Log Checking</strong>:    - Checked logs to identify any errors or issues with file transfers.    - Confirmed that files were being received but were not appearing in the expected locations due to timing issues.</li>
<li><strong>Communication</strong>:</li>
</ol>
<ul>
<li>Planned to inform the relevant parties about the resolution and ensure they were aware of the changes made.
By following these steps, the team was able to resolve the file transfer issues and ensure that files were being processed correctly.</li>
</ul>
<h4 id="generated-content-5">Generated Content</h4>
<h3 id="problem-and-solution-overview">Problem and Solution Overview</h3>
<h4 id="problem-the-team-was-experiencing-issues-with-file-transfers-between-their-system-and-a-client-s-system-specifically-files-were-not-being-picked-up-as-expected-leading-to-confusion-and-delays-key-points-of-the-problem-included-files-were-being-sent-but-not-appearing-in-the-expected-directories-error-files-were-being-generated-instead-of-successful-transfers-there-were-inconsistencies-in-file-paths-and-scheduled-tasks">Problem: The team was experiencing issues with file transfers between their system and a client’s system. Specifically, files were not being picked up as expected, leading to confusion and delays. Key points of the problem included: - Files were being sent but not appearing in the expected directories. - Error files were being generated instead of successful transfers. - There were inconsistencies in file paths and scheduled tasks.</h4>
<h3 id="step-by-step-breakdown-of-the-problem-and-solution">Step-by-Step Breakdown of the Problem and Solution</h3>
<h4 id="1-initial-identification-of-the-problem-observation-files-sent-from-the-team-were-not-being-picked-up-by-the-client-s-system-error-files-some-files-were-being-converted-into-error-files-indicating-a-failure-in-the-transfer-process-communication-team-members-discussed-the-issue-and-confirmed-that-files-were-being-sent-every-60-minutes-without-updates">1. <strong>Initial Identification of the Problem</strong>    - <strong>Observation</strong>: Files sent from the team were not being picked up by the client’s system.    - <strong>Error Files</strong>: Some files were being converted into error files, indicating a failure in the transfer process.    - <strong>Communication</strong>: Team members discussed the issue and confirmed that files were being sent every 60 minutes without updates.</h4>
<h4 id="2-testing-and-verification-testing-connection-the-team-tested-the-connection-to-ensure-that-the-systems-could-communicate-file-staging-maria-restaged-files-to-check-if-they-would-appear-in-the">2. <strong>Testing and Verification</strong>    - <strong>Testing Connection</strong>: The team tested the connection to ensure that the systems could communicate.    - <strong>File Staging</strong>: Maria restaged files to check if they would appear in the</h4>
<p>designated folder.    - <strong>Scheduler Check</strong>: The scheduler was verified to ensure it was running correctly.</p>
<h4 id="3-identifying-configuration-issues-script-review-the-script-used-for-file-transfers-was-reviewed-for-accuracy-particularly-focusing-on-file-paths-path-verification-the-team-discovered-discrepancies-in-the-file-paths-being-used-in-the-script-versus-what-was-expected-scheduled-task-issues-it-was-noted-that-the-scheduled-tasks-might-not-be-executing-as-intended">3. <strong>Identifying Configuration Issues</strong>    - <strong>Script Review</strong>: The script used for file transfers was reviewed for accuracy, particularly focusing on file paths.    - <strong>Path Verification</strong>: The team discovered discrepancies in the file paths being used in the script versus what was expected.    - <strong>Scheduled Task Issues</strong>: It was noted that the scheduled tasks might not be executing as intended.</h4>
<h4 id="4-troubleshooting-steps-taken-manual-file-drop-nicolas-manually-dropped-a-test-file-to-see-if-it-would-be-picked-up-by-the-system-running-scheduler-the-team-ran-the-scheduler-to-check-if-it-would-process-the-files-correctly-log-checking-logs-were-reviewed-to-identify-any-errors-or-issues-during-the-file-transfer-process">4. <strong>Troubleshooting Steps Taken</strong>    - <strong>Manual File Drop</strong>: Nicolas manually dropped a test file to see if it would be picked up by the system.    - <strong>Running Scheduler</strong>: The team ran the scheduler to check if it would process the files correctly.    - <strong>Log Checking</strong>: Logs were reviewed to identify any errors or issues during the file transfer process.</h4>
<h4 id="5-resolution-of-issues-correcting-file-paths-the-team-corrected-the-file-paths-in-the-script-to-ensure-they-matched-the-expected-locations-scheduler-functionality-it-was-determined-that-the-scheduled-tasks-were-not-functioning-properly-and-adjustments-were-made-to-ensure-they-would-run-as-expected-successful-transfers-after-making-the-necessary-adjustments-files-began-to-transfer-successfully-and-the-team-confirmed-that-they-could-see-the-files-in-the-correct-directories">5. <strong>Resolution of Issues</strong>    - <strong>Correcting File Paths</strong>: The team corrected the file paths in the script to ensure they matched the expected locations.    - <strong>Scheduler Functionality</strong>: It was determined that the scheduled tasks were not functioning properly, and adjustments were made to ensure they would run as expected.    - <strong>Successful Transfers</strong>: After making the necessary adjustments, files began to transfer successfully, and the team confirmed that they could see the files in the correct directories.</h4>
<h4 id="6-final-verification-inbound-and-outbound-checks-the-team-confirmed-that-both-inbound-and-outbound-file-transfers-were-functioning-correctly">6. <strong>Final Verification</strong>    - <strong>Inbound and Outbound Checks</strong>: The team confirmed that both inbound and outbound file transfers were functioning correctly.</h4>
<ul>
<li><strong>Communication with Client</strong>: Nicolas planned to inform the client about the resolution and ensure they were aware of the changes made.</li>
</ul>
<h3 id="summary-of-solutions-path-corrections-ensured-that-all-file-paths-in-the-transfer-scripts-were-correct-scheduler-adjustments-verified-and-adjusted-the-scheduled-tasks-to-ensure-they-executed-properly-manual-testing-conducted-manual-tests-to-confirm-that-the-system-was-functioning-as-expected-after-changes-were-made">Summary of Solutions - <strong>Path Corrections</strong>: Ensured that all file paths in the transfer scripts were correct. - <strong>Scheduler Adjustments</strong>: Verified and adjusted the scheduled tasks to ensure they executed properly. - <strong>Manual Testing</strong>: Conducted manual tests to confirm that the system was functioning as expected after changes were made.</h3>
<h3 id="key-takeaways-importance-of-accurate-configuration-proper-configuration-of-file-paths-and-scheduled-tasks-is-crucial-for-successful-file-transfers-effective-communication-continuous-communication-among-team-members-is-essential-for-troubleshooting-and-resolving-issues-testing-and-verification-regular-testing-and-verification-of-systems-can-help-identify-problems-before-they-escalate">Key Takeaways - <strong>Importance of Accurate Configuration</strong>: Proper configuration of file paths and scheduled tasks is crucial for successful file transfers. - <strong>Effective Communication</strong>: Continuous communication among team members is essential for troubleshooting and resolving issues. - <strong>Testing and Verification</strong>: Regular testing and verification of systems can help identify problems before they escalate.</h3>
<h3 id="potential-follow-up-actions-documentation-create-detailed-documentation-of-the-configuration-settings-and-troubleshooting-steps-for-future-reference-monitoring-implement-monitoring-tools-to-alert-the-team-of-any-future-issues-with-file-transfers-training-provide-training-for-team-members-on-the-importance-of-configuration-management-and-troubleshooting-techniques">Potential Follow-Up Actions - <strong>Documentation</strong>: Create detailed documentation of the configuration settings and troubleshooting steps for future reference. - <strong>Monitoring</strong>: Implement monitoring tools to alert the team of any future issues with file transfers. - <strong>Training</strong>: Provide training for team members on the importance of configuration management and troubleshooting techniques.</h3>
<h4 id="transcript">Transcript</h4>
<p>00:00 Michael C.: Hello, migos. I&#39;m just gonna use the bathroom real quick. 00:05 Nicolas S.: Sure, take your time. 04:25 Maria K.: They&#39;re not here, right?
04:27 Michael C.: Yes, there. 04:27 Nicolas S.: No, not yet. 05:24 Maria K.: Mega, I was talking about myself. Hold on, turning the camera. It&#39;s his school at 3 Nico. 05:32 Nicolas S.: This is what? 05:35 Maria K.: At 3 pm my time. 05:37 Nicolas S.: Yeah. 05:38 Maria K.: Okay. Did you hear what I say earlier or I was a mute? 05:45 Nicolas S.: Your polymute. 05:46 Maria K.: Oh, I was saying that the relationship. We have a ump is that they actually pick up files from us and drop files. It&#39;s not like we&#39;re pushing it in a scheduled basis, you know what I mean? 05:58 Nicolas S.: Weird. 05:59 Maria K.: So yeah, they actually pick it up. So yeah, we&#39;ll see what local show is gonna say. Because I told Sandy there was no visual. You know, that I can see, that&#39;s something is wrong. 06:13 Maria K.: unless they say, so, I know, right? That&#39;s just happened two weeks ago. That&#39;s weird. And you know what? The first thing we should do. Is drop one of the files. You know, the ones in the backup file. Are. Already stage and see if it shows up in the from the TS. 06:46 Nicolas S.: Yeah, I like that one better. 06:47 Maria K.: Right, let&#39;s do that. Let&#39;s see that. Let me just let me pull it up. Let me pull up, you. Know, it&#39;s the target. Today they have files, right? So yeah. All right, how there&#39;s stuff up. Possible equals. 07:30 Nicolas S.: Yeah. 07:41 Maria K.: You know what? Let me test something right before you. Let me do it. And repeat that over. On the DS. so, Just sharing my skin. 08:42 Nicolas S.: All right, there. 08:45 Maria K.: Okay. 08:46 Nicolas S.: Close the Closure screen. 08:47 Maria K.: Yeah. 08:53 Nicolas S.: Darien. 08:54 Darian : Hey, there Nick, are you doing?
08:55 Nicolas S.: Are you doing today, bro? 08:58 Darian : Not too bad man. Not too bad, this other issues kind of crazy. 09:03 Nicolas S.: Yeah, this one is this one. Seems all, by the way I have Maria and Michael with me. Darren. I don&#39;t know if you met him before Maria and Michael, we&#39;ll be joining this call. Just to see maybe the we could figure this out quicker than 09:17 Nicolas S.: Then possible. So 09:19 Darian : Yeah. 09:20 Nicolas S.: Maria Darien Michael Very 09:21 Maria K.: Darian. 09:23 Darian : hey there, y&#39;all 09:27 Michael C.: Hi there. 09:28 Darian : I&#39;m gonna go ahead and take us to the Universal Metal Server. I don&#39;t know. 09:32 Nicolas S.: Yep. 09:36 Darian : Unfortunately, anything preemptive that was done. What I, I may just restarted the service but don&#39;t know any other kind of background, she was kind of upset off the bat and I 09:50 Nicolas S.: All right. Sorry we&#39;re gonna touch all bases today. Basically daring, what I 09:53 Darian : Unfortunately couldn&#39;t get a lot of information out. 10:01 Nicolas S.: want to do, I want to make sure that they&#39;re their current setup. So for example, they&#39;re using the services right but do can you open up the scheduler Because if they&#39;re using the new setup, the services should have been shut down. Remember we had that issue with another customer. 10:21 Darian : Oh, he did. 10:23 Nicolas S.: Right? So we need to, we need to shut down the services. 10:27 Darian : What is a service again? 10:29 Nicolas S.: If DTS file transfer. Dts. 10:36 Darian : And even FD. 10:38 Nicolas S.: I here. No. I I saw it in your list. 10:39 Darian : All right. 10:42 Nicolas S.: No, no. I certain the list by five, if you are range, if you organize
it, there it is. At the top third. 10:50 Darian : Oh, dts at Ftts. Let&#39;s disabled already. 10:55 Nicolas S.: Okay, so that&#39;s disabled. That&#39;s good. The scheduler is up and running, right? 11:01 Darian : Mmm. 11:03 Nicolas S.: Okay, so that&#39;s up and running. Let&#39;s let&#39;s how about we test connection really quick? 11:09 Darian : Okay. 11:11 Nicolas S.: um, The credentials should be in the script. Yep, yep. And that script exactly. 11:18 Darian : Yeah. 11:22 Nicolas S.: Scheduled task. 11:31 Darian : That wasn&#39;t even. Oh yeah. 11:33 Nicolas S.: It&#39;s the second tab. Yeah. So that&#39;s good. Yep, should be somewhere in the try. 11:48 Darian : oh, 11:49 Nicolas S.: There there, 30? Yeah, from 32 to 33, 31 to 33. No, no. The host name is the, the URL. 12:03 Darian : This. Yeah, sir. 12:06 Nicolas S.: Yeah. The Maria the three staging the file probably gonna take a couple minutes so give it some time. 12:22 Maria K.: Yeah, I&#39;ll wait for it unto you guys do this. And then, 12:32 Darian : I did a whole line. Seems that works. 12:45 Nicolas S.: So there is connection so you can connect right? That&#39;s perfect. 12:47 Darian : 12:47 Maria K.: very, 12:51 Nicolas S.: Alright. So what does the script say about the from DTS for location? So, that should be in line. Seventies something. Let me go to my notes. That&#39;s not okay. 13:09 Darian : TX Global. I mean exists. 13:43 Nicolas S.: Go to Line 158 for me. What I have 50. That&#39;s word. 14:22 Darian : so, the old script from 2023, 14:25 Nicolas S.: Yeah.
14:26 Darian : That helps. 14:27 Nicolas S.: All right, so 50 40. 48, 49 are good. 74. 74 is ABS global DTS from DTS. Okay. So that 14:46 Darian : Yeah. 14:46 Nicolas S.: That&#39;s where it&#39;s gone. 14:48 Darian : 14:48 Nicolas S.: All right, cool. So go to that folder. Maria, did you restaged the file? 14:53 Maria K.: I can do it now again, but I did four. So let me 14:54 Nicolas S.: Yeah, you did. You did already right? 14:57 Maria K.: See, I mean let me do it again. Okay, I chose restage one. 15:03 Nicolas S.: Okay. 15:04 Maria K.: And I mean, and sure. Already, maybe check the folder. Change. 16:10 Maria K.: Vehicle. I resched you guys not saying I don&#39;t understand. 16:14 Nicolas S.: no, no no, yeah, don&#39;t worry you 16:17 Maria K.: And then do it. 16:18 Nicolas S.: No, no, yeah. 16:19 Maria K.: Okay. 16:23 Nicolas S.: And pay. 16:28 Maria K.: and checking in to see if there&#39;s another 16:39 Nicolas S.: Here it is. All right. Darian, do me a favor? Use up. instead of using the, When SAP client and go straight to the file you in the File Explorer, 16:54 Darian : Where my God? 16:57 Nicolas S.: Yeah. And from DTS Open it up. Alright, give me one second. Stay there for me. Okay. All right. 17:20 Maria K.: Yeah. I was gonna say Nico to drop a test 5 and see if it&#39;s set up because every staging I don&#39;t see it moving. So let&#39;s see. 17:28 Nicolas S.: All right, refresh the, the folder. 17:37 Darian : Nothing. 17:39 Maria K.: It didn&#39;t have. They haven&#39;t picked it up yet, it&#39;s still there. 17:41 Nicolas S.: Um, run the scheduler. 17:42 Darian : We sucked around the schedule. Yeah. 17:44 Nicolas S.: Yeah.
18:02 Darian : I&#39;ll say anything. 18:08 Nicolas S.: Okay. All right, Coach. 18:11 Maria K.: oh, That&#39;s weird. 18:19 Nicolas S.: In, why did it change all of a sudden? 18:22 Maria K.: Yeah. 18:23 Nicolas S.: Go to go to the C drive. 18:24 Darian : That&#39;s right. Yeah. 18:40 Nicolas S.: I&#39;ll go to DTS. 18:46 Darian : Not the one SAP login. This 18:48 Nicolas S.: oh, No, no. Go to details. and then added or open up in a notepad that dts that config Yeah. All right, so that from DTS is the same. Is that the same file location in this script? This is the old script. This doesn&#39;t this shouldn&#39;t even be running it is right? 19:10 Darian : Yeah. Yeah. For the most part. Minus the let&#39;s take the passwords are different and also 19:18 Nicolas S.: Yeah, of course but I&#39;m mostly importantly, the location file path. 19:22 Darian : Yeah. Yeah. 19:24 Nicolas S.: From details, global. 20:02 Nicolas S.: Um, let me see, let me see your script and don&#39;t close this. 20:09 Darian : Wait a second about this one. 20:11 Nicolas S.: Yeah, down and go to lines. What was it 48? No 50. Close it. 74. 74. is the from the Ail. Yeah. Okay. So Yeah. Okay, so we have from DTS Apps, Global DTS from DTS and here we have Just basically the D Drive apps, Global DTS from DTS. I mean. Give me a second. Let me open up your Original. Sound for Dts. Yeah. I mean. 20:58 Maria K.: Everything looks good on his and make, all right. 21:01 Nicolas S.: Yeah, everything is is as it should be on his end. 21:04 Maria K.: Well I think it&#39;s the problem is in our end that we have the check because when I restaged the file it&#39;s not moving into the folder and the test file is still 21:13 Maria K.: there waiting to be picked up. 21:16 Nicolas S.: Yeah. Well I, At least he should be able to see the test file, the restage file. I mean, yeah, we can look into that, but at least at the moment, he
should be able to see my light. 21:26 Maria K.: Be sure. Okay, to see it. Okay? Because it&#39;s already there for the waiting for them to. Yeah, okay. 21:31 Nicolas S.: Exactly, exactly. 21:34 Maria K.: So, let&#39;s take care of that. I just 21:56 Maria K.: Passwords are good, right? Nico and the users username is the same. 22:01 Nicolas S.: Yeah, I don&#39;t think anything would have changed. 22:06 Darian : I mean, it, we can get in to it on the one scp side. 22:09 Nicolas S.: Exactly when SAP is working. So the connection is good. I&#39;ll open up this from Dts in here. 22:18 Maria K.: oh, Nico, I know what&#39;s going. 22:20 Nicolas S.: Huh. 22:22 Darian : 1969. That&#39;s 22:24 Maria K.: Nico. 22:25 Nicolas S.: What? 22:26 Maria K.: Their ass their path. 22:28 Nicolas S.: Yeah. 22:28 Maria K.: Is, is not a T drive. It&#39;s the client. so, The target path is decline / 2751. Let me check, let me see why I was. 22:41 Nicolas S.: Yeah, all have go to the D drive. Well, most goes to the D drive first, then to get, they get sent to the T drive. 22:49 Maria K.: With tea. Okay, let me just go and see if anything is stopping. 22:51 Nicolas S.: Yeah. 22:55 Maria K.: No, nothing is there. The D drive. So maybe it&#39;s worth checking. The client. And see if anything is there. Was the account to what the BTS account to 751. 23:21 Darian : 27 Are to 751. Yeah. 23:21 Maria K.: Okay, perfect. So there&#39;s nothing in it from BTS stage from this. Yes, there&#39;s nothing stage 2. DPS. There&#39;s nothing. Intuitive or something. 23:37 Darian : I&#39;m confused though too. Because on their D drive, it looks like stuff has been moving. If I go to DTS, 519 102. Something was in there. Five, 19. 23:54 Maria K.: Yeah.
23:55 Nicolas S.: Exactly. This is exactly the, this is exactly what we were looking at before. 23:56 Maria K.: Yeah. 23:59 Nicolas S.: You got to go Darian, we know we noticed that there was files being sent over to you and move to your to your 24:08 Maria K.: Back. 24:09 Nicolas S.: To your backup drive, but for some reason, you&#39;re not seeing them. That&#39;s what 24:09 Darian : Yeah. 24:14 Nicolas S.: we&#39;re trying to figure out why you don&#39;t see. 24:17 Darian : Yeah, I don&#39;t know what they&#39;re doing. It manually. Yeah. 24:22 Nicolas S.: Yeah, but you only have three, we should have sent. 24:26 Maria K.: Now, we send let me go to the background. 24:48 Nicolas S.: There&#39;s quite a few footer today. 24:49 Maria K.: Is my finals here. We sing a lot actually. Yeah. We sent a lot of files. starting at At 12 6, am. 25:00 Darian : Oh, there&#39;s an errors. What? 25:10 Nicolas S.: There they are. 25:20 Maria K.: So, you&#39;re getting air file. 25:23 Darian : All right, yes they are. I don&#39;t know. 25:36 Maria K.: Why are there converting into error files? They don&#39;t look like that in our end. 25:40 Nicolas S.: They don&#39;t. 25:47 Darian : Yeah, I couldn&#39;t tell you. I don&#39;t know anything correlation to At least that. There are they coming in as aerophiles? 26:01 Maria K.: I mean, we&#39;re the file name is Normal the same that has not changed and I&#39;m using something. So we send files every 60 minutes. Nothing has been updated an hour and that I can see me. So, 26:51 Maria K.: So, you&#39;re getting the files. Are there ending in your backup instead, correct? 26:58 Darian : That&#39;s what it looks like, at least. 27:00 Maria K.: Why is that? 27:04 Nicolas S.: You know what? Let me try. One more thing. I&#39;m gonna drop this
test file on the D drive, Just to see what happens. 27:11 Maria K.: Okay. Are you manually dropping at him or you&#39;re like doing every stage and checking the D drive Nico? 27:28 Nicolas S.: No, I&#39;m manually dropped. 27:30 Maria K.: Okay. Let me also go the client. 27:32 Nicolas S.: Because there&#39;s stuff in here. 27:51 Maria K.: Did they go through stage or that&#39;s a different thing? Like Stage-friendly DS. 27:56 Nicolas S.: No, that&#39;s different. 27:57 Darian : And that&#39;s it. 27:58 Maria K.: Okay. Also, there are three files now there. Other. There is the file. There&#39;s two files from Yeah, just now the ones who dropped they&#39;re there. Let me do a run now and see. 28:20 Nicolas S.: Yeah. 28:21 Darian : Yeah, I&#39;m gonna run the schedule task for now. 28:22 Maria K.: And yeah. 28:24 Nicolas S.: Yeah, you can run the schedule task. 28:24 Maria K.: I&#39;m gonna just Yeah, do that. Yeah, because we don&#39;t control the schedule. 28:37 Darian : Yeah. 28:38 Nicolas S.: You could open. Open up the from details. 28:45 Darian : Nothing. 28:48 Nicolas S.: Yes, Google is exactly as it should. 28:59 Maria K.: Yeah, they&#39;re not moving. Let me do their normal mistake. 29:56 Maria K.: You go in the script. Looks good. That&#39;s correct. Okay. I was wondering because I, even restage one from data, and Recently. And I don&#39;t see it on the D drive. Into is that unico into 19? 30:25 Nicolas S.: I just dropped the test file, I didn&#39;t drop anything else. 30:28 Maria K.: You haven&#39;t dropped regular files. 30:29 Nicolas S.: No. 30:31 Maria K.: Also, that&#39;s mean so they&#39;re what I received. Oh, 30:34 Nicolas S.: Yeah. 30:35 Maria K.: Okay, they just haven&#39;t moved.
30:37 Nicolas S.: Right. 30:39 Maria K.: oh, So, after this, They go through the T drive, correct? 30:48 Nicolas S.: Yep. Yep. 30:49 Maria K.: From DTS, all right. 31:11 Nicolas S.: Okay, we&#39;re going to do something different. Let me, let me see your your script Darian, I think Think I found some odd. All right, that&#39;s script. Go to line. Go to line. Where&#39;s my script 34? 34. Okay, so that and G z. I Oh G7i. Okay, all right. So do me a favor? Check your win. Scp Go to from DTS. 31:57 Darian : On one side. 31:59 Nicolas S.: On that side, the right side. 32:00 Darian : Then. 32:10 Nicolas S.: Something is wrong log off. So close this session. Okay, and then, open it up, open up an sap again because I logged into windows, AP and I see the father, Oh, I see what&#39;s wrong, I see what&#39;s wrong. 32:31 Darian : What is it? 32:32 Nicolas S.: Use the use the The host name is incorrect. If you look at line, 30 32, 32:43 Maria K.: Oh, maybe. That&#39;s why. 32:45 Darian : Oh my gosh. 33:00 Nicolas S.: And then I use the password and line 34. There&#39;s my test drive, there&#39;s my test file. so, 33:18 Maria K.: He can see the other two that are there. 33:22 Nicolas S.: No, because they&#39;re not there in the T drive just yet. 33:22 Darian : More. 33:26 Nicolas S.: So our side hasn&#39;t processed, the the stuff indeed drive just yet. 33:26 Maria K.: How? 33:32 Nicolas S.: Are our stuff, the files you restaged. It should be automatic if I&#39;m looking at the amount if I&#39;m looking at the ECs manager. Universal metal products dash out is scheduled immediate. Send So, it should be within a minute or two. so, you should be able to see that test file that you see when SCP In your regular directory. Yeah. And this one 34:19 Maria K.: All my files disappear. 34:23 Nicolas S.: Okay, check the T drive.
34:25 Maria K.: I&#39;m going to the T drive. Okay? 34:34 Nicolas S.: Yeah, they&#39;re in the teacher. 34:39 Maria K.: So we&#39;re looking good over here so far and I&#39;ve found it. Yes. Okay, files on the phone dts right into Big picked up. 34:48 Nicolas S.: Yeah, exactly. 34:50 Maria K.: So we&#39;re our file cycle is working as expected. 34:54 Nicolas S.: Alright, so do me a favor Darian and forgive me for being such a pain. Go back to the go. Back to the script. Yeah. Go back to the script. Grab line, grab the path that you have in this. Full in this script for from the TS, which is in past 74, I think it is. 35:16 Darian : Yeah. 35:17 Nicolas S.: Grab that path and then go straight to that path in your, in your file director. 35:23 Darian : Yeah, already, I did that earlier. 35:25 Nicolas S.: You already did that, right? Okay, so that goes to here. 35:26 Darian : Yeah. 35:35 Nicolas S.: Then why? Is the file not here. 35:44 Maria K.: he can&#39;t see the file right to 35:47 Nicolas S.: Neither, my test file, nor the two files that you restaged. 35:51 Maria K.: oh, I mean they&#39;re still sitting in here so those are 35:53 Nicolas S.: Yeah, the exactly just be the mere fact that you can see them there Maria and he 35:59 Maria K.: Yeah. 35:59 Nicolas S.: can, that&#39;s a problem. 36:00 Maria K.: I know. 36:03 Nicolas S.: This. 36:19 Maria K.: Yeah. Oh yeah, there is a Pickup. 36:37 Nicolas S.: Okay, one last thing, Open Task Manager, Receive Services is still for some reason running. 36:45 Maria K.: Oh wait. They&#39;re right here is not isn&#39;t is not an issue. I think that&#39;s an issue. Nico. You know, where is his rights? So, there&#39;s name. Isn&#39;t that like, That&#39;s how the permission issue used to look like, I know you said there&#39;s no permission issue and this one but that&#39;s how I used to look like,
37:14 Maria K.: The end is that a NW, something is so small, and barely read. 37:19 Darian : Yeah, it&#39;s a car. 37:20 Maria K.: Are something I think that might be the problem. Nico. 37:33 Maria K.: How to fix that? Idea. 37:36 Nicolas S.: You know? No I&#39;m not. I&#39;m not convinced that that&#39;s wrong. 37:42 Darian : that&#39;s like, Yeah. 37:49 Maria K.: um, 38:00 Darian : It&#39;s probably the naming convention. Is it not? 38:05 Maria K.: No, we don&#39;t have naming conventions rules in our side for you guys. 38:05 Nicolas S.: You know. for outbound there for outbound files, we don&#39;t have 38:12 Maria K.: For inbound, yes. But wait, I are the imbalance working. Let me just double check. Let me go into. What video and go to that town. So I know how brown we having a problem, but let me check. 38:32 Maria K.: Just check the server here. 38:52 Maria K.: Universal. Okay, environment. 39:32 Maria K.: I think they just Pick up files from us. I don&#39;t see. Hold on. You and P. Oh yeah, ump in let me check 856. We&#39;re getting files from them because we just process today. in the morning, somebody some ascends so 39:53 Nicolas S.: Okay, so the inbound is working. 39:54 Maria K.: Yep. So like I just checked, um, Universal metal. always just Make a, Where&#39;s your ticket? I can add the inbound that way. You know we crossed 40:20 Maria K.: that one that is checked. 40:23 Nicolas S.: Where is my, what? 40:25 Maria K.: You&#39;re taking your chair? Let me find it. Okay? 40:29 Maria K.: I think I send it to you this morning, right? Yeah. Okay. No, that&#39;s not. so, 41:08 Maria K.: Know what that way. That I have coming. Okay, so 42:14 Maria K.: So, when it go in long as the 42:30 Nicolas S.: All right. Why is this not working on? Darren&#39;s cloud. 42:38 Maria K.: so, if we are able to grab files, You&#39;re not able to pick up. Did you check the script and Nicolas? You said that they match what they have? 42:50 Nicolas S.: Yeah. Yeah, the script is good.
42:51 Maria K.: We have 42:54 Nicolas S.: The path is good. 42:56 Maria K.: Yeah. 43:01 Nicolas S.: Where&#39;s the? What line is the 43:13 Maria K.: Yeah, there&#39;s something wrong with picking up because we are receiving. 43:29 Nicolas S.: And 32 33, 34 40. Okay. Darren the meat. Let me see. Here the script again, go to line. 43:42 Darian : Yes. 43:49 Nicolas S.: 58. Why does it? Why do I have 58 As the backup path? I don&#39;t think 58 is a backup path line. No, it&#39;s 44:01 Darian : 60. 44:02 Nicolas S.: 60. Yeah, okay, we fix that. Okay. So that&#39;s Okay. 44:15 Darian : Turn looking at the brackets. Making sure not missing in. 44:36 Maria K.: Nico. I have a question if they were having issues getting files like she said for two weeks. She said, Two weeks. wouldn&#39;t when we have worth of two weeks of file, sitting in the front of TS folder, 44:51 Nicolas S.: We theoretically, yes. I don&#39;t understand why she&#39;s saying this two weeks worth of data that she&#39;s not getting when. 45:00 Maria K.: Yeah. 45:01 Nicolas S.: They were picked up, right? Running out ideas here. Yeah, school teachers to details. and then, Hit the Hit the Play button here. Darren Okay. so, No errors. But your path is still not showing my file. Why? 46:08 Maria K.: Do you check the logs? His love. 46:12 Nicolas S.: We could do that. Go to DTS logs. 46:14 Maria K.: Okay. 46:21 Darian : In DCs. I want a CP. 46:24 Nicolas S.: It should be. 46:25 Maria K.: Log files. No. 46:28 Darian : I mean, they&#39;re both kind of there. Just because this one will have logs. Oh, I guess. 46:33 Nicolas S.: Nice. 46:34 Maria K.: Oh yeah.
46:37 Nicolas S.: No, I think it&#39;s. It should be your old setup. Oh, so look. You see the pat path 46:37 Maria K.: No, it&#39;s not. 46:41 Nicolas S.: right there to Lowe&#39;s right there in line 43. 46:51 Maria K.: Just the D, right? 46:59 Nicolas S.: Can you check that from DTS? Really quick before you open a blog. 47:05 Darian : What do you? 47:06 Nicolas S.: Go up one. That check that from DTS. There they are. 47:14 Darian : Oh my gosh. All right, it&#39;s yes. I took forever to get there. 327 13. 47:22 Maria K.: Okay, those are the files. 47:25 Nicolas S.: Yeah. 47:25 Darian : Yeah. 47:28 Nicolas S.: What came show me the path file. The path name here. 47:35 Darian : Yeah, they updated two minutes ago. 47:37 Nicolas S.: Okay, so it&#39;s a timing issue that takes time for you to get them, I guess. 47:42 Darian : That&#39;s that, yeah. 47:43 Maria K.: can you check at your schedule because you guys, Full files from US or and drop files. 47:49 Darian : I wonder if it&#39;s the scheduled task, so, 47:54 Maria K.: You guys have a scheduled task set up. 47:57 Darian : Look at this, I wonder if it&#39;s this. Also, the user. I don&#39;t know if it&#39;s supposed to be system. 48:14 Nicolas S.: Well, I&#39;ll tell you what, it picked up when you hit play in. 48:18 Darian : Yeah, that&#39;s what I&#39;m saying. Yeah. Special test and turning that. Yeah. 48:45 Nicolas S.: I didn&#39;t we didn&#39;t mess with these these settings before. Did we Darian? 48:49 Darian : No, we didn&#39;t. 48:51 Nicolas S.: Okay. 48:52 Darian : This is me messing with they just because if it worked from there, then it should work from here. And it looks like when we actually click the Play
button, that&#39;s when it actually went through. 49:07 Nicolas S.: Well, maybe whatever we did here it over. Wrote, what&#39;s already in set up in 49:14 Darian : Not sure, let&#39;s try one more file. 49:17 Nicolas S.: one more test file, I got 49:19 Darian : Yeah. 49:19 Maria K.: Here. Yes. 49:34 Nicolas S.: All right, we&#39;re gonna drop another top. Another test file. Okay, so I just dropped another test file, you should be able to pick it up. If you run, if you run the scheduler, That should. That should pick it up. I don&#39;t want to, I don&#39;t want to depend on hitting the play button, right? 49:53 Darian : Yeah. 49:57 Nicolas S.: Okay. 50:10 Darian : Pretty. Telling me run. I don&#39;t mean. 50:41 Nicolas S.: There it is. 50:45 Darian : yeah, something up with The scheduled times. 50:54 Nicolas S.: You can you can delete it. Run the run the Hit the Play button and it&#39;ll reinstall it. 51:05 Darian : I&#39;ll try. Yeah, it&#39;s just 51:30 Nicolas S.: Okay, so I should it should be in your one. I did still going. There. Now I should be here I should be in your schedule tasks. There it is. 51:44 Darian : I slowly wondered. This is finishing. Like the right. kind of, Was here, I guess send one more thing. 52:09 Nicolas S.: Okay. 52:23 Darian : Eminem. 52:25 Nicolas S.: We&#39;ll do. Okay, run it. 52:50 Darian : That&#39;s weird. 53:04 Nicolas S.: Now, you always change it to to Windows Server, 2019. 53:09 Darian : I have in the past. Yeah. We&#39;ll try this. 54:18 Darian : Oh, there&#39;s a second one, right? 54:20 Nicolas S.: All right. 54:21 Darian : Here we go. And try one more time. 54:25 Nicolas S.: All right.
54:27 Maria K.: Okay good. Oh Nico. Can you have them drop a file for us? Just to make sure. I know those files came this morning, I just want to make sure whatever you guys update is still working for inbound. 54:47 Nicolas S.: Got it. All right, run it. 55:04 Darian : Well, there we go. 55:04 Nicolas S.: Okay. All right, call Cutter cut. One of these tests and drop it in the two details file. 55:14 Darian : Okay. And then run it. 55:20 Nicolas S.: Yeah. 55:20 Darian : So that it sends. Yeah. And it should be gone. 55:26 Nicolas S.: Got it here. We got it. I&#39;m already. 55:28 Darian : Nice. 55:32 Nicolas S.: I just deleted it from the, from the fold. But we got it. All right, dude. I mean daring, I don&#39;t know what else we can do. 55:42 Darian : I mean, it looks like it was a scheduled testing more than anything maybe using 55:46 Nicolas S.: Yeah. 55:46 Darian : account thing. I don&#39;t know why it happened in the first place. 55:51 Nicolas S.: All right, you have all of the files in your in your history. 55:52 Maria K.: Yes. 55:57 Nicolas S.: And you if they need to access any files that they don&#39;t have, I would suggest asking them to reach into their history or their backup folder. First, if they 56:03 Darian : Yeah. 56:06 Nicolas S.: don&#39;t find what they&#39;re looking for in their back, then they can reach out to us and then we can restate any file, they&#39;re missing. 56:13 Darian : Yeah. Can you go and let them know in the email that we&#39;re all in together? 56:18 Nicolas S.: Yeah, I&#39;ll do that from. 56:19 Maria K.: Yeah, Nico will email Janet and let her know. 56:23 Darian : Because I don&#39;t think she believes me at this point. Okay? 56:27 Maria K.: I work with her a lot. 56:28 Darian : Yeah. Yeah. So I was like I mean I could say that she&#39;s like, are you
sure like I Something told me he 56:41 Maria K.: sometimes, she&#39;s not right, but you know, 56:43 Darian : Yeah, it&#39;s a whole and I was like I don&#39;t blame you. I&#39;m just I just don&#39;t know what&#39;s wrong. I&#39;m sorry. I can&#39;t like tell you exactly what it is. Just a 56:52 Maria K.: Yeah. 56:55 Darian : two-way street you just goes. Yeah. So 56:57 Maria K.: So you send us a file in at work perfectly, right? 57:02 Darian : Yes. And we received the files now. 57:02 Nicolas S.: Yeah. 57:05 Maria K.: Yeah, so I think were and we&#39;re good. You guys are awesome. 57:11 Darian : So we&#39;ve been doing trying to troubleshoot these one, one day at a time. 57:15 Maria K.: Right. We got wishing you so many at a time every day. 57:16 Darian : It&#39;s great. Yeah, it&#39;s it&#39;s crazy. 57:26 Maria K.: So we&#39;re good to go. 57:26 Darian : All right, then. We&#39;re good to go. 57:30 Maria K.: Awesome. Thank you. 57:31 Darian : We have a wonderful day. Yeah, no. 57:31 Nicolas S.: Perfect. Thank you very. You have a going. 57:34 Darian : Thank. 57:35 Maria K.: Bye. 57:35 Darian : You bye. 57:36 Michael C.: If no good. 57:38 Nicolas S.: All right guys. What did you guys learn? 57:41 Maria K.: So I was good. No I mean it was pretty straight for me, you know like straight for. I know he has to do some stuffing he&#39;s and that 57:50 Nicolas S.: So today the truth. Spring up. The sftp is not as complicated as he made. It made it out to be because he was changing a lot of stuff in the in the scheduler itself. 57:59 Maria K.: Yeah. Yeah. The scheduling this in the condition settings. Yeah. 58:05 Nicolas S.: Exactly. All of those things are, we never touched every call. I&#39;ve had with anybody else. We don&#39;t have to touch that, it just works as expected, right? But
58:14 Maria K.: Right. 58:14 Nicolas S.: I guess he needed to do some extra stuff on his and to what to get it working. But we found the issue. It was a scheduler, deleted it reinstalled, it got it to 58:23 Nicolas S.: work. Um, I do believe, I do have more calls tomorrow if you guys care to join, 58:28 Nicolas S.: let me know and I&#39;ll send you the links. 58:28 Maria K.: Yeah, let me know tomorrow. Yeah, because I think I want to be in one of those where you are. Migrated them. Oh, that&#39;ll be great to, to have that, you know, 58:35 Nicolas S.: Yeah. Okay, perfect. 58:38 Maria K.: disability. 58:40 Nicolas S.: Yep. 58:41 Maria K.: Already well, thank you Michael. I&#39;ll call you back in Slack. Okay. 58:46 Michael C.: okay, I I have it introduction to Cleo with Denise, I believe 58:52 Maria K.: Oh okay, do that? No, no. 58:54 Michael C.: Oh yes. Let me 58:54 Nicolas S.: Good. Of course. 58:54 Maria K.: Yeah, you don&#39;t want to miss it. 58:55 Michael C.: Okay, that&#39;s not for thank you. 58:58 Maria K.: okay, by
View original transcript at Tactiq.</p>
