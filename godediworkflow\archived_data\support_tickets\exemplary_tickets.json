[{"id": "CS-36972", "title": "PO&#39;s not received", "score": 105, "categories": ["connectivity", "document_flow"], "edi_types": ["SFTP", "FTP", "EDI"], "priority": "Medium", "description": "Starting last week, our global team has not been receiving our iDOCs. We have verified that the document was successfully generated and uploaded to the DTS folder (todts). However, the global team has informed us that the iDOCs are not appearing in SAP. It is unclear whether the issue lies with the FTP transfer or the file import into SAP. Could you please assist us in resolving this?", "root_cause": "you don&#39;t remove the files from the server once they are picked , you have an old sftp and i think j<PERSON>on went over and explain why you are experiencing these issues\nwe also fixed the ownership on the directory because we saw something wrong there as well\nyou should be able to pick up your files we are not sure why on your side you can pick up the files not sure if you made changes on your end because we got no way to clean this up as it is on your side\nthey are in your fromdts directory waiting to be picked up you need to check this on your end\nmaria keshwala datatrans solutions support@datatrans-inc.", "resolution": "the issues on our end are you sending us documents?\nmaria keshwala datatrans solutions support@datatrans-inc.", "troubleshooting_steps": ["have verified that the document was successfully generated and uploaded to the dts folder (todts).", "updated on our end for the ftp you should be able to received your files\nmaria keshwala datatrans solutions support@datatrans-inc.", "fixed the issues on our end are you sending us documents?\nmaria keshwala datatrans solutions support@datatrans-inc.", "fixed on our site\nwe cant tell if the files have been picked up because you don&#39;t remove the files from the server once they are picked , you have an old sftp and i think j<PERSON><PERSON> went over and explain why you are experiencing these issues\nwe also fixed the ownership on the directory because we saw something wrong there as well\nyou should be able to pick up your files we are not sure why on your side you can pick up the files not sure if you made changes on your end because we got no way to clean this up as it is on your side\nthey are in your fromdts directory waiting to be picked up you need to check this on your end\nmaria keshwala datatrans solutions support@datatrans-inc.", "the problem is on your end please schedule a time see below\nhttps://calendly."]}, {"id": "CS-30414", "title": "Re: ACTION REQUIRED: Caterpillar EDI Enablement: 30105 | Supplier Code: E0562J0 | | CAT 89 SANTA CATARINA", "score": 105, "categories": ["connectivity", "data_format", "mapping", "performance", "configuration", "document_flow", "error_handling"], "edi_types": ["850", "856", "810", "997", "AS2", "SFTP", "FTP", "X12", "EDI"], "priority": "Medium", "description": "Adding the DataTrans Support team as they&#39;d always be your first point of contact. I&#39;m actually off for vacation in 1/2 an hour.\nHave you reached out to Caterpillar and asked them what the issue is or have they advised what is in error? They approved moving this to production on the 18th. Last time... they had not turned on the relationship on their end so all failed.\n<PERSON><PERSON>\nOn 10/31/2024 5:23 PM, <PERSON><PERSON> wrote:\n<PERSON>,\nSomehow there’re lot of CAT 89 invoices got “rejected”. We couldn’", "root_cause": "of #7 we were not getting the edi information pertaining to our supplier account our product numbers we make.", "resolution": "01/nov/24\nstatus: resolved\nproject: customer support\ncomponents: none\naffects versions: none\nfix versions: none\ntype: support priority: medium\nreporter: albert assignee: nicholas sanchez resolution: done votes: 0\nlabels: none\nremaining estimate: not specified\ntime spent: not specified\noriginal estimate: not specified\nrequest type: emailed request\nrequest language: english\nrequest participants: organizations: label: asn label\ndescription\nhi.", "troubleshooting_steps": ["tested further.", "tested further.", "change certain settings.", "update on the po.", "updated quantity from the 860 documents\ncomment by al<PERSON> [ 29/oct/24 ]\n&lt;!doctype html public &#34;-//w3c//dtd xhtml 1."]}, {"id": "CS-37129", "title": "RJW 940s", "score": 95, "categories": ["connectivity", "data_format", "document_flow"], "edi_types": ["997", "AS2"], "priority": "Medium", "description": "Customer: Adams Extracts\nCustomer ID: 6061\nTrading Partner: RJW Logistics\nDocument Type: 940 – Warehouse Shipment Order\nI have not yet received 997s for these orders that were transmitted this morning at 0922. I retransmitted them at 1642. Please review and determine if these 940s are reaching RJW. Initially there was an issue with using the wrong AS2 connector.\nScott W<PERSON> Shippee\nNestell &amp; Associates| 5000 Birch Street | West Tower, Suite 3000 | Newport Beach, CA | 92660, USA\n<a href=\"http://", "root_cause": "another document is expected to be completed therefore it wont overwrite that and changed it but once you open the document you can see it has been accepted.", "resolution": "a few hours after see below\ninvalid as2\ncorrect as2\nmaria keshwala datatrans solutions support@datatrans-inc.", "troubleshooting_steps": ["change success blog comment by maria kesh<PERSON> [ 31/jan/25 ]\nhi scott\ni will look into this as well thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "fixed a few hours after see below\ninvalid as2\ncorrect as2\nmaria keshwala datatrans solutions support@datatrans-inc.", "i checked as2 connection is correct , 997s are being received the reason why you dont see the 997 “accepted” status is because another document is expected to be completed therefore it wont overwrite that and changed it but once you open the document you can see it has been accepted.", "cause another document is expected to be completed therefore it wont overwrite that and changed it but once you open the document you can see it has been accepted."]}, {"id": "CS-36065", "title": "FW: Ticket Updated: Your Finance Request ********* has been updated", "score": 85, "categories": ["data_format", "mapping", "configuration", "document_flow", "error_handling"], "edi_types": ["850", "810", "X12", "EDI"], "priority": "Medium", "description": "<PERSON>\nDoes this help?\n<PERSON>/Air Side Systems\n<a href=\"mailto:<EMAIL>\"><EMAIL></a>\n765.778.7895 ext. 27\nFrom: Caterpillar EDI Enablement <a href=\"mailto:<EMAIL>\"><EMAIL></a> Sent: Tuesday, January 21, 2025 12:41 AM To: <a href=\"mailto:<EMAIL>\"><EMAIL></a>; <PERSON> <a href=\"mailto:<EMAIL>\"><EMAIL></a> Subject: RE: Ticket Updated: Your Finance Req", "root_cause": "eh baare is not having any issues .", "resolution": "", "troubleshooting_steps": ["updated maria\ndoes this help?\nmichelle rice\nvee engineering/air side systems\n<a href=\"mailto:mrice@plant4.", "updated\nhi @michelle rice,\nthe invoice is not processed due to the following error.", "updated\nfor caterpillar employees - to verify the authenticity of this email, visit cat@work and search for: em%62ngw0\nfyi only: the following updates have been made to your request.", "changed this is how the by will populate for cat34 and this mapping rule is customized only for integrated document “not created in webedi” as we setup this condition only for cat34, logic if h record = cat34 in position 22 then the by which is the buying party will populate i just pulled a file from 11/25/2024 that looks correct so make sure you dont do this in webedi they have to be integrated\nmessage id 42446727 reference 255168 thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "cause eh baare is not having any issues ."]}, {"id": "CS-35612", "title": "Large File", "score": 80, "categories": ["performance", "document_flow"], "edi_types": ["856", "EDI"], "priority": "Medium", "description": "DataTrans,\nThis EDI 856 file did not make it over to Redwood due to the size. <PERSON> dropped yesterday and they did not receive last night. I re-dropped earlier this morning and still no luck.\nPlease split this file into smaller files and resend.\nThanks,\nMARIA ZAPATA I CUSTOMER CARE MANAGER I 972-829-2366 I UDFC", "root_cause": "now those files have been delivered multiple.", "resolution": "a year ago for you guys to process large files.", "troubleshooting_steps": ["fixed a year ago for you guys to process large files.", "cause now those files have been delivered multiple."]}, {"id": "CS-42990", "title": "Re: [EXTERNAL] Re: AAFES/Exchange x Radial System to System Integration - A Better Tomorrow LLC (07847402)", "score": 85, "categories": ["connectivity", "data_format", "configuration", "document_flow"], "edi_types": ["850", "856", "810", "997", "AS2", "EDI"], "priority": "Medium", "description": "Hi <PERSON>,\nCan we get an order from AAFES from testing? We are not seeing the shipstation integrator pick up the ASN when we make it in Shipstation manually or when my warehouse sends the fulfillment:\nWe want to test to ensure that the fulfillment passes up from our warehouse into EDI effectively.\nThank you, <PERSON>, Jan 30, 2025 at 7:28 AM Marianne Ka<PERSON> <a href=\"mailto:<EMAIL>\"><EMAIL></a> wrote:\nHi <PERSON>,\nYou&#39;ll need to send them the Inventor", "root_cause": "your organization is scheduled to begin trading data with our dropship manager on behalf of aafes/exchange.", "resolution": "", "troubleshooting_steps": ["change x radial system to system integration - a better tomorrow llc (07847402) hi marianne,\ncan we get an order from aafes from testing? we are not seeing the shipstation integrator pick up the asn when we make it in shipstation manually or when my warehouse sends the fulfillment:\nwe want to test to ensure that the fulfillment passes up from our warehouse into edi effectively.", "change x radial system to system integration - a better tomorrow llc (07847402)\nhi purushothaman, updated files have been sent.", "updated files have been sent.", "changed accordingly with inclusion of additional fees as mentioned in technical questionnaire document\n846 inventory\ncold you please send us the 846 inventory file with quantity on hand as mentioned in the technical questionnaire.", "change x radial system to system integration - a better tomorrow llc (07847402)\nhi purushothaman, an updated 810 has been sent."]}, {"id": "CS-31221", "title": "FW: Your Invoice(s) 159763 for Order(s) 0018457-0372", "score": 90, "categories": ["data_format", "document_flow", "error_handling"], "edi_types": ["856", "810", "EDI"], "priority": "Medium", "description": "Some additional information for Ticket  CS-31218 RESOLVED  , the last ASN they received was\nMessage ID 40830852 on 7/8/2024.\n<em>Best Regards, <PERSON> – IT Manager</em>\nEXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Ext: 202 Direct: (************* Email: <a href=\"mailto:<EMAIL>\"><EMAIL></a> Website: <a href=\"http://www.exacto.com\">http://www.exacto.com</a>\nFrom: <a href=\"mailto:<EMAIL>\"><EMAIL></a> <a hre", "root_cause": "is for all the parts not just this one.", "resolution": ", the last asn they received was\nmessage id 40830852 on 7/8/2024.", "troubleshooting_steps": ["cause is for all the parts not just this one."]}, {"id": "CS-40470", "title": "Upload EDI", "score": 85, "categories": ["connectivity", "configuration", "document_flow", "error_handling"], "edi_types": ["810", "SFTP", "EDI"], "priority": "Medium", "description": "Hello @Support!\nWe tried to upload an old EDI file that was requested to invoice against, but the portal is showing the following error:\nThe EDI files are attached. Can you help us with uploading them? Thank you in advance,", "root_cause": "your maps are setup via integration thats the only way.", "resolution": "", "troubleshooting_steps": ["cause your maps are setup via integration thats the only way."]}, {"id": "CS-30354", "title": "Errors Report", "score": 85, "categories": ["document_flow", "error_handling"], "edi_types": ["810", "X12", "EDI"], "priority": "Medium", "description": "We received an error report for two iDOC files we uploaded. Can you tell us what’s the cause of the error ?\nThanks,\n<PERSON>\nFrom: Datatrans Reports <a href=\"mailto:<EMAIL>\"><EMAIL></a> Sent: Thursday, October 31, 2024 11:00 AM To: ChempaxSupport, Dystar/US <a href=\"mailto:<EMAIL>\"><EMAIL></a>; <PERSON>, <PERSON>, DyStar/US <a href=\"mailto:<EMAIL>\"><PERSON>.<EMAIL></a>; <PERSON>, Rich, DyStar/US <a href=\"", "root_cause": "of the error ?\nthanks,\ncharles reeves\nfrom: datatrans reports <a href=\"mailto:alerts@datatrans-inc.", "resolution": "", "troubleshooting_steps": ["cause of the error ?\nthanks,\ncharles reeves\nfrom: datatrans reports <a href=\"mailto:alerts@datatrans-inc."]}, {"id": "CS-40382", "title": "SuperSalu Invoiced", "score": 80, "categories": ["data_format", "configuration", "document_flow"], "edi_types": ["810"], "priority": "Medium", "description": "Customer: Adams Extracts\nCustomer ID: 6061\nTrading Partner: UNFI – Conventional (SuperValu)\nDocument Type: 810 - Invoice\nTransmitted 10-12 invoices today, but they did not show up in WebEDI. Please advise.\n<PERSON> W. Shippee\nNestell &amp; Associates| 5000 Birch Street | West Tower, Suite 3000 | Newport Beach, CA | 92660, USA\n<a href=\"http://www.NestellAssociates.com\">www.NestellAssociates.com</a>\nMobile: (401)529-0678\nTimezone: EST/EDT\n<a href=\"https://www.linkedin.com/company/nestell-associ", "root_cause": "the files are not splitting into single files.", "resolution": "the issue and injected the files\nmaria keshwala datatrans solutions support@datatrans-inc.", "troubleshooting_steps": ["change success blog comment by maria keshwala [ 13/mar/25 ]\ngood morning scott\ni will look into this and advise thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "checked and the invoices themselves are going out, but it is not injecting into wededi.", "change success blog\ncomment by ma<PERSON> kesh<PERSON> [ 13/mar/25 ]\nfound the invoices but they are not splitting not sure if this is the reason they are not injecting into webedi\nmaria keshwala datatrans solutions support@datatrans-inc.", "fixed the issue and injected the files\nmaria keshwala datatrans solutions support@datatrans-inc.", "fixed the file name on the target- fixed the dom map added required segments that were missing."]}, {"id": "CS-37189", "title": "Missing 810/856 Reports DTS-494", "score": 80, "categories": ["document_flow", "error_handling"], "edi_types": ["856", "810"], "priority": "Medium", "description": "Hello DTS Support,\nWe are no longer getting report emails with the processing status for 810 and 856 files. (We are still receiving them for 855s)\nThe last 810 report we received was on the 29th at 10AM CDT. The last 856 report we received was on the 23rd at 6PM CDT.\nPlease look into this ASAP and send through all missing reports. I want to confirm there have been no issues with outbound file processing.\nThanks, <PERSON> The content of this email is confidential and intended for the recipient spec", "root_cause": "ecs receives the documents once a day, and that is the only time when tests can be performed and the error can be identified.", "resolution": "thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "troubleshooting_steps": ["verified that the 810 reports are now being received correctly.", "fixed the issue please let me know if you still not receiving the reports.", "cause ecs receives the documents once a day, and that is the only time when tests can be performed and the error can be identified."]}, {"id": "CS-37167", "title": "Issue opening file - in Webedi -DS10530", "score": 80, "categories": ["document_flow"], "edi_types": ["810"], "priority": "Medium", "description": "Hello - you called because you are having issues with opening an invoice that was already sent and is just loading I have addressed this with our dev team as we have an ongoing ticket for this issue.", "root_cause": "you are having issues with opening an invoice that was already sent and is just loading i have addressed this with our dev team as we have an ongoing ticket for this issue.", "resolution": "the issue thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "troubleshooting_steps": ["have fixed the issue thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "cause you are having issues with opening an invoice that was already sent and is just loading i have addressed this with our dev team as we have an ongoing ticket for this issue."]}, {"id": "CS-33537", "title": "Production Dataflow Error  Action Required (Coast To Coast - ALLPRO Corporation)", "score": 80, "categories": ["document_flow", "error_handling"], "edi_types": ["810", "EDI"], "priority": "Medium", "description": "Greetings Coast To Coast Computer Products, Inc! We have received production dataflow for your trading partnership with ALLPRO Corporation and there is at least one error detailed below. Please review and send revised documents as soon as possible. Doc Type: 810 Document Sender: Coast To Coast Computer Products Inc Document Receiver: ALLPRO Corporation Document IDs: [&#39;********&#39;, &#39;********&#39;] Error Message: If InvoiceTypeCode is CR or DI, then BuyerPartNumber is required.\nYou can v", "root_cause": "i see the document was restaged , and entered manually you added a line item “2” when it should be item line “1” make sure if you are only sending one item then the line items should should be 2 unless this part number was in line item 2 on the po.", "resolution": "the line item number\nmaria keshwala datatrans solutions support@datatrans-inc.", "troubleshooting_steps": ["fixed the line item number\nmaria keshwala datatrans solutions support@datatrans-inc.", "fixed the issue if so because i see the document was restaged , and entered manually you added a line item “2” when it should be item line “1” make sure if you are only sending one item then the line items should should be 2 unless this part number was in line item 2 on the po.", "cause i see the document was restaged , and entered manually you added a line item “2” when it should be item line “1” make sure if you are only sending one item then the line items should should be 2 unless this part number was in line item 2 on the po."]}, {"id": "CS-33298", "title": "CRITICAL: Dell POs being routed to unknown partner instead of Dell", "score": 80, "categories": ["document_flow"], "edi_types": ["850"], "priority": "Medium", "description": "Good Morning,\nSomehow our Dell Pos are getting routed to a partner we are unfamiliar with. Please help us get this fixed ASAP, and re-send all the Pos we have placed to Dell. This appears to have started 11/13. I can’t tell you a msg id because it tells me I don’t have permission for that trading partner.\n<PERSON> Director of Enterprise IT\nToll-Free (*************\nGeneral (*************</p>\n<ul>\n<li>Website * | [ LinkedIn |https://www.linkedin.com/company/traferaofficial/] | [ Twitter |http", "root_cause": "it tells me i don’t have permission for that trading partner.", "resolution": "asap, and re-send all the pos we have placed to dell.", "troubleshooting_steps": ["fixed asap, and re-send all the pos we have placed to dell.", "cause it tells me i don’t have permission for that trading partner."]}, {"id": "CS-33294", "title": "Class error", "score": 80, "categories": ["mapping", "document_flow", "error_handling"], "edi_types": ["810"], "priority": "Medium", "description": "Good Morning,\nPlease advise on this error….\n| <PERSON><PERSON> Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href=\"http://www.LubricationSpecialties.com\">www.LubricationSpecialties.com</a> |", "root_cause": "they are working on other things for you.", "resolution": "we can troubleshoot again i cant give this to development if there are mapping errors that needs to be address first.", "troubleshooting_steps": ["fixed we can troubleshoot again i cant give this to development if there are mapping errors that needs to be address first.", "update\nmaria keshwala datatrans solutions support@datatrans-inc.", "fixed it.", "cause they are working on other things for you."]}, {"id": "CS-31270", "title": "FW: FW: Prod | Error in workflow : 841946508 | BP Name : FM_SPEDeEnvelope_I", "score": 80, "categories": ["data_format", "document_flow", "error_handling"], "edi_types": ["810", "EDI"], "priority": "Medium", "description": "Good afternoon,\nWhen I am sending the <PERSON> invoices and I put in the allowance, the allowance amount doesn’t come off of the total (see below). Can this please be fixed? Thank you!\n| <PERSON><PERSON> Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href=\"http://www.LubricationSpecialties.com\">www.LubricationSpecialties.com</a> |\nFrom: <PERSON> <a href=\"mailto:jmartin@datatrans-in", "root_cause": "the edi goes out with the correct amount.", "resolution": "thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "troubleshooting_steps": ["fixed thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "cause the edi goes out with the correct amount."]}, {"id": "CS-31110", "title": "Auto pick and pack issue- DS-10433", "score": 80, "categories": ["document_flow"], "edi_types": ["856"], "priority": "High", "description": "Hi team,\nI hope you&#39;re well and having a good week so far.\nWe&#39;re having a lot of problems with our auto pick and pack option on Datatrans. Can we organise a google meet call for Thursday 7pm GMT to go through this and get a fix in place?\nAll the best, Alexandria", "root_cause": "is going to multiple distribution centers and i asked this question while on the call, once you create the asn please check your draft as it creates multiple asn that you need to work on.", "resolution": "is the auto packing issue\npo number 15250628 customer is working\nmaria keshwala datatrans solutions support@datatrans-inc.", "troubleshooting_steps": ["cause is going to multiple distribution centers and i asked this question while on the call, once you create the asn please check your draft as it creates multiple asn that you need to work on."]}, {"id": "CS-43217", "title": "Fwd: HSN Urgent Communication - SSL certificates expiring for EDI services 6/1/2025.", "score": 75, "categories": ["connectivity", "document_flow", "error_handling"], "edi_types": ["AS2", "EDI"], "priority": "Medium", "description": "Hello DataTrans,\nCan you help me understand if we will need to do something manually or if the new certificate will be automatically connected?\n---------- Forwarded message ---------From: <PERSON> <a href=\"mailto:<PERSON>@hsn.net\"><PERSON><PERSON>@hsn.net</a> Date: Thu, May 1, 2025 at 4:03 PM Subject: HSN Urgent Communication - SSL certificates expiring for EDI services 6/1/2025. To:\nHello HSN EDI Partner,\nHSN’s SSL certificates for production FTPS and HTTPS connection will update on 05/2", "root_cause": "a service interruption to your ftps scripting.", "resolution": "", "troubleshooting_steps": ["update on 05/28/2025 at 9am eastern (est).", "update the current certificates will no longer be valid.", "update may cause a service interruption to your ftps scripting.", "update on 05/28/2025 at 9am eastern (est).", "cause a service interruption to your ftps scripting."]}, {"id": "CS-43024", "title": "RE: [EXTERNAL] CS-42354 Data Trans missing 855 for Grainger and 810 unread in Draft folder", "score": 75, "categories": ["connectivity", "configuration", "document_flow", "error_handling"], "edi_types": ["850", "810", "SFTP"], "priority": "Medium", "description": "Hello <PERSON>,\nWe never received the 850 file, please check why this PO was not translated.\nThank you. <PERSON><PERSON> Senior Applications Analyst : Burlington, Ontario Hadrian &amp; World Dryer : Office: (************* Mobile: (************* Email: <a href=\"mailto:<EMAIL>\"><EMAIL></a>\n<a href=\"https://www.linkedin.com/company/hadrian-manufacturing-inc./\">https://www.linkedin.com/company/hadrian-manufacturing-inc./</a>\n<a href=\"https://www.facebook.com/HadrianInc\">https:/", "root_cause": "", "resolution": "data trans missing 855 for grainger and 810 unread in draft folder\n———-—\nreply above this line.", "troubleshooting_steps": ["found in d:\\maps and boms\\world dryer\\wesco international\nwesco 850 to world dryer event rule exists\n850 raw data shows “}“ as delimiters instead of “”.", "updated event rule to match tps delimiter.", "i updated the error preventing the map to process the po and restaged its delivery."]}, {"id": "CS-39922", "title": "DTS SFTP Migration BSP Filing Solutions", "score": 75, "categories": ["connectivity", "performance", "document_flow", "error_handling"], "edi_types": ["SFTP", "FTP", "X12"], "priority": "Medium", "description": "Hello,\nWe are in the process of migrating our FTP to a new SFTP over the next 60 days. Failure to complete the migration will result in document delivery issues. Please use the calendar link below to schedule a time.\nThank you,\n<a href=\"https://calendly.com/tdaughrity/30min\">https://calendly.com/tdaughrity/30min</a>", "root_cause": "", "resolution": "the issue that was holding the files from being processed.", "troubleshooting_steps": ["change in production data.", "update you once i have more details."]}, {"id": "CS-40310", "title": "Invoice Issues- DTS-569", "score": 75, "categories": ["data_format", "mapping", "performance", "configuration", "document_flow", "error_handling"], "edi_types": ["856", "810", "X12", "EDI"], "priority": "Medium", "description": "Hello,\nWhen trying to send invoices to <PERSON><PERSON><PERSON>, the message below pops up. Is there anything you could do to assist?\nThe PO number for this is **********\nThanks,\n<PERSON> Operations Engineer\n1700 Central Plank Rd\nWetumpka, AL 36092\n334.541.4070 | c: 334.557.6880\n<a href=\"mailto:<EMAIL>\"><EMAIL></a>\n<a href=\"http://www.TheOhSoCo.com\">www.TheOhSoCo.com</a>\n<a href=\"http://www.BahamaBos.com\">www.BahamaBos.com</a>\n<a href=\"http://www.WorthyPromo.com\">www.WorthyPromo.com</a", "root_cause": "", "resolution": "today i see the last comment added last night.", "troubleshooting_steps": ["update on this?\ncody worthy operations engineer\n1700 central plank rd\nwet<PERSON>ka, al 36092\n mandatory data missing at isa*16 &amp; extra segment delimiter in the isa segment\ngsindts6256dltrcan202502121510100000005x004010~\nst8100005~\nbig202501242025017202409120001312435199411~\ncurbycad~\nrefvr7609~\nn1stdelta dc 4119299411~\nn37530 hopcott road~\nn4deltabcv4g 1j1can~\nn1refoley dog treat company~\nn31945b bollinger rd~\nn4nanaimobcv9s5w9can~\nitd013 x12-0  (0.", "update this is still in development almost done they are in the last phase of this .", "update is complete and if i can send dollar tree the invoices?\nthank you,\ndaniel\ndaniel stiefvater\nowner/operator\nfoley dog treat company\n1945-b bollinger rd\nnanaimo, bc, v9s 5w9\nmobile: ************\nwww.", "update i had sent this back to our dev team but please send this to dollar tree canada below their specs only allows one delimiter as the segment terminator they requested to add an extra terminator at the end of the isa16 which we did but we need that stated on the specs can you please have dollar tree canada sent their updated specs stating this rule ? please advise thank you\nthe specs we have are from 07/14/2015\nmaria keshwala datatrans solutions support@datatrans-inc.", "updated specification i received another email from you with a required delimiter that is different from this email and we need to get this right."]}, {"id": "CS-37128", "title": "Bad Date/Times DTS-496", "score": 75, "categories": ["connectivity", "performance", "document_flow"], "edi_types": ["856", "997", "AS2"], "priority": "Medium", "description": "Customer: Adams Extracts\nCustomer ID: 6061\nTrading Partner: Woods Distribution\nDocument Type: 940/997\nTaking a look at the snippet below\nTime Sent: 01/30/2025 @ 03:04PM – I confirm that this timestamp is expected based on time transmitted and expected processing time.\nLast Modified Time: 01/30/2025 @ 09:04 AM – That is 6 hours before it was sent to DTS, how was it modified before it was received? If the clock was 12 hours off, it is still incorrect, as that time hasn’t yet arrived. If it is GMT/", "root_cause": "", "resolution": "and changed to cst - please let us know if you still having any issues thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "troubleshooting_steps": ["updated to cst?\nplease advise.", "change success blog comment by ma<PERSON> <PERSON> [ 31/jan/25 ]\nhello scott\ni will look into this very odd if it was sent already being modified before not sure could be it was edited in the morning then sent in the afternoon but i will check the entire process and advise.", "fixed and changed to cst - please let us know if you still having any issues thank you\nmaria keshwala datatrans solutions support@datatrans-inc."]}, {"id": "CS-36571", "title": "Franklin International - Problem with Walmart 850&#39;s received on 01/22/2025", "score": 75, "categories": ["data_format", "mapping", "document_flow"], "edi_types": ["850", "X12", "EDI"], "priority": "High", "description": "Hello Data Trans Support,\n<PERSON> received 30 Walmart PO’s this morning that we are unable to process.\nIt looks like there is missing data in the “flat files”\nThe first attachment is one of the Walmart PO’s received today (01/22/2025)\nThe second attachment is one of the Walmart PO’s received last week (01/15/2025)\nWhen I compare the two files, the 01/22/2025 file is missing big chunks of data that are present on the 01/15/2025 file.\nTo me, it looks like something happened to the Walmart 850 map", "root_cause": "", "resolution": "they want all the files restaged\nfile from 01/15/2025 batch id 141365687, message id 42954205, reference number 4481207071\nfile from 01/22/2025 batch id 141510921, message id in webedi 43027929, reference number 6736166211\nmaria keshwala datatrans solutions support@datatrans-inc.", "troubleshooting_steps": ["checked the first po\nmessage id in webedi 43027929\nreference number 6736166211\nbatch id translated file 141510921\npo from 01/15/2025 message id 42954205\nreference number 4481207071\nbatch id 141365687\ncomparing to see what is it that is missing\nmaria keshwala datatrans solutions support@datatrans-inc.", "fixed they want all the files restaged\nfile from 01/15/2025 batch id 141365687, message id 42954205, reference number 4481207071\nfile from 01/22/2025 batch id 141510921, message id in webedi 43027929, reference number 6736166211\nmaria keshwala datatrans solutions support@datatrans-inc.", "update waiting to hear back.", "changed to ignore the td5 segment?\nthanks,\nfranklin international\njoe mcmahan\ncomment by ma<PERSON> [ 23/jan/25 ]\ntd5 is a key element used in the mapping.", "updated to tell it what to do if it&#39;s missing\na map mod is required for us to rerun the files asking jennifer if a work auth is required to update the map\nmaria keshwala datatrans solutions support@datatrans-inc."]}, {"id": "CS-33860", "title": "FW: Finance Request ********* has been resolved", "score": 75, "categories": ["data_format", "configuration", "document_flow", "error_handling"], "edi_types": ["856", "810", "X12", "EDI"], "priority": "Medium", "description": "We are still having issues with CAT England’s 810s. Is the map missing something?\n<PERSON>/Air Side Systems\n<a href=\"mailto:<EMAIL>\"><EMAIL></a>\n765.778.7895 ext. 27\nFrom: Caterpillar EDI Enablement <a href=\"mailto:<EMAIL>\"><EMAIL></a> Sent: Monday, December 2, 2024 2:51 AM To: <a href=\"mailto:<EMAIL>\"><EMAIL></a>; <PERSON> <a href=\"mailto:<EMAIL>\">MRice@Pl", "root_cause": "", "resolution": "we are still having issues with cat england’s 810s.", "troubleshooting_steps": ["resolution notes:\nadditional comment by mi<PERSON><PERSON> rice [ 03/dec/24 ]\ni just learned that baar<PERSON> is not having issues with cat england."]}, {"id": "CS-31716", "title": "FW: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS", "score": 75, "categories": ["data_format", "document_flow"], "edi_types": ["850", "X12", "EDI"], "priority": "Medium", "description": "<PERSON><PERSON>\nLogistics Manager | <a href=\"https://www.nationwide-products.com\">https://www.nationwide-products.com</a> | <a href=\"https://rvair.com\">https://rvair.com</a>\nNation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602\n📞: (325) 675 – 5062 ext. 117\n✉: <a href=\"mailto:<EMAIL>\"><EMAIL></a>\nFrom: <PERSON><PERSON>t: Tuesday, November 19, 2024 9:41 AM To: &#39;<PERSON>&#39; <a href=\"mailto:<EMAIL>\"><PERSON><PERSON>@jmisales.com</a>; Se", "root_cause": "", "resolution": "fw: fl2 plant city expansion - v#88531 nation wide products\n———-—\nreply above this line.", "troubleshooting_steps": ["update for ?\nmaria keshwala datatrans solutions support@datatrans-inc.", "update for ?\nmaria keshwala datatrans solutions support@datatrans-inc."]}, {"id": "CS-30452", "title": "Fwd: EDI Transmission Failure - 2024-10-31 06:00:05 to 2024-11-01 06:00:05 DTS5763", "score": 75, "categories": ["data_format", "document_flow", "error_handling"], "edi_types": ["856", "X12", "EDI"], "priority": "Medium", "description": "Can I get more details on why these failed? Mainly rule#1?\n---------- Forwarded message ---------From: Datatrans Reports <a href=\"mailto:<EMAIL>\"><EMAIL></a> Date: Fri, Nov 1, 2024 at 8:00 AM Subject: EDI Transmission Failure - 2024-10-31 06:00:05 to 2024-11-01 06:00:05 To: <a href=\"mailto:<EMAIL>\"><EMAIL></a></p>\n<h1 id=\"errors-report\">Errors Report</h1>\n<ol>\n<li>Date/Time File Name Message Batch ID Status\n1 2024-10-31 07:23:16 DESAD", "root_cause": "", "resolution": "second batch 139867502\ncostco files that failed are all set restaged those files\nmaria keshwala datatrans solutions\nsupport@datatrans-inc.", "troubleshooting_steps": ["fixed\nsecond batch 139867502\ncostco files that failed are all set restaged those files\nmaria keshwala datatrans solutions\nsupport@datatrans-inc."]}, {"id": "CS-30350", "title": "Halliburton/DataTrans EDI invoice file problems DTS-334", "score": 75, "categories": ["connectivity", "data_format", "configuration", "document_flow"], "edi_types": ["810", "SFTP", "EDI"], "priority": "Medium", "description": "DataTrans Support,\n<em>We continue to receive several 0kb EDI invoice files from DataTrans each day. On Oct 17 we received the following “ <PERSON> commented: Good morning <PERSON> I was told by our IT that your setup is different and you cant be migrated to the new sftp but they looked into the 0kb issue and they resolved the problem please let us know if you still having any 0kb files so I can alert the IT to continue and investigate the root cause. But it was confirmed they have fixed it.”.", "root_cause": "", "resolution": "the problem please let us know if you still having any 0kb files so i can alert the it to continue and investigate the root cause.", "troubleshooting_steps": ["have fixed it."]}, {"id": "CS-44353", "title": "RE: For domestic fill- Vayda US corp", "score": 70, "categories": ["data_format", "document_flow"], "edi_types": ["EDI"], "priority": "Medium", "description": "", "root_cause": "the pos will b\nni<PERSON><PERSON> sanchez senior support engineer i cleo communications webedi-support@cleo.", "resolution": "re: for domestic fill- vayda us corp\n———-—\nreply above this line.", "troubleshooting_steps": ["change your company name we can certainly help you with that, however, if you want “vayda us corp“ used on only domestic pos, we cannot accommodate your request.", "change your company nam\nnicolas sanchez senior support engineer i cleo communications webedi-support@cleo.", "cause the pos will b\nni<PERSON><PERSON> sanch<PERSON> senior support engineer i cleo communications webedi-support@cleo."]}, {"id": "CS-43280", "title": "Fwd: [EXTERNAL] Re: Rejected EDI Invoice(s) for vendor 364930 (2025-05-05)", "score": 70, "categories": ["data_format", "document_flow", "error_handling"], "edi_types": ["810", "EDI"], "priority": "Medium", "description": "", "root_cause": "they are not taking your al\nnicolas sanchez senior support engineer i cleo communications webedi-support@cleo.", "resolution": "asap\ninvoice# 2025-086\ntotal of 9 line items (prior to allowance) should be 30301.", "troubleshooting_steps": ["cause they are not taking your al\nnicolas sanchez senior support engineer i cleo communications webedi-support@cleo."]}, {"id": "CS-43214", "title": "FW: POPTIME SNACK BRANDS - 850 Failure", "score": 70, "categories": ["data_format", "mapping", "performance", "document_flow", "error_handling"], "edi_types": ["850", "EDI"], "priority": "Medium", "description": "Hello, We received this PO 304370 which failed because the Ship to information (N102) is longer than the maximum length of 35 characters.\nPlease correct and resend.\nThanks\nEDI Production Support\n<em><a href=\"mailto:<EMAIL>\"><EMAIL></a> North America Quote-to-Cash</em>\n1 Becton Drive Franklin Lakes, NJ 07417 US\nBD Restricted\nFrom: WMProd <a href=\"mailto:<EMAIL>\"><EMAIL></a> Sent: Friday, May 2, 2025 1:01 AM To: E_Business_Prod <a href=\"mailto:E_Business_P", "root_cause": "the ship to information (n102) is longer than the maximum length of 35 characters.", "resolution": "", "troubleshooting_steps": ["changed the ship to info but i don’t see that the order was entered.", "cause the ship to information (n102) is longer than the maximum length of 35 characters.", "root cause : root cause: translation failed due to value in n1_02 exceeded the allowable maximum length of 35 characters, per map specs.", "root cause : root cause: translation failed due to value in n1_02 exceeded the allowable maximum length of 35 characters, per map specs."]}, {"id": "CS-43356", "title": "Nordstrom - Store 0881 (DS-995)", "score": 70, "categories": ["data_format", "document_flow", "error_handling"], "edi_types": ["850", "856", "EDI"], "priority": "Medium", "description": "Hello <PERSON>,\nThank you for calling into support for assistance with your Nordstrom ASN for PO #40513060. I am currently looking into this issue and will update you once I have more details.", "root_cause": "", "resolution": "the error with store 0881 and the asns are now in your draft folder with message ids 44200964, 44200967, and 44200992.", "troubleshooting_steps": ["update you once i have more details.", "found in our sdq tabling.", "found an old asn message id 37827315 ref elz40193980\naccepted asn\npo 40193980 to test created a new asn on the old po from 2023\nlaurens ticket regarding nordstrom 850 new edi format https://datatrans-inc."]}, {"id": "CS-43294", "title": "Re: Target POs Not Syncing into ShipStation Integration", "score": 70, "categories": ["data_format", "mapping", "configuration", "document_flow", "error_handling"], "edi_types": ["856", "810", "EDI"], "priority": "Medium", "description": "Adding support......\n<PERSON><PERSON> : Implementation Engineer I\nEmail: <a href=\"mailto:k<PERSON><PERSON><PERSON>@cleo.com\">k<PERSON><PERSON><PERSON>@cleo.com</a> | Web: <a href=\"http://www.cleo.com\">www.cleo.com</a>\nJoin us at one of our upcoming events. Check out the list!\nFrom: <PERSON><PERSON> <a href=\"mailto:<EMAIL>\"><EMAIL></a> Sent: Monday, May 5, 2025 3:20 PM To: Johnson, <PERSON><PERSON> <a href=\"mailto:k<PERSON><PERSON><PERSON>@cleo.com\">k<PERSON><PERSON><PERSON>@cleo.com</a> Cc: <PERSON>, <PERSON> <a href=\"mailto:<EMAIL>\"><EMAIL></a", "root_cause": "the error says i need to contact you guys for it.", "resolution": "", "troubleshooting_steps": ["change and is defined as…; blogs edi blog, trends &amp; insights; case studies read various case studies, white papers and implementation examples…; edi terminology &amp; complete edi glossary learn more about the edi industry terminology and definitions… datatrans-inc.", "i found some errors while validating your document.", "update your quantities when necessary.", "update in the system.", "change that other project to, instead, setup to auto-send function."]}, {"id": "CS-43021", "title": "RE: [EXTERNAL] Re: AAFES/Exchange x Radial System to System Integration - A Better Tomorrow LLC (07847402)", "score": 70, "categories": ["connectivity", "data_format", "configuration", "document_flow"], "edi_types": ["850", "856", "810", "997", "EDI"], "priority": "Medium", "description": "<PERSON>,\nFor Shipstation, our Support team would be able to assist. I do see they’re copied here already so they should be reaching out.\nThanks,\n<PERSON> : Implementation Engineer I\nEmail: <a href=\"mailto:<EMAIL>\"><EMAIL></a> | Web: <a href=\"http://www.cleo.com\">www.cleo.com</a>\nJoin us at one of our upcoming events. Check out the list!\nFrom: <PERSON> <a href=\"mailto:<EMAIL>\"><EMAIL></a> Sent: Monday, April 28, 2025 5:48 PM To: <PERSON>", "root_cause": "your organization is scheduled to begin trading data with our dropship manager on behalf of aafes/exchange.", "resolution": "", "troubleshooting_steps": ["change x radial system to system integration - a better tomorrow llc (07847402) hi boris,\nfor shipstation, our support team would be able to assist.", "change x radial system to system integration - a better tomorrow llc (07847402)\ncaution: this email originated from outside of the organization.", "change x radial system to system integration - a better tomorrow llc (07847402)\nhi purushothaman, updated files have been sent.", "updated files have been sent.", "changed accordingly with inclusion of additional fees as mentioned in technical questionnaire document\n846 inventory\ncold you please send us the 846 inventory file with quantity on hand as mentioned in the technical questionnaire."]}, {"id": "CS-33784", "title": "RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024", "score": 70, "categories": ["data_format", "performance", "document_flow", "error_handling"], "edi_types": ["856", "810", "EDI"], "priority": "Medium", "description": "@DataTrans Solutions, Inc.\nCan you assist please?\n<PERSON>\nPowder Coat Supervisor\nTruline MFG\n256-350-1002 ext.108\nFrom: <PERSON><PERSON> <a href=\"mailto:<EMAIL>\">Diaz<PERSON><EMAIL></a> Sent: Friday, November 29, 2024 8:29:59 AM To: <PERSON> <a href=\"mailto:<EMAIL>\"><EMAIL></a>; <PERSON> <a href=\"mailto:<EMAIL>\"><EMAIL></a>; <PERSON> <a href=\"mailto:<EMAIL>\"><EMAIL><", "root_cause": "we are not shipping a regular truck next week to you guys.", "resolution": "", "troubleshooting_steps": ["cause we are not shipping a regular truck next week to you guys.", "cause we are not shipping a regular truck next week to you guys."]}, {"id": "CS-31218", "title": "Fwd: FW: Your Invoice(s) 159763 for Order(s) 0018457-0372 DS-10331", "score": 70, "categories": ["connectivity", "data_format", "performance", "configuration", "document_flow", "error_handling"], "edi_types": ["856", "810", "EDI"], "priority": "Medium", "description": "From: <PERSON> <a href=\"mailto:<PERSON><PERSON>@exacto.com\"><PERSON><PERSON>@exacto.com</a> Date: Thu, Nov 14, 2024 at 8:41 AM Subject: FW: Your Invoice(s) 159763 for Order(s) 0018457-0372 To: <a href=\"mailto:<EMAIL>\"><EMAIL></a> <a href=\"mailto:<EMAIL>\"><EMAIL></a>\nHere is the first email I sent….\n<PERSON>,\n<PERSON>\nFrom: <PERSON>: Thursday, November 14, 2024 8:26 AM To: DataTrans Support <a href=\"mailto:support@datatrans", "root_cause": "is for all the parts not just this one.", "resolution": ".", "troubleshooting_steps": ["change iccnet is the only one goes to loren data not sure what caused to stop from picking them up and channel has not been disabled on 04 yet\nmaria keshwala datatrans solutions support@datatrans-inc.", "fixed as soon as possible.", "fixed the issue thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "update this afternoon.", "i checked our mail logs as well (this was attached on the previous email) and it doesn’t show anything coming in."]}, {"id": "CS-31100", "title": "FW: 856 ASN - Production Error - Canadian Tire - NATION WIDE PRODUCTS", "score": 70, "categories": ["data_format", "mapping", "document_flow", "error_handling"], "edi_types": ["856", "997", "EDI"], "priority": "Medium", "description": "SMTL is the carrier can you please advise?\n<PERSON><PERSON>\nLogistics Manager | <a href=\"https://www.nationwide-products.com\">https://www.nationwide-products.com</a> | <a href=\"https://rvair.com\">https://rvair.com</a>\nNation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602\n📞: (325) 675 – 5062 ext. 117\n✉: <a href=\"mailto:<EMAIL>\"><EMAIL></a>\nFrom: <PERSON> <a href=\"mailto:<PERSON><PERSON>@cantire.com\"><PERSON><PERSON>@cantire.com</a> Se", "root_cause": "the specs stated that if they send a rush order you need to send this segment therefore we cant remove it thank you\nmaria keshwala datatrans solutions support@datatrans-inc.", "resolution": "", "troubleshooting_steps": ["cause the specs stated that if they send a rush order you need to send this segment therefore we cant remove it thank you\nmaria keshwala datatrans solutions support@datatrans-inc."]}]