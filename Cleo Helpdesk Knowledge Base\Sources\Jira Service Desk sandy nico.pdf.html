<h4 id="cs-41567-fw-sage-rental-invoice-ri-5052004-failed-integration-please-check-advance-logs-ds-11270-created-30-mar-25-updated-03-jun-25-resolved-03-jun-25">[CS-41567] FW: Sage Rental Invoice #RI-5052004 failed integration, please check advance logs. DS-11270 Created: 30/Mar/25  Updated: 03/Jun/25  Resolved: 03/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Our Rental invoice RI-5052004 processed several times over two days and is listed in WebEDI multiple times with Accepted as the status for all of them.
Additionally, we received several emails notifying us that this invoice failed validation over the same timeframe.
Why is this happening? It is my understanding that if an 810 with an invoice number has processed successfully, the system will not allow another 810 with the exact same invoice number to process again. Clearly this is not happening, and it is causing a significant increase in our number of transactions and error messages. Which in turn are increasing our workload and costs.
I have reported this error a few times and it clearly is not getting resolved. Please let us know what can be done to fix this issue.
Kevin Skold
From: DataTrans Solutions Inc. <a href="mailto:<EMAIL>"><EMAIL></a> Date: Sunday, March 30, 2025 at 12:00 AM To: Kevin Skold <a href="mailto:<EMAIL>"><EMAIL></a>, EDI <a href="mailto:<EMAIL>"><EMAIL></a>, AR <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Sage Rental Invoice #RI-5052004 failed integration, please check advance logs.
External sender <a href="mailto:<EMAIL>"><EMAIL></a>
Make sure you trust this sender before taking any actions.
Sage Rental Invoice #RI-5052004 failed integration, please check advance logs.
Comments
Comment by Sandy Karidas [ 31/Mar/25 ]
Hello Kevin,
The mapper is not returning a response that the file was completed which is why the duplication is occurring. This is happening due to the large size of the file. I will escalate this to the developer for additional discovery.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 02/Apr/25 ]
Hello Kevin
I spoke with accounting regarding a credit for the duplicate invoices. A $144 credit will be applied to your account and will reflect on the April invoice.
Our development team is working on the duplication issue with the Sage integrator.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-44821-fwd-vendor-action-required-online-only-skus-cancelled-customer-order-v-g7d5-oa-dream-team-amer-created-02-jun-25-updated-03-jun-25">[CS-44821] Fwd: VENDOR ACTION REQUIRED &gt; Online Only SKUs &gt; Cancelled Customer Order V# G7D5 OA DREAM TEAM AMER Created: 02/Jun/25  Updated: 03/Jun/25</h4>
<p>Resolved: 03/Jun/25
Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Image.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Data Trans
Can we be advised how to cancel a PO though EDI using 855 The options we have available to us are Accepted or Back ordered
We do not have a CANCEL option yet we are being requested to by Canadian Tire through 855
Todd Taylor Owner Of The Future
Toll Free: 1-844-DRM-TEAM Phone/Facetime: ************
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
www DreamTeamAmericas com BELIEVE in the DREAM TRUST in the TEAM
CONFIDENTIALITY NOTICE: This email may contain privileged and or confidential information for the sole use of the intended recipient(s). Any unauthorized use, distribution or disclosure by others is strictly prohibited. Dream Team Americas INC &amp; LLC 2019/2022. Live Life #DreamTeaming
Begin forwarded message:
color: Color value is invalid
From:
<a href="mailto:<EMAIL>"><EMAIL></a>
color: Color value is invalid
Subject:
VENDOR ACTION REQUIRED &gt; Online Only SKUs &gt; Cancelled Customer Order V# G7D5 OA DREAM TEAM AMER
color: Color value is invalid
Date:
June 2, 2025 at 07:40:42 EDT
color: Color value is invalid
To:
<a href="mailto:<EMAIL>"><EMAIL></a>, <a href="mailto:<EMAIL>"><EMAIL></a>
color: Color value is invalid
Cc:
<a href="mailto:<EMAIL>"><EMAIL></a>, <a href="mailto:<EMAIL>"><EMAIL></a>, <a href="mailto:<EMAIL>"><EMAIL></a>
*<em><strong>URGENT</strong>
A customer has cancelled an order at canadiantire.ca for your product - please check the current order status.
Vendor Number Vendor Name PO Number Product Number FirstOfVendProdNo ProdDescr Quantity ReqShipDate 1. of notifications</em>
G7D5 OA DREAM TEAM AMER 1088813 1749921 EMEVSE1-BHJ EMP 48A HW UNI BLK 1.000 06/01/2025 1
VENDOR ACTION REQUIRED
a. If the order line has NOT been shipped, please cease any shipping activities and do not ship the product to the store. Please cancel the order line immediately using the EDI 855 transaction and notify your Replenishment Analyst. b. If the order has already been shipped, please make sure the ASN has been sent and the store will follow the reverse flow process once the product is received.
If there any questions or concerns, please respond to your Replenishment Analyst (<a href="mailto:<EMAIL>"><EMAIL></a>).
Please DO NOT reply to <a href="mailto:<EMAIL>"><EMAIL></a> as this is an outgoing mailbox only.
Thank you for your support.
Best Regards, Canadian Tire Supply Chain
This message, including any attachments, is privileged and may contain confidential information intended only for the person(s) named above. If you are not the intended recipient or have received this message in error, please notify the sender immediately by reply email and permanently delete the original transmission from the sender, including any attachments, without making a copy. Thank you.
Ce message, y compris toutes ses pièces jointes, est confidentiel et peut contenir des renseignements destinés uniquement aux personnes dont le nom est indiqué ci-dessus. Si vous n&#39;êtes pas le destinataire prévu ou si vous avez reçu ce message par erreur, veuillez en aviser l&#39;expéditeur immédiatement, en lui répondant par courriel. Veuillez aussi supprimer définitivement le message original de l&#39;expéditeur, y compris toute pièce jointe, sans faire de copie. Merci.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
Comment by Sandy Karidas [ 02/Jun/25 ]
Hello Todd,
Canadian Tire does not use the 855 for cancelling/rejecting orders. Please use the 870 document for this process.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-44048-flexovit-edi-ftp-stuck-created-20-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44048] Flexovit EDI Ftp Stuck Created: 20/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250520-174610.png      image-20250520-211921.png      image-20250521-141307.png      image-20250521-141355.png      image001.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: SFTP/FTP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good Morning,
It’s been a while, but it looks like the ftp is ‘stuck’ again.
Thanks
Herman van Leeuwen
MIS Supervisor
Flexovit USA, Inc.
1305 Eden-Evans Center Rd.
Angola, NY 14006
Phone: (716)549-5100 Ext. 237
Cell: (716)261-7880</p>
<ul>
<li><a href="mailto:<EMAIL>"><EMAIL></a> +
flexovitabrasives.com
Comments
Comment by Nicholas Sanchez [ 20/May/25 ]
Hello Herman,
I see todays files were sent at 11:12am (MST). Do you want me to restage their delivery?
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 20/May/25 ]
Good Afternoon,
It looks like it did get ‘unstuck’ by itself.
We are all good.
Thanks!
Herman van Leeuwen
MIS Supervisor
Flexovit USA, Inc.
1305 Eden-Evans Center Rd.
Angola, NY 14006
Phone: (716)549-5100 Ext. 237
Cell: (716)261-7880</li>
<li><a href="mailto:<EMAIL>"><EMAIL></a> +
flexovitabrasives.com
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 20/May/25 ]
Good Afternoon,
Looks like still something stuck ( Amazon order).
Thanks
Herman van Leeuwen
MIS Supervisor
Flexovit USA, Inc.
1305 Eden-Evans Center Rd.
Angola, NY 14006
Phone: (716)549-5100 Ext. 237
Cell: (716)261-7880</li>
<li><a href="mailto:<EMAIL>"><EMAIL></a> +
flexovitabrasives.com
Comment by Nicholas Sanchez [ 20/May/25 ]
Herman,
The file was sent and is waiting to be picked up on your side.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 20/May/25 ]
Can you send again please?
Nothing there …
Herman van Leeuwen
MIS Supervisor
Flexovit USA, Inc.
1305 Eden-Evans Center Rd.
Angola, NY 14006
Phone: (716)549-5100 Ext. 237
Cell: (716)261-7880</li>
<li><a href="mailto:<EMAIL>"><EMAIL></a> +
flexovitabrasives.com
Comment by Nicholas Sanchez [ 21/May/25 ]
The file is still in the ‘fromdts’ folder.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Nicholas Sanchez [ 21/May/25 ]
Hello Herman,
The file is still in the ‘fromdts’ directory.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
Good Morning,
I do not see it:
Thanks
Herman van Leeuwen
MIS Supervisor
Flexovit USA, Inc.
1305 Eden-Evans Center Rd.
Angola, NY 14006
Phone: (716)549-5100 Ext. 237
Cell: (716)261-7880</li>
<li><a href="mailto:<EMAIL>"><EMAIL></a> +
flexovitabrasives.com
Comment by Nicholas Sanchez [ 21/May/25 ]
Herman,
Seems you are still using our old SFTP setup. Please use these credentials to access our New SFTP server.
{{ServerName= transfer.datatrans-inc.com }}
{{Port= 22 }}
{{Username= dts4688 }}
password= VTRpeBAYxYohBPj9Dl4t
Nicolas Sanchez Senior Support Engineer I Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
Good Morning,
At this point, I cannot switch that easy.
Can we keep the old server going?
Thanks
Herman van Leeuwen
MIS Supervisor
Flexovit USA, Inc.
1305 Eden-Evans Center Rd.
Angola, NY 14006
Phone: (716)549-5100 Ext. 237
Cell: (716)261-7880</li>
<li><a href="mailto:<EMAIL>"><EMAIL></a> +
flexovitabrasives.com
Comment by Nicholas Sanchez [ 21/May/25 ]
Herman,
We will need you to migrate to the new SFTP.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
Good Afternoon,
I’m working on it.
It will take me about 2 weeks.
Thanks
Herman van Leeuwen
MIS Supervisor
Flexovit USA, Inc.
1305 Eden-Evans Center Rd.
Angola, NY 14006
Phone: (716)549-5100 Ext. 237
Cell: (716)261-7880</li>
<li><a href="mailto:<EMAIL>"><EMAIL></a> +
flexovitabrasives.com
Comment by Nicholas Sanchez [ 21/May/25 ]
Thank you Herman,
Please note that we will be shutting down your old SFTP. Please migrate to the new SFTP server by June 7th.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
Good Afternoon Nicholas,
Can you re-batch Ackland orders : 5003237455 and 5003241088 ??
Thanks
Herman van Leeuwen
MIS Supervisor
Flexovit USA, Inc.
1305 Eden-Evans Center Rd.
Angola, NY 14006
Phone: (716)549-5100 Ext. 237
Cell: (716)261-7880
<a href="mailto:<EMAIL>"><EMAIL></a>
flexovitabrasives.com
Comment by Nicholas Sanchez [ 02/Jun/25 ]
Hello Herman,
The files were delivered again.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ul>
<h4 id="cs-43868-fw-reminder-urgent-cleo-dts-sftp-update-created-16-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-43868] FW: REMINDER - URGENT - Cleo/DTS SFTP update Created: 16/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      Outlook-kybmexpr.png      Outlook-ppztejfm.png
Request Type: Emailed request
Request language: English Request participants: None
Organizations: Label: SFTP/FTP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Sandy and team,
If you have the connection information for the new FTP site, we can test it out before doing the migration.
I would like to do that before we set up a meeting for the migration.
Can you send me the connection information for the new site?
Jennifer Johnson
Customer Service &amp; Logistics Manager
Mobile: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Jón Fjölnir Albertsson <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 15, 2025 10:33 AM To: Jennifer Johnson <a href="mailto:<EMAIL>"><EMAIL></a>; MJ Dickson <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Sv: REMINDER - URGENT - Cleo/DTS SFTP update
Hi Jennifer / MJ
This might be a small issue if the authentication method is the same and the folder structure similar. Then we only have to change the path, username and password in the FTP setup, in BC. If there are some more changes it might require some more work from our site.
If you have the connection information for the new FTP site, we can test it out before doing the migration.
I would like to do that before we set up a meeting for the migration.
Can you send me the connection information for the new site?
Fra: Jennifer Johnson Sendt: torsdag, 15. mai 2025 16:09
Til: Jón Fjölnir Albertsson; MJ Dickson Emne: Fw: REMINDER - URGENT - Cleo/DTS SFTP update
Good morning,
I am not sure how this will affect us from and EDI/BC standpoint. I want to make sure we have full knowledge on how the change will affect our business.
Is there a date or time that works well for all or us to join?
[Jennifer Johnson|]
Customer Service &amp; Logistics Manager
Mobile: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Karidas, Sandy Sent: Wednesday, May 14, 2025 5:03 PM Subject: REMINDER - URGENT - Cleo/DTS SFTP update
Attention customers and trading partners,
URGENT - ATTENTION NEEDED:
Cleo/DTS has a new SFTP which is replacing the current FTP connection. We will be sunsetting the current FTP on June 7, 2025. Please use the calendar link below to schedule a 30-minute call to perform the migration. Do not reply directly to this email. Please disregard this notice if you have already completed the migration or scheduled a call.
To avoid document delivery interruptions, the migration must be completed before June 7, 2025.
<a href="https://calendly.com/nsanchez-datatrans-inc/30min">https://calendly.com/nsanchez-datatrans-inc/30min</a>
Sandy | | Karidas |
Cleo | : | Support Manager |
Email: | <a href="mailto:<EMAIL>"><EMAIL></a> |
|
Web:
<a href="http://www.cleo.com">www.cleo.com</a></p>
<ul>
<li>Join us at one of our upcoming events. Check out the list! *
From: Karidas, Sandy <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 6, 2025 11:11 AM Subject: URGENT - Cleo/DTS SFTP update
Attention customers and trading partners,
URGENT - ATTENTION NEEDED:
Cleo/DTS has a new SFTP which is replacing the current FTP connection. We will be sunsetting the current FTP on June 7, 2025. Please use the calendar link below to schedule a 30-minute call to perform the migration.
To avoid document delivery interruptions, the migration must be completed before June 7, 2025.
<a href="https://calendly.com/nsanchez-datatrans-inc/30min">https://calendly.com/nsanchez-datatrans-inc/30min</a>
<a href="https://calendly.com/nsanchez-datatrans-inc/30min">https://calendly.com/nsanchez-datatrans-inc/30min</a> 30 Minute Meeting - Nicolas Sanchez calendly.com
Sandy | | Karidas |
Cleo | : | Support Manager |
Email: | <a href="mailto:<EMAIL>"><EMAIL></a> |
|
Web:
<a href="http://www.cleo.com">www.cleo.com</a></li>
<li>Join us at one of our upcoming events. Check out the list! *
This is an EXTERNAL email. Please exercise caution. DO NOT open attachments or click links from unknown senders or unexpected email. ****
This is an EXTERNAL email. Please exercise caution. DO NOT open attachments or click links from unknown senders or unexpected email. ****
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/May/25 ]
Comment by Sandy Karidas [ 16/May/25 ]
Hi Jennifer,
Nico is handling the SFTP migration and will be back in the office on Monday. I will have him reach out to you then.
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Nicholas Sanchez [ 19/May/25 ]
Hello Jennifer,
Below are your new SFTP credentials. Please let me know when you plan on using this setup in production.
{{url: transfer.datatrans-inc.com }}
{{port: 22 }}
{{username: dts1798 }}
password: uJ4uENN6uBZibNXbXqzw
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ul>
<h4 id="cs-44754-fwd-greenwood-distribution-center-go-live-created-30-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44754] Fwd: Greenwood Distribution Center Go Live Created: 30/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.jpg    Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good morning.
Please review the email below from our TP about a new Ship to location and advise.
---------- Forwarded message ---------From: EDI US Onboarding <a href="mailto:<EMAIL>"><EMAIL></a> Date: Tue, May 27, 2025 at 4:08 PM Subject: Greenwood Distribution Center Go Live To: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Cc: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a>
Greetings,
In the ALDI transition plan to the new ERP system, as of May 21st, 2025, ALDI US has begun transmission of select POs for the Greenwood, IN (GRE) Distribution Center (DC) using our new ERP system. This is being executed with the same plan used for the other ALDI DC Go Lives that have taken place thus far.
If you are a supplier that supports our GRE DC, please ensure that the new GRE* <em>Ship-To GLN of 4099200123035 is configured for processing transmissions from ALDI into your systems. This is necessary so that EDI POs sent from our new GRE ERP system can be fulfilled without interruption.
Please note that for any newly issued Greenwood POs, please send ASNs and Invoices via the same method POs were issued. (i.e. if new PO is received via EDI, then please send ASNs and Invoices via EDI. If new PO is received via email, please manually send ASNs via Manhattan SE/AS and invoices via email)
Technical example: During this transition, GRE has two distinct ERP systems and EDI connections to route and manage EDI messages. While the below screenshot contains all ALDI US GLN identifiers, please review the below summary for GRE:
Legacy ERP Greenwood, Indiana (GRE)
EDI Transmissions until June 4th 2025 PO
Routed through an ISA GLN of 0041498000097 Buyer of GRE, with an N1</em>BY GLN of 0041498000240 Ship To address of GRE, with an N1<em>ST GLN of 0041498000240 PO number of “GRE-nnnnn”
ASN message type not supported 810 Invoice to be submitted to the Legacy EDI system
New ERP Greenwood, Indiana (GRE)
EDI Transmissions starting May 21st 2025 PO
Routed through an ISA GLN of 4099200007533 Buyer of ALDI, with an N1</em>BY GLN of 4099200046860 Ship To address of GRE, with an N1*ST GLN of 4099200123035 PO number of “75nnnnnnnn”
ASN messages supported. Manhattan web portal is available for manual submissions. 810 Invoice to be submitted to the new ERP system
Please note that we have more DCs going live over the next few months. The following are the next few Go-Lives to be aware of and prepared for in 2025:
Month Distribution Center
July 2025 Springfield
August 2025 Tully
September 2025 Oak Creek
Thank you,
ALDI US EDI Team
__________________________________ COR_USA EDIUSOnboarding
ALDI Inc. National IT US -
1245 Corporate Blvd. Aurora, IL 60505
SAVE PAPER - THINK BEFORE YOU PRINT
<em>This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to which they are addressed. If you have received this email in error please notify the sender. Please note that any views or opinions presented in this email are solely those of the author and do not necessarily represent those of the company. Finally, the recipient should check this email and any attachments for the presence of viruses. ALDI accepts no liability for any damage caused by any virus transmitted by this email.</em>
...prima di stampare pensa all&#39;ambiente...think before you print...
Nutkao srl, in ottemperanza alla normativa ex D.Lgs 231/01 e successive modifiche, si è dotata di un proprio Codice Etico, di un Modello di organizzazione e ha nominato un Organismo di Vigilanza. Il Codice Etico è a disposizione sul sito Internet <a href="http://www.nutkao.com">www.nutkao.com</a>. Chiunque intrattiene rapporti con Nutkao srl si impegna a rispettare i principi contenuti nel Codice Etico. La violazione di tali principi comporta cessazione del rapporto fiduciario: Nutkao srl si riserva di interrompere ogni rapporto con chiunque violi tali principi e di agire legalmente per il ristoro di eventuali danni.
In accordance to the Italian laws, Decree N. 231/01 and subsequent amendments, Nutkao has decided to adopt a Code of Ethics, such as an organization and management Model. Therefore Nutkao has also designate a Supervisory Board in order to watch on this Model. The Code of Ethics is available on the Company website <a href="http://www.nutkao.com">www.nutkao.com</a>. Anyone who has dealings with Nutkao srl is request to be engaged towards the respect of Nutkao’s Code of Ethics. Violation of its rules imposes the end of the partnership. Nutkao srl has then the right to break down any relationship with anyone who violates these principles and to act under Laws regulations to solve any contingent damage.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Comment by Sandy Karidas [ 02/Jun/25 ]
Hello Manuel,
No further action is needed at this time.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
Hi Sandy,
Was the new DC added to this TP&#39;s existing DCs?
Comment by Sandy Karidas [ 02/Jun/25 ]
Hello Manuel,
You are all set with Aldi, no additional mapping is needed as you are not integrated with them at this time.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
Thanks, Sandy for the confirmation.</p>
<h4 id="cs-43436-fw-external-re-20009554-world-dryer-corportation-grainger-drop-ship-non-compliance-ds-11849-created-08-may-25-updated-02-jun-25-resolved">[CS-43436] Fw: [<strong>External</strong>]: RE: 20009554 WORLD DRYER CORPORTATION - Grainger Drop Ship Non-Compliance DS-11849 Created: 08/May/25  Updated: 02/Jun/25  Resolved:</h4>
<p>02/Jun/25
Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Vinothkumar Narayanamoorthy Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image004.png      image001.png      image002.png      image003.png      image008.png      image009.png      image010.png      image011.png     image012.jpg      image013.png      image014.png      image015.png      image016.png      image017.png      image005.png      image006.png      image007.png      WORLD DRYER CORPORTATION_packinglist.jpg
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Sandy,
We need your immediate assistance to check our below email from Grainger and kindly get back to us.
Thank you Vinoth
From: Cesar Acevedo <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 8, 2025 7:28:05 pm To: Vinoth Kumar Narayana Moorthy <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Amit Das <a href="mailto:<EMAIL>"><EMAIL></a>; Iain McNaughton <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: [<strong>External</strong>]: RE: 20009554 WORLD DRYER CORPORTATION - Grainger Drop Ship Non-Compliance
Hello Vinoth
Please reach out to Datatrans support explaining this need as urgent. This should be part of the Grainger profile or so…
Thanks,
<a href="https://www.hadrian-inc.com/">https://www.hadrian-inc.com/</a>  <a href="https://worlddryer.com/">https://worlddryer.com/</a> |
Cesar Acevedo. P.Eng Head of Enterprise Applications and Architecture Hadrian and World Dryer 965 Syscon Road, Burlington, ON L7L 5S3 Mobile: (************* : Office: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.linkedin.com/company/hadrian-manufacturing-inc./">https://www.linkedin.com/company/hadrian-manufacturing-inc./</a>
<a href="https://www.facebook.com/HadrianInc">https://www.facebook.com/HadrianInc</a>
<a href="https://www.instagram.com/hadrian_inc/">https://www.instagram.com/hadrian_inc/</a>
<a href="http://zurn-elkay.com/">http://zurn-elkay.com/</a>
From: Iain McNaughton <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 7, 2025 5:23 PM To: Cesar Acevedo <a href="mailto:<EMAIL>"><EMAIL></a>; Amit Das <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: [<strong>External</strong>]: RE: 20009554 WORLD DRYER CORPORTATION - Grainger Drop Ship Non-Compliance
Cesar
Who can work with DataTrans to address these requirements?
Grainger requires the following items to exist on their packing slip that prints out of Datatrans:
Reference of Grainger on packing list Customer PO number on Packing list
<a href="https://www.hadrian-inc.com/">https://www.hadrian-inc.com/</a>
<a href="https://urldefense.com/v3/__https:/worlddryer.com/__;!!A2CI49EpPj0!yyhYDRAGmtBaoR7Qn9gjyleEo1bGJ002QGghUuBNgQhRy64BZyt26gh_F-90Kp7LnMHe25bUV8L0QrfUEZBZ0kcdTV7hdowc$">https://urldefense.com/v3/__https:/worlddryer.com/__;!!A2CI49EpPj0!yyhYDRAGmtBaoR7Qn9gjyleEo1bGJ002QGghUuBNgQhRy64BZyt26gh_F-90Kp7LnMHe25bUV8L0QrfUEZBZ0kcdTV7hdowc$</a> |
Iain McNaughton Operations Manager : Logistics Manager : Phoenix, AZ Hadrian Inc. : Office: (480)481-2583 Email: <a href="mailto:<EMAIL>"><EMAIL></a>  &lt;<a href="https://urldefense.com/v3/__https:/www.linkedin.com/company/hadrian-manufacturing-inc./__;!!A2CI49EpPj0!yyhYDRAGmtBaoR7Qn9gjyleEo1bGJ002QGghUuBNgQhRy64BZyt26gh_F-">https://urldefense.com/v3/__https:/www.linkedin.com/company/hadrian-manufacturing-inc./__;!!A2CI49EpPj0!yyhYDRAGmtBaoR7Qn9gjyleEo1bGJ002QGghUuBNgQhRy64BZyt26gh_F-</a>
90Kp7LnMHe25bUV8L0QrfUEZBZ0kcdTYJFuLI9$&gt;  <a href="https://urldefense.com/v3/__https:/www.facebook.com/HadrianInc__;!!A2CI49EpPj0!yyhYDRAGmtBaoR7Qn9gjyleEo1bGJ002QGghUuBNgQhRy64BZyt26gh_F-90Kp7LnMHe25bUV8L0QrfUEZBZ0kcdTT93qOD0$">https://urldefense.com/v3/__https:/www.facebook.com/HadrianInc__;!!A2CI49EpPj0!yyhYDRAGmtBaoR7Qn9gjyleEo1bGJ002QGghUuBNgQhRy64BZyt26gh_F-90Kp7LnMHe25bUV8L0QrfUEZBZ0kcdTT93qOD0$</a>  <a href="https://urldefense.com/v3/__https:/www.instagram.com/hadrian_inc/__;!!A2CI49EpPj0!yyhYDRAGmtBaoR7Qn9gjyleEo1bGJ002QGghUuBNgQhRy64BZyt26gh_F-90Kp7LnMHe25bUV8L0QrfUEZBZ0kcdTTzRlv0P$">https://urldefense.com/v3/__https:/www.instagram.com/hadrian_inc/__;!!A2CI49EpPj0!yyhYDRAGmtBaoR7Qn9gjyleEo1bGJ002QGghUuBNgQhRy64BZyt26gh_F-90Kp7LnMHe25bUV8L0QrfUEZBZ0kcdTTzRlv0P$</a>
<a href="https://urldefense.com/v3/__http:/zurn-elkay.com/__;!!A2CI49EpPj0!yyhYDRAGmtBaoR7Qn9gjyleEo1bGJ002QGghUuBNgQhRy64BZyt26gh_F-90Kp7LnMHe25bUV8L0QrfUEZBZ0kcdTc8t42yN$">https://urldefense.com/v3/__http:/zurn-elkay.com/__;!!A2CI49EpPj0!yyhYDRAGmtBaoR7Qn9gjyleEo1bGJ002QGghUuBNgQhRy64BZyt26gh_F-90Kp7LnMHe25bUV8L0QrfUEZBZ0kcdTc8t42yN$</a>
This message and any files transmitted with it are directed in confidence solely for the use of the individual(s) to whom they are addressed. If you are not the intended recipient, be advised that you have received this email in error and that any use, dissemination, forwarding, printing, or copying of this email is strictly prohibited. You are requested to immediately advise the sender by return email and delete this email from your computer. Thank you.
From: Launa Taylor <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 7, 2025 12:12 PM To: Iain McNaughton <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: [<strong>External</strong>]: RE: 20009554 WORLD DRYER CORPORTATION - Grainger Drop Ship Non-Compliance
Add’l info on this FYI…
Launa Taylor
Customer Service Supervisor – Process Improvement Specialist
7420 Clover Ave., Mentor, Ohio 44060
Mobile: (*************
<a href="https://worlddryer.com/">https://worlddryer.com/</a>  <a href="https://zurnelkay.com/">https://zurnelkay.com/</a>  <a href="https://www.linkedin.com/company/world-dryer-corporation/">https://www.linkedin.com/company/world-dryer-corporation/</a>
<a href="https://www.facebook.com/WorldDryerCorp">https://www.facebook.com/WorldDryerCorp</a>  <a href="https://twitter.com/WorldDryerCorp">https://twitter.com/WorldDryerCorp</a>
<a href="https://www.hadrian-inc.com/">https://www.hadrian-inc.com/</a>  <a href="https://zurnelkay.com/">https://zurnelkay.com/</a>  <a href="https://www.linkedin.com/company/hadrian-manufacturing-inc./">https://www.linkedin.com/company/hadrian-manufacturing-inc./</a>
<a href="https://www.facebook.com/HadrianInc">https://www.facebook.com/HadrianInc</a>  <a href="https://www.instagram.com/hadrian_inc/">https://www.instagram.com/hadrian_inc/</a>
<em>We are currently experiencing an increased volume of customer inquiries and there may be a slight delay in our response time. Rest assured that your message has been received and we are committed to providing you with a response as soon as possible. If this is an urgent matter, please contact your local Sales Agent or Regional Sales Manager: Hadrian Rep Locator .</em>
From: Usher, Duncan <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 7, 2025 1:24 PM To: LKREP-083-Driskell, Brian <a href="mailto:<EMAIL>"><EMAIL></a>; David Richmond <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; Launa Taylor <a href="mailto:<EMAIL>"><EMAIL></a>; Tolis Demertzis <a href="mailto:<EMAIL>"><EMAIL></a>; Allison Schindler <a href="mailto:<EMAIL>"><EMAIL></a>; Joseph Cannizzaro <a href="mailto:<EMAIL>"><EMAIL></a>; LKREP-083-Zimmerman, Meghan <a href="mailto:<EMAIL>"><EMAIL></a> Subject: [EXTERNAL] RE: [<strong>External</strong>]: RE: 20009554 WORLD DRYER CORPORTATION - Grainger Drop Ship Non-Compliance
Hi Brian,
Attached are the images we received from PO 4645160382 which was audited a few months ago. It looks like the requirements that were not met are:
Reference of Grainger in Ship From section o f shipping label Reference of Grainger on packing list Customer PO number on Packing list
The customer PO number for this order was ZoroProjectDeeWhite, and it looks like you correctly included it on the Shipping Label, but not the packing list.
Please let me know if you have any questions, or if you have a compliant Shipping Label and Packing List from a past or future drop ship order for me to review!
Thanks,
Duncan Usher | Analyst – Supplier Performance Management | Supply Chain Rotational Development Program | W.W. Grainger, Inc 100 Grainger Parkway | Lake Forest, IL 60045 | <a href="mailto:<EMAIL>"><EMAIL></a>
Book time with Usher, Duncan
Comments
Comment by Vinothkumar Narayanamoorthy [ 08/May/25 ]
Comment by Sandy Karidas [ 08/May/25 ]
Hello Vinothkumar,
I have escalated this to our development team for review. I will follow back up once I have an update.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Vinothkumar Narayanamoorthy [ 12/May/25 ]
Hi Sandy,
Thank you so much for your response, we are yet to receive an update on this.
Thank you Vinoth
Comment by Sandy Karidas [ 12/May/25 ]
Hello Vinothkumar,
I have asked developer for an update. I will try to get status in the morning.
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 14/May/25 ]
Hello Vinothkumar,
A custom packing list needs to be created for Grainger. The development team is working on it.
Thank you,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Vinothkumar Narayanamoorthy [ 15/May/25 ]
Hello Sandy,
Thank you for the below update.
Regards, Vinoth
Comment by Sandy Karidas [ 02/Jun/25 ]
Hello Vinothkumar,
The Grainger packing list is completed and in production on your account.
Please contact support if you need additional assistance.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-43994-system-maintenance-4-8-pm-pt-sun-05-31-25-created-19-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-43994] System Maintenance 4 - 8 pm PT Sun 05/31/25 Created: 19/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Subject: Important Notice - Albertsons Companies IBM B2B AS2 System Maintenance
Date/Time: Saturday 05/31/2025 4:00 pm to 8 pm PT System Maintenance.
Vendors:
Albertsons Companies is planning a system maintenance outage Saturday May 31st from 4 pm to 8 pm PT. Please make sure to resend any failed transactions after the outage window.
System Impacted: IBM B2B AS2 Production System
AS2 ID: safewayAS2
URL: <a href="https://b2b.albertsons.com/as2">https://b2b.albertsons.com/as2</a> and <a href="http://b2b.albertsons.com/as2">http://b2b.albertsons.com/as2</a>
Thank you in advance for your assistance.
If you have any questions, please email us at: <a href="mailto:<EMAIL>"><EMAIL></a>
Maria Beal-Uribe | Enterprise Information Governance &amp; Standards | Albertsons Companies 5918 Stoneridge Mall Rd. Pleasanton, CA 94588 | Work: ************ Cell: ************ | Email: <a href="mailto:<EMAIL>"><EMAIL></a>
Is your inquiry regarding a missing payment? Please go to our AP Portal to look up invoices and payments. <a href="https://safeway.firstvendor.apexanalytix.com/Default.aspx">https://safeway.firstvendor.apexanalytix.com/Default.aspx</a>
Warning: All e-mail sent to this address will be received by the corporate e-mail system, and is subject to archival and review by someone other than the recipient. This e-mail may contain proprietary information and is intended only for the use of the intended recipient(s). If the reader of this message is not the intended recipient(s), you are notified that you have received this message in error and that any review, dissemination, distribution or copying of this message is strictly prohibited. If you have received this message in error, please notify the sender immediately.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Hello All,
The time for the maintenance has been changed to 7 pm to 11 pm PT
Date/Time: Sunday 05/31/2025 7:00 pm to 11pm PT System Maintenance.
Best,
Maria Beal-Uribe | Enterprise Information Governance &amp; Standards | Albertsons Companies 5918 Stoneridge Mall Rd. Pleasanton, CA 94588 | Work: ************ Cell: ************ | Email: <a href="mailto:<EMAIL>"><EMAIL></a>
Is your inquiry regarding a missing payment? Please go to our AP Portal to look up invoices and payments. <a href="https://safeway.firstvendor.apexanalytix.com/Default.aspx">https://safeway.firstvendor.apexanalytix.com/Default.aspx</a>
From: AS2 Support
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Hello All,
The time for the maintenance has been changed to 7 pm to 11 pm PT.
Date/Time: Saturday05/31/2025 7:00 pm to 11pm PT System Maintenance.
Best,
Maria Beal-Uribe | Enterprise Information Governance &amp; Standards | Albertsons Companies 5918 Stoneridge Mall Rd. Pleasanton, CA 94588 | Work: ************ Cell: ************ | Email: <a href="mailto:<EMAIL>"><EMAIL></a>
Is your inquiry regarding a missing payment? Please go to our AP Portal to look up invoices and payments. <a href="https://safeway.firstvendor.apexanalytix.com/Default.aspx">https://safeway.firstvendor.apexanalytix.com/Default.aspx</a>
From: AS2 Support</p>
<h4 id="cs-44719-invoices-not-appearing-in-web-edi-created-30-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44719] Invoices not appearing in Web EDI Created: 30/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Christopher Morrison Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  EDI Error - 2025-05-29 170004 to 2025-05-29 180004.msg      image-20250530-153508.png      image001.png      image002.png     12557893_880_20250529_11122512.arc.bak      image003.png      image004.png      12557894_880_20250529_11124612.arc.bak
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Customer: Adams Extracts
ID: 6061
The invoices listed below were transmitted to DTS on 5/29/2025 but never showed up in Web EDI.
Three of the invoices were retransmitted to DTS on 5/30/2025. These have not shown up in Web EDI either.
I checked Walmart’s Retail Link website, and they have not received the invoices listed below.
Invoice Trading Partner Retransmitted
12557774 Walmart x
12557775 Walmart
12557776 Walmart x
12557777 Walmart
12557778 Walmart x
12557893 Kroger
12557894 Kroger
12557899 Walmart
12557900 Walmart
12557901 Walmart
Christopher Morrison | ERP Lead Developer Adams Flavors, Foods &amp; Ingredients 3217 Johnson Rd | PO Box 1726 Gonzales, TX 78629
C: 610-513-6680 Adams1888.com
Adams EDI Team: <a href="mailto:<EMAIL>"><EMAIL></a>
Adams ERP Admin: <a href="mailto:<EMAIL>"><EMAIL></a>
Book time with Morrison, Christopher
Comments
Comment by Scott Shippee [ 30/May/25 ]
We have corrected the Walmart issue however the 2 Kroger invoices are still a problem
After the purchase of Johny’s, the orders are now going through our system, however the orders are being manually entered so the data isn’t complete. WE have adjusted the EDI system to check for the Johnny’s and we are not transmitting those invoices.
Comment by Christopher Morrison [ 30/May/25 ]
EDI Error - 2025-05-29 170004 to 2025-05-29 180004.msg
Comment by Christopher Morrison [ 30/May/25 ]
Comment by Sandy Karidas [ 30/May/25 ]
Hello Chris,
In reviewing the Walmart invoices it appears the UPC number is missing from the files we received and is a required value. Please correct and resend.
I am still looking into the Kroger invoices.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 30/May/25 ]
Hello Chris,
The Kroger files appear to be formatted incorrectly. The bill to and ship to information is missing from the two files. Please correct and resend.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Scott Shippee [ 30/May/25 ]
Regarding the KROGER Invoices:
There should be a new map because of the changes requested by KROGER. The Bill To &amp; Ship To address were in the files (see attached, but there were also two new type 8 (REF/N9) records as requested by Kroger.
I have deleted the type 8 records, and I have retransmitted them, however, It is very possible that Kroger will reject them.
NOTE: The attached files are plain TEXT files.
Scott
Scott W. Shippee
Nestell &amp; Associates| 5000 Birch Street | West Tower, Suite 3000 | Newport Beach, CA | 92660, USA
<a href="http://www.NestellAssociates.com">www.NestellAssociates.com</a>
Mobile: (401)529-0678
Timezone: EST/EDT
<a href="https://www.linkedin.com/company/nestell-associates/">https://www.linkedin.com/company/nestell-associates/</a>  <a href="https://twitter.com/NestellAssocia1">https://twitter.com/NestellAssocia1</a>  <a href="https://www.facebook.com/nestellandassociates">https://www.facebook.com/nestellandassociates</a> <a href="https://www.youtube.com/channel/UCJY2x8gU2h_PeT9fv7wTxDQ">https://www.youtube.com/channel/UCJY2x8gU2h_PeT9fv7wTxDQ</a>
The ERPocj Podcast
The ERP Organizational Change Success Blog
<em>12557893_880_20250529_11122512.arc.bak  (12 kB)</em>
<em>12557894_880_20250529_11124612.arc.bak  (12 kB)</em>
Comment by Sandy Karidas [ 30/May/25 ]
Hi Scott,
I spoke with Daniel and Kroger/Ralphs was not part of the map updates, only Kroger Modernized. Daniel will need to include them into the new mapping.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Scott Shippee [ 30/May/25 ]
Hmm… I thought it was… I will double check and we can review next Tuesday at our weekly meeting.
Don’t worry about it until then.
Scott W. Shippee
Nestell &amp; Associates| 5000 Birch Street | West Tower, Suite 3000 | Newport Beach, CA | 92660, USA
<a href="http://www.NestellAssociates.com">www.NestellAssociates.com</a>
Mobile: (401)529-0678
Timezone: EST/EDT
<a href="https://www.linkedin.com/company/nestell-associates/">https://www.linkedin.com/company/nestell-associates/</a>  <a href="https://twitter.com/NestellAssocia1">https://twitter.com/NestellAssocia1</a>  <a href="https://www.facebook.com/nestellandassociates">https://www.facebook.com/nestellandassociates</a> <a href="https://www.youtube.com/channel/UCJY2x8gU2h_PeT9fv7wTxDQ">https://www.youtube.com/channel/UCJY2x8gU2h_PeT9fv7wTxDQ</a>
The ERPocj Podcast
The ERP Organizational Change Success Blog</p>
<h4 id="cs-44472-re-datatrans-solutions-edi-production-notice-created-28-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44472] Re: DataTrans Solutions EDI Production Notice Created: 28/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Brittany Keegan Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Looking to get a hold of Drew Fisher or Kayleigh Benedict.
I haven&#39;t gotten any emails from Drew and it&#39;s been awhile since this email was sent. I have Chewy asking for inventory to be uploaded, I emailed Kayleigh, but haven&#39;t heard back - thinking maybe she&#39;s not my contact anymore. Looking for an update
Also can we please update my main email with you all to be <a href="mailto:<EMAIL>"><EMAIL></a> This one works, but gets a ton of emails a day
Thank you!
ᐧ
On Wed, May 21, 2025 at 9:24 PM Kyle Keegan <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
---------- Forwarded message ---------From: Datatrans Solutions Inc. <a href="mailto:<EMAIL>"><EMAIL></a> Date: Wed, May 21, 2025 at 4:03 PM Subject: DataTrans Solutions EDI Production Notice To: <a href="mailto:<EMAIL>"><EMAIL></a>
Hello Zaley Designs LLC,
Great news! Your setup is complete and you are now in full EDI production with Chewy.com Dropship DSCO. I hope you enjoy the EDI solutions provided by DataTrans.
Now that you are in production, our Customer Success Manager, Drew Fisher will reach out to you with information related to your next steps and provide further guidance.
Can I also ask a favor? Could you please complete the quick DataTrans Customer Experience Survey which goes over how I performed in addressing your EDI needs with this project? If you can respond with all 5’s my manager will know I did my best and performed well on your project.
In the eyes of DataTrans, we shoot for all 5’s and consider anything below that level as an area where we must improve. If there is anything I could have done differently to earn all 5’s, please let us know what area needs improvement.
Please keep in mind that you can always e-mail <a href="mailto:<EMAIL>"><EMAIL></a> or call ************ option 2 for any support issues.
Also, our informative weekly Training Class is available EVERY Monday at 10:30 AM CST. Training is required in order for our Department to respond to any inquiries. Please use this same invite to log in to each training session you and your team wish to attend. Q &amp; A is available during each session. Click Here to join the meeting on Monday!
All of our contacts are listed under &#39;HELP&#39; in your WebEDI account. Thanks so much for choosing DataTrans as your EDI provider and have a great day!
Thanks,
Kayleigh Benedict <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comments
Comment by Sandy Karidas [ 29/May/25 ]
Hello Brittany,
You have the ability to create an 846 document in the WebEDI portal. Instructions below. If you are interested in automating the 846 document please speak with Drew regarding pricing. I have included him on this email. Also, the WebEDI training class is held every Monday morning at 10:30am CST. I have included the link to the class below.
Training Class
WebEDI Training Tutorial- Monday&#39;s 10:30AM CST
<a href="https://meet.google.com/rsk-dphb-rhu">https://meet.google.com/rsk-dphb-rhu</a> to join the meeting.</p>
<ol>
<li>Microphone and speakers are required; a headset is recommended.
There is also a tutorial video at <a href="https://www.youtube.com/watch?v=RuL_k7pSAFg&amp;t">https://www.youtube.com/watch?v=RuL_k7pSAFg&amp;t</a>
846 Creation
The best way to can create a new 846 Inventory document is to use the WebEDI catalog. You may update the inventory quantities on the actual 846 document or you may use the quantity field of the WebEDI catalog.</li>
<li>Click on Catalog. 2. Click on the check boxes next to the needed item(s) to select that item(s). That will turn the row blue. 3. If the items have a number in the quantity column that quantity will be loaded onto your new 846 Inventory document. See the notes below on options for updating the quantity information. 4. Click New Document. 5. Select Inventory Inquiry/Advice (846) and then your trading partner&#39;s name. 6. Make sure that the Reference Identification number is unique. Don&#39;t repeat a number with the last 365 days. You may use your Document Default screen to sequentially automate this number. 7. Make sure that the date is today&#39;s date. 8. Confirm that the report is accurate. 9. Click Save</li>
<li>Click Send to transmit your document.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Brittany Keegan [ 30/May/25 ]
Thank you for this information! I will definitely hop on that training monday. I have watched the tutorial video you sent as well.
I&#39;m not finding that I&#39;ve been sent access to a WebEDI platform can you help with that? ᐧ
Comment by Sandy Karidas [ 30/May/25 ]
Hello Brittany,
Here is the URL for the WebEDI portal. Your account number is 7561. Please use the forgot password option to reset your password.
<a href="http://www.datatrans-inc.com">www.datatrans-inc.com</a>
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</li>
</ol>
<h4 id="cs-44353-re-for-domestic-fill-vayda-us-corp-created-24-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44353] RE: For domestic fill- Vayda US corp Created: 24/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.jpg      image002.jpg      image003.png      image004.png      image005.png      image006.png      image007.png      image008.jpg      image009.png      image010.png      image011.png      image012.png      image013.png      image014.jpg      image001 (e5969d7f-3192-41cd-9aef-8afe57b884a7).jpg
Request Type: Emailed request
Request language: English Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Adding <a href="mailto:<EMAIL>"><EMAIL></a> for response.
Kindly advise on below query.
Thanks &amp; Regards,
Arvind Kumar | Divisional Merchandising Manager | Vayda Overseas
Plot No 149-154, 25 Sector Part 1, Industrial Estate, Huda, Panipat, Haryana 132103
Mob No. +91-********** | <a href="http://www.vayda.in">www.vayda.in</a>
&lt;<a href="https://urldefense.com/v3/__https:/www.facebook.com/Vayda-Overseas-101640721766500/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-">https://urldefense.com/v3/__https:/www.facebook.com/Vayda-Overseas-101640721766500/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-</a>
4ofZ03Bs8O$&gt;  <a href="https://urldefense.com/v3/__https:/www.instagram.com/vaydaoverseas/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofR_kqyuH$">https://urldefense.com/v3/__https:/www.instagram.com/vaydaoverseas/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofR_kqyuH$</a>
<a href="https://urldefense.com/v3/__https:/www.linkedin.com/in/vayda-overseas-65128b1bb/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofboiUOAg$">https://urldefense.com/v3/__https:/www.linkedin.com/in/vayda-overseas-65128b1bb/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofboiUOAg$</a>
<a href="https://urldefense.com/v3/__https:/in.pinterest.com/VaydaOverseas/_saved/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofVC7sg0-$">https://urldefense.com/v3/__https:/in.pinterest.com/VaydaOverseas/_saved/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofVC7sg0-$</a> <a href="https://urldefense.com/v3/__https:/twitter.com/Vayda34032764__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofSsOq18M$">https://urldefense.com/v3/__https:/twitter.com/Vayda34032764__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofSsOq18M$</a>
From: Arvind <a href="mailto:<EMAIL>"><EMAIL></a> Sent: 24 May 2025 04:01 To: &#39;Bacon, Keli&#39; Cc: &#39;Rajat&#39;; &#39;Anoop&#39;; &#39;Manish Singla&#39; Subject: RE: For domestic fill- Vayda US corp
Hi Keli,
For domestic fill shipments, we would like to have the purchase orders issued in the name of Vayda US Corp on VMM (US-based), under Supplier ID: 2003457.
To initiate the EDI online registration process, we understand from the POL that we need the EDI ID and Qualifier.
Could you please advise how we can obtain the EDI ID and Qualifier in the name of Vayda US Corp.
Additionally, please let us know if any information is required from our side in this regard.
Looking forward for your guidance.
Thanks &amp; Regards,
Arvind Kumar | Divisional Merchandising Manager | Vayda Overseas
Plot No 149-154, 25 Sector Part 1, Industrial Estate, Huda, Panipat, Haryana 132103
Mob No. +91-********** | <a href="http://www.vayda.in">www.vayda.in</a>
&lt;<a href="https://urldefense.com/v3/__https:/www.facebook.com/Vayda-Overseas-101640721766500/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-">https://urldefense.com/v3/__https:/www.facebook.com/Vayda-Overseas-101640721766500/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-</a>
4ofZ03Bs8O$&gt;  <a href="https://urldefense.com/v3/__https:/www.instagram.com/vaydaoverseas/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofR_kqyuH$">https://urldefense.com/v3/__https:/www.instagram.com/vaydaoverseas/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofR_kqyuH$</a>
<a href="https://urldefense.com/v3/__https:/www.linkedin.com/in/vayda-overseas-65128b1bb/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofboiUOAg$">https://urldefense.com/v3/__https:/www.linkedin.com/in/vayda-overseas-65128b1bb/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofboiUOAg$</a>
<a href="https://urldefense.com/v3/__https:/in.pinterest.com/VaydaOverseas/_saved/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofVC7sg0-$">https://urldefense.com/v3/__https:/in.pinterest.com/VaydaOverseas/_saved/__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofVC7sg0-$</a> <a href="https://urldefense.com/v3/__https:/twitter.com/Vayda34032764__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofSsOq18M$">https://urldefense.com/v3/__https:/twitter.com/Vayda34032764__;!!IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofSsOq18M$</a>
From: Bacon, Keli [<a href="mailto:<EMAIL>">mailto:<EMAIL></a>] Sent: 23 May 2025 05:41 To: Anoop Cc: &#39;Arvind&#39;; &#39;Rajat&#39; Subject: RE: For domestic fill- Vayda US corp
Hello,
Please reach out to Target directly to make this change. Our system simply receives the PO from Target. Target is creating it and needs to be alerted of the correct information to enter into their PO.
Thanks,
Keli
Keli Bacon
Cleo : Manager, Procurement &amp; Special Projects
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a></li>
<li>Join us at one of our upcoming events. Check out the list! *
From: Anoop <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 23, 2025 1:16 AM To: &#39;DataTrans Invoicing&#39; <a href="mailto:<EMAIL>"><EMAIL></a>; &#39;Keli Bacon&#39; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Cc: &#39;Arvind&#39; <a href="mailto:<EMAIL>"><EMAIL></a>; &#39;Rajat&#39; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: For domestic fill- Vayda US corp
color: Color value is invalid
Some people who received this message don&#39;t often get email from <a href="mailto:<EMAIL>"><EMAIL></a>. [ Learn why this is important
https://aka.<a class="g3mark-shortlink" href="https://ms.corp.google.com/LearnAboutSenderIdentification">ms/LearnAboutSenderIdentification</a>] color: Color value is invalid color: Color value is invalid
color: Color value is invalid color: Color value is invalid
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hi Team,
We Vayda Overseas (BP# 2000253 / Supplier ID: 100853) are currently involved in direct business with Target.
As per Target’s request, we are now setting up a domestic fill arrangement in US. In this regard, we have created a new entity Vayda US Corp on VMM (US-based, Supplier ID: 2003457).
We would like to have the Po in the name of Vayda US Corp. Kindly advise on the process.
For production, we will continue using our existing factory Vayda Overseas – India (Factory ID: 432187) which will handle the manufacturing (stitching) of pillow covers. These goods will then be shipped to Brentwood – US (Factory ID: 16781588) for filling and final domestic dispatch to Target.
Regards,
Anoop S. Dankash | Sr.Merchandiser | Vayda Overseas
Plot No 149 - 154, Sector - 25, Part 1, Industrial Estate, Huda, Panipat, Haryana 132103
Landline. 0180-2671257 | Ph. No. +91 9910612500 | [www.vayda.in|www.vayda.in]
<a href="https://www.facebook.com/Vayda-Overseas-101640721766500/">https://www.facebook.com/Vayda-Overseas-101640721766500/</a>  <a href="https://www.instagram.com/vaydaoverseas/">https://www.instagram.com/vaydaoverseas/</a>  <a href="https://www.linkedin.com/in/vayda-overseas-65128b1bb/">https://www.linkedin.com/in/vayda-overseas-65128b1bb/</a> <a href="https://in.pinterest.com/VaydaOverseas/_saved/">https://in.pinterest.com/VaydaOverseas/_saved/</a>  <a href="https://twitter.com/Vayda34032764">https://twitter.com/Vayda34032764</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 24/May/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Hi Team,
Kindly revert back on urgent basis to proceed further.
Thanks &amp; Regards,
Arvind Kumar | Divisional Merchandising Manager | Vayda Overseas
Plot No 149-154, 25 Sector Part 1, Industrial Estate, Huda, Panipat, Haryana 132103
Mob No. +91-********** | <a href="http://www.vayda.in">www.vayda.in</a>
&lt;<a href="https://urldefense.com/v3/__https:/www.facebook.com/Vayda-Overseas-101640721766500/__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofZ0">https://urldefense.com/v3/__https:/www.facebook.com/Vayda-Overseas-101640721766500/__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofZ0</a>
5TEiNTKCb1OpmK27-4ofR_kqyuH$&gt;  &lt;<a href="https://urldefense.com/v3/__https:/www.linkedin.com/in/vayda-overseas-65128b1bb/__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOu">https://urldefense.com/v3/__https:/www.linkedin.com/in/vayda-overseas-65128b1bb/__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOu</a>
WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofVC7sg0-$&gt;  &lt;<a href="https://urldefense.com/v3/__https:/twitter.com/Vayda34032764__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKW">https://urldefense.com/v3/__https:/twitter.com/Vayda34032764__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKW</a>
From: Customer Support <a href="mailto:<EMAIL>"><EMAIL></a> Sent: 24 May 2025 04:07 To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-44353 RESOLVED  RE: For domestic fill- Vayda US corp
———-—
Reply above this line.
Hello <a href="mailto:<EMAIL>"><EMAIL></a>,
Your support inquiry has been received. DataTrans Customer Support will be responding to your support issue.
Ticket:  CS-44353 RESOLVED  (<a href="https://help.desk.datatrans-inc.com/servicedesk/customer/portal/1/CS-44353">https://help.desk.datatrans-inc.com/servicedesk/customer/portal/1/CS-44353</a>? token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0Z3QiOiJhbm9ueW1vdXMtbGluayIsInFzaCI6IjI2MGUxZDcwYmE1ZWFkN2Y5MTg1ZDNiMjE4ZGMxMjU3NmEwYzdkZGMzZDk3MjkyODkyYzMyOGVkMDA3YmQ4YjEiLCJp sP4Wq_rzS5C0pOSHAOHN1i6Uyx04n_Wwi6WmQ&amp;sda_source=notification-email)
Status: Waiting for support
It can take up to 1 business day to receive a response.
You may also reach us at ************ for support, if you need immediate assistance.
If you have future inquiries regarding this support issue, please have the above ticket number on hand as a reference.
DataTrans Customer Support <a href="mailto:<EMAIL>"><EMAIL></a> 800-469-0877
View request · Turn off this request&#39;s notifications
Sent on May 24, 2025 4:37:20 AM MDT
Comment by Nicholas Sanchez [ 27/May/25 ]
Hello Arvind,
If you would like to change your company name we can certainly help you with that, however, if you want “Vayda US Corp“ used on ONLY domestic POs, we cannot accommodate your request. If we change your company nam
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Hi Nicholas,
Please advise how can we add Vayda US Corp for domestic fill only so that we can receive PO in this name of Vayda US corp in case of domestic fill request only and rest in the name of Vayda Overseas.
Thanks and regards
Arvind
Comment by Nicholas Sanchez [ 27/May/25 ]
Arvind,
That is not possible in our system.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Hi Nicholas,
So in case of domestic fill how vendor will get PO. How other vendor are doing in this case.
Thanks and regards Arvind
Comment by Nicholas Sanchez [ 27/May/25 ]
Arvind,
Our vendors have a 1:1 relationship with their trading partners, meaning if Target sends a PO to “Vendor 1”, all of their POs will be fulfilled by “Vendor 1”. We cannot set you up with two different vendor names for specific POs.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
In EDI system, is it possible to add a separate account of Vayda US Corp for domestic fill PO only as it is compulsory from 26C1 season as advised by Target.
Thanks and regards Arvind
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Can we register Vayda US Corp as vendor2. Target will raise a specfic PO of domestic fill to Vayda US Corp (vendor2) and import PO to Vayda Overseas (vendor1). It would be 1:1 relation ship with vendor and trading partner se
Thanks and regards Arvind
Comment by Nicholas Sanchez [ 27/May/25 ]
Arvind,
You would have to create a new separate account. You would have 2 accounts Vayda Overseas (vendor1) and Vayda US Corp (vendor2). We will not need to import any POs from one account to the other because the POs will b
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Yes, we need to create a new account in the name of Vayda US corp and no PO will be import from one account to another. Kindly create a second account and advise what information you require for this.
Thanks and regards Arvind
Comment by Nicholas Sanchez [ 27/May/25 ]
Hi Keli,
Should Arvind start the process by registering as a new user?
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Nicholas Sanchez [ 28/May/25 ]
Arvind,
Can you confirm you have two separate supplier numbers assigned to you by Target? One for Vayda Overseas and the other for Vayda US Corp.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Hi Nicholas,
Yes, we have two vendor id as shown in below screen shot from supplier management. Vayda US corp is for domestic fill.
Thanks &amp; Regards,
Arvind Kumar | Divisional Merchandising Manager | Vayda Overseas
Plot No 149-154, 25 Sector Part 1, Industrial Estate, Huda, Panipat, Haryana 132103
Mob No. +91-********** | <a href="http://www.vayda.in">www.vayda.in</a>
&lt;<a href="https://urldefense.com/v3/__https:/www.facebook.com/Vayda-Overseas-101640721766500/__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofZ0">https://urldefense.com/v3/__https:/www.facebook.com/Vayda-Overseas-101640721766500/__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofZ0</a>
5TEiNTKCb1OpmK27-4ofR_kqyuH$&gt;  &lt;<a href="https://urldefense.com/v3/__https:/www.linkedin.com/in/vayda-overseas-65128b1bb/__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOu">https://urldefense.com/v3/__https:/www.linkedin.com/in/vayda-overseas-65128b1bb/__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOu</a>
WBB3LQVRafYQKWumOurwfAjFeI7ucbPr3PP6v7DN8JPUWpyzHgzOuo-5TEiNTKCb1OpmK27-4ofVC7sg0-$&gt;  &lt;<a href="https://urldefense.com/v3/__https:/twitter.com/Vayda34032764__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKW">https://urldefense.com/v3/__https:/twitter.com/Vayda34032764__;!&amp;#33;IfjTnhH9!SB7oB-WBB3LQVRafYQKW</a>
From: Nicholas Sanchez <a href="mailto:<EMAIL>"><EMAIL></a> Sent: 28 May 2025 07:23 To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-44353 RESOLVED  RE: For domestic fill- Vayda US corp
———-—
Reply above this line.
Nicholas Sanchez commented:
Arvind,
Can you confirm you have two separate supplier numbers assigned to you by Target? One for Vayda Overseas and the other for Vayda US Corp.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
View request · Turn off this request&#39;s notifications
Sent on May 28, 2025 7:53:13 AM MDT
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
Hi Arvind,
Please contact our sales team (<a href="mailto:<EMAIL>"><EMAIL></a>) to add a new WebEDI account.
Thanks,
Keli
Keli Bacon
Cleo : Manager, Procurement &amp; Special Projects
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!</li>
</ul>
<h4 id="cs-44470-sftp-host-migration-nucor-for-five-rivers-created-28-may-25-updated-30-may-25-resolved-30-may-25">[CS-44470] SFTP Host Migration - Nucor for Five Rivers Created: 28/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello,
I hope this message finds you well. I am writing to inform you that we will soon be sunsetting our current FTP client and transitioning to a new SFTP client. It appears that you are currently pulling data for Five Rivers vid FTP and we need to migrate you to SFTP as soon as possible. To ensure a smooth migration and avoid any potential service interruptions, I would like to schedule a call with you at your earliest convenience. The deadline to migrate over to our SFTP is 6/7/2025.
Please use the link below to select a suitable time for the call. Should you have any questions or require further clarification, feel free to reach out.
Thank you for your attention to this matter.
<a href="https://calendly.com/nsanchez-datatrans-inc/30min">https://calendly.com/nsanchez-datatrans-inc/30min</a>
Comments
Comment by Nicholas Sanchez [ 30/May/25 ]
Hello Nucor team,
We are in the process of migrating our customers and trading partners to a new SFTP server. It appears you have access to our SFTP server for Five Rivers data. Can you please use the link below to schedule a call with me to change you over to the new client. If you only need the new credentials I can also provide those to you. We will be sunsetting the old FTP server by 6/7. Please let me know if you have any questions.
<a href="https://calendly.com/nsanchez-datatrans-inc/30min">https://calendly.com/nsanchez-datatrans-inc/30min</a>
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Five Rivers currently connects to Nucor’s FTP Server to send &amp; receive EDI. We do not connect to Five Rivers server.
Please confirm if this communication will change and you want Nucor to connect to Five Rivers SFTP server.</p>
<h4 id="cs-44503-fw-trading-partner-review-created-29-may-25-updated-30-may-25-resolved-30-may-25">[CS-44503] Fw: Trading Partner Review Created: 29/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Support,
Please see the highlighted section in the email below.
Could you confirm if we are able to do SKU/MC/Pallet Configurations, multi-sized labels, receive POs, Changes/Cancellations, Build Cartons/Pallets/Truckloads, Send ASN (if needed) and invoice from the EDI Platform?
Thank you,
Andrew Fisher
Cleo : Account Manager
Tel: 281‑292‑8686x205
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
From: Amy Lakanen <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 23, 2025 7:15 AM To: Fisher, Andrew <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: Trading Partner Review CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Good Afternoon Andrew, Thanks for the message. I just added a meeting for next Wednesday with the following items to address -Michaels Craft Stores &amp; Hobby Lobby - SKU/MC/Pallet Configurations, multi-sized labels, ability to receive POs, Changes/Cancellations, Build Cartons/Pallets/Truckloads, Send ASN (if needed) and invoice from EDI Platform. If we could confirm the ability to do the above with the technical team on Wednesday, we would be happy to begin onboarding right away! Thanks, looking forward to getting this rolling as soon as possible! Have a great holiday weekend, Amy ************ x114
From: Fisher, Andrew <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 23, 2025 7:09 AM
To: Amy Lakanen <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: Trading Partner Review
Hi Amy,
Connor informed me that you were trying to contact me. Email is the best way to get in touch with me as my phone sometimes blocks unknown numbers for some reason. My email is <a href="mailto:<EMAIL>"><EMAIL></a>.
When ready, please use my Calendly link to schedule your technical review and I&#39;ll coordinate with the team to join accordingly: Technical Review
Thank you, Andrew
<a href="https://calendly.com/dfisher-cleo/30min">https://calendly.com/dfisher-cleo/30min</a> Quick Meeting - Drew Fisher calendly.com
Andrew Fisher
Cleo : Account Manager
Tel: 281‑292‑8686x205
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
From: Fisher, Andrew <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 7, 2025 11:24 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Trading Partner Review Hi Amy,
We&#39;ve been notified of your request to add additional trading partners and would like to schedule a call to discuss.
Please use the following link to schedule at your convenience: Trading Partner Review
Let me know if anything is needed in the meantime.
Thanks, Andrew
Andrew Fisher
Cleo : Account Manager
Tel: 281‑292‑8686x205
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
Comments
Comment by Sandy Karidas [ 30/May/25 ]
Hello Drew,
We support all the document types, 850, 860, 810, 856 that are required by the trading partner. We also support the UCC-128 label configurations. Documents will be designed to meet the trading partner requirements. We currently have customers set up with both Hobby Lobby and Michaels Stores.
Please let me know if you have any other questions.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-44715-next-level-apparel-supplier-portal-partner-onboarding-created-30-may-25-updated-30-may-25-resolved-30-may-25">[CS-44715] Next Level Apparel Supplier Portal Partner Onboarding Created: 30/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Keli , Web EDI Team ,
Next Level Apparel have signed SOW for Supplier portal. We are working on there Partner onboarding in CIC and we would like to onboard partners in Supplier Portal as well.
Can you please help us on next steps what is required and who can help in setting up the projects and onboard partners.
Thanks,
Atish Kumar
Solution Architect ,Professional Services
<a href="mailto:<EMAIL>"><EMAIL></a>
Comments
Comment by Sandy Karidas [ 30/May/25 ]
Hello Atish,
I would suggest to start with the products team to see if the supplier portal is ready for Next Level Apparel, possibly contact Ankit or Dave B.
Thank you,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-43738-fw-fedex-integrator-ds-12051-created-15-may-25-updated-30-may-25-resolved-30-may-25">[CS-43738] FW: FedEx Integrator DS-12051 Created: 15/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  FedEXIntegrationNewVersion.docx
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: FedEx
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Conner,
I bought the FedEx Integrator through the webEDI app for Purely Bamboo (7635) but the documentation sent is outdated and does not match the current fedEx interface(s). Is there updated documentation for the new API build required at FedEx?
Thanks,
Bernie
Comments
Comment by Sandy Karidas [ 15/May/25 ]
Hello Conner,
We have run into this before where FedEx makes changes to the interface without notification. I will work with our internal team to get the documentation updated.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 29/May/25 ]
Hello Conner,
Due to the FedEx interface change, our development team will need to do a review of the integrator process to determine if changes will need to be made to the integrator and setup guide. Once complete I will provide the new details.
Please contact me if you need additional assistance.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 30/May/25 ]
Hello Bernie,
Attached is the updated documentation for configuring the FedEx Integrator.
Please contact support if you need additional assistance.
Regards,FedEXIntegrationNewVersion.docx
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-44702-label-for-message-44386913-ready-created-30-may-25-updated-30-may-25">[CS-44702] Label for Message 44386913 Ready Created: 30/May/25  Updated: 30/May/25</h4>
<p>Status: Canceled
Project: Customer Support
Components: None
Affects versions: None Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Unresolved Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
The label is available at: <a href="https://www.datatranswebedi.com/webedi34/labels/msg44386913fQWDfL.pdf">https://www.datatranswebedi.com/webedi34/labels/msg44386913fQWDfL.pdf</a></p>
<h4 id="cs-44542-fw-document-processing-error-lippert-components-2025-05-29-16-36-55-created-29-may-25-updated-30-may-25-resolved-30-may-25">[CS-44542] FW: Document Processing Error: Lippert Components 2025-05-29 16:36:55 Created: 29/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified Original estimate: Not Specified
Attachments:  ~WRD1279.jpg      image-20250530-130258.png      image-20250530-130422.png      image-20250530-131140.png      image-20250530-133316.png      image-20250530-133507.png      image-20250530-133452.png
Request Type: Emailed request Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello,
Can you please let me know why the below was rejected, can you please let me know why the IC code continues to pull into the 855 files</li>
</ul>
<hr>
<p>Anne Stanton | Customer Service Representative II
2100 Commerce Drive, Carver, MN 55315
Tel: ******.368.5242
<a href="mailto:<EMAIL>"><EMAIL></a> | <a href="http://www.lakeviewindustries.com">www.lakeviewindustries.com</a>
Office Hours
Monday-Thursday 7:00AM-4:00PM
Friday 7:00AM-2:00PM
LAKEVIEW INDUSTRIES WILL BE CLOSED THE FOLLOWING DAYS:
Friday July 4th
Monday September 1st
Thursday November 27th
Friday November 28th
Thursday December 25th
Friday December 26th
From: SPS Commerce <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 29, 2025 12:00 PM To: <a href="mailto:<EMAIL>"><EMAIL></a>; Jennie E. Winter <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Document Processing Error: Lippert Components 2025-05-29 16:36:55
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
SPS Commerce was unable to successfully process the following document:
Serial Number: PCL60819395880
Document Originator: Lakeview Industries Document Recipient: Lippert Components Document Type: 855
Source Filename: todts<del>SPSCAS2</del>144386452.edi
VendorNumber: 025844 TsetPurposeCode: 00 PurchaseOrderNumber: PO-0002086109
PurchaseOrderNumber:PO-0002086109 Invalid data IC in ItemStatusCode in line 23, column 48. The value &#39;IC&#39; is not one of the possible choices [DR, IA, IB, IP, IQ, IR] in ItemStatusCode
PurchaseOrderNumber:PO-0002086109 Missing element: expect ItemStatusCode before ending LineItemAcknowledgement in line 28,column 35
An error has occurred while processing the document referenced above. If you would like assistance to resolve the error or need additional information, please visit our Support Center and choose one of our convenient contact channels to connect with a support representative.
For complimentary expert training visit <a href="https://trainingcenter.spscommerce.com/">https://trainingcenter.spscommerce.com/</a> and for complimentary support visit [https://www.spscommerce.com/customer-support/support/
https://www.spscommerce.com/customer-support/support/] Kind regards, SPS Customer Operations Team
SPS Commerce Inc. 333 S 7th St #1000, Minneapolis, MN 55402 Having issues? Contact our support team Message Reference ID: SPS-LJQ-GZ-3UP
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Comment by Sandy Karidas [ 30/May/25 ]
855 sent to TP with ACK01 as IC which is not on the specs. Document rejected at SPS for invalid code.
DOM is correct
Customer is sending incorrect code on flat file
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 30/May/25 ]
Hello Anne,
The flat file being received has an invalid Acknowledgement reason code in the 855-l-ACK-STATUS field. Lippert only accepts DR, IA, IB, IP, IQ, and IR. They do not accept IC which what was on the file.
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-44464-michigan-call-17344536258-has-left-you-a-message-25-second-s-long-created-28-may-25-updated-30-may-25-resolved-30-may-25">[CS-44464] MICHIGAN CALL (+17344536258) has left you a message 25 second(s) long Created: 28/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  message.wav
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Dear Voicemail to support : You received a message from MICHIGAN CALL (+17344536258). The message is in mailbox 140, dated Wednesday, May 28, 2025 at 4:18:11 PM. You can get this message via the phone, or use the links below to download the message. Download the message and mark it as read Download the message and delete it Important: This will completely delete the message and this link will no longer work. Thank you!
Terms of Use | Privacy Policy | ©2025 Sangoma All Rights Reserved
<a href="https://www.facebook.com/sangoma">https://www.facebook.com/sangoma</a>  <a href="https://www.twitter.com/sangoma">https://www.twitter.com/sangoma</a>  <a href="https://www.linkedin.com/company/sangoma/">https://www.linkedin.com/company/sangoma/</a>  <a href="https://www.sangoma.com/">https://www.sangoma.com/</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
<em>message.wav  (404 kB)</em>
Comment by Nicholas Sanchez [ 28/May/25 ]
Spoke to Craig, we have a meeting set up for tomorrow to go over SFTP migration.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-44408-tractor-supply-810-item-description-dts-682-created-27-may-25-updated-30-may-25-resolved-30-may-25">[CS-44408] Tractor Supply - 810 Item Description (DTS-682) Created: 27/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Invoice errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Cody,
Thank you for calling into support in regard to your Tractor Supply (TSC) invoices. I have asked our Dev team to allow your TSC invoices to pull the item descriptions from your catalog. I will update you once I have more details. Please let me know if you have any questions.
Comments
Comment by Nicholas Sanchez [ 27/May/25 ]
Hello Cody,
Thank you for calling into support in regard to your Tractor Supply (TSC) invoices. I have asked our Dev team to allow your TSC invoices to pull the item descriptions from your catalog. I will update you once I have more details. Please let me know if you have any questions.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Nicholas Sanchez [ 30/May/25 ]
Hello Cody,
Our dev team completed their task and your item descriptions are now being populated into your TSC invoices. Please let me know if there is anything else I can assist with.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-44380-r-accesso-to-wm-data-trans-edi-created-27-may-25-updated-30-may-25-resolved-30-may-25">[CS-44380] R: Accesso to WM - Data Trans - EDI Created: 27/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png
Request Type: Emailed request
Request language: English Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Olivia,
with data trans we are changing the FTP server in STF,
below the communication from Nicolas
“Thank you for joining our call today. As discussed, we will need to switch you over to our SFTP server by June 7th. This will be a very quick process and will not impact your production data. All we need to do is access your current FTP setup and switch the credentials to the new SFTP server. We will also test the connection to ensure you can receive and send data to/from our server. Please use the link below to reschedule a call with me and Maria. Please let me know if you have any questions or concerns.
<a href="https://calendly.com/nsanchez-datatrans-inc/30min">https://calendly.com/nsanchez-datatrans-inc/30min</a> “
@Nicholas Sanchez do you need more details?
Federico Alvieri
ICT Manager
2A S.p.A.
Address: Via Tetti dell’Oleo, 9, 10071 Borgaro Torinese (TO), Italy
Email: + <a href="mailto:<EMAIL>"><EMAIL></a> +
Tel: +39 011 45 16 499 – Fax: +39 011 45 03 003 – Internet: <a href="http://www.fonderie2a.com">www.fonderie2a.com</a>
This e-mail communication and any attachments may contain confidential and privileged information for the use of the designated recipients named above. Any disclosure, dissemination, distribution or copying of it or its contents is strictly prohibited according to art. 616 c.p., to D.Lgs. n. 196/2003 and also to General Data Protection Regulation (GDPR) UE 679/206.
Da: Olivia Palmerino <a href="mailto:<EMAIL>"><EMAIL></a> Inviato: martedì 27 maggio 2025 07:58 A: Federico Alvieri <a href="mailto:<EMAIL>"><EMAIL></a>; Auburn Technology Solutions, LLC <a href="mailto:<EMAIL>"><EMAIL></a>; Steven Manela <a href="mailto:<EMAIL>"><EMAIL></a> Cc: &#39;Nicolas Sanchez&#39; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Oggetto: Re: Accesso to WM - Data Trans - EDI
Hey Federico!
I am not sure I know what you need from me here exactly?
Yes there is a script running on the abas server as a crib job which downloads the files From DATATRANS FTP server.
Is there a problem I am not aware of?
Thanks
Olivia
Von: Federico Alvieri <a href="mailto:<EMAIL>"><EMAIL></a> Gesendet: Sonntag, Mai 25, 2025 11:48 PM An: Auburn Technology Solutions, LLC <a href="mailto:<EMAIL>"><EMAIL></a>; Olivia Palmerino <a href="mailto:<EMAIL>"><EMAIL></a> Cc: &#39;Nicolas Sanchez&#39; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Betreff: R: Accesso to WM - Data Trans - EDI
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello Nicolas,
I add + @Olivia Palmerino + from abas.
if you have any doubts we can arrange a meeting for Tuesday with Charles, you and Olivia
let me know
thank you
Federico Alvieri
ICT Manager
2A S.p.A.
Address: Via Tetti dell’Oleo, 9, 10071 Borgaro Torinese (TO), Italy
Email: + <a href="mailto:<EMAIL>"><EMAIL></a> +
Tel: +39 011 45 16 499 – Fax: +39 011 45 03 003 – Internet: + <a href="http://www.fonderie2a.com">www.fonderie2a.com</a> +
This e-mail communication and any attachments may contain confidential and privileged information for the use of the designated recipients named above. Any disclosure, dissemination, distribution or copying of it or its contents is strictly prohibited according to art. 616 c.p., to D.Lgs. n. 196/2003 and also to General Data Protection Regulation (GDPR) UE 679/206.
Da: Auburn Technology Solutions, LLC <a href="mailto:<EMAIL>"><EMAIL></a> Inviato: giovedì 22 maggio 2025 22:53 A: Federico Alvieri <a href="mailto:<EMAIL>"><EMAIL></a> Cc: &#39;Nicolas Sanchez&#39; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Oggetto: RE: Accesso to WM - Data Trans - EDI
The local IP is ********** if accessing over the VPN. I can access it through the VM console and logged in as the ABAS user but I do not see where to change this configuration. I also do not have the root password if that is needed. I believe ABAS changed the root password and did not tell me. What I have recorded from when I set the server up is not working.
Since I had no interaction in setting this up initially, I am not exactly sure what to look for. I can access the GUI, so if someone can provide instructions then I can provide further information. Is this something we need to change on the server or is just a port that needs to be opened and forwarded to the server through the firewall?
Charles Sanda
Auburn Technology Solutions, LLC
118 N Ross St, Suite 3
Auburn, AL 36830
Office: ************
Cell: ************
+[ www.au-technology.com |www.au-technology.com] +
From: Federico Alvieri <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 7:53 AM To: Auburn Technology Solutions, LLC <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Nicolas Sanchez <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: R: Accesso to WM - Data Trans - EDI
Hello Charles,
Yes could be, do you have the access^
regards
Federico Alvieri
ICT Manager
2A S.p.A.
Address: Via Tetti dell’Oleo, 9, 10071 Borgaro Torinese (TO), Italy
Email: + <a href="mailto:<EMAIL>"><EMAIL></a> +
Tel: +39 011 45 16 499 – Fax: +39 011 45 03 003 – Internet: + <a href="http://www.fonderie2a.com">www.fonderie2a.com</a> +
This e-mail communication and any attachments may contain confidential and privileged information for the use of the designated recipients named above. Any disclosure, dissemination, distribution or copying of it or its contents is strictly prohibited according to art. 616 c.p., to D.Lgs. n. 196/2003 and also to General Data Protection Regulation (GDPR) UE 679/206.
Da: Auburn Technology Solutions, LLC &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Inviato: mercoledì 21 maggio 2025 14:42 A: Federico Alvieri &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Cc: Nicolas Sanchez &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; + <a href="mailto:<EMAIL>"><EMAIL></a> Oggetto: Re: Accesso to WM - Data Trans - EDI
Did ABAS or Datatrans set this up initially? I assume it is on the ABAS-v2 server. This server is Linux based and I believe ABAS has SSH access to this.
Charles Sanda
Auburn Technology Solutions, LLC 118 N Ross St, Suite 3 Auburn, AL 36830 Office: ************ Cell: ************ + <a href="http://www.au-technology.com">www.au-technology.com</a> +
On May 21, 2025, at 4:30 AM, Federico Alvieri &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; wrote:
Hello Charles
Below the details comunicated by ABAS
we are downloading the EDI import files currently from an FTP server that DATATRANS provided.
This is the server IP address, the FTP folder and the EDI directory on your abas server that we download the files into.
SERVER=&#39;sftp://transfer.datatrans-inc.com&#39;
FTPDIR=&#39;/fromdts&#39;
EDIDIR=/u/abas/erp/dfue/empfangen
Federico Alvieri
ICT Manager
2A S.p.A.
Address: Via Tetti dell’Oleo, 9, 10071 Borgaro Torinese (TO), Italy
Email: + <a href="mailto:<EMAIL>"><EMAIL></a> +
Tel: +39 011 45 16 499 – Fax: +39 011 45 03 003 – Internet: + <a href="http://www.fonderie2a.com">www.fonderie2a.com</a> +
This e-mail communication and any attachments may contain confidential and privileged information for the use of the designated recipients named above. Any disclosure, dissemination, distribution or copying of it or its contents is strictly prohibited according to art. 616 c.p., to D.Lgs. n. 196/2003 and also to General Data Protection Regulation (GDPR) UE 679/206.
Da: Federico Alvieri Inviato: martedì 20 maggio 2025 16:54 A: Auburn Technology Solutions, LLC &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Cc: &#39;Nicolas Sanchez&#39; &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; + <a href="mailto:<EMAIL>"><EMAIL></a> Oggetto: R: Accesso to WM - Data Trans - EDI
Ok let me check
Federico Alvieri
ICT Manager
2A S.p.A.
Address: Via Tetti dell’Oleo, 9, 10071 Borgaro Torinese (TO), Italy
Email: + <a href="mailto:<EMAIL>"><EMAIL></a> +
Tel: +39 011 45 16 499 – Fax: +39 011 45 03 003 – Internet: + <a href="http://www.fonderie2a.com">www.fonderie2a.com</a> +
This e-mail communication and any attachments may contain confidential and privileged information for the use of the designated recipients named above. Any disclosure, dissemination, distribution or copying of it or its contents is strictly prohibited according to art. 616 c.p., to D.Lgs. n. 196/2003 and also to General Data Protection Regulation (GDPR) UE 679/206.
Da: Auburn Technology Solutions, LLC &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Inviato: martedì 20 maggio 2025 16:42 A: Federico Alvieri &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Cc: &#39;Nicolas Sanchez&#39; &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; + <a href="mailto:<EMAIL>"><EMAIL></a> Oggetto: RE: Accesso to WM - Data Trans - EDI
Or if you can provide me the program name or service name, I can find which server it is running on.
Thank you.
Charles Sanda
Auburn Technology Solutions, LLC
118 N Ross St, Suite 3
Auburn, AL 36830
Office: ************
Cell: ************
+[ www.au-technology.com |www.au-technology.com] +
&lt;image001.png&gt;
From: Federico Alvieri &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Sent: Tuesday, May 20, 2025 9:20 AM To: Auburn Technology Solutions, LLC &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Cc: Nicolas Sanchez &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; + <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Accesso to WM - Data Trans - EDI
Hello Charles,
I’m with Data Trans (EDI provider), could you provide the accesso to data trans VM? We have to switch from FTP to SFTP
Please let me know
Is quite urgent
Regards
Federico Alvieri
ICT Manager
2A S.p.A.
Address: Via Tetti dell’Oleo, 9, 10071 Borgaro Torinese (TO), Italy
Email: + <a href="mailto:<EMAIL>"><EMAIL></a> +
Tel: +39 011 45 16 499 – Fax: +39 011 45 03 003 – Internet: + <a href="http://www.fonderie2a.com">www.fonderie2a.com</a> +
This e-mail communication and any attachments may contain confidential and privileged information for the use of the designated recipients named above. Any disclosure, dissemination, distribution or copying of it or its contents is strictly prohibited according to art. 616 c.p., to D.Lgs. n. 196/2003 and also to General Data Protection Regulation (GDPR) UE 679/206.
: Auburn Technology Solutions, LLC &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Inviato: martedì 6 maggio 2025 18:41 A: Federico Alvieri &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Cc: Stefania Cajumi &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Oggetto: Re: Access to Abas VM
**********
User ABAS
Password 2aUSAabas
Charles Sanda
Auburn Technology Solutions, LLC 118 N Ross St, Suite 3 Auburn, AL 36830 Office: ************ Cell: ************ + <a href="http://www.au-technology.com">www.au-technology.com</a> +
On May 6, 2025, at 11:43 AM, Federico Alvieri &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; wrote:
Windows machine
Federico Alvieri
ICT Manager
2A S.p.A.
Address: Via Tetti dell’Oleo, 9, 10071 Borgaro Torinese (TO), Italy
From: Auburn Technology Solutions, LLC &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Tuesday, May 6, 2025 5:13:46 PM To: Federico Alvieri &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Cc: Stefania Cajumi &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Subject: Re: Access to Abas VM
The actual ABAS server or just the windows virtual machine that has access to the software?
Charles Sanda
Auburn Technology Solutions, LLC 118 N Ross St, Suite 3 Auburn, AL 36830 Office: ************ Cell: ************ + <a href="http://www.au-technology.com">www.au-technology.com</a> +
On May 6, 2025, at 10:11 AM, Federico Alvieri &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; wrote:
Hello Charles,
just to clarify,
I need the access to remote desktop
Please let me know
regards
Federico Alvieri
ICT Manager
2A S.p.A.
Address: Via Tetti dell’Oleo, 9, 10071 Borgaro Torinese (TO), Italy
Email: + <a href="mailto:<EMAIL>"><EMAIL></a> +
Tel: +39 011 45 16 499 – Fax: +39 011 45 03 003 – Internet: + <a href="http://www.fonderie2a.com">www.fonderie2a.com</a> +
This e-mail communication and any attachments may contain confidential and privileged information for the use of the designated recipients named above. Any disclosure, dissemination, distribution or copying of it or its contents is strictly prohibited according to art. 616 c.p., to D.Lgs. n. 196/2003 and also to General Data Protection Regulation (GDPR) UE 679/206.
Da: Federico Alvieri Inviato: lunedì 5 maggio 2025 15:28 A: Charles Sanda &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Cc: Stefania Cajumi &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Oggetto: Access to Abas VM
Hello Charles,
coudl you give me the access to vm of ABAS?
I need also the ip in order to enter
Please let me know
regards
Federico Alvieri
ICT Manager
2A S.p.A.
Address: Via Tetti dell’Oleo, 9, 10071 Borgaro Torinese (TO), Italy
Email: + <a href="mailto:<EMAIL>"><EMAIL></a> +
Tel: +39 011 45 16 499 – Fax: +39 011 45 03 003 – Internet: + <a href="http://www.fonderie2a.com">www.fonderie2a.com</a> +
This e-mail communication and any attachments may contain confidential and privileged information for the use of the designated recipients named above. Any disclosure, dissemination, distribution or copying of it or its contents is strictly prohibited according to art. 616 c.p., to D.Lgs. n. 196/2003 and also to General Data Protection Regulation (GDPR) UE 679/206.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Comment by Olivia Palmerino [ 27/May/25 ]
Ok, understood.
But are we not using SFTP credentials already?
We are using SERVER=&#39;sftp://transfer.datatrans-inc.com&#39;
I don&#39;t have the password handy at the moment but I think we don&#39;t want to send it via email anyway.
So if that SFTP server mentioned above is going to be replaced on June 7th with another one, then please send us the new SFTP credentials and we can make the changes in the abas script and run some tests. Or I can have one of our Linux techs set up a call with whoever is in charge at DATATRANS to implement and test these changes together during a Teams meeting before June 7th. Just let me know how you want to proceed here please.
Thanks, Olivia
Comment by Nicholas Sanchez [ 27/May/25 ]
Hello Olivia and Federico,
Since you are currently using transfer.datatrans-inc.com, that tells me you are already connected to our new SFTP server and are also using the updated credentials. Nothing else needs to be done on your part and thank you for your assistance. Please let me know if you have any questions.</p>
<h4 id="cs-44343-re-cs-44294-deere-no-asn-s-created-23-may-25-updated-30-may-25-resolved-30-may-25">[CS-44343] Re: CS-44294 Deere no ASN&#39;s Created: 23/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: ASN questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
I understand that, but they are all claiming no ASN received on their side.
Josh
From: Nicholas Sanchez <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 23, 2025 3:50:01 PM
To: Josh Fuller <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-44294 RESOLVED  Deere no ASN&#39;s
———-— Reply above this line.
Nicholas Sanchez commented:
Hello,
All of the ASNs that were sent to John Deere 5/22 show an accepted status in your Sent folder. This is only possible if John Deere’s system send us back a functional acknowledgment file (EDI 997) that indicate John Deere accepted the ASNs you sent. Please let me know if there is anything else I can assist with.
image-20250523-204706.png
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
View request · Turn off this request&#39;s notifications Sent on May 23, 2025 2:49:58 PM MDT <strong>This email originated outside of TYRI. Do not click on links or attachments unless you recognize the sender and know the content is safe.</strong>
Comments
Comment by Nicholas Sanchez [ 27/May/25 ]
Hello,
If John Deere’s system is responding to our system with Accepted acknowledgments for the ASNs, that means your ASNs are being received. There is little I can do on our side of the process since we are sending the ASNs accordingly and these are being accepted.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-44321-fw-cleo-communications-datatrans-created-23-may-25-updated-30-may-25-resolved-30-may-25">[CS-44321] Fw: Cleo Communications/DataTrans Created: 23/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      Outlook-msvw43rl.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Cleo, We need an accounting contact at Cleo that we can reach out to so we can make the needed payment changes on our side. We have some outstanding invoices that we need to process.
Kayla, This will go to the tech team, hopefully they have a contact phone number for the billing dept.
Thanks folks
From: Kayla Houle <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 23, 2025 2:17 PM To: Bud Philbrick <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Cleo Communications/DataTrans
Hi Bud,
Just following up on this. We do have some past due invoices that we have not been able to voucher or pay.
Thank you,
Kayla Houle | Accounting Specialist
Marmon Utility LLC
53 Old Wilton Rd, Milford, NH 03055 Direct: ************ | <a href="mailto:<EMAIL>"><EMAIL></a>
From: Kayla Houle Sent: Thursday, May 15, 2025 1:19 PM To: Bud Philbrick <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Cleo Communications/DataTrans
Hello,
With the acquisition of DataTrans, do you have a new contact with Cleo Communications? I have been calling the phone numbers on their website, but their directory does not work, and I have not been able to reach a person.
Thank you,
Kayla Houle | Accounting Specialist
Marmon Utility LLC
53 Old Wilton Rd, Milford, NH 03055 Direct: ************ | <a href="mailto:<EMAIL>"><EMAIL></a>
NOTICE: This e-mail message is for the sole use of the intended recipient(s) and may contain CONFIDENTIAL, ATTORNEY-CLIENT, WORK PRODUCT or other LEGALLY PRIVILEGED information. Any unauthorized review, use, disclosure or distribution of any kind is strictly prohibited. If you are not the intended recipient, please contact the sender via reply e-mail and destroy all copies of the original message. Thank you.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/May/25 ]
Comment by Nicholas Sanchez [ 27/May/25 ]
Hello Phil,
Please reach out to our accounting team at <a href="mailto:<EMAIL>"><EMAIL></a>.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-44295-re-cs-44294-deere-no-asn-s-created-23-may-25-updated-30-may-25-resolved-30-may-25">[CS-44295] RE: CS-44294 Deere no ASN&#39;s Created: 23/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.jpg      RE__C_F_5_22_Orders.eml
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: ASN questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Attached is history</p>
<ul>
<li>Josh Fuller _________________________________________________________ TYRI Lights | I N T E L L I G E N T L I G H T I N G S O L U T I O N S
From: Jira Service Management Widget <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 23, 2025 7:44 AM To: Josh Fuller <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-44294 RESOLVED  Deere no ASN&#39;s
———-—
Reply above this line.
Hello <a href="mailto:<EMAIL>"><EMAIL></a>,
Your support inquiry has been received. DataTrans Customer Support will be responding to your support issue.
Ticket:  CS-44294 RESOLVED  (<a href="https://help.desk.datatrans-inc.com/servicedesk/customer/portal/1/CS-44294">https://help.desk.datatrans-inc.com/servicedesk/customer/portal/1/CS-44294</a>? token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0Z3QiOiJhbm9ueW1vdXMtbGluayIsInFzaCI6IjQ4ZTI4OTM1ZGU3OTYxMTNhNDdlYTkwZWVmZDk3NDQ1YTUyMzU4OWFkNzIzM2I1YzE2ZjEzMTAxMzU4NTEyMzYiLCJp email)
Status: Waiting for support
It can take up to 1 business day to receive a response.
You may also reach us at ************ for support, if you need immediate assistance.
If you have future inquiries regarding this support issue, please have the above ticket number on hand as a reference.
DataTrans Customer Support <a href="mailto:<EMAIL>"><EMAIL></a> 800-469-0877
View request · Turn off this request&#39;s notifications
Sent on May 23, 2025 6:43:38 AM MDT
<strong>This email originated outside of TYRI. Do not click on links or attachments unless you recognize the sender and know the content is safe.</strong>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/May/25 ]
<em>RE__C_F_5_22_Orders.eml  (934 kB)</em></li>
</ul>
<h4 id="cs-44294-deere-no-asn-s-created-23-may-25-updated-30-may-25-resolved-30-may-25">[CS-44294] Deere no ASN&#39;s Created: 23/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250523-204706.png
Request Type: Support
Request language: English
Request participants: None
Organizations: Label: ASN questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Deere said they got no ASN&#39;s we sent yesterday
Comments
Comment by Nicholas Sanchez [ 23/May/25 ]
Hello,
All of the ASNs that were sent to John Deere 5/22 show an accepted status in your Sent folder. This is only possible if John Deere’s system send us back a functional acknowledgment file (EDI 997) that indicate John Deere acce
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-44159-re-invoice-automation-results-created-21-may-25-updated-30-may-25-resolved-30-may-25">[CS-44159] Re: Invoice Automation Results Created: 21/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Jeremy King Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-n1vzbuvd.png      Outlook-https___ww.png      Outlook-Facebook.png      Outlook-Linkedin.png      Outlook-Google.png      image-20250521-222146.png     image-20250521-222129.png      image-20250521-222320.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: SFTP/FTP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
@<a href="mailto:<EMAIL>"><EMAIL></a> can you please resend the files below. The original files were empty.
Can you also please review and provide information on why this keeps happening?
MDH_INV_50803_TP_20250520.csv
MDH_INV_50804_TP_20250520.csv
Thank you,
From: Maureen Saliba <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 21, 2025 9:11 AM To: EmpowerRM Support <a href="mailto:<EMAIL>"><EMAIL></a>; Matthew Kornberg <a href="mailto:<EMAIL>"><EMAIL></a>; Jeremy King <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: Invoice Automation Results
Good morning Jeremy -
Can you advise on these 2 bad files?</li>
</ul>
<h4 id="bad-files">Bad Files</h4>
<p>Process Date File Name File Path
5/20/2025 11:54:00 PM MDH_INV_50803_TP_20250520.csv C:\Invoice Automation\Ascent\BadFiles\MDH_INV_50803_TP_20250520.csv
5/20/2025 11:54:00 PM MDH_INV_50804_TP_20250520.csv C:\Invoice Automation\Ascent\BadFiles\MDH_INV_50804_TP_20250520.csv
Thank you,
Maureen Saliba Operations Manager 105 Baylis Road, Melville, NY 11747 p: 631-755-1155 e: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="http://www.ascentconsumerproducts.com">www.ascentconsumerproducts.com</a>
<em>Wire Fraud is Real. Before wiring ANY money, call the intended recipient at a number you know is valid to confirm the instructions.</em>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 20, 2025 11:55 PM To: Maureen Saliba <a href="mailto:<EMAIL>"><EMAIL></a>; Matthew Kornberg <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Invoice Automation Results</p>
<h4 id="ascent">ASCENT</h4>
<p>Invoice No. Import Date File Name Status
9383143162 5/20/2025 11:54:00 PM MDH_INV_50807_TP_20250520.csv Status OK
9537548267 5/20/2025 11:54:00 PM MDH_INV_50812_TP_20250520.csv Status OK
8633103566 5/20/2025 11:54:00 PM MDH_INV_50799_TP_20250520.csv Status OK
6882708553 5/20/2025 11:54:00 PM MDH_INV_50810_TP_20250520.csv Status OK
3683450904 5/20/2025 11:54:00 PM MDH_INV_50800_TP_20250520.csv Status OK
5833480380 5/20/2025 11:54:00 PM MDH_INV_50805_TP_20250520.csv Status OK
2983361815 5/20/2025 11:54:00 PM MDH_INV_50801_TP_20250520.csv Status OK
1083401639 5/20/2025 11:54:00 PM MDH_INV_50806_TP_20250520.csv Status OK
4133411290 5/20/2025 11:54:00 PM MDH_INV_50809_TP_20250520.csv Status OK
3032836921 5/20/2025 11:54:00 PM MDH_INV_50811_TP_20250520.csv Status OK
9483411088 5/20/2025 11:54:00 PM MDH_INV_50798_TP_20250520.csv Status OK
2933441064 5/20/2025 11:54:00 PM MDH_INV_50808_TP_20250520.csv Status OK
5433411408 5/20/2025 11:54:00 PM MDH_INV_50802_TP_20250520.csv Status OK</p>
<h4 id="bad-files-2">Bad Files</h4>
<p>Process Date File Name File Path
5/20/2025 11:54:00 PM MDH_INV_50803_TP_20250520.csv C:\Invoice Automation\Ascent\BadFiles\MDH_INV_50803_TP_20250520.csv
5/20/2025 11:54:00 PM MDH_INV_50804_TP_20250520.csv C:\Invoice Automation\Ascent\BadFiles\MDH_INV_50804_TP_20250520.csv
Comments
Comment by Jeremy King [ 21/May/25 ]
Comment by Nicholas Sanchez [ 21/May/25 ]
Files Restaged:
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Nicholas Sanchez [ 21/May/25 ]
Hello Jeremy,
I have restaged the files and they are ready to be picked up. I will ask our team to look into the cause of the empty files. Please let me know if there is anything else I can assist with.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-44049-fw-edi-maps-moved-to-production-gwd_media_llc-v64312-created-20-may-25-updated-30-may-25-resolved-30-may-25">[CS-44049] FW: EDI Maps Moved to Production GWD_MEDIA_LLC V64312 Created: 20/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image002.png      image003.png      image-20250520-203523.png      image-20250520-203901.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello,
Can you advise where I can open the PO listed below? I’m finalizing testing with Ace Hardware for the labels and I can’t seem to locate that PO they sent over.
Thanks
From: GS1128compliance <a href="mailto:<EMAIL>"><EMAIL></a> Date: Tuesday, May 13, 2025 at 6:01 PM To: Maciek Zurawski <a href="mailto:<EMAIL>"><EMAIL></a>, GS1128compliance <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: EDI Maps Moved to Production GWD_MEDIA_LLC V64312
Hello,
It is not. The PO# listed is not a valid PO#.
If this is a mapping issue, we send the PO# in BEG03 of the EDI 850.
Please revise / resubmit a new sample.
I still need the samples of raw ASN data I requested as well.
Thank you
Scott Stimetz, CLTD | Ace Hardware Corporation | Supply Chain Vendor Improvement Analyst Lead
2915 Jorie Blvd. Oak Brook, IL 60523
email: <a href="mailto:<EMAIL>"><EMAIL></a>| phone: ************
PLEASE REPLY ALL
Please Include [ <a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>] on all emails
Ace Hardware’s GS1-128 Label Specifications can be found at:
<a href="https://www.acehardware-vendors.com/Vendors%20Public/Documents/GS1-128-Pallet-and-Carton-Label-Specifications.pdf">https://www.acehardware-vendors.com/Vendors%20Public/Documents/GS1-128-Pallet-and-Carton-Label-Specifications.pdf</a>
Ace Hardware’s 856 ASN Map can be found at:
<a href="https://www.acehardware-vendors.com/Vendors%20Public/Documents/856Map.pdf">https://www.acehardware-vendors.com/Vendors%20Public/Documents/856Map.pdf</a>
From: Maciek Zurawski <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 13, 2025 1:53 PM To: GS1128compliance <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: EDI Maps Moved to Production GWD_MEDIA_LLC V64312
Hi Team,
Our EDI system is up and running. Can you please review the attached GS1-128 label and let me know if everything is in order?
Thanks,
Maciek
From: GS1128compliance <a href="mailto:<EMAIL>"><EMAIL></a> Date: Thursday, March 6, 2025 at 3:33 PM To: GS1128compliance &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;, Maciek Zurawski &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: EDI Maps Moved to Production GWD_MEDIA_LLC V64312
Hello,
Now that you have completed the structural testing / onboarding for the EDI 856 ASN, we need to address both the GS1-128 label &amp; the ASNs accuracy / timeliness as well:
Ace’s expectation is the ASN is sent accurately the first time and sent when the load leaves your dock. We use the ASNs to receive with / the GS1-128 labels do not work without them being accurate.
As a general summary, Ace requires:
For all orders that are shipped palletized (LTL/TL shipments) to Ace RSC’s, Ace requires GS1-128 tare/pallet level data/labels (designated as “HLT”/tare level in the MAN segment of the ASN).
In other words, Ace requires one unique GS1-128 label per pallet. No carton level GS1-128 data/labels are needed nor desired. For all small parcel shipments (i.e. FedEx, UPS) to Ace RSC’s, Ace requires GS1-128 pack/carton level data/labels (designated with “HLP”/pack level in the MAN segment of the ASN).
In other words, Ace requires one unique GS1-128 label should be applied to each carton.
<em>Never send both Pallet level and Carton level ASN data for the same ASN line or it will be rejected by our system</em> <em><em>Ensure you follow the Ace Routing Guide’s “Preferred SCAC List” when applicable
Also Note: please review the Inbound Load Quality Requirements and let me know if you have any questions on the GS1-128 requirements.
Please ensure you review the Traverse Help Portal &amp; Ace Vendor Portal for additional helpful information
There is a link to our Gs1-128 label specifications in my email signature. These are also available for all vendors to pull from our vendor portal. There is a link to our ASN map in my email signature as well – which is also available for all vendors to pull from our vendor portal.
For validation on GS1-128, I need 3 things:
<em>1 sample GS1-128 label, formatted as a PDF attachment file. I will be validating that all required info is populating on the label &amp; that the barcodes are formatted per GS1 standards &amp; scannable 1 sample of raw ASN data for a palletized load, formatted as a .txt file ( you should have tested the ASNs for structure with our EDI team but I just like to double check the content is accurate) 1 sample of raw ASN data for a parcel load, formatted as a .txt file</em>
Please let me know if you have any questions.
Thank you
Scott Stimetz, CLTD | Ace Hardware Corporation | Supply Chain Vendor Improvement Analyst Lead
2915 Jorie Blvd. Oak Brook, IL 60523
email: <a href="mailto:<EMAIL>"><EMAIL></a>| phone: ************
PLEASE REPLY ALL
Please Include [ <a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>] on all emails
Ace Hardware’s GS1-128 Label Specifications can be found at:
<a href="https://www.acehardware-vendors.com/Vendors%20Public/Documents/GS1-128-Pallet-and-Carton-Label-Specifications.pdf">https://www.acehardware-vendors.com/Vendors%20Public/Documents/GS1-128-Pallet-and-Carton-Label-Specifications.pdf</a>
Ace Hardware’s 856 ASN Map can be found at:
<a href="https://www.acehardware-vendors.com/Vendors%20Public/Documents/856Map.pdf">https://www.acehardware-vendors.com/Vendors%20Public/Documents/856Map.pdf</a>
From: Simeonova, Persiana <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, March 6, 2025 3:21 PM To: VendorCompliance <a href="mailto:<EMAIL>"><EMAIL></a>; GS1128compliance <a href="mailto:<EMAIL>"><EMAIL></a>; Maciek Zurawski <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Purcell, Pat <a href="mailto:<EMAIL>"><EMAIL></a>; Thomas, Kathy <a href="mailto:<EMAIL>"><EMAIL></a>; Vasilevski, Sonya <a href="mailto:<EMAIL>"><EMAIL></a>; Adams, Ryan <a href="mailto:<EMAIL>"><EMAIL></a>; Ryan, Brett <a href="mailto:<EMAIL>"><EMAIL></a>; Freund, Emily <a href="mailto:<EMAIL>"><EMAIL></a>; Schielke, Colleen <a href="mailto:<EMAIL>"><EMAIL></a>; Spaseski, Jagoda <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: EDI Maps Moved to Production GWD_MEDIA_LLC V64312
Dear Ace Hardware Trading Partner:
I moved your 810, 812, 820, 850, 856, 864 and 997 maps into PRODUCTION today. Please change from Ace Hardware Test Qualifier/ID - ZZ/7089906600 to our Production Qualifier/ID - 01/006928311 and change the ISA15 record from ‘T’ to ’P’.
We are also reminding our Vendors once an 850 file is received; Ace requires you to send back a detailed 997 (functional acknowledgement) file within 24 – 48 hours. This file must include the following mandatory segments: AK1, AK2, AK5 and AK9.
If you have not done so, please contact <a href="mailto:<EMAIL>"><EMAIL></a> to work with them on your label requirements.
Please let me know if you have any questions.
Regards,
Persiana Simeonova
Production Svcs Analyst II | Information Technology
Ace Hardware Corporation | 2915 Jorie Blvd. Oak Brook, IL 60523</em> Office: ************ | Cell: ************</em> | Email: + <a href="mailto:<EMAIL>"><EMAIL></a> +
CAUTION: This email originated from outside of the corporation. Do not click links or open attachments unless you recognize the sender.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 20/May/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 20/May/25 ]
Also, how can create a .txt file with the raw ASN data? One of my partners is looking for a sample.
Thanks
Comment by Nicholas Sanchez [ 20/May/25 ]
Hello Maciek,
It seems the POs are in your Archive folder. You can unarchive them to put them back in your inbox.
The ASNs are in your Draft folder so these were never sent to Ace.
If you need the raw data, you can export the ASN “Message as EDI File” then change the file from .edi to .txt.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ul>
<h4 id="cs-44445-re-missing-pos-phoenix-leather-goods-llc-created-28-may-25-updated-30-may-25-resolved-30-may-25">[CS-44445] RE: Missing POs / PHOENIX LEATHER GOODS LLC Created: 28/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  ~WRD0002.jpg      image001.png      image-20250528-165439.png      image-20250528-165602.png      image-20250528-170000.png      PO2.png      PO1.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Brad,
We could see both the POs 8042105 and 8043700 were transmitted successfully.
Thanks &amp; regards,
Shanthi
EDI Analyst | Macy&#39;s, Inc.
<em>145 Progress Place | Springdale, OH 4524</em>6
From: Brad Rusin <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 28, 2025 12:36 AM To: EDISupport <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Antonio Martinez <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Missing POs
⚠ EXT MSG:
Hello Macy&#39;s support. I&#39;m not sure if this email address has remained CCed to the case we have open with Datatrans/Cleo. The email header is  CS-44299 RESOLVED  . We are missing several Bloomingdale&#39;s POs in Datatrans. Datatrans claims to have not received them on their server. They are asking for an MDN. Is that something you can provide? The latest response from Datatrans support is below.
Thank you for any information you can provide.
Hello Antonio,
Our transmission protocol used to connect with Bloomingdales is via AS2. An MDN (Message Disposition Notification) is an encrypted file we send to your system for the AS2 message we received. It appears the screenshots you provided are of the software you use to send those POs but unfortunately they do not show if an MDN was received on your end indicating your transmission was successful. We still do not see POs 8042105 and 8043700 in our system. The last POs we received were 7926220 and 7934833 on 5/21 in one batch transmission (EDI Control Number: 000000326).
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a>
| Brad Rusin Vice President, Ecommerce <a href="mailto:<EMAIL>"><EMAIL></a> : ************ Phoenix Leather Goods : <a href="http://www.phoenixleathergoods.com">www.phoenixleathergoods.com</a> |</p>
<ul>
<li>This is an EXTERNAL EMAIL. Stop and think before clicking a link or opening attachments.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Comment by Nicholas Sanchez [ 28/May/25 ]
POs finally received in ECS03
8042105 8043700
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Nicholas Sanchez [ 28/May/25 ]
Hello Brad and Shanthi,
I am not sure what changed on this transmission but did receive the POs this today. Thank you for your help Shanthi. Brad, please let me know if there is anything else I can assist with.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Brad Rusin [ 28/May/25 ]
Hi, Shanthi.
I am now seeing them in Datatrans. However, these POs were originally submitted on May 21, then resubmitted on May 23.
Datatrans Support, what happened to the original transmissions? Why did the re-sends take 5 days to show up in Datatrans?
Antonio and/or Macy&#39;s Support, was anything done differently when the POs were re-sent?
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Hi all,
Jumping in here to confirm that nothing was changed in the way we have been sending through POs for replenishment on our end.
Thanks!
Antonio Martinez | Senior Assistant Buyer | Men’s Tailored Clothing &amp; Dress Furnishings
Bloomingdale’s | 28-07 Jackson Avenue 19th FL, Long Island City, NY 11101
Comment by Brad Rusin [ 29/May/25 ]
Thanks, Antonio.
Have you sent this week&#39;s POs?
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
@Brad Rusin – POs were just sent today, they should show within the next few days in your system.
Antonio Martinez | Senior Assistant Buyer | Men’s Tailored Clothing &amp; Dress Furnishings
Bloomingdale’s | 28-07 Jackson Avenue 19th FL, Long Island City, NY 11101
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
POs were transmitted this afternoon/ you should be able to see them in your system within the next couple of days!
Antonio Martinez | Senior Assistant Buyer | Men’s Tailored Clothing &amp; Dress Furnishings
Bloomingdale’s | 28-07 Jackson Avenue 19th FL, Long Island City, NY 11101
Comment by Brad Rusin [ 30/May/25 ]
Hello, Antonio.
PO&#39;s 8164271 and 8160905 were received last night.
Are there anymore outstanding POs that we haven&#39;t seen yet?
Thank you.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Hi Brad,
Confirming these are the only two POs that were sent this week! Each week you should only receive two POs max – one for COM and one for stores.
Thanks,
Antonio Martinez | Senior Assistant Buyer | Men’s Tailored Clothing &amp; Dress Furnishings
Bloomingdale’s | 28-07 Jackson Avenue 19th FL, Long Island City, NY 11101
Comment by Tom Mintal [ 30/May/25 ]
Antonio, thanks for confirming that information. Have a great weekend, Tom-
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Of course, hope you have a lovely weekend as well!
Antonio Martinez | Senior Assistant Buyer | Men’s Tailored Clothing &amp; Dress Furnishings
Bloomingdale’s | 28-07 Jackson Avenue 19th FL, Long Island City, NY 11101</li>
</ul>
<h4 id="cs-44440-edi-change-ftp-to-sftp-gsrx-car020-***********-0-gsrx-created-28-may-25-updated-30-may-25-resolved-30-may-25">[CS-44440] EDI Change; FTP to SFTP || GSRX-CAR020-***********-0-GSRX Created: 28/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified Original estimate: Not Specified
Attachments:  image001.gif      image002.gif      image003.gif      image004.gif      image005.gif      image006.gif      image007.jpg
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: SFTP migration
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good morning Kaitlynn and Nic!
Could either of you inform me further on the DataTrans changes that need to be made for Cardinal?
Thank you in advance and have a wonderful day!
Cheers,
Darian Pennick : Cloud Analyst
Global Shop Solutions : 975 Evergreen Circle : The Woodlands, TX 77380, USA <a href="https://www.globalshopsolutions.com">https://www.globalshopsolutions.com</a> Phone: (713)589-3052
<a href="https://www.facebook.com/globalshopsolutions/">https://www.facebook.com/globalshopsolutions/</a>  <a href="https://twitter.com/globalshoperp">https://twitter.com/globalshoperp</a>  <a href="https://www.youtube.com/channel/UCDhHbvXXqJLC2R-hcbfxurg">https://www.youtube.com/channel/UCDhHbvXXqJLC2R-hcbfxurg</a>  <a href="https://www.linkedin.com/company/global-shop-solutions/">https://www.linkedin.com/company/global-shop-solutions/</a>  <a href="https://www.instagram.com/globalshopsolutions">https://www.instagram.com/globalshopsolutions</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Good morning Darian,
I think they are changing the URL and possibly the sign on information Darian. Nicholas might be able to provide us more information.
Thank you &amp; Have a nice day!
We will be CLOSED Monday, May 26 in observance of the Memorial Day Holiday. We will resume our normal scheduled work hours and operations on Tuesday, May 27. We wish you all a safe and relaxing long weekend!
Office Hours: 6am to 2:30pm EST
Please consider the environment before printing this page.
PRIVACY NOTICE:
<em>The information contained in this transmission is confidential, proprietary or privileged and may be subject to protection under the law, including the Health Insurance Portability and Accountability Act (HIPAA). The message is intended for the sole use of the individual or entity to which it is addressed. If you are not the intended recipient, you are notified that any use, distribution or copying of the message is strictly prohibited and may subject you to criminal or civil penalties. If you received this transmission in error, please contact the sender immediately by replying to this email and delete the material from any computer.</em>
From: Darian Pennick <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 28, 2025 10:13 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Nicholas Sanchez <a href="mailto:<EMAIL>"><EMAIL></a> Subject: EDI Change; FTP to SFTP || GSRX-CAR020-***********-0-GSRX
Good morning Kaitlynn and Nic!
Could either of you inform me further on the DataTrans changes that need to be made for Cardinal?
Thank you in advance and have a wonderful day!
Cheers,
Darian Pennick : Cloud Analyst
Global Shop Solutions : 975 Evergreen Circle : The Woodlands, TX 77380, USA <a href="https://www.globalshopsolutions.com">https://www.globalshopsolutions.com</a> Phone: (713)589-3052
<a href="https://www.facebook.com/globalshopsolutions/">https://www.facebook.com/globalshopsolutions/</a>  <a href="https://twitter.com/globalshoperp">https://twitter.com/globalshoperp</a>  <a href="https://www.youtube.com/channel/UCDhHbvXXqJLC2R-hcbfxurg">https://www.youtube.com/channel/UCDhHbvXXqJLC2R-hcbfxurg</a>  <a href="https://www.linkedin.com/company/global-shop-solutions/">https://www.linkedin.com/company/global-shop-solutions/</a>  <a href="https://www.instagram.com/globalshopsolutions">https://www.instagram.com/globalshopsolutions</a>
Comment by Nicholas Sanchez [ 28/May/25 ]
Hello Darian,
We are migrating her to the SFTP server. Do you want the credentials or would you rather meet for this one?
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Good morning Nic!
I’d like to meet for this one today if you’re available sir!
Comment by Nicholas Sanchez [ 29/May/25 ]
Good Morning Darian,
Sounds good. I do have availability today. You can certainly schedule a call using this link <a href="https://calendly.com/nsanchez-datatrans-inc/30min">https://calendly.com/nsanchez-datatrans-inc/30min</a>
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Nicholas Sanchez [ 29/May/25 ]
Hello Kaitlynn,
Darian and I have successfully migrated you over to our new SFTP server today. Please let me know if you have any questions.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Okay, thank you for the update. I will let you know if anyone on our team has any issues or questions.
Thank you &amp; Have a nice day!
We will be CLOSED Monday, May 26 in observance of the Memorial Day Holiday. We will resume our normal scheduled work hours and operations on Tuesday, May 27. We wish you all a safe and relaxing long weekend!
Office Hours: 6am to 2:30pm EST
Please consider the environment before printing this page.
PRIVACY NOTICE:
<em>The information contained in this transmission is confidential, proprietary or privileged and may be subject to protection under the law, including the Health Insurance Portability and Accountability Act (HIPAA). The message is intended for the sole use of the individual or entity to which it is addressed. If you are not the intended recipient, you are notified that any use, distribution or copying of the message is strictly prohibited and may subject you to criminal or civil penalties. If you received this transmission in error, please contact the sender immediately by replying to this email and delete the material from any computer.</em>
From: Nicholas Sanchez <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 29, 2025 9:45 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-44440 RESOLVED  EDI Change; FTP to SFTP || GSRX-CAR020-***********-0-GSRX
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Great, thank you!
Thank you &amp; Have a nice day!
Office Hours: 6am to 2:30pm EST
Please consider the environment before printing this page.
PRIVACY NOTICE:
<em>The information contained in this transmission is confidential, proprietary or privileged and may be subject to protection under the law, including the Health Insurance Portability and Accountability Act (HIPAA). The message is intended for the sole use of the individual or entity to which it is addressed. If you are not the intended recipient, you are notified that any use, distribution or copying of the message is strictly prohibited and may subject you to criminal or civil penalties. If you received this transmission in error, please contact the sender immediately by replying to this email and delete the material from any computer.</em>
From: Nicholas Sanchez <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 30, 2025 9:46 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-44440 RESOLVED  EDI Change; FTP to SFTP || GSRX-CAR020-***********-0-GSRX</p>
<h4 id="cs-43963-fw-invoice-254784-vid-168595-ez-groom-created-19-may-25-updated-30-may-25-resolved-28-may-25">[CS-43963] Fw: Invoice# 254784. VID# 168595 , EZ GROOM. Created: 19/May/25  Updated: 30/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image002.png      image003.png      image.png      image-20250523-150717.png      image-20250523-152536.png      image-20250523-152733.png      image-20250523-152233.png      image-20250523-153753.png      image-20250523-161234.png      image-20250528-145247.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi LorenData Support Team,
We have a customer ZZ:DTS6237 sending 810s to trading partner 12:**********
The issue appears that there is (1) Invoice that keeps sending to 12:********** daily. Invoice Number: 254784
We have looked on our end and I am NOT seeing where we are sending the data to you on a daily basis. This invoice was ONLY sent from us one time on 04/01/2025.
Can you please see why Invoice 254784 keeps sending to the trading partner?
Here is a snippet of just the past couple of days: This has been happening since 04/01/2025 - This duplicate invoice needs to be removed from the queue and stop sending to 12:**********
Holly Criswell
Cleo : Senior Implementation Engineer
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
From: Cindy Mitskavich Sent: Thursday, May 8, 2025 7:21 AM To: Holly Criswell Cc: Wendi Williams Subject: FW: Invoice# 254784. VID# 168595 , EZ GROOM.
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hi Holly,
We received the email below from Petco and Wendi asked me to reach out to you for help. I do not understand what happened. This invoice was one of 1300 that was auto loaded into Data Trans on 4/1. Why only this one ? This invoice was in the middle of the batch.
Thank you for your assistance with this matter.
Cindy
From: AP Disputes/Resolution <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 07, 2025 5:49 PM To: Cindy Mitskavich <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Mylene Ortiz <a href="mailto:<EMAIL>"><EMAIL></a>; Senthilkannan Thiruppathyraj <a href="mailto:<EMAIL>"><EMAIL></a>; Chilambarasan Chinnadurai <a href="mailto:<EMAIL>"><EMAIL></a>; Shouvik Das <a href="mailto:<EMAIL>"><EMAIL></a>; Syed Azhar Ullah Quadri <a href="mailto:<EMAIL>"><EMAIL></a>; AP Disputes/Resolution <a href="mailto:<EMAIL>"><EMAIL></a>; EDI 810 Invoice Mailbox <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Invoice# 254784. VID# 168595 , EZ GROOM.
Hi Cindy,
Please be informed that the invoice# 254784, was initially sent to us on 04/01/25 and the same was processed in our system. However, the same file is being sent to us every day as tabulated below since then.
This is creating lot of issues at our end and generating rejection logs each day once the file hits to us server. If your system is set to auto mode, can you please have it changed to cease the files being sent to us immediately ?
Status Date/time Type Sender Sender name
Receiver Receiver name
Reference number
GS Sender
GS Receiver
Source filename
Destination filename
Success 05/05/2025 17:00:25
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90e5dda3.edi ED168595.200007269200007239254784.P6
Success 05/04/2025 17:00:21
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90d186c4.edi ED168595.200007269200007239254784.P6
Success 05/03/2025 17:00:21
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90c9a690.edi ED168595.200007269200007239254784.P6
Success 05/02/2025 17:00:29
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90be66a3.edi ED168595.200007269200007239254784.P6
Success 05/01/2025 17:00:23 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90aa30bb.edi ED168595.200007269200007239254784.P6
Success 04/30/2025 17:00:30 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 9094d809.edi ED168595.200007269200007239254784.P6
Success 04/29/2025 17:00:50 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 907e835d.edi ED168595.200007269200007239254784.P6
Success 04/28/2025 17:00:44 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 906806bc.edi ED168595.200007269200007239254784.P6
Success 04/27/2025 17:00:22 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 9053b63a.edi ED168595.200007269200007239254784.P6
Success 04/26/2025 17:01:36
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 904c142e.edi ED168595.200007269200007239254784.P6
Success 04/25/2025 17:00:19
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 9040ed76.edi ED168595.200007269200007239254784.P6
Success 04/24/2025 17:00:25
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 902cfff7.edi ED168595.200007269200007239254784.P6
Success 04/23/2025 17:00:30
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 9017fc3e.edi ED168595.200007269200007239254784.P6
Success 04/22/2025 17:00:59
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90021472.edi ED168595.200007269200007239254784.P6
Success 04/21/2025 17:00:29
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fec2718.edi ED168595.200007269200007239254784.P6
Success 04/20/2025 17:00:26 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fd94dd8.edi ED168595.200007269200007239254784.P6
Success 04/19/2025 17:00:28 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fd2777a.edi ED168595.200007269200007239254784.P6
Success 04/18/2025 17:00:40 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fc8caef.edi ED168595.200007269200007239254784.P6
Success 04/17/2025 17:00:29 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fb73961.edi ED168595.200007269200007239254784.P6
Success 04/16/2025 17:03:28 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fa1ce76.edi ED168595.200007269200007239254784.P6
Success 04/15/2025 17:00:31
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f8ae8a1.edi ED168595.200007269200007239254784.P6
Success 04/14/2025 17:00:49 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f742f5d.edi ED168595.200007269200007239254784.P6
Success 04/13/2025 17:00:21 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f5f9a30.edi ED168595.200007269200007239254784.P6
Success 04/12/2025 17:00:15 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f57657d.edi ED168595.200007269200007239254784.P6
Success 04/11/2025 17:00:34 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f4bf94c.edi ED168595.200007269200007239254784.P6
Success 04/10/2025 17:00:23 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f378d76.edi ED168595.200007269200007239254784.P6
Success 04/09/2025 17:00:22 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f2233d4.edi ED168595.200007269200007239254784.P6
Success 04/08/2025 17:00:40
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f0b39e8.edi ED168595.200007269200007239254784.P6
Success 04/07/2025 17:00:26
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8ef4892c.edi ED168595.200007269200007239254784.P6
Success 04/06/2025 17:00:21
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8ee02e15.edi ED168595.200007269200007239254784.P6
Success 04/05/2025 17:00:22
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8ed83e0a.edi ED168595.200007269200007239254784.P6
Success 04/04/2025 17:00:26
810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8ecd174f.edi ED168595.200007269200007239254784.P6
Success 04/03/2025 17:00:34 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8eb87296.edi ED168595.200007269200007239254784.P6
Success 04/02/2025 17:00:16 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8ea31dc8.edi ED168595.200007269200007239254784.P6
Success 04/01/2025 17:00:30 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8e8cbac7.edi ED168595.200007269200007239254784.P6
Regards,
Victor : Accounts Payable Team: PETCO : t - : <a href="mailto:<EMAIL>"><EMAIL></a>
From: EDI 810 Invoice Mailbox &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Wednesday, May 7, 2025 1:47 AM To: AP Disputes/Resolution &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Cc: Mylene Ortiz &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; EDI 810 Invoice Mailbox &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Senthilkannan Thiruppathyraj &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Chilambarasan Chinnadurai &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Shouvik Das &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Syed Azhar Ullah Quadri &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Subject: Invoice# 254784. VID# 168595 , EZ GROOM.
Hi AP Team,
Can you please forward the below email to respective vendor contact CC’ing us, as we do not have those details available with us now ?
Hi EZ Groom,
Please be informed that the invoice# 254784, was initially sent to us on 04/01/25 and the same was processed in our system. However, the same file is being sent to us every day as tabulated below since then.
This is creating lot of issues at our end and generating rejection logs each day once the file hits to us server. If your system is set to auto mode, can you please have it changed to cease the files being sent to us immediately ?
Status Date/time Type Sender Sender name Receiver Receiver name Reference number GS Sender GS Receiver Source filename Destination filename
Success 05/05/2025 17:00:25 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90e5dda3.edi ED168595.200007269200007239254784.P6
Success 05/04/2025 17:00:21 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90d186c4.edi ED168595.200007269200007239254784.P6
Success 05/03/2025 17:00:21 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90c9a690.edi ED168595.200007269200007239254784.P6
Success 05/02/2025 17:00:29 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90be66a3.edi ED168595.200007269200007239254784.P6
Success 05/01/2025 17:00:23 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90aa30bb.edi ED168595.200007269200007239254784.P6
Success 04/30/2025 17:00:30 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 9094d809.edi ED168595.200007269200007239254784.P6
Success 04/29/2025 17:00:50 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 907e835d.edi ED168595.200007269200007239254784.P6
Success 04/28/2025 17:00:44 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 906806bc.edi ED168595.200007269200007239254784.P6
Success 04/27/2025 17:00:22 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 9053b63a.edi ED168595.200007269200007239254784.P6
Success 04/26/2025 17:01:36 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 904c142e.edi ED168595.200007269200007239254784.P6
Success 04/25/2025 17:00:19 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 9040ed76.edi ED168595.200007269200007239254784.P6
Success 04/24/2025 17:00:25 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 902cfff7.edi ED168595.200007269200007239254784.P6
Success 04/23/2025 17:00:30 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 9017fc3e.edi ED168595.200007269200007239254784.P6
Success 04/22/2025 17:00:59 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 90021472.edi ED168595.200007269200007239254784.P6
Success 04/21/2025 17:00:29 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fec2718.edi ED168595.200007269200007239254784.P6
Success 04/20/2025 17:00:26 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fd94dd8.edi ED168595.200007269200007239254784.P6
Success 04/19/2025 17:00:28 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fd2777a.edi ED168595.200007269200007239254784.P6
Success 04/18/2025 17:00:40 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fc8caef.edi ED168595.200007269200007239254784.P6
Success 04/17/2025 17:00:29 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fb73961.edi ED168595.200007269200007239254784.P6
Success 04/16/2025 17:03:28 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8fa1ce76.edi ED168595.200007269200007239254784.P6
Success 04/15/2025 17:00:31 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f8ae8a1.edi ED168595.200007269200007239254784.P6
Success 04/14/2025 17:00:49 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f742f5d.edi ED168595.200007269200007239254784.P6
Success 04/13/2025 17:00:21 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f5f9a30.edi ED168595.200007269200007239254784.P6
Success 04/12/2025 17:00:15 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f57657d.edi ED168595.200007269200007239254784.P6
Success 04/11/2025 17:00:34 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f4bf94c.edi ED168595.200007269200007239254784.P6
Success 04/10/2025 17:00:23 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f378d76.edi ED168595.200007269200007239254784.P6
Success 04/09/2025 17:00:22 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f2233d4.edi ED168595.200007269200007239254784.P6
Success 04/08/2025 17:00:40 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8f0b39e8.edi ED168595.200007269200007239254784.P6
Success 04/07/2025 17:00:26 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8ef4892c.edi ED168595.200007269200007239254784.P6
Success 04/06/2025 17:00:21 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8ee02e15.edi ED168595.200007269200007239254784.P6
Success 04/05/2025 17:00:22 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8ed83e0a.edi ED168595.200007269200007239254784.P6
Success 04/04/2025 17:00:26 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8ecd174f.edi ED168595.200007269200007239254784.P6
Success 04/03/2025 17:00:34 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8eb87296.edi ED168595.200007269200007239254784.P6
Success 04/02/2025 17:00:16 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8ea31dc8.edi ED168595.200007269200007239254784.P6
Success 04/01/2025 17:00:30 810 DTS6237 EZ Groom ********** PETCO 254784 DTS6237 ********** 8e8cbac7.edi ED168595.200007269200007239254784.P6
Thanks and Regards,
Syed Azhar Ullah Quadri
EDI Support | <a href="mailto:<EMAIL>"><EMAIL></a> |
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 19/May/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 19/May/25 ]
Dear DataTrans Support,
Your ticket has been submitted and will be followed up by a member of the support team. We will be reviewing your ticket and will follow up as quickly as possible. We strive to respond to customer inquiries within 4 hours, but no
Your ticket number is 276114.
Thank you for using Loren Data services and we look forward to addressing your inquiry quickly.
Sincerely, ECGrid Support Team 276114:250746
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 20/May/25 ]
Hi Holly,
I am investigating if there is something on our side that is causing this looping send!
As soon as I have additional information, I will provide an update!
Thank you!
Ticket: 276113
Jon Auclair, Customer Support Analyst <a href="mailto:<EMAIL>"><EMAIL></a> P: +1.813.426.3355
On Mon, May 19 at 12:31 PM , Criswell, Holly <a href="mailto:<EMAIL>"><EMAIL></a> wrote: Hi LorenData Support Team,
We have a customer ZZ:DTS6237 sending 810s to trading partner 12:**********
The issue appears that there is (1) Invoice that keeps sending to 12:********** daily. Invoice Number: 254784
We have looked on our end and I am NOT seeing where we are sending the data to you on a daily basis. This invoice was ONLY sent from us one time on 04/01/2025.
Can you please see why Invoice 254784 keeps sending to the trading partner?
Here is a snippet of just the past couple of days: This has been happening since 04/01/2025 - This duplicate invoice needs to be removed from the queue and stop sending to 12:**********
Holly Criswell
Cleo : Senior Implementation Engineer
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
Hi Holly,
Upon checking our system, there is nothing we are doing to trigger this sending every day. Our system is receiving the file daily and sending it out. It appears to be triggering from the sending ID.
Please let me know if you have any questions!
Regards,
Ticket: 276113
Jon Auclair, Customer Support Analyst <a href="mailto:<EMAIL>"><EMAIL></a> P: +1.813.426.3355
On Tue, May 20 at 11:38 AM , Jon Auclair <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Hi Holly,
I am investigating if there is something on our side that is causing this looping send!
As soon as I have additional information, I will provide an update!
Thank you!
Ticket: 276113
Jon Auclair, Customer Support Analyst <a href="mailto:<EMAIL>"><EMAIL></a> P: +1.813.426.3355
On Mon, May 19 at 12:31 PM , Criswell, Holly <a href="mailto:<EMAIL>"><EMAIL></a> wrote: Hi LorenData Support Team,
We have a customer ZZ:DTS6237 sending 810s to trading partner 12:**********
The issue appears that there is (1) Invoice that keeps sending to 12:********** daily. Invoice Number: 254784
We have looked on our end and I am NOT seeing where we are sending the data to you on a daily basis. This invoice was ONLY sent from us one time on 04/01/2025.
Can you please see why Invoice 254784 keeps sending to the trading partner?
Here is a snippet of just the past couple of days: This has been happening since 04/01/2025 - This duplicate invoice needs to be removed from the queue and stop sending to 12:**********
Holly Criswell
Cleo : Senior Implementation Engineer
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
Hi Jon,
Thank you for checking. Are you able to kill this. I am not seeing that we are sending this file?
Holly Criswell
Cleo : Senior Implementation Engineer
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
Hello,
Thank you for reaching out to our ECGrid Support team. Unfortunately, you have replied to a ticket that is already closed and cannot be reopened. This is typically caused by replying to a previous email thread.
To create a new ticket just send a new email to <a href="mailto:<EMAIL>"><EMAIL></a> with a new subject line. If this is not a new issue, please reference the previous ticket number for background information.
Thank you,
ECGrid Customer Support 276114:250746
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Dear DataTrans Support,
Our support team has indicated that your ticket has been Resolved, you can review the status of this ticket via our support portal - <a href="https://support.ecgrid.com/helpdesk/tickets/276114">https://support.ecgrid.com/helpdesk/tickets/276114</a>.
If there is no response from you, we will assume that the ticket has been resolved and the ticket will be automatically closed after 5 days.
Thank you!
Loren Data Corp.Support Team
Please tell us what you think of your support experience.
Customer Support was Excellent Customer Support was Average Customer Support was Not Good
276114:250746
Comment by EDI Analyst [ 23/May/25 ]
Hi Nicholas Sanchez can you please assist as my second eyes to see if we are sending the same invoice 254784 to LorenData to pass to PETCO. According to the LorenData portal, the same invoice was sent again on 05.22.25
LorenData ticket 276113 reported that they are receiving this invoice from us to pass to PETCO; however, I do not see that we are sending it.
But for some reason, there is a loop going on with just this ONE invoice.
Comment by Nicholas Sanchez [ 23/May/25 ]
EDI Analyst
Hi Holly,
This one is weird.
As you already know, invoice 254784 was sent to Petco via Loren Data through ECS 02 using file name “133163529.edi”
This is using date range 4/1 - 5/23
It seems Loren Data continues to process the SAME FILE over and over, and over… Loren Data Message ID 1191062680 2025-04-01 8:24:00 AM (-05:00)
Loren Data processing the file again 5/22 7:PM Loren Data Message ID 1219676958 2025-04-01 8:24:00 AM (-05:00)
As you also already know, the Target 0 - VAN - Out - Loren Data (directory D:\VanLorenData\fromdts) removes the file once it is sent so I&#39;m not sure what is really happening.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by EDI Analyst [ 27/May/25 ]
Hi Nicholas Sanchez thank you so much for looking at this with me.
Could you please create a dev ticket to ask for further investigation from our end?
Here is the latest response from LorenData
Hi Holly,
My apologies, I forgot to send the email that said I was having my team lead look into this.
We can confirm that the file is not originating from us (thus meaning we don&#39;t have a way to stop it from sending sadly).
In our system, even though it&#39;s the same file, we are receiving new, unique mailbag numbers for each day&#39;s transaction.  We&#39;re also seeing the files come in around the same time every day (midnight UTC).  However, the ORIGI
The most likely culprit here is the file is stuck somewhere in the COMM channel.  The recommendation is to check the FTP script.
Please let me know if you have any questions!
Regards,
Ticket: 276113
Jon Auclair, Customer Support Analyst Comment by Nicholas Sanchez [ 27/May/25 ]
EDI Analyst <a href="https://dtsprojects.atlassian.net/browse/DTS-683">https://dtsprojects.atlassian.net/browse/DTS-683</a>
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Nicholas Sanchez [ 28/May/25 ]
EDI Analyst HI Holly,
Kris found several files, including our EZ Groom invoice, that were stuck in ecs-02: D:\VanLorenData\stage\fromdts. He deleted them which will prevent them from being sent again.
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by EDI Analyst [ 28/May/25 ]
Thank you, Nicholas Sanchez for taking care of this. Now that this has been completed and the file has been removed to stop looping, I am closing this ticket. I have also notified the customer that this has been resolved.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Hello,
Thank you for reaching out to our ECGrid Support team. Unfortunately, you have replied to a ticket that is already closed and cannot be reopened. This is typically caused by replying to a previous email thread.
To create a new ticket just send a new email to <a href="mailto:<EMAIL>"><EMAIL></a> with a new subject line. If this is not a new issue, please reference the previous ticket number for background information.
Thank you,
ECGrid Customer Support 276114:250746
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Dear DataTrans Support,
Our support team has indicated that your ticket has been Resolved, you can review the status of this ticket via our support portal - <a href="https://support.ecgrid.com/helpdesk/tickets/276114">https://support.ecgrid.com/helpdesk/tickets/276114</a>.
If there is no response from you, we will assume that the ticket has been resolved and the ticket will be automatically closed after 5 days.
Thank you!
Loren Data Corp.Support Team
Please tell us what you think of your support experience.
Customer Support was Excellent Customer Support was Average Customer Support was Not Good
276114:250746</p>
<h4 id="cs-44505-re-ecgrid-interconnect-request-new-ulta-beauty-mexi-intreq-513934-1-6589944017-ticket-277080-created-29-may-25-updated-29-may-25">[CS-44505] Re: ECGrid Interconnect Request: NEW - Ulta Beauty Mexi (IntReq#513934) | 1-6589944017 - Ticket 277080 Created: 29/May/25  Updated: 29/May/25</h4>
<p>Status: Canceled
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Unresolved Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  LD Authorization Letter Template.doc
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Inovis, Thank you for your assistance!
Hi DataTrans,
Good day , as confirmed by Inovis, the qualifier ZZ<em>DTS4493 is currently pointing to a direct DataTrans connection.
To proceed with the migration to Loren Data, please complete and return the attached authorization letter.
Best regards,
Ticket: 277080
Jocelin Lindholm , Team Lead- EDI Customer Support * * <a href="mailto:<EMAIL>"><EMAIL></a> P: +1.813.426.3355 * * <a href="http://www.ecgrid.com">www.ecgrid.com</a>
PTO: Holidays:
On Mon, May 26 at 1:12 PM , Inovis <a href="mailto:<EMAIL>"><EMAIL></a> wrote: Hi Team, Good afternoon! Please be advised that the ID ZZ</em>DTS4493 currently points to Datatrans on our end. If they are your customer now, please send a separate email to migrate the ID. Thanks! Richelle Rojero
Thanks Website: <a href="http://www.opentext.com">www.opentext.com</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> To: ; Cc: Sent: 5/26/2025 05:35:05 AM Subject: Inovis Support:
The following is a new ID to be added to the Loren Data/ECGrid Interconnect. Please make sure that interchanges sent to this QID are forwarded to ECGrid and confirm that it is now in place.
Name: Symphony Beauty Box QID: ZZ*DTS4493
Note: User requested: datatrans-solutions.
Thank you for your assistance.
ECGrid Reference: IntReq-513934 Customer Reference: Ulta Beauty Mexi
Regards,
ECGrid Support ECGrid NetOps Loren Data Corp. <a href="mailto:<EMAIL>"><EMAIL></a> +1-813-426-3355 Opt 1
ECGridOS v4.1 (Build 478) (214.van)
[THREAD ID:1-30ZSYMX]
277080:250746
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
<em>LD Authorization Letter Template.doc  (35 kB)</em>
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Dear DataTrans Support,
Your ticket has been submitted and will be followed up by a member of the support team. We will be reviewing your ticket and will follow up as quickly as possible. We strive to respond to customer inquiries within 4 hours, but non-urgent inquiries may take up to 24 hours.
Your ticket number is 277399.
Thank you for using Loren Data services and we look forward to addressing your inquiry quickly.
Sincerely, ECGrid Support Team 277399:250746
Comment by Sandy Karidas [ 29/May/25 ]
Marianne working on
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-44218-fw-zf-lifetec-important-edi-changes-action-required-was-zf-passive-safety-system-us-tor-created-22-may-25-updated-29-may-25-resolved-29-may-25">[CS-44218] FW: ZF Lifetec – Important EDI changes – ACTION Required / WAS ZF PASSIVE SAFETY SYSTEM US -TOR Created: 22/May/25  Updated: 29/May/25  Resolved: 29/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      AxwayCloud_CommunicationSheet_Lifetec_March2025_S.xlsx      ZF Lifetec EDI Communication March 2025_S.docx     AxwayCloud_CommunicationSheet_Lifetec_March2025_S (46e1b98a-9a71-4514-801a-e34ec3e67690).xlsx      ZF Lifetec EDI Communication March 2025_S (57a1efea-3620-4a25-b5f4-303cc1b76d07).docx
Request Type: Emailed request
Request language: English Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello
Note that ZF PASSIVE SAFETY SYSTEM US -TOR has changes, please see the attachments and advise asap how soon changes can be made.
GREYSTONE HOLIDAY SCHEDULE
MAY 26TH – CLOSED
JUNE 19TH – CLOSED
JULY 4TH – CLOSED
AUGUST 11TH – CLOSED
SEPTEMBER 1ST – CLOSED
OCTOBER 13TH – CLOSED
NOVEMBER 11TH – CLOSED
NOVEMBER 27TH – CLOSED
DECEMBER 25TH - CLOSED
Thanks,
Marie Gauvin | Customer Service Representative
Greystone of Lincoln|W (401)-333-0444 ext.218 |C (401)-651-9124 |F (401)-334-5745
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Sabrina Muellener ALF RFX <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 19, 2025 3:40 AM To: Marie Gauvin <a href="mailto:<EMAIL>"><EMAIL></a> Subject: ZF Lifetec – Important EDI changes – ACTION Required
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello EDI Partner,
ZF Lifetec is moving to a new EDI platform, provided by Axway. Details are provided in the attached Word document.
Your response is REQUIRED by May 23, 2025. Lack of response will result in disconnection of EDI which would be non-compliant with our expectations of you as a supplier.
Also attached is an Excel sheet with parameters for the connection to Axway.
Please read the attached information and reply with your completed parameter sheet as soon as possible. Forward this email to the correct IT/ EDI person in your company to complete if needed.
Once your reply is received, Axway will contact you to establish the connection. Include your supplier code, &lt; 140311&gt;, in your completed parameter sheet.
After the connection is established, Axway will identify the related supplier codes to be moved to the new connection and they will work on scheduling the change.
Thank you for your prompt cooperation,
ZF Lifetec
Mit freundlichen Grüßen / Kind regards Sabrina Muellener
Information Technology - Communications
ZF LIFETEC
Industriestrasse 20
D-73553 Alfdorf / Deutschland/Germany
E-Mail: <a href="mailto:<EMAIL>"><EMAIL></a>
Invoice address: ZF Automotive Germany GmbH, Industriestrasse 20, D-73553 Alfdorf
Vorsitzender des Aufsichtsrats/Chairman of the Supervisory Board: Holly Swanson Geschäftsführer/Managing Directors: Eduard Bausch, Dirk Schultz,
Handelsregistereintrag ZF Automotive Germany GmbH, Amtsgericht Stuttgart HRB 282093/Trade register of ZF Automotive Germany GmbH, the municipal court of Stuttgart HRB 282093
Informationen zur Verarbeitung Ihrer Daten und zu Ihren Rechten erhalten Sie jederzeit in unseren Datenschutzhinweisen:
<a href="https://www.zf.com/de/data-protection-notice">https://www.zf.com/de/data-protection-notice</a> You can find information about how we process your data and your rights in our data protection notice:
<a href="https://www.zf.com/en/data-protection-notice">https://www.zf.com/en/data-protection-notice</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
<em>AxwayCloud_CommunicationSheet_Lifetec_March2025_S.xlsx  (77 kB)</em>
<em>ZF Lifetec EDI Communication March 2025_S.docx  (36 kB)</em>
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Dear Axway-Team,
see attached the feedback from supplier 140311.
Mit freundlichen Grüßen / Kind regards Sabrina Muellener
Information Technology - Communications
ZF LIFETEC
Industriestrasse 20
D-73553 Alfdorf / Deutschland/Germany
E-Mail: <a href="mailto:<EMAIL>"><EMAIL></a>
Invoice address: ZF Automotive Germany GmbH, Industriestrasse 20, D-73553 Alfdorf
Vorsitzender des Aufsichtsrats/Chairman of the Supervisory Board: Holly Swanson Geschäftsführer/Managing Directors: Eduard Bausch, Dirk Schultz,
Handelsregistereintrag ZF Automotive Germany GmbH, Amtsgericht Stuttgart HRB 282093/Trade register of ZF Automotive Germany GmbH, the municipal court of Stuttgart HRB 282093
Informationen zur Verarbeitung Ihrer Daten und zu Ihren Rechten erhalten Sie jederzeit in unseren Datenschutzhinweisen:
<a href="https://www.zf.com/de/data-protection-notice">https://www.zf.com/de/data-protection-notice</a> You can find information about how we process your data and your rights in our data protection notice:
<a href="https://www.zf.com/en/data-protection-notice">https://www.zf.com/en/data-protection-notice</a>
<em>AxwayCloud_CommunicationSheet_Lifetec_March2025_S (46e1b98a-9a71-4514-801a-e34ec3e67690).xlsx  (77 kB)</em>
<em>ZF Lifetec EDI Communication March 2025_S (57a1efea-3620-4a25-b5f4-303cc1b76d07).docx  (36 kB)</em>
Comment by Sandy Karidas [ 28/May/25 ]
project was created for UMP and changes for connection made
All ISA ID’s have been updated to new ones.
Doesn’t appear any additional action is needed
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2 Comment by Sandy Karidas [ 29/May/25 ]
Hello Marie,
The map updates are in progress for the EDI migration to Axway. I will let you know once they are completed.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 29/May/25 ]
Hello Marie,
All changes are complete and in production for your account.
Please contact me if you need additional assistance.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-44416-7665-sherpa-business-technology-sa-de-cv-question-created-27-may-25-updated-29-may-25-resolved-29-may-25">[CS-44416] (7665) SHERPA BUSINESS TECHNOLOGY SA DE CV - question Created: 27/May/25  Updated: 29/May/25  Resolved: 29/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-012912.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello support team,
We are trying to work with Penske in our EDI integration, and they’re requesting the following:</li>
</ul>
<ol>
<li>Please complete the Penske EDI Profile form with your Prod Support contacts and ISA/GS IDs you’ll use, we need it done in order to request our internal setups and generate/process your test files. 2. Initially we will exchange test files by e-mail and later in the integration process we will provide you with a link to the sFTP setup form or let us know if you are able to use VAN to exchange documents with us. 3. The transactions that will be exchanged are:
<em>EDI 204(Original 00, Cancel 01, Replace 05) EDI 214( X3,AF,AG,X6,X1,D1). EDI 824 (Application advise if your statuses are rejected) Not in Scope: 997/210/990</em></li>
<li>Penske&#39;s Sender/Receiver ID&#39;s are as follows:
TEST = ZZ:PENSKELEAR T PROD = ZZ:PENSKELEAR</li>
<li>We would like to use specific filenames for transactions:
LEAR_OUT_204/824_SPBY_YYYYMMDDhhmmSS sss.edi PENSKE_LEAR_SPBY_214_YYYYMMDDhhmmSS sss.edi
Please add 2-3 milliseconds. If not possible, anyuniquesequence or ID your system can generate. E.g. ISA Ctrl#.
However, it seems that we require an EDI Enveloper. I’ve checked our account, and I cannot find the section to do this. Here’s the support page I found with information on this process:
<a href="https://support.cleo.com/hc/en-us/articles/************-Creating-and-defining-EDI-Envelopers">https://support.cleo.com/hc/en-us/articles/************-Creating-and-defining-EDI-Envelopers</a>
But I’m unable to find that in our dashboard. Do we have to pay extra to have it, add a new vendor, or something similar?
Thanks,
-Ali Darwich
SPBY
Get Outlook for Mac
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]</li>
</ol>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello Team, can you help us please? Otherwise, can you let us know what’s standard scheduled time to respond to an inquiry?
Thank you,
Fernando
Non-PC
On May 27, 2025, at 4:42 pm, Ali Darwich <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Hello support team,
We are trying to work with Penske in our EDI integration, and they’re requesting the following:</li>
</ul>
<ol>
<li>Please complete the Penske EDI Profile form with your Prod Support contacts and ISA/GS IDs you’ll use, we need it done in order to request our internal setups and generate/process your test files. 2. Initially we will exchange test files by e-mail and later in the integration process we will provide you with a link to the sFTP setup form or let us know if you are able to use VAN to exchange documents with us. 3. The transactions that will be exchanged are:
<em>EDI 204(Original 00, Cancel 01, Replace 05) EDI 214( X3,AF,AG,X6,X1,D1). EDI 824 (Application advise if your statuses are rejected) Not in Scope: 997/210/990</em></li>
<li>Penske&#39;s Sender/Receiver ID&#39;s are as follows:
TEST = ZZ:PENSKELEAR T PROD = ZZ:PENSKELEAR</li>
<li>We would like to use specific filenames for transactions:
LEAR_OUT_204/824_SPBY_YYYYMMDDhhmmSS sss.edi PENSKE_LEAR_SPBY_214_YYYYMMDDhhmmSS sss.edi
Please add 2-3 milliseconds. If not possible, anyuniquesequence or ID your system can generate. E.g. ISA Ctrl#.
However, it seems that we require an EDI Enveloper. I’ve checked our account, and I cannot find the section to do this. Here’s the support page I found with information on this process:
<a href="https://support.cleo.com/hc/en-us/articles/************-Creating-and-defining-EDI-Envelopers">https://support.cleo.com/hc/en-us/articles/************-Creating-and-defining-EDI-Envelopers</a>
But I’m unable to find that in our dashboard. Do we have to pay extra to have it, add a new vendor, or something similar?
Thanks,
-Ali Darwich
SPBY
Get Outlook for Mac
Comment by Sandy Karidas [ 29/May/25 ]
Hello Ali,
Have you completed the Add Trading Partner form to open a project to add Penske to your account? Once that is opened an analyst will be assigned. Please follow the instructions below.
To add a new trading partner:</li>
<li>Sign onto the WebEDI site at <a href="http://datatrans-inc.com/login">http://datatrans-inc.com/login</a> 2. From the menu at the top click on &#34;Add Trading Partner.&#34; (Screen shot below) 3. Select your trading partner from the drop down list. 4. The contact information is not needed, but the vendor number will be helpful. 5. Click on the Send button.
Once we receive the setup form, an associate will be assigned to the project. That associate will then work with you and the trading partner on both the EDI requirements and any EDI testing.
For any documents you may want mapped, the project analyst assigned will be able to provide you an additional quote.
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Hi Sandy,
Thanks for your reply.
We couldn’t find the vendor in the list (Penske) but someone else from the Cleo team is already helping us and we’ve paid the mapping fee as well.
Thanks,
-Ali Darwich
Get Outlook for Mac</li>
</ol>
<h4 id="cs-44534-re-clar-core-po-7207688-created-29-may-25-updated-29-may-25-resolved-29-may-25">[CS-44534] Re: Clar Core PO# 7207688 Created: 29/May/25  Updated: 29/May/25  Resolved: 29/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.png      image004.png      image005.png      image006.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Amit,
I can see the map initiated twice which caused the order to be delivered twice. I will monitor the files to ensure future files do not experience the same map issue.
Thank you,
Sandy Karidas
Cleo : Support Manager
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
From: Amit Das <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 28, 2025 2:10 PM To: Karidas, Sandy <a href="mailto:<EMAIL>"><EMAIL></a>; Support <a href="mailto:<EMAIL>"><EMAIL></a> Cc: IT_Applications <a href="mailto:<EMAIL>"><EMAIL></a>; Vinoth Kumar Narayana Moorthy <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Clar Core PO# 7207688 * CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello Sandy,
We received a PO from Clark core today with 15 lines, but we received two files for the same PO and it became a 30 line order in JDE. Attached are the files that was dropped in our FTP folder.
Can you please look into this and let us know what happened here? And how do we prevent this from happening?
Thank you. Amit
Amit Das Senior Applications Analyst : Burlington, Ontario Hadrian &amp; World Dryer : Office: (************* Mobile: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.linkedin.com/company/hadrian-manufacturing-inc./">https://www.linkedin.com/company/hadrian-manufacturing-inc./</a>
<a href="https://www.facebook.com/HadrianInc">https://www.facebook.com/HadrianInc</a>
<a href="https://www.instagram.com/hadrian_inc/">https://www.instagram.com/hadrian_inc/</a>
<a href="http://zurn-elkay.com/">http://zurn-elkay.com/</a> |
This message and any files transmitted with it are directed in confidence solely for the use of the individual(s) to whom they are addressed. If you are not the intended recipient, be advised that you have received this email in error and that any use, dissemination, forwarding, printing, or copying of this email is strictly prohibited. You are requested to immediately advise the sender by return email and delete this email from your computer. Thank you.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]</p>
<h4 id="cs-44498-re-zf-lifetec-important-edi-changes-action-required-was-zf-passive-safety-system-us-tor-created-29-may-25-updated-29-may-25-resolved-29-may-25">[CS-44498] Re: ZF Lifetec – Important EDI changes – ACTION Required / WAS ZF PASSIVE SAFETY SYSTEM US -TOR Created: 29/May/25  Updated: 29/May/25  Resolved: 29/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      ZF Lifetec EDI Communication March 2025_S.docx      AxwayCloud_CommunicationSheet_Lifetec_March2025_S.xlsx
Request Type: Emailed request
Request language: English Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi @<a href="mailto:<EMAIL>"><EMAIL></a>, @Hariharan Mohan HYD RFXM3
We do not have details for this partner. Could you please look into this.
From: Sabrina Muellener ALF RFX <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 26, 2025 1:55 PM To: PSO_NOID_TSIM_ONBD <a href="mailto:<EMAIL>"><EMAIL></a>
Cc: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a>; Marie Gauvin <a href="mailto:<EMAIL>"><EMAIL></a> Subject: AW: ZF Lifetec – Important EDI changes – ACTION Required / WAS ZF PASSIVE SAFETY SYSTEM US -TOR
Dear Axway-Team,
see attached the feedback from supplier 140311.
Mit freundlichen Grüßen / Kind regards Sabrina Muellener
Information Technology - Communications
ZF LIFETEC
Industriestrasse 20
D-73553 Alfdorf / Deutschland/Germany
E-Mail: <a href="mailto:<EMAIL>"><EMAIL></a>
Invoice address: ZF Automotive Germany GmbH, Industriestrasse 20, D-73553 Alfdorf
Vorsitzender des Aufsichtsrats/Chairman of the Supervisory Board: Holly Swanson Geschäftsführer/Managing Directors: Eduard Bausch, Dirk Schultz,
Handelsregistereintrag ZF Automotive Germany GmbH, Amtsgericht Stuttgart HRB 282093/Trade register of ZF Automotive Germany GmbH, the municipal court of Stuttgart HRB 282093
Informationen zur Verarbeitung Ihrer Daten und zu Ihren Rechten erhalten Sie jederzeit in unseren Datenschutzhinweisen:
<a href="https://www.zf.com/de/data-protection-notice">https://www.zf.com/de/data-protection-notice</a> You can find information about how we process your data and your rights in our data protection notice:
<a href="https://www.zf.com/en/data-protection-notice">https://www.zf.com/en/data-protection-notice</a>
Von: Marie Gauvin <a href="mailto:<EMAIL>"><EMAIL></a> Gesendet: Thursday, May 22, 2025 4:53 PM An: <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Sabrina Muellener ALF RFX <a href="mailto:<EMAIL>"><EMAIL></a> Betreff: FW: ZF Lifetec – Important EDI changes – ACTION Required / WAS ZF PASSIVE SAFETY SYSTEM US -TOR
Hello
Note that ZF PASSIVE SAFETY SYSTEM US -TOR has changes, please see the attachments and advise asap how soon changes can be made.
GREYSTONE HOLIDAY SCHEDULE
MAY 26TH – CLOSED
JUNE 19TH – CLOSED
JULY 4TH – CLOSED
AUGUST 11TH – CLOSED
SEPTEMBER 1ST – CLOSED
OCTOBER 13TH – CLOSED
NOVEMBER 11TH – CLOSED
NOVEMBER 27TH – CLOSED
DECEMBER 25TH - CLOSED
Thanks,
Marie Gauvin | Customer Service Representative
Greystone of Lincoln|W (401)-333-0444 ext.218 |C (401)-651-9124 |F (401)-334-5745
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Sabrina Muellener ALF RFX <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 19, 2025 3:40 AM To: Marie Gauvin <a href="mailto:<EMAIL>"><EMAIL></a> Subject: ZF Lifetec – Important EDI changes – ACTION Required
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello EDI Partner,
ZF Lifetec is moving to a new EDI platform, provided by Axway. Details are provided in the attached Word document.
Your response is REQUIRED by May 23, 2025. Lack of response will result in disconnection of EDI which would be non-compliant with our expectations of you as a supplier.
Also attached is an Excel sheet with parameters for the connection to Axway.
Please read the attached information and reply with your completed parameter sheet as soon as possible. Forward this email to the correct IT/ EDI person in your company to complete if needed.
Once your reply is received, Axway will contact you to establish the connection. Include your supplier code, &lt; 140311&gt;, in your completed parameter sheet.
After the connection is established, Axway will identify the related supplier codes to be moved to the new connection and they will work on scheduling the change.
Thank you for your prompt cooperation,
ZF Lifetec
Mit freundlichen Grüßen / Kind regards Sabrina Muellener
Information Technology - Communications
ZF LIFETEC
Industriestrasse 20
D-73553 Alfdorf / Deutschland/Germany
E-Mail: <a href="mailto:<EMAIL>"><EMAIL></a>
Invoice address: ZF Automotive Germany GmbH, Industriestrasse 20, D-73553 Alfdorf
Vorsitzender des Aufsichtsrats/Chairman of the Supervisory Board: Holly Swanson Geschäftsführer/Managing Directors: Eduard Bausch, Dirk Schultz,
Handelsregistereintrag ZF Automotive Germany GmbH, Amtsgericht Stuttgart HRB 282093/Trade register of ZF Automotive Germany GmbH, the municipal court of Stuttgart HRB 282093
Informationen zur Verarbeitung Ihrer Daten und zu Ihren Rechten erhalten Sie jederzeit in unseren Datenschutzhinweisen:
<a href="https://www.zf.com/de/data-protection-notice">https://www.zf.com/de/data-protection-notice</a> You can find information about how we process your data and your rights in our data protection notice:
<a href="https://www.zf.com/en/data-protection-notice">https://www.zf.com/en/data-protection-notice</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
<em>AxwayCloud_CommunicationSheet_Lifetec_March2025_S.xlsx  (77 kB)</em>
<em>ZF Lifetec EDI Communication March 2025_S.docx  (36 kB)</em>
Comment by Sandy Karidas [ 29/May/25 ]
working on this ticket <a href="https://datatrans-inc.atlassian.net/jira/servicedesk/projects/CS/queues/custom/2/CS-44218">https://datatrans-inc.atlassian.net/jira/servicedesk/projects/CS/queues/custom/2/CS-44218</a>
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-44497-re-zf-lifetec-important-edi-changes-action-required-was-zf-passive-safety-system-us-tor-created-29-may-25-updated-29-may-25-resolved-29-may-25">[CS-44497] RE: ZF Lifetec – Important EDI changes – ACTION Required / WAS ZF PASSIVE SAFETY SYSTEM US -TOR Created: 29/May/25  Updated: 29/May/25  Resolved: 29/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      <strong>utf_8_B_Q1MtNDQyMTggRlc6IFpGIExpZmV0ZWMg4oCTIEltcG9ydGFudCBFREkgY2hh</strong> _<strong>utf_8_B_bmdlcyDigJMgQUNUSU9OIFJlcXVpcmVkIC8gV0FTIFpGIFBBU1NJVkUgU0FG</strong> _<strong>utf_8_Q_ETY_SYSTEM_US__TOR</strong>.eml
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello GREYST-Team,
@Marie Gauvin
@<a href="mailto:<EMAIL>"><EMAIL></a>
Please start exchanging your connection parameters and get the EDI connection established via Axway. Otherwise, you might lose receiving EDI releases via our OLD ZF EDI system as we won’t be having access in upcoming months.
Appreciate your prompt action on this connection change. Kindly note, only connection is changing, EDI messages and their format/structures stays as is.
Thanks,
Hariharan
EDI Team (RFXM3)
IT Market, Material and ERP (RFXM)
ZF LIFETEC
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
Legal Entity: ZF Passive Safety Systems / ZF-LIFETEC
Upcoming Holidays/Vacation : Jun 04th 2025
From: Anvay Jain <a href="mailto:<EMAIL>"><EMAIL></a> Sent: 29 May 2025 16:27 To: Sabrina Muellener ALF RFX <a href="mailto:<EMAIL>"><EMAIL></a>; Hariharan Mohan HYD RFXM3 <a href="mailto:<EMAIL>"><EMAIL></a> Cc: <a href="mailto:<EMAIL>"><EMAIL></a>; Marie Gauvin <a href="mailto:<EMAIL>"><EMAIL></a>; PSO_NOID_TSIM_ONBD <a href="mailto:<EMAIL>"><EMAIL></a>; Zbysław Ciszyński CZS RFXM <a href="mailto:<EMAIL>"><EMAIL></a>; Robert Lane WSH RFXM3 <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: ZF Lifetec – Important EDI changes – ACTION Required / WAS ZF PASSIVE SAFETY SYSTEM US -TOR
Hi @<a href="mailto:<EMAIL>"><EMAIL></a>, @Hariharan Mohan HYD RFXM3
We do not have details for this partner. Could you please look into this.
From: Sabrina Muellener ALF RFX <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 26, 2025 1:55 PM To: PSO_NOID_TSIM_ONBD &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: <a href="mailto:<EMAIL>"><EMAIL></a> &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Marie Gauvin &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: AW: ZF Lifetec – Important EDI changes – ACTION Required / WAS ZF PASSIVE SAFETY SYSTEM US -TOR
Dear Axway-Team,
see attached the feedback from supplier 140311.
Mit freundlichen Grüßen / Kind regards Sabrina Muellener
Information Technology - Communications
ZF LIFETEC
Industriestrasse 20
D-73553 Alfdorf / Deutschland/Germany
E-Mail: <a href="mailto:<EMAIL>"><EMAIL></a>
Invoice address: ZF Automotive Germany GmbH, Industriestrasse 20, D-73553 Alfdorf
Vorsitzender des Aufsichtsrats/Chairman of the Supervisory Board: Holly Swanson Geschäftsführer/Managing Directors: Eduard Bausch, Dirk Schultz,
Handelsregistereintrag ZF Automotive Germany GmbH, Amtsgericht Stuttgart HRB 282093/Trade register of ZF Automotive Germany GmbH, the municipal court of Stuttgart HRB 282093
Informationen zur Verarbeitung Ihrer Daten und zu Ihren Rechten erhalten Sie jederzeit in unseren Datenschutzhinweisen:
<a href="https://www.zf.com/de/data-protection-notice">https://www.zf.com/de/data-protection-notice</a> You can find information about how we process your data and your rights in our data protection notice:
<a href="https://www.zf.com/en/data-protection-notice">https://www.zf.com/en/data-protection-notice</a>
Von: Marie Gauvin <a href="mailto:<EMAIL>"><EMAIL></a> Gesendet: Thursday, May 22, 2025 4:53 PM An: <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Sabrina Muellener ALF RFX <a href="mailto:<EMAIL>"><EMAIL></a> Betreff: FW: ZF Lifetec – Important EDI changes – ACTION Required / WAS ZF PASSIVE SAFETY SYSTEM US -TOR
Hello
Note that ZF PASSIVE SAFETY SYSTEM US -TOR has changes, please see the attachments and advise asap how soon changes can be made.
GREYSTONE HOLIDAY SCHEDULE
MAY 26TH – CLOSED
JUNE 19TH – CLOSED
JULY 4TH – CLOSED
AUGUST 11TH – CLOSED
SEPTEMBER 1ST – CLOSED
OCTOBER 13TH – CLOSED
NOVEMBER 11TH – CLOSED
NOVEMBER 27TH – CLOSED
DECEMBER 25TH - CLOSED
Thanks,
Marie Gauvin | Customer Service Representative
Greystone of Lincoln|W (401)-333-0444 ext.218 |C (401)-651-9124 |F (401)-334-5745
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Sabrina Muellener ALF RFX <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 19, 2025 3:40 AM To: Marie Gauvin <a href="mailto:<EMAIL>"><EMAIL></a> Subject: ZF Lifetec – Important EDI changes – ACTION Required
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello EDI Partner,
ZF Lifetec is moving to a new EDI platform, provided by Axway. Details are provided in the attached Word document.
Your response is REQUIRED by May 23, 2025. Lack of response will result in disconnection of EDI which would be non-compliant with our expectations of you as a supplier.
Also attached is an Excel sheet with parameters for the connection to Axway.
Please read the attached information and reply with your completed parameter sheet as soon as possible. Forward this email to the correct IT/ EDI person in your company to complete if needed.
Once your reply is received, Axway will contact you to establish the connection. Include your supplier code, &lt; 140311&gt;, in your completed parameter sheet.
After the connection is established, Axway will identify the related supplier codes to be moved to the new connection and they will work on scheduling the change.
Thank you for your prompt cooperation,
ZF Lifetec
Mit freundlichen Grüßen / Kind regards Sabrina Muellener
Information Technology - Communications
ZF LIFETEC
Industriestrasse 20
D-73553 Alfdorf / Deutschland/Germany
E-Mail: <a href="mailto:<EMAIL>"><EMAIL></a>
Invoice address: ZF Automotive Germany GmbH, Industriestrasse 20, D-73553 Alfdorf
Vorsitzender des Aufsichtsrats/Chairman of the Supervisory Board: Holly Swanson Geschäftsführer/Managing Directors: Eduard Bausch, Dirk Schultz,
Handelsregistereintrag ZF Automotive Germany GmbH, Amtsgericht Stuttgart HRB 282093/Trade register of ZF Automotive Germany GmbH, the municipal court of Stuttgart HRB 282093
Informationen zur Verarbeitung Ihrer Daten und zu Ihren Rechten erhalten Sie jederzeit in unseren Datenschutzhinweisen:
<a href="https://www.zf.com/de/data-protection-notice">https://www.zf.com/de/data-protection-notice</a> You can find information about how we process your data and your rights in our data protection notice:
<a href="https://www.zf.com/en/data-protection-notice">https://www.zf.com/en/data-protection-notice</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Comment by Sandy Karidas [ 29/May/25 ]
working on ticket <a href="https://datatrans-inc.atlassian.net/browse/CS-44218">https://datatrans-inc.atlassian.net/browse/CS-44218</a>
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Hello Hariharan
Please see the attachment as our EDI provider is working on the map updates and will let advise when completed
GREYSTONE HOLIDAY SCHEDULE
JUNE 19TH – CLOSED
JULY 4TH – CLOSED
AUGUST 11TH – CLOSED
SEPTEMBER 1ST – CLOSED
OCTOBER 13TH – CLOSED
NOVEMBER 11TH – CLOSED
NOVEMBER 27TH – CLOSED
DECEMBER 25TH - CLOSED
Thanks,
Marie Gauvin | Customer Service Representative
Greystone of Lincoln|W (401)-333-0444 ext.218 |C (401)-651-9124 |F (401)-334-5745
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
<em>[^_utf_8_B_Q1MtNDQyMTggRlc6IFpGIExpZmV0ZWMg4oCTIEltcG9ydGFudCBFREkgY2hh_ __utf_8_B_bmdlcyDigJMgQUNUSU9OIFJlcXVpcmVkIC8gV0FTIFpGIFBBU1NJVkUgU0FG_ __utf_8_Q_ETY_SYSTEM_USTOR.eml] _(67 kB)</em></p>
<h4 id="cs-44537-happening-now-albert-poopbags-com-is-inviting-you-to-a-video-call-created-29-may-25-updated-29-may-25">[CS-44537] Happening now: <a href="mailto:<EMAIL>"><EMAIL></a> is inviting you to a video call Created: 29/May/25  Updated: 29/May/25</h4>
<p>Status: Canceled
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas
Resolution: Unresolved Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
<a href="mailto:<EMAIL>"><EMAIL></a> is inviting you to join a video call happening now
Join Call meet.google.com/kcg-jjex-rhiDial-in: (US) +1 551-800-5371 PIN: 804 209 599# More numbers</p>
<h4 id="cs-43728-re-external-reminder-urgent-cleo-dts-sftp-update-created-14-may-25-updated-29-may-25-resolved-15-may-25">[CS-43728] RE: [EXTERNAL]REMINDER - URGENT - Cleo/DTS SFTP update Created: 14/May/25  Updated: 29/May/25  Resolved: 15/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sandy Karidas Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image005.jpg      image004.png      image003.png      image001 (e9604c38-0ea0-4ca0-a017-b93810125a4c).png     image003 (1247a59f-873c-468d-aa37-d03673db8933).png      image004 (6fa10ffb-024f-487c-9a62-71f354838e6b).png      image002 (877694d1-5d98-4516-95ac-558cf2a3bb93).png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello DataTrans Support team,
We got the below email from CLEO to join the migration call.
Is it really scheduled for us to join or is it just for letting us know about this?
Is this taken care of already or we need to call IT person for this changeover?
Please let me know at your earliest convenience.
Thanks,
Regards
Mohammed Ishan Iqbal | Supply Chain Engineer
US Address: 2410 West Tech Lane, Auburn AL, 36832 - USA
Office: +1 (346) 504-3257 | Internet: + <a href="http://www.fonderie2a.com">www.fonderie2a.com</a> +
ITA Headquarter: Via Asti, 67/bis,10026 Santena (TO), Italy
<em>+ <a href="mailto:<EMAIL>"><EMAIL></a> +</em>
From: Karidas, Sandy <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 14, 2025 4:03 PM Subject: [EXTERNAL]REMINDER - URGENT - Cleo/DTS SFTP update
Attention customers and trading partners,
URGENT - ATTENTION NEEDED:
Cleo/DTS has a new SFTP which is replacing the current FTP connection. We will be sunsetting the current FTP on June 7, 2025. Please use the calendar link below to schedule a 30-minute call to perform the migration. Do not reply directly to this email. Please disregard this notice if you have already completed the migration or scheduled a call.
To avoid document delivery interruptions, the migration must be completed before June 7, 2025.
<a href="https://calendly.com/nsanchez-datatrans-inc/30min">https://calendly.com/nsanchez-datatrans-inc/30min</a>
Sandy Karidas
Cleo : Support Manager
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a></li>
<li>Join us at one of our upcoming events. Check out the list! *
From: Karidas, Sandy <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 6, 2025 11:11 AM Subject: URGENT - Cleo/DTS SFTP update
Attention customers and trading partners,
URGENT - ATTENTION NEEDED:
Cleo/DTS has a new SFTP which is replacing the current FTP connection. We will be sunsetting the current FTP on June 7, 2025. Please use the calendar link below to schedule a 30-minute call to perform the migration.
To avoid document delivery interruptions, the migration must be completed before June 7, 2025.
<a href="https://calendly.com/nsanchez-datatrans-inc/30min">https://calendly.com/nsanchez-datatrans-inc/30min</a>
<a href="https://calendly.com/nsanchez-datatrans-inc/30min">https://calendly.com/nsanchez-datatrans-inc/30min</a> | 30 Minute Meeting - Nicolas Sanchez calendly.com |
Sandy | | Karidas |
Cleo | : | Support Manager |
Email: | <a href="mailto:<EMAIL>"><EMAIL></a> |
|
Web:
<a href="http://www.cleo.com">www.cleo.com</a></li>
<li>Join us at one of our upcoming events. Check out the list! *
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 14/May/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 15/May/25 ]
Hello,
Please use the calendar link in the original email to schedule a 30-minute call to perform the migration. The current FTP being used will be discontinued on June 7th and document delivery will fail after that date.
Thank you,
Sandy Karidas
Cleo : Support Manager
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
Comment by Sandy Karidas [ 15/May/25 ]
replied directly through email
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 15/May/25 ]
This notice is for you to schedule a migration call. Please use the link received in the original email.
Thank you
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 15/May/25 ]
I was informed that our Logistics Manager is taking care of this. Has this not happened yet on their end?
Kind Regards,
Christy Funderburk | Accounting / Purchasing | USA Division</li>
</ul>
