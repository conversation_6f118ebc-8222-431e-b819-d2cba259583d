<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive EDI Troubleshooter</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f4f7f9;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        #container {
            background-color: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
            text-align: center;
        }
        h1 {
            color: #1a253c;
            margin-bottom: 10px;
        }
        p {
            color: #5a677d;
            margin-bottom: 30px;
        }
        textarea {
            width: 100%;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dcdcdc;
            font-size: 16px;
            min-height: 100px;
            box-sizing: border-box;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-top: 20px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #chart-container {
            margin-top: 40px;
            text-align: left;
        }
    </style>
</head>
<body>
    <div id="container">
        <h1>Interactive EDI Troubleshooter</h1>
        <p>Describe your EDI problem below, and I'll generate a visual troubleshooting guide.</p>
        <textarea id="problem-description" placeholder="For example: I sent an 810 invoice to Walmart, and it says 'Sent', but they haven't received it..."></textarea>
        <button id="diagnose-button">Diagnose Issue</button>
        <div id="chart-container">
            <div class="mermaid" id="chart">
                graph TD
                    A[Enter a problem description to begin]
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true });

        document.getElementById('diagnose-button').addEventListener('click', diagnose);

        function diagnose() {
            console.log("Diagnose function called");
            const description = document.getElementById('problem-description').value.toLowerCase();
            const chartContainer = document.getElementById('chart');
            let chartDefinition = '';
            console.log("Description:", description);

            // Simple keyword-based analysis
            if (description.includes('810') || description.includes('invoice')) {
                if (description.includes('sent') && (description.includes('not received') || description.includes('what\'s going on') || description.includes("haven't received"))) {
                    chartDefinition = `
                        graph TD
                            A[Start: Outbound 810 Sent] --> B{Did you receive a 997 Functional Acknowledgment?};
                            B --> |Yes, Accepted| C{Has the partner sent an 824 Application Advice?};
                            B --> |No| D[Check your system's outbound queue. Is the 810 still there?];
                            B --> |Yes, Rejected| E[Review the 997 for error codes. Common issues: invalid syntax, wrong ID.];
                            
                            C --> |Yes| F[Review the 824 for business-level errors. The invoice may have been rejected due to price mismatches, wrong PO number, etc.];
                            C --> |No| G[Contact the trading partner. Provide them with the ISA/GS control numbers and date/time of transmission. The issue may be on their end.];

                            style A fill:#D4EDDA,stroke:#155724
                            style G fill:#D1ECF1,stroke:#0C5460
                            style E fill:#F8D7DA,stroke:#721C24
                            style F fill:#F8D7DA,stroke:#721C24
                    `;
                }
            } else {
                 chartDefinition = `
                    graph TD
                        A[Enter a problem description to begin]
                 `;
            }

            // Update the chart
            chartContainer.innerHTML = chartDefinition;
            chartContainer.removeAttribute('data-processed');
            mermaid.init(undefined, chartContainer);
        }
    </script>
</body>
</html>