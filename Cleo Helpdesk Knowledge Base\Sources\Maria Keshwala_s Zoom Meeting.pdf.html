<h3 id="maria-keshwala-s-zoom-meeting"><PERSON>&#39;s Zoom Meeting</h3>
<p>Meeting started: May 20, 2025, 11:37:58 AM Meeting duration: 85 minutes Meeting participants: <PERSON>, <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="generated-content">Generated Content</h4>
<p>Maria is using Notepad++ to perform several tasks related to managing purchase orders and document types. Here are the key activities she is engaged in:</p>
<ol>
<li><strong>File Management</strong>:    - She is saving files, likely related to purchase orders (e.g., A75, A50).    - She mentions using the &#34;Save As&#34; function to create specific file names for different document types.</li>
<li><strong>Document Creation</strong>:    - Maria is generating a 997 document, which is an acknowledgment of receipt for the purchase orders.    - She is also creating a 907 document, which is another type of acknowledgment.</li>
<li><strong>Data Analysis</strong>:    - She is analyzing the structure of purchase orders (A75s) to ensure they meet the required standards.    - Maria is checking for discrepancies or errors in the documents, such as control numbers and segments.</li>
<li><strong>Troubleshooting</strong>:    - She is investigating issues related to the purchase orders being sent back and
forth, particularly focusing on the control numbers and document types.</li>
<li><strong>Note-taking</strong>:    - <PERSON> is adding notes to herself for future reference, indicating her process of documenting findings and observations.</li>
<li><strong>Collaboration</strong>:    - She mentions communicating with team members about the status of documents and any issues that arise.
Overall, Maria is utilizing Notepad++ as a tool for file management, document creation, data analysis, and collaboration in the context of handling purchase orders.</li>
</ol>
<h4 id="generated-content-2">Generated Content</h4>
<h1 id="meeting-notes">Meeting Notes</h1>
<h2 id="participants">Participants</h2>
<ul>
<li>Michael     *   Maria</li>
</ul>
<h2 id="main-topics-discussed">Main Topics Discussed</h2>
<h3 id="1-purchase-orders">1. Purchase Orders</h3>
<ul>
<li><strong>Sender:</strong> Save A Lot     *   <strong>Receiver:</strong> USA Imports</li>
<li><strong>Types of Purchase Orders:</strong>         *   <strong>A875:</strong> Grocery store purchase orders             *   <strong>A850:</strong> Retail purchase orders (e.g., clothing, furniture)             *   <strong>A830:</strong> Used by Caterpillar for scheduling         *   <strong>Batch Processing:</strong>         *   Multiple purchase orders can be sent in one batch.             *   Each order is injected individually into the WebVDI portal.</li>
</ul>
<h3 id="2-document-handling">2. Document Handling</h3>
<ul>
<li><strong>Source of Data:</strong> LorenData     *   <strong>Target for Injection:</strong> WebVDI     *   <strong>Control Numbers:</strong>         *   Control numbers (e.g., Control 001, Control 002) are used to track purchase orders.             *   997 documents are generated to confirm receipt of purchase orders.</li>
</ul>
<h3 id="3-error-handling">3. Error Handling</h3>
<ul>
<li><strong>TA1 Segment:</strong> Used for error control.</li>
<li><strong>997 Document:</strong> Auto-generated to confirm the status of sent documents.     *   <strong>Issues Identified:</strong>         *   Discrepancies in control numbers and document types.             *   Need for further investigation into the structure of A875 and A850 orders.</li>
</ul>
<h3 id="4-learning-and-adaptation">4. Learning and Adaptation</h3>
<ul>
<li>Continuous learning is emphasized, with team members encouraged to research document types and processes.     *   Importance of diligence in checking document structures before escalating issues.</li>
</ul>
<h2 id="key-concepts">Key Concepts</h2>
<ul>
<li><strong>Purchase Order Types:</strong> Understanding the differences between A875 and A850 is crucial for processing.     *   <strong>WebVDI Portal:</strong> The platform where customers view their documents.     *   <strong>Control Numbers:</strong> Essential for tracking and confirming purchase orders.</li>
</ul>
<h2 id="thought-provoking-questions">Thought-Provoking Questions</h2>
<ul>
<li>How do different purchase order types affect the processing workflow?</li>
<li>What are the implications of discrepancies in control numbers on inventory management?</li>
</ul>
<h2 id="real-world-applications">Real-World Applications</h2>
<ul>
<li>The processes discussed can be applied in supply chain management and logistics, particularly in grocery and retail sectors.</li>
</ul>
<h2 id="areas-for-further-research">Areas for Further Research</h2>
<ul>
<li>Investigate the specific requirements for A875 and A850 purchase orders.     *   Explore best practices for error handling in document processing.</li>
</ul>
<h2 id="memory-aids">Memory Aids</h2>
<ul>
<li><strong>A875 = Grocery Orders</strong>     *   <strong>A850 = Retail Orders</strong>     *   <strong>A830 = Caterpillar Scheduling</strong></li>
</ul>
<h2 id="potential-exam-questions">Potential Exam Questions</h2>
<ol>
<li>What are the differences between A875 and A850 purchase orders?     2.  Explain the significance of control numbers in the purchase order process.</li>
</ol>
<h2 id="glossary">Glossary</h2>
<ul>
<li><strong>Purchase Order (PO):</strong> A document issued by a buyer to a seller indicating the products, quantities, and agreed prices.     *   <strong>WebVDI:</strong> A web-based platform for document viewing and processing.     *   <strong>997 Document:</strong> Acknowledgment of receipt of a purchase order.</li>
</ul>
<h2 id="summary-of-main-takeaways">Summary of Main Takeaways</h2>
<ul>
<li>Understanding the types of purchase orders and their processing is essential for effective supply chain management.     *   Continuous learning and adaptation are key in addressing discrepancies and improving processes.</li>
</ul>
<h4 id="generated-content-3">Generated Content</h4>
<p>In the meeting, Maria is using Notepad to perform several tasks related to managing purchase orders and their associated documents. Here’s a breakdown of her activities:</p>
<ol>
<li><strong>Viewing and Managing Purchase Orders</strong>:    - She is checking the details of purchase orders received from &#34;Save a Lot&#34; and sent to &#34;USA Imports.&#34;    - She identifies different types of purchase orders (e.g., A75 for grocery stores, A50 for retail).</li>
<li><strong>Document Injection</strong>:    - Maria discusses the process of injecting purchase order data into the WebVDI
system, which allows customers to view documents.</li>
<li><strong>Generating and Analyzing Control Numbers</strong>:    - She references control numbers associated with the purchase orders and how they relate to the 997 acknowledgment documents.</li>
<li><strong>Creating and Saving Files</strong>:    - Maria mentions saving files in Notepad and creating specific document types (like 997) for tracking and acknowledgment purposes.</li>
<li><strong>Troubleshooting and Research</strong>:    - She is analyzing the structure of the documents and checking for discrepancies or errors in the data being sent.</li>
<li><strong>Note-Taking</strong>:    - Maria takes notes for herself as she goes along, which helps her keep track of important information and findings.
Overall, her use of Notepad is focused on managing and documenting the workflow related to purchase orders and ensuring that the data is correctly processed and acknowledged.</li>
</ol>
<h4 id="generated-content-4">Generated Content</h4>
<h3 id="maria-s-ticket-details">Maria&#39;s Ticket Details</h3>
<h4 id="overview-purpose-to-analyze-and-troubleshoot-issues-related-to-purchase-orders-and-their-corresponding-acknowledgments-context-involves-communication-between-save-a-lot-sender-and-usa-imports-receiver">Overview - <strong>Purpose</strong>: To analyze and troubleshoot issues related to purchase orders and their corresponding acknowledgments. - <strong>Context</strong>: Involves communication between Save A Lot (sender) and USA Imports (receiver).</h4>
<h4 id="key-components-1-purchase-orders-types-a75-purchase-order-for-grocery-stores-a50-purchase-order-for-retail-clothing-furniture-a830-used-by-caterpillar-for-scheduling-example-a75-indicates-a-grocery-store-order">Key Components 1. <strong>Purchase Orders</strong>:    - <strong>Types</strong>:      - <strong>A75</strong>: Purchase order for grocery stores.      - <strong>A50</strong>: Purchase order for retail (clothing, furniture).      - <strong>A830</strong>: Used by Caterpillar for scheduling.    - <strong>Example</strong>: A75 indicates a grocery store order.</h4>
<ol start="2">
<li><strong>Data Flow</strong>:    - <strong>Source</strong>: LorenData (event data).    - <strong>Target</strong>: WebVDI (where customers view documents).    - <strong>Process</strong>: Purchase orders can be sent in batches (e.g., 10-15 orders) and are injected individually into the WebVDI portal.</li>
<li><strong>Acknowledgment Process</strong>:    - <strong>997 Acknowledgment</strong>: Auto-generated response for received purchase orders.    - <strong>Control Numbers</strong>: Used to track and reference specific purchase orders.</li>
</ol>
<h4 id="issues-identified-acknowledgment-discrepancies-different-acknowledgment-responses-997s-for-similar-purchase-orders-potential-cause-differences-in-acknowledgment-types-e-g-value-of-0-vs-1">Issues Identified - <strong>Acknowledgment Discrepancies</strong>:   - Different acknowledgment responses (997s) for similar purchase orders.   - Potential cause: Differences in acknowledgment types (e.g., value of 0 vs. 1).</h4>
<h4 id="troubleshooting-steps-1-comparison-of-documents-analyzing-a75-documents-from-different-senders-to-identify-structural-differences-checking-control-numbers-and-acknowledgment-values">Troubleshooting Steps 1. <strong>Comparison of Documents</strong>:    - Analyzing A75 documents from different senders to identify structural differences.    - Checking control numbers and acknowledgment values.</h4>
<ol start="2">
<li><strong>Testing Changes</strong>:    - Modifying acknowledgment values to see if it resolves discrepancies.    - Deleting problematic purchase orders to prevent confusion.</li>
<li><strong>Documentation</strong>:    - Keeping detailed notes on findings and changes made during the troubleshooting process.</li>
</ol>
<h4 id="tools-used-notepad-for-formatting-and-organizing-data-screen-sharing-to-collaborate-and-demonstrate-findings-in-real-time">Tools Used - <strong>Notepad++</strong>: For formatting and organizing data. - <strong>Screen Sharing</strong>: To collaborate and demonstrate findings in real-time.</h4>
<h4 id="key-takeaways-continuous-learning-and-adaptation-are-essential-in-troubleshooting-document-types-and-their-structures-play-a-critical-role-in-ensuring-proper-acknowledgment-and-processing-collaboration-and-communication-with-team-members-are-crucial-for-resolving-issues-effectively">Key Takeaways - Continuous learning and adaptation are essential in troubleshooting. - Document types and their structures play a critical role in ensuring proper acknowledgment and processing. - Collaboration and communication with team members are crucial for resolving issues effectively.</h4>
<h4 id="questions-for-further-consideration-what-specific-changes-can-be-made-to-the-acknowledgment-process-to-prevent-future-discrepancies-how-can-the-team-improve-documentation-practices-to-enhance-clarity-and-efficiency-in-troubleshooting">Questions for Further Consideration - What specific changes can be made to the acknowledgment process to prevent future discrepancies? - How can the team improve documentation practices to enhance clarity and efficiency in troubleshooting?</h4>
<h4 id="suggested-further-research-explore-best-practices-for-managing-purchase-orders-and-acknowledgments-in-supply-chain-management-investigate-the-impact-of-document-structure-on-automated-processing-systems">Suggested Further Research - Explore best practices for managing purchase orders and acknowledgments in supply chain management. - Investigate the impact of document structure on automated processing systems.</h4>
<h4 id="glossary-of-terms-purchase-order-po-a-document-sent-from-a-buyer-to-a-seller-indicating-the-products-quantities-and-agreed-prices-acknowledgment-997-a-confirmation-document-sent-in-response-to-a-purchase-order">Glossary of Terms - <strong>Purchase Order (PO)</strong>: A document sent from a buyer to a seller, indicating the products, quantities, and agreed prices. - <strong>Acknowledgment (997)</strong>: A confirmation document sent in response to a purchase order.</h4>
<ul>
<li><strong>Control Number</strong>: A unique identifier for tracking documents in a system.
This structured overview provides a comprehensive understanding of Maria&#39;s ticket and the associated processes.</li>
</ul>
<h4 id="transcript">Transcript</h4>
<p>00:00 Michael H.: Yes, thank you so much. 00:01 Maria K.: Got it? Okay, perfect. Uh, put your status and the meeting. Like, you know, like, change the status, it says meeting, so people know you are in a meeting. 00:11 Michael H.: Oh, okay. 00:11 Maria K.: I&#39;m gonna… I&#39;m gonna put a note on the team that you are with Wait, hold on. Okay, with… Michael… 00:48 Maria K.: Okay. All right, perfect. So let me share… 00:51 Michael H.: Yes. 01:01 Maria K.: Okay, I want to share. And I want to share… Yeah, screen 1. Okay, perfect. Okay, so… All right, let me get this out of the way. Okay. So… Um, alright, so now… like I said, we&#39;re gonna… search for the purchase order, right? Okay. So, save a lot is a sender. And the receiver is… USA imports. 01:36 Michael H.: Yes. 01:55 Maria K.: Okay. So here&#39;s the purchase order. Received from… save a lot. And as you can see, the source is… zero-band lauren data, which is our event, because it comes from LorenData. 02:11 Maria K.: And it injects into the server, right? Once we receive it. 02:14 Michael H.: Yes. 02:17 Maria K.: As you can see down here. The target is zero rabbit NQ WebVDI ingestion. This is our target, uh, um… file, uh, file to inject into WebDI. This is what triggers to inject the file into the WebVDI where the customer… where the customer views the document, right?
02:40 Maria K.: The pretty view of it. Um, so this is the target, and I&#39;ll show you where that is, how it is there, and all of that. 02:41 Michael H.: Okay. 02:48 Maria K.: Okay, so I&#39;m gonna look at this and see… it, you know, why… So, in A75, is a purchase order for grocery stores, okay? And A50 is a purchase order for retail. Right? But… Like, clothing, uh, right? Uh… uh, furniture, whatever, right? Um, and uh… And then, I think 8.30 is usually, um, like, Caterpillar uses 830s, which is, like. 03:18 Michael H.: Yes. 03:28 Maria K.: You know, schedulers and stuff like that. But this one, an A775, is used for a grocery store. So when you see an A75, you know it&#39;s coming from a grocery store, okay? Alright, so what they did here, they sent two purchase orders, okay? And it&#39;s okay. They can send 10, 15, it doesn&#39;t matter, in one batch. We make sure that those get separated, and they inject individually within the WebVDI portal. Okay? All right. So… Now… I forgot one thing, um… I forgot one thing. Let me open another… thing, and I&#39;ll show you… Yes, yes. 03:58 Michael H.: Yes. So it&#39;s $8.75 always for groceries, and $850… okay. 04:16 Maria K.: Yes, yes. You know it&#39;s for food. Yeah. Okay, so purchase orders, perfect. Mm-hmm. 04:20 Michael H.: Yes.
04:26 Maria K.: Or consumables, okay. So, um, alright. And we go here and do my standard USA imports. I just wanna… flavor a lot. Just want to view the 997. Advance this, uh, what is this? You can open to windows at the same time, okay? By double-clicking the icon. 04:51 Michael H.: Okay. 04:51 Maria K.: You just right-click it, and then… you know, click on it, and it&#39;ll open a new one. 04:58 Michael H.: Yes. 04:58 Maria K.: In case you want to have to… to open. All right, so how do I know for which one… for which purchase order is this for? Um, well… Hold on. Okay. When you look at the moment. All right, it says Control 26. Control 001. And control002. So we send the 997 for those two um, orders, okay? All right, so let me see… Okay, so the CV Controls 101, 102? So, uh, the 987… reflects the… the control numbers are from the purchase order, right? So a reference, oh, we are setting these documents from this control that you sent us, okay? 06:21 Maria K.: Alright, so I am gonna see if I see anything. Weird, um… Because… This number right… you know what I noticed? Okay, there you go. There you are. All right, so we&#39;ve got… I&#39;m looking at a number that was after the, uh. Da1, which is editing and Right here. This is what that TA1 is sending. So, regenerating. Air control… on that TA1. How do I know that? 07:29 Maria K.: Okay, so let me see what happens. I&#39;m gonna also check other 997s, if we send them the pass, and see, you know. What&#39;s going on? Alright, let me go back here. No, no, no, that&#39;s the same word. 08:13 Maria K.: Okay… And it looks like… Okay. Because… The A75… Paise. The control before the… Let me check… Could I close the other one now? I&#39;m just tying to the term. You see, like. You always get… this is new to me, you know that? We always knew things. Every day. 09:02 Michael H.: Yes. 09:07 Maria K.: Some things I already know, some things is, like. What&#39;s going on here? 09:12 Michael H.: Yes, that it just seems like, um… It&#39;s constant, non-stop learning. 09:16 Maria K.: Yes, which is so cool, right? Save a lot. Well, I like that, because I don&#39;t get bored.
09:21 Michael H.: Yeah. 09:25 Maria K.: You know? I don&#39;t like… Uh, monotony. 09:25 Michael H.: Oh, yes, that&#39;s the number one thing. 09:33 Maria K.: Um, so let&#39;s see, um… 09:53 Maria K.: From… But, alright, let me see if I can replicate. We got that there. 10:08 Maria K.: Format. Now, let me… let me generate an IX7. So I go to Tools. Sorry, I need to do… Let me save the file. Let me see if it works this way. 11:20 Maria K.: Here is my deliverance, right? It&#39;s saying that it went to WebDI, like I show you, right? I injected the file there. This auto thing… It&#39;s the, uh, 997 that was auto-generated, okay? Here&#39;s my 997, and convert… in conversations, okay? You can also view it down here. 12:06 Maria K.: Okay, so under the envelope? We go to tools, and I&#39;m gonna say Create. 907? Way to go. And she&#39;ll create a 997 for you. Let me go to the one that I saved here, see if he&#39;ll do it. 12:44 Maria K.: I&#39;m gonna… 13:46 Maria K.: Let me see what I can do. I&#39;ve never seen this before. Listen, let me too. Created N97, which is where usually I would do that. Let me put a no on the team. 16:33 Maria K.: Let&#39;s see… Well, I&#39;m gonna do… let me see if we have sent any Google 97s before. You know, that&#39;s a good thing to check, too. Because I can&#39;t replicate. Okay. 17:30 Maria K.: Alright, um, okay. Um… I&#39;m gonna do my call, I&#39;m gonna go back. 17:45 Michael H.: Okay. 17:48 Maria K.: Like, I don&#39;t know when they became effective with them, but… Okay, so we do Central 97. Let me just see how we send it, because I can… what I can do is add the one we send wrong. And we send it back. We&#39;ve been too long. 19:21 Maria K.: Okay. So… I mean, you go way back to them Why are we doing that? I guess the first time we… I guess they became active, we saved a lot. We started in May. First. Um… Because… what I&#39;m seeing here… We&#39;ve been sending Standard versions. We&#39;ve been sending that. That&#39;s see why putting the control there. Let me check some… 21:19 Maria K.: Now I know, you know, now that I know that, I&#39;m gonna add a note for myself. The… Oh, gosh, no. Say the law. Thanks. April 2025… That appears
back. E81 segment. Has been sent on the 997s. Please stop. I just like to add notes as I go, you know. 22:17 Michael H.: Yes. 22:18 Maria K.: Um, so… Now I&#39;m gonna check… what&#39;s on… Now I&#39;m going to check in general. You can do the document types here. I&#39;ll say minus 7. And, um… I will say voila. The thing is, no doubt has their VTS. As their ISA, you know? But… USA Imports has a different kind of sender, um, AISAID. So, I mean, obviously, this has the correct information. You see, they have the GS after the P, And it says .sa. 23:29 Maria K.: Dot sender. I mean, I can&#39;t fix that, but… Um… I have to ask Sandy… Roger. 23:49 Maria K.: How we alter Let me check one more thing. So you see how I&#39;m analyzing, so I&#39;m checking… okay. 23:59 Michael H.: Okay. 24:03 Maria K.: So now I&#39;m gonna check. No cows, A75s. Versus USA Imports A75s. If they&#39;re standing the same structure, because for some reason. 24:14 Michael H.: Yes. 24:17 Maria K.: We are sending back 801. For this particular customer. And not for the other one, you know? Before I go to somebody, I have to, like, do all my diligence, you know? Like, on my discoveries, and then… 24:37 Michael H.: Yes, so you think it&#39;s the $875, like, international and domestic imports that are causing problems? 24:42 Maria K.: No, no. A75 is not international. Is that just, uh… Those, uh, grocery store, uh, purchase order. It&#39;s like when they ask for, like, food stuff, you know? 24:45 Michael H.: Oh, okay. 24:53 Maria K.: I know cap… no car probably since, I don&#39;t know, chocolate or whatever, and then save a lot wants their chocolate. 24:54 Michael H.: Oh, okay, yes. 25:01 Maria K.: And they&#39;re ordering chocolates. I don&#39;t know you were saying, of course, when I sell different things to save a lot. And then my order, you know, I don&#39;t know. Something, but it has to be food… it&#39;s food-related asymptotes. And, you know, um… Google… This is how I learned to. I did my own research on documents.
25:14 Michael H.: Okay. 25:24 Maria K.: Like, I googled them, and I&#39;m like, okay, can you explain to me what this document really is, right? What it&#39;s really for? And it literally explains you Why? You can search it. So, we just… yeah, it helps, you know, like, all the document types. 25:39 Michael H.: Yes. 25:41 Maria K.: By ATM is always invoice. Basically, USA, and I… you know, and you might find reverse relationships. 25:45 Michael H.: Yes. 25:51 Maria K.: What is a reverse relationship? Meaning, when… when we… Oh, shit. All right, and you reported that. The meeting. This is not a regular setup, because already our credentials are discussed to check the internal testing to see whether to use. Okay. I&#39;ll jump… And I call that… You have to… Attached, so… migration. All right, so… I got off track, uh… What says that? Okay. Uh, let me see something… Um, let me check the A75 snap. 27:00 Maria K.: So… so far… 27:30 Maria K.: Distribution. Oh, 875s… Let&#39;s see right there. Not… Reason, uh, for you all. Okay. So… I&#39;m going to check… Save a lot. Oh, no, sorry, A Zelena. So I&#39;m going to… And now, um… No power plus plus? You have that one? 28:49 Michael H.: Uh, plus… 28:49 Maria K.: Uh, good thing to… I&#39;m gonna say no. Good thing to consider. 28:55 Michael H.: Okay. 28:55 Maria K.: Don&#39;t slow. Or update anything in the survey. 29:01 Michael H.: Okay, yes. 29:03 Maria K.: All right. Now, in your desktop, yes, but… Here? No. I&#39;m telling you this because Tyler once did that, and it broke a lot of things. Awesome, and you get those, like, you see, they asked me to update a new version, whatever, he said, no. I don&#39;t want to… I don&#39;t want any trouble. Okay. So I&#39;m gonna open this, because I want to comfort the two files, so… I&#39;m gonna minimize this. And I&#39;m gonna save… AMS75, and look out. 29:46 Maria K.: When I do save as… Let&#39;s say… No cow. 875. And… you as saying, or… Pull it. Okay. All right. So, alright, let me… Or we can… 30:42 Maria K.: One is the one…
31:12 Maria K.: So… 32:01 Maria K.: Um, I know there&#39;s, uh, I always forget this. There is, um… Oh, I don&#39;t know. Sorry about that. I haven&#39;t stopped here. 32:25 Michael H.: Yes. 32:33 Maria K.: I want to make it look straight. Let me click call to do it like that. 33:05 Maria K.: Give me one second. Hello, sorry. 33:24 Michael H.: Oh, no problem. 33:29 Maria K.: Oh, I have a screenshot, but… To get to that screen, I&#39;m trying to find where… oh… It&#39;s ready control.
33:48 Maria K.: Got it. 34:46 Maria K.: I know how to do the X amount, but… I&#39;m making pretty things for me. Oh, man, I just do it in the… I&#39;ll do it like this. Sorry about that, give me one sec. I&#39;m gonna do it like this. 35:41 Maria K.: Let me see if I can do that one like that. 36:10 Maria K.: You want to stop the recording so it doesn&#39;t, you know… ruin your… recordings. 36:18 Michael H.: Okay. 36:21 Maria K.: And I&#39;ll tell you when to start reporting. Some kid is running, screaming. I hope they&#39;re okay. 37:05 Michael H.: Did you say some kid is running and screaming?
37:08 Maria K.: You&#39;re so… I&#39;m sorry, dividing you. Thanks. 38:10 Maria K.: Awesome. Had to warm up my nice coffee. That I made. My brother gave you for Christmas, because he loved me so much, he bought me an espresso. 38:26 Michael H.: Yes. Oh… 38:36 Maria K.: Everything is gonna be sad. 38:38 Michael H.: You buy your an espresso machine? Oh, man. 38:41 Maria K.: Thank you! Alright, let me take a copy of this. I didn&#39;t mention, put it in my notes. I got my Notepad++ open. 38:58 Michael H.: Yes, I see that. 39:01 Maria K.: Yeah, because I… Okay, make a CTRL-F, CTRL… then, depending on the dilemma where it is… Oh, depending on the delimiter. Okay. Request. That&#39;s why I need to know what the delivery is. Gotcha! All right. Bang here… Okay, so my delimiter… let me start over. No. Let me go and… Let&#39;s try this one more time. Um, okay.
40:21 Maria K.: I think he did that. I&#39;ll try. And then reflect. And then my delimiter yield is I just… the little star. Things are in my delimited. Mm-hmm. 40:40 Michael H.: Oh, I can&#39;t hear you, uh, you&#39;re… you sound muscular. Okay, I hear you now, yes. 40:42 Maria K.: Can you hear me? Okay, my delimiter is this star thing, and… And
then… Here is my… Replace, or… That does not work. 41:06 Michael H.: Yes, sorry, Murray. I&#39;m confused about the notepad part. 41:11 Maria K.: Yeah, I&#39;m trying to… figure it out. I just want to make it ready, you know what I mean? 41:15 Michael H.: Oh, okay, yes. Yes. 41:16 Maria K.: I just want to make it look pretty. Um… Uh, let me try this. 41:26 Michael H.: So, what you&#39;re doing right now is just formatting it to make it look more organized and everything, correct? 41:32 Maria K.: You got it. For some reason, it&#39;s not working! 41:32 Michael H.: Okay, yeah.
41:40 Maria K.: Oh, there you go. Um, I don&#39;t want to bother him. You know, we all have our specialty things where we&#39;re good at. Sometimes I forget things, sometimes take up a good thing. I see… What&#39;s my delimiter? My delimiter circulator here. That&#39;s what I got to start. Okay. The limiter here. Okay, so… Let me ask, then… 42:55 Maria K.: One more time. Ben. Profile. Replays, and I&#39;m gonna replace my needle this, with… That. Mm-hmm, and then wrap around. That&#39;s selected, and it&#39;s gonna be… And then… replace all. Oh. 43:19 Michael H.: Okay. Okay.
43:31 Maria K.: But, uh… oh. Oh, it worked. Oh, no. That doesn&#39;t look the way I want it to look. I&#39;m almost there. Let me do… 44:12 Maria K.: Bigger, so… 44:33 Maria K.: That&#39;s my delimiter separated. And I want to replace with… And… Yeah, this is selected. Standard. This is selected. And I did replace all. Well, this does not look the way I want it to look, but I don&#39;t know if you know what I mean, how I want it. I want it to, like. That&#39;s the best way to convert files. Oh, gosh. I mean, this is what he showed me, look, on the screenshot. You said the limiter here… and do that, and then replace all. And my delimiter… separator of the document is an asterisk. As you saw, right? So… I will find why this. 45:47 Michael H.: Yes. 45:53 Maria K.: Partially… let me see something.
46:00 Maria K.: Actually… wait. So, okay, let me see something. So I got ISA… The… Oh… The Diaz… I hope for my delivery separator is actually the, uh… 47:20 Maria K.: I figured it out. 47:22 Michael H.: Oh. 47:24 Maria K.: I got it. Thank you. You know why? Because I was reading the document to see where it ends, and then I see… you see, this ends with that. So that&#39;s my separator. So I got it now. Okay. All right. Perfect. Now let&#39;s find the other one. This one is for, say, uh, new cow. So let me open the other file. Well, now we&#39;re in track. Um, and I&#39;m going to do the same thing. This one got tilted. 49:33 Maria K.: The Davis, they&#39;re looking… they&#39;re buying black beans. That means… And then from local, they buy in hazelnuts, right? I&#39;m just gonna check this right here. So we&#39;ve got the… Sander. We&#39;ll see that. Sunday. Let me see here… I&#39;m just making sure everything was All these classes, zero. 50:44 Maria K.: Let me double-check that. Yeah, they all have a zero after that. Because I&#39;m trying to find anything that can trigger that, you know what I mean? After the, uh, the ISA, So, I&#39;m just trying to see… Why can you cause that? Let me check all of my colleagues. Zero. 51:51 Maria K.: Hold on, I mean, I&#39;m open the door. One. I think that&#39;s the problem. 51:58 Maria K.: I think that&#39;s why, maybe. You know, I&#39;m trying to find a difference. 52:20 Maria K.: All right, I&#39;m trying to find a difference, and to explain to you the,
uh… document here. Down here is the body, it&#39;s the line item level. I don&#39;t care about that. 52:31 Michael H.: Okay. 52:31 Maria K.: I care about the ISA. And receiver, that&#39;s the envelope, right? That&#39;s what says Who&#39;s the sender? Who&#39;s receiving the control number of that? File. So, what I&#39;m noticing here, I&#39;m just comparing the two. So this is the same. This is a sender, that&#39;s correct. I&#39;m just checking every little detail. This says 23 because it&#39;s just sending files are brand new, so it&#39;s okay. Uh, the… the version of the document type is correct. 53:09 Maria K.: But what I noticed here, as you can see, you see, this is the… this is the receiver, right? This is their receiver. But if you scroll here. We got… the dates, right? 53:27 Michael H.: Yes. 53:27 Maria K.: We got this data, we got the U, uh, for, uh, I&#39;ll show you where you can see that. Really, that&#39;s a version of the document. And then this is a version tie. This is the control, and after the control, they&#39;re adding a 1. I&#39;m pretty sure if they send us… a… A, uh, one with a zero? With zero here, it will generate 997. Let me see if I can test that myself. 54:03 Maria K.: I&#39;m gonna try. Let&#39;s give it a try, right? All right, let me take a screenshot. This is what I can identify that looks different on the age 75, because the A75… I mean, the 997s are generated automatically. 54:09 Michael H.: Yes. 54:20 Maria K.: We don&#39;t control that. So, I&#39;m gonna manipulate… by inbound. Um. They choose the difference. I&#39;m gonna… check what setting is this, because I need to tell her. That segment ends. Let&#39;s check that out. That&#39;s going to… edit mode… And, uh. 55:37 Maria K.: Yeah, uh, yeah. Yeah, that one. Okay. Yeah, Acknowledgement 1. So I think the acknowledgement type That&#39;s what&#39;s causing the… creating the, uh… as you can see, do you see the 7 terminators? Or… is this? And this is the element separator, which is the asterisk, and then the separator is this. The, like, so, uh, it&#39;s called Silk Elean Separating, okay? And there would be characters with this, too. There is a vaccine that we sell that. I used to be… work on part of, uh, it&#39;s called primary configurations. In the configurations, that&#39;s what you set up
this, but we don&#39;t set this up. It&#39;s automatically done anyways. But if there is an issue, they&#39;re saying, oh, you&#39;re sending us Iran. 56:36 Maria K.: Burned him and brought Terminator. Ben and I go in and let But you see the acknowledgement says one. I think that&#39;s my problem, I swear. 56:48 Maria K.: Well, I&#39;m gonna test that out, and… I&#39;m gonna see if I&#39;m a threat. And I need to double check… That, or no doubt. 57:09 Michael H.: Oh, I can&#39;t hear you, Maria, again. Yes, yeah. Yes. 57:10 Maria K.: Oh, sorry, can you hear me now? Can you hear me now? Okay, sorry. Sorry. Uh, let me check if our notepad was the same. Okay, yep, you see that? The acknowledgement is zero. Let me see if we can play with this. I always just have to delete that PO from the customer&#39;s account. When I say injects. I&#39;m gonna say plus, or something like that. Um, okay. Okay, so this one is… No cats… And 75 with the… Larry, actually, you might not say it, okay? 58:12 Michael H.: Okay. 58:12 Maria K.: Maybe I add Lichman. And I&#39;m gonna give you the length of this ticket, you can see my notes. 58:23 Maria K.: Okay? Uh… okay. All right. As value, uh… Zero. And that you would say, imports… Acknowledge, man. A value of… One. Okay. 59:04 Michael H.: Yes. 59:09 Maria K.: So in this, these have to stay out of the box into any, you know. Any possibilities, right? Just come for the documents. So, I&#39;m like, why are we sending back a response from this source document a value of an ATA1, right? So that&#39;s why I was like, let me check two different files from two different people. It&#39;s the same trading partner, right? So they&#39;re sending the same document, the same 59:36 Maria K.: Specifications for the A75. So, I&#39;m like, okay, it has to be something in there different. That is generating two separate different 997s. And I&#39;m pretty sure that might be the problem, but to confirm that. 59:51 Maria K.: I&#39;m gonna see if I can test it. Ah, amazing website. So, I&#39;m gonna do… I&#39;m gonna grab one of the, uh, USA imports A75, right? And I&#39;m gonna say… Copy to new batch. I&#39;m gonna edit the data. And I&#39;m gonna go in there. And… edit this to zero. I don&#39;t want to say okay. I&#39;m just gonna make sure it&#39;s not showing anywhere else. We&#39;re doing that, um, below.
01:00:44 Maria K.: And then we take a look at… You see? It&#39;s the zero is there. Alrighty, I&#39;m going to delete this, um. Po, because I don&#39;t want about PO there. You know, I don&#39;t want to, like, jack two POs in Worthy eye, because I&#39;m pretty sure it&#39;s going to inject, so… I will log in to their account right now. Just to get rid of that feel, and I&#39;m gonna call it… I&#39;m gonna edit this. I&#39;m gonna add a version. And, uh, actually, let me do a… this version. Can I do this version? No, okay. 01:01:31 Maria K.: Okay, I know where the purchase order is. Where&#39;s my purchase order? Possibly a piercing. My reference number. Where I… where are you? That&#39;s the bill to ship to… Uh, this is my purchase order. Sorry. I&#39;m gonna do tests. Darn touch. Uh, I&#39;m gonna do… uh, yeah, I just need to do tasks. I say, okay, right? Uh, let me see… I&#39;ll see if that would generate $9.97, just to test it out. 01:02:38 Maria K.: Okay. Uranium ion? Your vegetables soon. Okay. Yeah, but they don&#39;t say football. That&#39;s the thing. Um, what&#39;s the account number? Uh, so to find an account number, if I don&#39;t know it. You know, you either go to where I show you, but I also go here. Right. Oh, let me know my password. Let me go in here and type USA Imports. So the account is 2609. I just saw it in the bottom, right here. 01:03:36 Maria K.: Mm-hmm. All right. I have their account open. Just to delete that A75 injects, you know? 01:03:46 Michael H.: Yes. 01:03:46 Maria K.: Because I don&#39;t want to… confused. So I think we&#39;re good to go. I am gonna… Uh, let&#39;s see… save everything that I did? And… Uh, I&#39;m gonna close, and then I&#39;m gonna say OK. Okay. And when I change the date, because it&#39;s… when you copy a badge. It will reflect on today&#39;s date, just so you know. Okay? 01:04:18 Michael H.: Yes. 01:04:25 Maria K.: There&#39;s my vibe. So, a January 997, so… Fingers crossed. That was the problem. Damn. You see that? 01:04:47 Michael H.: Oh, yes. Yes. 01:04:53 Maria K.: It has gas on it. Mm-hmm. 01:04:53 Michael H.: So now, everything&#39;s good. 01:04:58 Maria K.: So, what I&#39;m going to do is… Uh, I don&#39;t know if she will do it, or I will do it. Maybe if I&#39;m gonna say, go out. Let me, uh… Let me take a copy of this.
And put it in my notes. So, that&#39;s the problem. There you go. All right, all of that. Well, define the problem? Okay. Um, by… Because we don&#39;t control the inbounds, so I cannot… change every… inbound to a zero value to generate a correct acknowledgement. 01:05:41 Maria K.: Right? The training partner has to do it, so… Um, let me let them know. 01:05:43 Michael H.: Yes. 01:05:49 Maria K.: So by adding the… Zero. Halyard. On the A75. It will… January, the… I will generate the correct 99 soda. Um… The assignment. You know, that&#39;s not our problem right now. If they run 997s, then send us a good document. 01:06:33 Maria K.: Right? So, let me take a copy of this… Edit… Here… 01:06:34 Michael H.: Yes. 01:06:46 Maria K.: I&#39;m gonna delete the file at probably exactly the amount Let me see. Let me refresh everything. Yeah, that test injector, I just… deleting. Alright, I made me just plaython and writes, like, underneath, you know? But I&#39;ll show you in my other screen now what I&#39;m going to do. So, what I&#39;m going to do here… I don&#39;t want to confuse anybody. Let me keep this one open until I get on my findings. 01:07:09 Michael H.: Yes. 01:07:16 Maria K.: And then I&#39;m gonna go ahead and delete this, because I don&#39;t need it. All right, so let me go into… let me share my screen. Okay? At least you… we both want something new today. 01:07:31 Michael H.: Yes. The notepad part, I was just confused. Yes. 01:07:36 Maria K.: Uh, huh? Yeah, you still confused, or…? It&#39;s basically to make it look pretty, but you, you know, I&#39;ll show you more about notepad when you… play with that. It&#39;s good when you&#39;re, like, doing a hands-on. 01:07:44 Michael H.: Oh my… Yes. 01:07:53 Maria K.: Um, you know, it&#39;s easier. But, alright. So… All right, those are all my notes, right? As you can see, I was identifying what the problem is. 01:08:04 Michael H.: Oh, yes. No, that&#39;s… that&#39;s what, um… How do I go in and see, like, the past tickets of your, so I could see your notes and emails? 01:08:05 Maria K.: Okay. Oh, you can… oh, uh… Well, it… okay, that&#39;s a trick one. So, if you have, like, an issue. Uh… I don&#39;t… because everybody&#39;s different, Michael. Everybody&#39;s different, unless you have a client that has an issue that
they had it in the past. 01:08:33 Maria K.: You can search past tickets of the client, and the search here. By the account number, or, you know. And, uh, maybe pull that up. But if you won… If you&#39;re taking notes right now, right, for this particular scenario. Which is an invalid settlement on the 997, what you need to do, right? What we did was to check the inbound. Compare with other inbound? That were… that was generating a CORET 997s versus this one, right? And… and, uh, you can… I&#39;m gonna give you this link. 01:09:06 Michael H.: Yeah. 01:09:10 Maria K.: You can copy this link on your notes and your e-notes. And Red Friends. To my ticket. Uh, let me go… So if that&#39;s helpful, you know? 01:09:18 Michael H.: Yes. 01:09:22 Maria K.: So, every time we work on a ticket, you can copy that link and always go back to it and read it. 01:09:27 Maria K.: Okay? All right. 01:09:28 Michael H.: Okay, yes. 01:09:38 Maria K.: I want to show Nico this. Problem. Uh, let me see, are you… And… very local. 01:09:52 Maria K.: Um, to 20 years. And we, um, San Nico the link I sent you to join this meeting. Oh, actually, I want to invite Lauren, too. 01:10:20 Maria K.: They&#39;re like, you know, when I find something new, I like people to… learning. 01:10:37 Michael H.: Yeah, I&#39;m going through the ticket and looking at everything. 01:11:03 Nicolas S.: Hello, folks. 01:11:06 Maria K.: Hi, I sent it to Lauren to if she wants to join, and… because I, you know, when I find something new, a new problem, and I discover it, I found the solution, I like to share. 01:11:17 Maria K.: I, I agree. You guys get the same… uh, problem. 01:11:17 Nicolas S.: Cool. 01:11:23 Maria K.: Uh, is Lauren in here, or…? I don&#39;t know if you cannot hear that. 01:11:34 Maria K.: He&#39;s not in. 01:11:36 Nicolas S.: Michael! What&#39;s up there. 01:11:36 Michael H.: Oh, hi, Nico.
01:11:39 Maria K.: All right, I&#39;m just gonna go ahead and jump in and… And she&#39;ll show you? So, let me share… I might… are you guys living on my ticket, both of you right now, right? So, the issue here was… True farmers, right? The ADI provider. 01:11:58 Maria K.: Send an email to Marianne. Should it come to us? Saying, hey. We receive… we&#39;re receiving invalid 997s from you guys. Right here, with an unexpected TA1 segment. After the… after the… I messed up this. And I say that it shouldn&#39;t be a GS segment, right? Instead… I&#39;m like, okay, we generated NI7s automatically, right? We don&#39;t control that mapping. 01:12:32 Nicolas S.: Mm-hmm. 01:12:32 Maria K.: And how do we fix that? Right? So, my… you know, I did several troubleshooting. Which was, first, comparing And 997 from… Uh, new cow. Because we only have two clients that said, uh, 997s do save a lot. Right? And documents that save a lot. And I was checking the Newcastles, new cows looks good. They don&#39;t… they don&#39;t have that… 81 segment, they, they have the, uh… the, uh, sorry, they have the DS on it, right? As you can see. 01:12:55 Nicolas S.: Yeah. Mm-hmm. 01:13:15 Maria K.: Here, it has a GS, and then here has an ATA1 with a control number, right? So… so, okay, then I&#39;m like, okay. So, there has to be a problem, because we generate the response based on a source, right? And the source is… the 75, right? 01:13:20 Nicolas S.: Mm-hmm. 01:13:35 Maria K.: So, the 75s… I was like, okay, let me confer… New crowds in USA in Port 75. When I did my comparing, the only difference that I can… I don&#39;t care what happens on the bottom, in the line item level, it&#39;s the envelope, right? Which… that, um, ISA and all of that. So, uh, the only difference I discovered was that NewCal had a zero. 01:13:53 Nicolas S.: Yeah. 01:14:06 Maria K.: After the… the, um… this data here, which is not the confirmed… after the control number, sorry. And this one had a 1 to it. Which I thought… This might be the problem. Then I went in. 01:14:22 Maria K.: And I&#39;m gonna… then I went in and checked the envelope. Bar, here. Inside to see where that zero was belonging to. And this is the
acknowledgement. So, in NoCal, they had a zero. And they haven&#39;t won, right? 01:14:40 Nicolas S.: Mm-hmm. 01:14:40 Maria K.: So, what I did was… to test it out and confirm that the inbound document, the A75 they&#39;re sending us, is invalid, with an invalid interchange acknowledgement. Changed a setup to one. Is creating this acknowledgement, uh, uh, version. With an ATA segment in it. Right? So… I went in, copy. Their batch. Let me go back to… let me share… Because I want to share everything, um… Where&#39;s my thing, Chad? Okay. And I&#39;m going to share here. All right. Then I went in, and I copied The A75. To a new batch. Change my reference to text. 01:15:35 Maria K.: So that way the customer doesn&#39;t get confused in case it injects quickly and they&#39;re looking at our POs, right? 01:15:41 Nicolas S.: Yeah. 01:15:41 Maria K.: Change it to task. Because I was going to inject to the WebDI. And I change it to the zero value within the acknowledgement. Right? I save it. And then they generated the… the 997, right? And great. After I generated the 997, And I go here, delivery… vibration. I went in. And I… and I saw that my test 997… sorry, that&#39;s the same spot. Generated with a GS7? Let&#39;s see… Oh, no, that&#39;s the AP, that&#39;s a bad one. We&#39;re looking at the bad one. 01:16:28 Maria K.: Uh, where&#39;s my Google? Where does the Google one that we saw? Alright, yeah, I think it&#39;s this one. Oh, no, it&#39;s 141. Should do this. I&#39;m gonna say… Yeah, let me say, let me just… Yeah, I just… I just literally did it, um… Uh, let me change… 01:17:04 Nicolas S.: What was it, sent? 01:17:22 Maria K.: I literally did it. Hold on, let me show you the copy of it. Right? Um, uh, we opened it, and it looked good. So here, let me share this screen, because I think I… I got rid of it. Hold on. Where are you? We did Zoom, me and Michael, because he wanted to record You know, and you can&#39;t record Slack. 01:17:47 Maria K.: Um, here. Okay, share. Alright, this is the copy that I took, the ISA that generated, right? 01:17:53 Nicolas S.: Yeah. 01:17:53 Maria K.: So, this is as… you see it says test? The A75, but… it degenerated without… With our, uh… the ATA. Oh my god, Michael, didn&#39;t we look at it just now?
01:18:14 Michael H.: Yes. 01:18:14 Nicolas S.: Why don&#39;t you find it in the sent folder? 01:18:16 Maria K.: Yeah, no, no, no, not in the sand. Remember, we don&#39;t send 997s from WebVDI, um… 01:18:23 Nicolas S.: No, not, not in YBDA and ECS. Like, the… 01:18:27 Maria K.: And let me go back. Uh, let me go back there… And go into… I wonder who is still generating that. But it didn&#39;t know, I immediately was there. Are you guys looking at my… my… my server? 01:18:48 Nicolas S.: Yeah, ECSO2. 01:18:49 Maria K.: Okay, cool. All right, let&#39;s do this. Um… Let me do USA… No, let me do a knockout. Hold on. Huh. Oh, let me send that to you, okay? One second. Right. All right. Sorry, let me send this confirmation code. Okay. Interesting. Alright, so USA. I don&#39;t mind like this. What the heck am I doing? Save a lot. This is the one we did today, right? So I went in… Can I remove? And I change it to a zero. And then, at the auto thing, which is the… 97… I wonder if it will create an… This is NA75. This went in, I said. Nico, does that work for you? Create a 997 from the 850, I put a no on the team, nobody responded. It&#39;s not working for me. I see Sally doing. 01:21:05 Nicolas S.: Let me go check. 01:21:06 Maria K.: But I know that&#39;s a problem. That&#39;s the fix. 01:21:13 Maria K.: Hello. Give me one second. Hold on. Hold on, guys. It is X, Z as in zebra, 7, F as in Frank, 9, 8, S as in Sam. Okay, fine. Um. So, do you… are you able to, Nico? 01:21:43 Nicolas S.: I&#39;m checking right now. 01:21:48 Maria K.: But I know that&#39;s the problem. 01:21:55 Maria K.: Type, that creates. 01:21:57 Nicolas S.: Yeah, I can create it. 01:21:58 Maria K.: Oh, and I see it? Why again not? That&#39;s bugging me. Uh, I&#39;m gonna stop sharing. You can share. Anybody here can share. Okay, we&#39;re gonna install… I&#39;ll stop sharing. Okay, fair, Nico. I want to see if that will fix it. 01:22:26 Nicolas S.: Yeah. So… Tools, create FA… There it is. 01:22:26 Maria K.: Oh, mmm… Yeah. They do. Can you click on the 997? And can you click on task version? Can I make a bigger piece?
01:22:45 Nicolas S.: Can&#39;t… 01:22:48 Maria K.: Um, oh… He, after the B, Y, C. 01:23:00 Nicolas S.: Gs. 01:23:01 Maria K.: Perfect. That makes sense. Okay, can you send me that? 01:23:07 Nicolas S.: Screenshot. 01:23:08 Maria K.: Uh, I want you to actually, uh, copy the data, not a screenshot. Oh, you can send a screenshot, too. 01:23:15 Maria K.: Okay. And that&#39;s a good slap. Forward. Yeah, slap. So yeah, that fixed the problem. So that acknowledgement thing on the inbounds. 01:23:29 Maria K.: Is… it does… it does make a difference for the our knowledge from inside. In case you get something like that, that somebody&#39;s getting, like, an invalid with an invalid segment, Nico? That&#39;s where you go. Yeah, I&#39;m gonna send that to TrueCommerce, and I&#39;ll be like, well. When they send us correct 875s. That… 01:23:58 Nicolas S.: Well, yeah, it&#39;s… tell me specifically the ISA14. Yeah. 01:24:05 Maria K.: Isa14, right? Okay, because I can&#39;t see it. I… 01:24:09 Nicolas S.: I pulled up the… the specs. 01:24:14 Maria K.: Yeah. Of one. Obviously, you have the space? 01:24:24 Nicolas S.: Right? 01:24:29 Maria K.: Hold on. Yeah, that&#39;s on the… that&#39;s the A75 link what you&#39;re looking at? 01:24:42 Nicolas S.: Yep. 01:24:44 Maria K.: Yeah, let me take a screenshot of that. Save it. 01:24:56 Michael H.: Um, Maria, um, right now it&#39;s my lunch through ICO, uh, am I allowed to… Okay. 01:24:56 Maria K.: Yep. Oh, no, go to lunch. Yeah. Go to lunch, yeah, yeah. I&#39;ll take lunch too, so that way we kind of come back at the same time, okay? 01:25:04 Michael H.: Yes. Okay, yes, thank you. 01:25:10 Maria K.: I&#39;ll see you later. 01:25:10 Michael H.: Okay, bye, Nico. Bye, Laura.
View original transcript at Tactiq.</p>
