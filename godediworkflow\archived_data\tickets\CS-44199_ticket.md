# CS-44199: Notification to all Jscape MFT Users (Dev Environment) - Need SSH Key + Password configured for all Jscape sftp users by 06/07/2025

## Ticket Information
- **Key**: CS-44199
- **Status**: In Progress
- **Priority**: Medium
- **Assignee**: Likhith P
- **Reporter**: <EMAIL>
- **Created**: 2025-05-22T03:25:09.408-0600
- **Updated**: 2025-06-13T09:12:24.936-0600
- **Customer**: <EMAIL>

## Description
| {color:#172B4D}{color} *{color:white} *Notification for all Jscape SFTP users (Dev Environment)*{color}* | 
|  *{color:#404040} *TO:*{color}* | All JSCAPE {color:#404040}MFT users{color} (Dev Environment) | 
|  *{color:#404040} *FROM:*{color}* | {color:#404040}Halliburton{color} MFT Team | 
|  *{color:#404040} *SUBJECT:*{color}* | Security Requirement- Need a 2048-bit SSH Public Key + Password configured for all Jscape SFTP users | 
| You are receiving this message since you have been identified as an SFTP user for Halliburton JSCAPE. As part of our ongoing efforts to enhance security and adhere to Halliburton IT security best practices, we are implementing multi-factor authentication for our SFTP users, which is the addition of  *SSH key authentication.* SFTP users are those users who connect via SFTP protocol and use the hostname – ftpdevjscape.halliburton.com and port 22 and connect via tools like WinSCP etc. To ensure a seamless migration and to minimize any potential impact on your operation, we are reaching out to you to provide us the  *SSH key authentication* in advance.  *Action Required (these steps will have to be followed twice, once each for Dev and prod accounts):* #  *Key Generation and Configuration:* Kindly create an SSH key with 2048 bit or higher encryption using the steps provided in document attached. Configure the private key in your tool and send the public key in .pub format to FTP support team ([{color:windowtext}<EMAIL>{color}|mailto:<EMAIL>]).The public key in .pub format will be configured by FTP support team in your account. 
#  *Testing:* Once configured, please verify if you can access and transfer files using sftp protocol using the sftp tool of your choice. 
#  *Reporting Issues/Concerns:* If you encounter any issues or have concerns, please drop a detailed email to [{color:windowtext}<EMAIL>{color}|mailto:<EMAIL>]. Your prompt communication will assist our FTP Support Team in addressing any challenges that may arise. 

 *Note : Access to all SFTP accounts without password and SSH key enabled will be revoked on 06/07/2025 for Dev environment.* 
We appreciate your cooperation and valuable contribution to this transition process. 
Thank you for your attention to this matter, and please feel free to reach out if you have any questions or require further assistance. | 
| {color:white}This email, including any attached files, contains confidential and privileged information and is for intended recipients only. Any review, use, distribution, or disclosure by others is strictly prohibited. If you are not the intended recipient (or authorized to receive information for the intended recipient), please contact the sender by reply mail and delete all copies of this message.{color} |
 

                 This e-mail, including any attached files, may contain confidential and privileged information for the sole use of the intended recipient. Any review, use, distribution, or disclosure by others is strictly prohibited. If you are not the intended recipient (or authorized to receive information for the intended recipient), please contact the sender by reply e-mail and delete all copies of this message.

## Components


## Labels

