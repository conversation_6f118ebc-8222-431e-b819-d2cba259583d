"""
Root Cause Analyzer - Intelligent Pattern Detection and Causal Analysis
Identifies the actual root causes of EDI issues from ticket clusters
"""

import re
import json
import sqlite3
import logging
from typing import Dict, List, Optional, Tuple, Set
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from dataclasses import dataclass
import numpy as np
from sklearn.cluster import DBSCAN
from sklearn.feature_extraction.text import TfidfVectorizer
import networkx as nx

from config import settings, DATABASE_PATH

logger = logging.getLogger(__name__)


@dataclass
class RootCauseHypothesis:
    """A hypothesis about the root cause of an issue"""
    cause: str
    confidence: float
    evidence: List[str]
    affected_count: int
    pattern: str
    remediation: str
    similar_past_issues: List[Dict]


@dataclass
class CausalChain:
    """Represents a causal chain from symptom to root cause"""
    symptom: str
    intermediate_causes: List[str]
    root_cause: str
    confidence: float
    supporting_tickets: List[str]


class RootCauseAnalyzer:
    """
    Analyzes patterns in EDI tickets to identify root causes
    Uses clustering, pattern matching, and causal inference
    """
    
    # Known root cause patterns
    ROOT_CAUSE_PATTERNS = {
        'partner_config_change': {
            'indicators': ['suddenly', 'was working', 'stopped working', 'since', 'changed'],
            'patterns': [
                r'worked until \d+[/-]\d+',
                r'failing since',
                r'started (failing|erroring)',
                r'no changes on our end'
            ],
            'common_causes': [
                'Trading partner updated specifications',
                'Partner changed validation rules',
                'Partner modified data format requirements'
            ]
        },
        'certificate_issue': {
            'indicators': ['certificate', 'expired', 'SSL', 'TLS', 'handshake', 'trust'],
            'patterns': [
                r'certificate.*expir',
                r'SSL.*error',
                r'handshake.*fail',
                r'trust.*certif'
            ],
            'common_causes': [
                'Certificate expired',
                'Certificate not trusted',
                'Certificate chain incomplete'
            ]
        },
        'format_change': {
            'indicators': ['format', 'invalid', 'unexpected', 'mismatch', 'parsing'],
            'patterns': [
                r'invalid.*format',
                r'expected.*but.*received',
                r'format.*changed',
                r'parsing.*error'
            ],
            'common_causes': [
                'Date format changed (YYMMDD to YYYYMMDD)',
                'Time format changed (HHMM to HHMMSS)',
                'Decimal precision changed',
                'Field length requirements changed'
            ]
        },
        'network_connectivity': {
            'indicators': ['timeout', 'connection', 'refused', 'unreachable', 'firewall'],
            'patterns': [
                r'connection.*timeout',
                r'connection.*refused',
                r'host.*unreachable',
                r'firewall.*block'
            ],
            'common_causes': [
                'Firewall rule blocking connection',
                'Network routing issue',
                'DNS resolution failure',
                'Port not open/listening'
            ]
        },
        'data_quality': {
            'indicators': ['missing', 'required', 'mandatory', 'blank', 'null'],
            'patterns': [
                r'missing.*required',
                r'mandatory.*field',
                r'cannot be (blank|null|empty)',
                r'required.*not found'
            ],
            'common_causes': [
                'Source system not populating required fields',
                'Data mapping incomplete',
                'Business rule validation failure'
            ]
        }
    }
    
    def __init__(self, db_path: str = None):
        """Initialize the Root Cause Analyzer"""
        self.db_path = db_path or str(DATABASE_PATH)
        self.vectorizer = TfidfVectorizer(max_features=500, stop_words='english')
        self.causal_graph = nx.DiGraph()
        self._load_historical_patterns()
    
    def analyze_ticket_cluster(self, tickets: List[Dict], 
                             time_window: Optional[timedelta] = None) -> List[RootCauseHypothesis]:
        """
        Analyze a cluster of tickets to identify root causes
        
        Args:
            tickets: List of ticket dictionaries
            time_window: Optional time window to focus on
            
        Returns:
            List of root cause hypotheses ranked by confidence
        """
        if not tickets:
            return []
        
        # Filter by time window if specified
        if time_window:
            cutoff_time = datetime.now() - time_window
            tickets = [t for t in tickets if self._parse_date(t.get('created', '')) > cutoff_time]
        
        # Extract features from tickets
        features = self._extract_features(tickets)
        
        # Perform clustering to group similar issues
        clusters = self._cluster_tickets(tickets, features)
        
        # Analyze each cluster for root causes
        hypotheses = []
        for cluster_id, cluster_tickets in clusters.items():
            if cluster_id == -1:  # Skip noise cluster
                continue
                
            # Generate hypotheses for this cluster
            cluster_hypotheses = self._analyze_cluster(cluster_tickets)
            hypotheses.extend(cluster_hypotheses)
        
        # Rank hypotheses by confidence and affected count
        hypotheses.sort(key=lambda h: (h.confidence * h.affected_count), reverse=True)
        
        return hypotheses[:10]  # Return top 10 hypotheses
    
    def trace_causal_chain(self, symptom: str, tickets: List[Dict]) -> Optional[CausalChain]:
        """
        Trace the causal chain from a symptom to root cause
        
        Args:
            symptom: The observed symptom/error
            tickets: Related tickets to analyze
            
        Returns:
            CausalChain object if found, None otherwise
        """
        # Build causal graph from tickets
        self._build_causal_graph(tickets)
        
        # Find symptom node in graph
        symptom_node = self._find_closest_node(symptom)
        if not symptom_node:
            return None
        
        # Trace back to root cause
        root_causes = []
        for node in self.causal_graph.nodes():
            if self.causal_graph.out_degree(node) == 0:  # Leaf node (potential root)
                try:
                    path = nx.shortest_path(self.causal_graph, symptom_node, node)
                    if len(path) > 1:
                        root_causes.append((node, path))
                except nx.NetworkXNoPath:
                    continue
        
        if not root_causes:
            return None
        
        # Select most likely root cause
        best_root, best_path = max(root_causes, 
                                  key=lambda x: self._calculate_path_confidence(x[1]))
        
        return CausalChain(
            symptom=symptom,
            intermediate_causes=best_path[1:-1],
            root_cause=best_root,
            confidence=self._calculate_path_confidence(best_path),
            supporting_tickets=[t['ticket_key'] for t in tickets[:5]]
        )
    
    def identify_emerging_patterns(self, time_window: timedelta = timedelta(hours=24)) -> List[Dict]:
        """
        Identify emerging patterns that might indicate new issues
        
        Args:
            time_window: Time window to analyze
            
        Returns:
            List of emerging patterns with metadata
        """
        # Get recent tickets
        recent_tickets = self._get_recent_tickets(time_window)
        
        # Extract patterns
        patterns = defaultdict(list)
        for ticket in recent_tickets:
            ticket_patterns = self._extract_patterns(ticket)
            for pattern_type, pattern_value in ticket_patterns:
                patterns[pattern_type].append({
                    'value': pattern_value,
                    'ticket': ticket['ticket_key'],
                    'time': ticket['created']
                })
        
        # Identify emerging patterns
        emerging = []
        for pattern_type, occurrences in patterns.items():
            if len(occurrences) >= 3:  # At least 3 occurrences
                # Check if this is new (not in historical patterns)
                if not self._is_historical_pattern(pattern_type, occurrences[0]['value']):
                    emerging.append({
                        'pattern_type': pattern_type,
                        'pattern_value': occurrences[0]['value'],
                        'occurrence_count': len(occurrences),
                        'first_seen': min(o['time'] for o in occurrences),
                        'affected_tickets': [o['ticket'] for o in occurrences],
                        'severity': self._calculate_pattern_severity(pattern_type, len(occurrences))
                    })
        
        return sorted(emerging, key=lambda x: x['severity'], reverse=True)
    
    def _extract_features(self, tickets: List[Dict]) -> np.ndarray:
        """Extract features from tickets for clustering"""
        # Combine text fields
        texts = []
        for ticket in tickets:
            text = f"{ticket.get('summary', '')} {ticket.get('description', '')} {ticket.get('resolution', '')}"
            texts.append(text)
        
        # Vectorize texts
        features = self.vectorizer.fit_transform(texts)
        return features.toarray()
    
    def _cluster_tickets(self, tickets: List[Dict], features: np.ndarray) -> Dict[int, List[Dict]]:
        """Cluster similar tickets using DBSCAN"""
        clustering = DBSCAN(eps=0.3, min_samples=2, metric='cosine').fit(features)
        
        # Group tickets by cluster
        clusters = defaultdict(list)
        for i, label in enumerate(clustering.labels_):
            clusters[label].append(tickets[i])
        
        return clusters
    
    def _analyze_cluster(self, cluster_tickets: List[Dict]) -> List[RootCauseHypothesis]:
        """Analyze a cluster of similar tickets to identify root causes"""
        hypotheses = []
        
        # Check against known root cause patterns
        for cause_type, pattern_info in self.ROOT_CAUSE_PATTERNS.items():
            score = self._score_pattern_match(cluster_tickets, pattern_info)
            
            if score > 0.5:  # Threshold for considering a pattern
                # Find the most likely specific cause
                cause_counts = Counter()
                evidence = []
                
                for ticket in cluster_tickets:
                    text = f"{ticket.get('summary', '')} {ticket.get('description', '')}"
                    
                    # Check each specific cause
                    for cause in pattern_info['common_causes']:
                        if self._text_matches_cause(text, cause):
                            cause_counts[cause] += 1
                            evidence.append(f"{ticket['ticket_key']}: {text[:100]}...")
                
                if cause_counts:
                    best_cause = cause_counts.most_common(1)[0][0]
                    confidence = score * (cause_counts[best_cause] / len(cluster_tickets))
                    
                    hypothesis = RootCauseHypothesis(
                        cause=best_cause,
                        confidence=confidence,
                        evidence=evidence[:5],  # Top 5 evidence items
                        affected_count=len(cluster_tickets),
                        pattern=cause_type,
                        remediation=self._get_remediation(cause_type, best_cause),
                        similar_past_issues=self._find_similar_past_issues(best_cause)
                    )
                    hypotheses.append(hypothesis)
        
        return hypotheses
    
    def _score_pattern_match(self, tickets: List[Dict], pattern_info: Dict) -> float:
        """Score how well tickets match a root cause pattern"""
        total_score = 0.0
        
        for ticket in tickets:
            text = f"{ticket.get('summary', '')} {ticket.get('description', '')}".lower()
            
            # Check indicators
            indicator_matches = sum(1 for indicator in pattern_info['indicators'] 
                                  if indicator in text)
            
            # Check regex patterns
            pattern_matches = sum(1 for pattern in pattern_info['patterns']
                                if re.search(pattern, text, re.IGNORECASE))
            
            # Calculate ticket score
            ticket_score = (indicator_matches / len(pattern_info['indicators']) * 0.6 +
                          pattern_matches / len(pattern_info['patterns']) * 0.4)
            
            total_score += ticket_score
        
        return total_score / len(tickets)
    
    def _text_matches_cause(self, text: str, cause: str) -> bool:
        """Check if text matches a specific cause"""
        # Simple matching for now - can be enhanced with NLP
        cause_keywords = cause.lower().split()
        text_lower = text.lower()
        
        matches = sum(1 for keyword in cause_keywords if keyword in text_lower)
        return matches >= len(cause_keywords) * 0.5
    
    def _get_remediation(self, pattern_type: str, specific_cause: str) -> str:
        """Get remediation steps for a root cause"""
        remediations = {
            'partner_config_change': {
                'default': 'Contact trading partner to confirm specification changes',
                'Trading partner updated specifications': 'Request latest specifications from partner and update mappings',
                'Partner changed validation rules': 'Review partner validation requirements and adjust data'
            },
            'certificate_issue': {
                'default': 'Check certificate validity and renewal',
                'Certificate expired': 'Renew certificate immediately and update in all systems',
                'Certificate not trusted': 'Add certificate to trust store or obtain from trusted CA'
            },
            'format_change': {
                'default': 'Review and update data format mappings',
                'Date format changed (YYMMDD to YYYYMMDD)': 'Update date format in mapping to YYYYMMDD',
                'Time format changed (HHMM to HHMMSS)': 'Update time format to include seconds'
            },
            'network_connectivity': {
                'default': 'Check network connectivity and firewall rules',
                'Firewall rule blocking connection': 'Request firewall rule update for required ports/IPs',
                'DNS resolution failure': 'Verify DNS entries and update if necessary'
            },
            'data_quality': {
                'default': 'Review source data and mapping configuration',
                'Source system not populating required fields': 'Work with source system team to ensure data completeness',
                'Data mapping incomplete': 'Update mapping to include all required fields'
            }
        }
        
        pattern_remediations = remediations.get(pattern_type, {})
        return pattern_remediations.get(specific_cause, pattern_remediations.get('default', 'Investigate and resolve'))
    
    def _find_similar_past_issues(self, cause: str, limit: int = 3) -> List[Dict]:
        """Find similar past issues from the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Search for similar resolved issues
            cause_keywords = cause.lower().split()[:3]  # Use first 3 keywords
            where_clause = " OR ".join([f"resolution LIKE '%{kw}%'" for kw in cause_keywords])
            
            query = f"""
            SELECT ticket_key, summary, resolution, updated
            FROM jira_tickets
            WHERE status = 'Done' 
            AND ({where_clause})
            ORDER BY updated DESC
            LIMIT {limit}
            """
            
            cursor.execute(query)
            results = []
            for row in cursor.fetchall():
                results.append({
                    'ticket_key': row[0],
                    'summary': row[1],
                    'resolution': row[2],
                    'resolved_date': row[3]
                })
            
            conn.close()
            return results
            
        except Exception as e:
            logger.error(f"Error finding similar issues: {e}")
            return []
    
    def _build_causal_graph(self, tickets: List[Dict]):
        """Build a causal graph from ticket data"""
        self.causal_graph.clear()
        
        # Extract cause-effect relationships
        for ticket in tickets:
            text = f"{ticket.get('description', '')} {ticket.get('resolution', '')}"
            
            # Look for causal indicators
            causal_patterns = [
                (r'caused by (.+?)(?:\.|,|;|$)', 'effect', 'cause'),
                (r'due to (.+?)(?:\.|,|;|$)', 'effect', 'cause'),
                (r'(.+?) resulted in (.+?)(?:\.|,|;|$)', 'cause', 'effect'),
                (r'(.+?) led to (.+?)(?:\.|,|;|$)', 'cause', 'effect')
            ]
            
            for pattern, first_role, second_role in causal_patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    if first_role == 'cause':
                        cause = match.group(1).strip()
                        effect = match.group(2).strip()
                    else:
                        effect = ticket.get('summary', 'Unknown issue')
                        cause = match.group(1).strip()
                    
                    # Add to graph
                    self.causal_graph.add_edge(cause, effect)
    
    def _find_closest_node(self, symptom: str) -> Optional[str]:
        """Find the closest node in the causal graph to a symptom"""
        symptom_lower = symptom.lower()
        best_match = None
        best_score = 0
        
        for node in self.causal_graph.nodes():
            node_lower = node.lower()
            # Simple similarity score
            score = len(set(symptom_lower.split()) & set(node_lower.split()))
            if score > best_score:
                best_score = score
                best_match = node
        
        return best_match if best_score > 0 else None
    
    def _calculate_path_confidence(self, path: List[str]) -> float:
        """Calculate confidence score for a causal path"""
        # Simple confidence based on path length and edge weights
        base_confidence = 1.0 / (1 + len(path))
        
        # Adjust based on graph properties
        for i in range(len(path) - 1):
            edge_weight = self.causal_graph.edges[path[i], path[i+1]].get('weight', 1.0)
            base_confidence *= edge_weight
        
        return min(base_confidence, 0.95)
    
    def _get_recent_tickets(self, time_window: timedelta) -> List[Dict]:
        """Get tickets from the specified time window"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cutoff_time = (datetime.now() - time_window).isoformat()
            
            query = """
            SELECT ticket_key, summary, description, resolution, created, category
            FROM jira_tickets
            WHERE created > ?
            ORDER BY created DESC
            """
            
            cursor.execute(query, (cutoff_time,))
            
            tickets = []
            for row in cursor.fetchall():
                tickets.append({
                    'ticket_key': row[0],
                    'summary': row[1],
                    'description': row[2],
                    'resolution': row[3],
                    'created': row[4],
                    'category': row[5]
                })
            
            conn.close()
            return tickets
            
        except Exception as e:
            logger.error(f"Error getting recent tickets: {e}")
            return []
    
    def _extract_patterns(self, ticket: Dict) -> List[Tuple[str, str]]:
        """Extract patterns from a ticket"""
        patterns = []
        text = f"{ticket.get('summary', '')} {ticket.get('description', '')}"
        
        # Extract error codes
        error_codes = re.findall(r'[A-Z]{2,}\d{3,}', text)
        for code in error_codes:
            patterns.append(('error_code', code))
        
        # Extract partner names
        for partner in self.ROOT_CAUSE_PATTERNS['partner_config_change']['indicators']:
            if partner.lower() in text.lower():
                patterns.append(('partner', partner))
        
        return patterns
    
    def _is_historical_pattern(self, pattern_type: str, pattern_value: str) -> bool:
        """Check if a pattern exists in historical data"""
        # Simplified check - in production would check against historical database
        return False
    
    def _calculate_pattern_severity(self, pattern_type: str, occurrence_count: int) -> int:
        """Calculate severity score for an emerging pattern"""
        base_severity = {
            'error_code': 5,
            'partner': 7,
            'connection': 8,
            'format': 6
        }.get(pattern_type, 5)
        
        # Increase severity based on occurrence count
        return min(base_severity + (occurrence_count // 3), 10)
    
    def _load_historical_patterns(self):
        """Load historical patterns from database"""
        # Placeholder - would load from database in production
        logger.info("Historical patterns loaded")
    
    def _parse_date(self, date_str: str) -> datetime:
        """Parse date string to datetime"""
        try:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except:
            return datetime.min


def demonstrate_root_cause_analysis():
    """Demonstrate the Root Cause Analyzer"""
    analyzer = RootCauseAnalyzer()
    
    # Sample tickets for demonstration
    sample_tickets = [
        {
            'ticket_key': 'CS-12001',
            'summary': 'Walmart 856 ASN failing - Invalid date format',
            'description': 'Started failing this morning. Error: Date format invalid in DTM segment',
            'resolution': 'Walmart changed date format from YYMMDD to YYYYMMDD',
            'created': datetime.now().isoformat(),
            'category': 'EDI'
        },
        {
            'ticket_key': 'CS-12002', 
            'summary': 'Walmart 856 validation errors',
            'description': 'ASN rejected with date format error. Was working yesterday.',
            'resolution': '',
            'created': datetime.now().isoformat(),
            'category': 'EDI'
        }
    ]
    
    print("Root Cause Analyzer Demonstration\n")
    print("=" * 60)
    
    # Analyze ticket cluster
    hypotheses = analyzer.analyze_ticket_cluster(sample_tickets)
    
    for i, hypothesis in enumerate(hypotheses, 1):
        print(f"\nHypothesis {i}:")
        print(f"Root Cause: {hypothesis.cause}")
        print(f"Confidence: {hypothesis.confidence:.2%}")
        print(f"Affected Tickets: {hypothesis.affected_count}")
        print(f"Pattern Type: {hypothesis.pattern}")
        print(f"Remediation: {hypothesis.remediation}")
        print(f"Evidence: {hypothesis.evidence[0] if hypothesis.evidence else 'N/A'}")
    
    # Demonstrate causal chain
    print("\n" + "=" * 60)
    print("Causal Chain Analysis:")
    
    chain = analyzer.trace_causal_chain("Invalid date format", sample_tickets)
    if chain:
        print(f"Symptom: {chain.symptom}")
        print(f"Root Cause: {chain.root_cause}")
        print(f"Causal Path: {' → '.join([chain.symptom] + chain.intermediate_causes + [chain.root_cause])}")
        print(f"Confidence: {chain.confidence:.2%}")


if __name__ == "__main__":
    demonstrate_root_cause_analysis()