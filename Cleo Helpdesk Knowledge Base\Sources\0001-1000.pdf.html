<h4 id="cs-44826-needed-assistance-creating-an-855-document-po-rejection-created-02-jun-25-updated-02-jun-25-resolved-02-jun-25">[CS-44826] Needed assistance creating an 855 document PO rejection Created: 02/Jun/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Phone call
Request language: English
Request participants: None
Organizations: Label: WebEDI Questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Customer called needed to cancel an order
Advise to create the 855 document rejecting the PO</p>
<h4 id="cs-44824-non-compliant-edi-856-asn-po-c6658409whg-shipment-136340-dts7494-created-02-jun-25-updated-02-jun-25-resolved-02-jun-25">[CS-44824] Non-Compliant EDI 856 ASN - PO: C6658409WHG, Shipment: 136340-DTS7494 Created: 02/Jun/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: LINDSEY VENABLE Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: asn
Remaining Estimate: Not Specified
Time Spent: Not Specified Original estimate: Not Specified
Attachments:  image-********-140640.png      image-********-142002.png      image-********-141855.png      image-********-142112.png      image-********-142410.png     image-********-142611.png
Request Type: Support
Request language: English
Request participants: None
Organizations: Label: ASN rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
We are still receiving Non-Compliant notices from Cardinal Health (Pharmaceuticals/PD) regarding the 856 ASN documents sent through DataTrans. See their message below for details. Please make all necessary changes to the ASN form so that the correct information is transmitted to Cardinal Health (Pharmaceuticals/PD).
Thanks,
Lindsey Venable
Medecor Pharma LLC
<a href="mailto:<EMAIL>"><EMAIL></a>
----- Forwarded Message -----
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a>
To: &#34;<a href="mailto:<EMAIL>"><EMAIL></a>&#34; <a href="mailto:<EMAIL>"><EMAIL></a>
Cc: &#34;<a href="mailto:<EMAIL>"><EMAIL></a>&#34; <a href="mailto:<EMAIL>"><EMAIL></a>; &#34;<a href="mailto:<EMAIL>"><EMAIL></a>&#34; <a href="mailto:<EMAIL>"><EMAIL></a>
Sent: Monday, June 2, 2025 at 08:05:50 AM CDT
Subject: Non-Compliant EDI 856 ASN - PO: C6658409WHG, Shipment: 13634
To ,
The EDI 856 that  (from DTS7494) sent to Cardinal Health (CARDINAL) today (********) failed our initial compliance validation due to:</p>
<blockquote>
<p>Missing  N1_01 Missing Ship From info
Missing  N1_02 Missing Ship From Name
Missing  N1_01 H. R. 3204-19  DSCSA Law (I) the business name and address of the person from whom ownership is being transferred is missing
Missing  N1_02 H. R. 3204-19  DSCSA Law (I) the business name and address of the person from whom ownership is being transferred is missing
Missing  N1_03 Missing Seller HIN, DEA or DUNS Number
Missing  N1_04 Missing Seller HIN, DEA or DUNS Number
Missing  N3_01 H. R. 3204-19  DSCSA Law (I) the business name and address of the person from whom ownership is being transferred is missing
Missing  N4 H. R. 3204-19  DSCSA Law (I) the business name and address of the person from whom ownership is being transferred is missing
Missing  N1_01 H. R. 3204-19  DSCSA Law (J) the business name and address of the person to whom ownership is being transferred is missing
Missing  N1_02 H. R. 3204-19  DSCSA Law (J) the business name and address of the person to whom ownership is being transferred is missing
Missing  N1_03 Missing Buyer HIN, DEA or DUNS Number
Missing  N1_04 Missing Buyer HIN, DEA or DUNS Number
Missing  N3_01 H. R. 3204-19  DSCSA Law (J) the business name and address of the person to whom ownership is being transferred is missing
Missing  N4 H. R. 3204-19  DSCSA Law (J) the business name and address of the person to whom ownership is being transferred is missing
Missing  YNQ_05 Missing transaction statement
Missing  YNQ_09 Missing transaction statement YNQ_09 with TS
Missing  PID_05 H. R. 3204-19  DSCSA Law  (A) the proprietary or established name or names of the product and (B) the strength and dosage form of the product are missing
Please send a corrected ASN before your shipment arrives on our dock, otherwise Cardinal Health may refuse to receive it. This deficiency may be reflected in your quarterly deductions/billings. If you have an inquiry, please respond to this e-mail.
Compliantly yours, Track and Trace Compliance Team Cardinal Health
Comments
Comment by Maria Keshwala [ 02/Jun/25 ]
1 - confirmed the customers account
2- check if there are any recent projects or closed projects
3- check if this is the first time they are sending unsuccessful ASNs.
4 If this is the first time they are sending ASNs and the are invalid please reach out to the analyst to correct the mapping errors
5 If they have successful ASNs and then one was invalid we will look into the error .
per below image a recent Project was closed which tells me that this is the first time , second step is to check Webedi and see when they started sending 856s
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 02/Jun/25 ]
Hello
When this error started is it just for this ASN? for PO number PO: C6658409WHG, Shipment: 13634 I see we been sending ASNs since 04/2025 please advise Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 02/Jun/25 ]
So far the EDI 856 document does not have any errors -
Next going over the list of errors
Missing  N1_01 Missing Ship From info - present
Missing  N1_02 Missing Ship From Name
Missing  N1_01 H. R. 3204-19  DSCSA Law (I) the business name and address of the person from whom ownership is being transferred is missing
Missing  N1_02 H. R. 3204-19  DSCSA Law (I) the business name and address of the person from whom ownership is being transferred is missing
Missing  N1_03 Missing Seller HIN, DEA or DUNS Number
Missing  N1_04 Missing Seller HIN, DEA or DUNS Number
Missing  N3_01 H. R. 3204-19  DSCSA Law (I) the business name and address of the person from whom ownership is being transferred is missing
Missing  N4 H. R. 3204-19  DSCSA Law (I) the business name and address of the person from whom ownership is being transferred is missing
Missing  N1_01 H. R. 3204-19  DSCSA Law (J) the business name and address of the person to whom ownership is being transferred is missing
Missing  N1_02 H. R. 3204-19  DSCSA Law (J) the business name and address of the person to whom ownership is being transferred is missing
Missing  N1_03 Missing Buyer HIN, DEA or DUNS Number
Missing  N1_04 Missing Buyer HIN, DEA or DUNS Number
Missing  N3_01 H. R. 3204-19  DSCSA Law (J) the business name and address of the person to whom ownership is being transferred is missing
Missing  N4 H. R. 3204-19  DSCSA Law (J) the business name and address of the person to whom ownership is being transferred is missing
Missing  YNQ_05 Missing transaction statement
Missing  YNQ_09 Missing transaction statement YNQ_09 with TS
Missing  PID_05 H. R. 3204-19  DSCSA Law  (A) the proprietary or established name or names of the product and (B) the strength and dosage form of the product are missing
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 02/Jun/25 ]
I noticed that the customer fixed the ASN and added the missing information and resend the document today 06/02/2025
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 02/Jun/25 ]
Hello ,
I have noticed that this ASN was correct and resend to the trading partner if you have any issues with the recent sent asn and need our assistance please let us know. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by LINDSEY VENABLE [ 02/Jun/25 ]
The error has occurred for every ASN document we have sent to Cardinal Health since we began using DataTrans.
Comment by Maria Keshwala [ 02/Jun/25 ]
Thank you for letting me know I will have the analyst look into this Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 02/Jun/25 ]
Sandy is working on this issue - <a href="https://datatrans-inc.atlassian.net/browse/CS-42564">https://datatrans-inc.atlassian.net/browse/CS-42564</a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 02/Jun/25 ]
Sandy,
Has a ticket open with Cardinal working on this matter , I will closed this one and will keep Sandys ticket.
Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
</blockquote>
<h4 id="cs-44776-expanding-business-created-30-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44776] Expanding Business Created: 30/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Support
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello, We&#39;d like to discuss growing our EDI with Cleo and would like to speak to someone in the sales department. Thank you
Comments
Comment by Maria Keshwala [ 02/Jun/25 ]
Hello
See below sales contact information Thank you
Sales Support
<a href="mailto:<EMAIL>"><EMAIL></a>
(281)292-8686
For English select 1, followed by 1 for Sales
For Spanish select 2, followed by 1 for Sales
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44772-5431-westside-research-ship-stations-integration-problems-created-30-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44772] 5431 Westside Research Ship Stations Integration Problems Created: 30/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-143552.png      image-********-143414.png      image-********-143731.png
Request Type: Technical support
Request language: English
Request participants: None
Organizations: Label: Shipstation
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Ship station Integrations Technical Issues, Need Support to Assist.
Comments
Comment by Maria Keshwala [ 02/Jun/25 ]
Hello
We will look into this and advise we might need a zoom I will advise shortly. Let me first look into your account. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 02/Jun/25 ]
It looks like customer was able to integrate POs
Now the store is assigned -
Original Error was Invalid customer mapping error - This issue is when the trading partner store is disconnected from the shipstation site customer needs to create a support ticket within the ship station to resolved this issue as it is disassociated with us.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 02/Jun/25 ]
Hello
I noticed that the original issue was an “Invalid customer mapping error,” which stemmed from a disconnection between your store and ShipStation. It appears this issue has now been resolved, as the error is no longer occurring and the store is once again properly mapped in ShipStation.
Other customers experienced similar issues and were able to resolve them by working directly with Ship Station support, as store connectivity is managed through their platform not through DataTrans.
If you&#39;re still encountering any problems, I’d be happy to schedule a Zoom call to walk through the issue with you.
For now, I’ll go ahead and close this ticket. However, if the problem persists, feel free to reply to this message. For any new issues, please create a separate ticket.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44757-load-files-missing-from-sftp-created-30-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44757] Load files missing from SFTP Created: 30/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250530-141140.png      image-20250530-141223.png
Request Type: Phone call
Request language: English
Request participants: None
Organizations: Label: Missing documents FTP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Donna this is the ticket for the Load files
Comments
Comment by Maria Keshwala [ 30/May/25 ]
files came this morning -
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
We just checked you guys are not migrated I will have SFTP rep to contact you guys to migrate
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44755-use-jamesl-from-culinary-arts-specia-inbox-loading-issue-created-30-may-25-updated-30-may-25-resolved-30-may-25">[CS-44755] Use JamesL from Culinary Arts Specia inbox loading issue - Created: 30/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Maria Keshwala Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250530-140220.png
Request Type: Phone call
Request language: English
Request participants: None
Organizations: Label: WebEDI Questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Login as the customer Inbox loaded in seconds -
Advice the customer to = CRTL F5 Clear CACHE, to clean the cookies -</p>
<h4 id="cs-44673-error-asn-rejected-created-30-may-25-updated-30-may-25-resolved-30-may-25">[CS-44673] Error: ASN rejected Created: 30/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: ASN errors, ASN rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Dear EDI Partner,
We received your message but it was failed. Could you please correct the document reference number? As I see there is one more zero in it.
UNB+UNOA:3+DTS3546:ZZ+0013000046ZF-AG-TOR+250529:1210+10188++DESADV&#39;
UNH+1+DESADV :07A:UN:GAVF13&#39;
BGM+351+0096704+9&#39;
DTM+137:20250430:102&#39;
DTM+11:20250430:102&#39;
DTM+132:20250430:102&#39;
MEA+AAX+G+KGM:322&#39;
MEA+AAX+AAA+KGM:282&#39;
RFF+CRN:0096704&#39;
NAD+SE+0000140311::92+Greystone of Lincoln, Inc.&#39;
NAD+SF+0000140311::92+Greystone of Lincoln, Inc.&#39;
NAD+ST+452A::92+452A&#39;
LOC+11+MAIN&#39;
TDT+12+30+FDEN::92:0&#39;
EQD+TE&#39;
CPS+1++4&#39;
PAC+1+:35:AAD+PLT::92+F:34063957:SA&#39;
QTY+52:14000:PCE&#39;
PCI+17+++6J::5&#39;
GIN+ML+34063957A&#39;
LIN+1++34063957A:IN&#39;
QTY+12:14000:PCE&#39;
ALI+US&#39;
RFF+ON:55000167001:00020&#39;
RFF+AAU:0096704:00020&#39;
UNT+25+1&#39;
UNZ+1+10188&#39;
Thank you,
Kind regards,
Éva Bárdos
Data Exchange
EDI Support Services (FIXE32)
ZF Group
ZF Hungária Kft.
Külsősor u. 14.
3300 Eger Ungarn/Hungary
<a href="mailto:<EMAIL>"><EMAIL></a>
Vorsitzender des Aufsichtsrats/Chairman of the Supervisory Board: Dr. Rolf Breidenbach Vorstand/Board of Management: Dr. Holger Klein (Vorsitzender/CEO), Dr. Lea Corzilius, Michael Frick, Dr. Peter Holdmann, Prof. Dr. Peter Laier, Mathias Miedreich Sitz/Headquarters: Friedrichshafen Handelsregistereintrag Amtsgericht Ulm HRB 630206/Trade register of the municipal court of Ulm HRB 630206
Informationen zur Verarbeitung Ihrer Daten und zu Ihren Rechten erhalten Sie jederzeit in unseren Datenschutzhinweisen: <a href="https://www.zf.com/de/data-protection-notice">https://www.zf.com/de/data-protection-notice</a> You can find information about how we process your data and your rights in our data protection notice: <a href="https://www.zf.com/en/data-protection-notice">https://www.zf.com/en/data-protection-notice</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Correction was made this morning and I resent the ASN
GREYSTONE HOLIDAY SCHEDULE
JUNE 19TH – CLOSED
JULY 4TH – CLOSED
AUGUST 11TH – CLOSED
SEPTEMBER 1ST – CLOSED
OCTOBER 13TH – CLOSED
NOVEMBER 11TH – CLOSED
NOVEMBER 27TH – CLOSED
DECEMBER 25TH - CLOSED
Thanks,
Marie Gauvin | Customer Service Representative
Greystone of Lincoln|W (401)-333-0444 ext.218 |C (401)-651-9124 |F (401)-334-5745
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Maria Keshwala [ 30/May/25 ]
Hello This is DataTrans Cleo if you need our assistance let us know I see you already sent the correct information. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44533-ship-station-order-integration-isssue-created-29-may-25-updated-30-may-25-resolved-30-may-25">[CS-44533] Ship Station order integration Isssue Created: 29/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250529-155707.png      image-20250529-155800.png      image-20250530-191821.png
Request Type: Phone call
Request language: English
Request participants: None
Organizations: Label: Shipstation
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Issue = 2 days ago customer has not been able to integrate orders into Ship Station
Comments
Comment by Maria Keshwala [ 29/May/25 ]
Invalid customer mapping error in the integrator logs
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 29/May/25 ]
Here is the meeting link for 1pm EST. Thank you
Maria Keshwala is inviting you to a scheduled Zoom meeting.
Topic: Ship Station Mapping issue Time: May 29, 2025 01:00 PM Eastern Time (US and Canada) Join Zoom Meeting <a href="https://cleo.zoom.us/j/98123429799">https://cleo.zoom.us/j/98123429799</a>
Meeting ID: 981 2342 9799
One tap mobile +13126266799,,98123429799# US (Chicago) +16465588656,,98123429799# US (New York)
Dial by your location</p>
<ul>
<li>****** 626 6799 US (Chicago)</li>
<li>****** 558 8656 US (New York)</li>
<li>****** 931 3860 US</li>
<li>****** 715 8592 US (Washington DC)</li>
<li>****** 224 1968 US</li>
<li>****** 205 3325 US</li>
<li>****** 215 8782 US (Tacoma)</li>
<li>****** 248 7799 US (Houston)</li>
<li>****** 209 5623 US</li>
<li>****** 347 5053 US</li>
<li>****** 473 4847 US</li>
<li>****** 217 2000 US</li>
<li>****** 444 9171 US</li>
<li>****** 900 9128 US (San Jose)</li>
<li>****** 278 1000 US</li>
<li>****** 359 4580 US</li>
<li>****** 205 0468 US</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free
Meeting ID: 981 2342 9799
Find your local number: <a href="https://cleo.zoom.us/u/adnumBS7OG">https://cleo.zoom.us/u/adnumBS7OG</a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 29/May/25 ]
Hello Albert I will go ahead and open the meeting the other meeting was running late
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 29/May/25 ]
Hi Albert are you still joining in Im in the meeting I will wait another 10 mins
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Maria,
I can&#39;t connect to the link. Can we do a google meet?
Comment by Maria Keshwala [ 29/May/25 ]
Join on the browser online try that first
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 29/May/25 ]
if that doesn&#39;t work I will see if I can do google meets we use Zoom
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications</em>
<a href="mailto:<EMAIL>"><EMAIL></a>
************
Comment by Maria Keshwala [ 29/May/25 ]
<a href="https://meet.google.com/imq-sroy-nau">https://meet.google.com/imq-sroy-nau</a> try this
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Hi There I see the stores are now map was ship station able to fixed the store connection? I see you have integrated some POs let me know please thank you I see only Petco dropship being mapped and the other stores but not regular PETCO is this one done within WEBEDI?
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Maria,
Yes, all good now. PETCO is done solely through EDI. I appreciate your follow up.
Have a great weekend!
Comment by Maria Keshwala [ 30/May/25 ]
Ok great thank you I will go ahead and closed this ticket . Have a nice weekend.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</li>
</ul>
<h4 id="cs-44450-acadia-cardinal-ship-layout-issue-in-edi-file-acadops-2088-created-28-may-25-updated-30-may-25-resolved-30-may-25">[CS-44450] Acadia | CARDINAL SHIP | LAYOUT ISSUE IN EDI FILE | ACADOPS-2088 Created: 28/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image002.png      image003.png      image004.png      image005.png      image009.png      image010.png      image001.png      Outlook-atvqei3v.png     image-20250530-195029.png      image-20250530-195405.png
Request Type: Emailed request
Request language: English Request participants: None
Organizations: Label: Linx map, Mapping Rules
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good morning DataTrans team,
My team once again noticed a mismatch between the edi and text files for ACADIA_CAH_867Ship_202505240530_144287213.txt. Please see below for details. The message ID is 44389668.
Thanks, Danielle
From: Dasari, Laxmi Praharsha <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 26, 2025 8:00 AM To: Lindsey, Danielle <a href="mailto:<EMAIL>"><EMAIL></a>; Bansal, Vijay <a href="mailto:<EMAIL>"><EMAIL></a> Cc: DL NAR INVENT_LA_GLOBAL_Acadia_OPs <a href="mailto:<EMAIL>"><EMAIL></a> Subject: CARDINAL SHIP | LAYOUT ISSUE IN EDI FILE | ACADOPS-2088
Hi Danielle,
Today we again started received below file, we captured that there is a difference between edi file and text file data. Below are the observations.
FILENAME: ACADIA_CAH_867Ship_202505240530_144287213.txt we received the below text file with 34 records.
EDI:
While we are validating NDC column we observed that the count between edi and text file was same, but for ORDERTRANS NO in text file it was 34 but in edi it was 33. Please communicate the same to CARDINAL and let us know the exact count of this EDI and TXT file.
We received only 1 record in edi file also
NDC_NO:
ORDER_TRANS_NO:
Thanks &amp; Regards Dasari Laxmi Praharsha
Analyst | Quaero360 – Better Insights | Precise Actions
Capgemini [IN] | Hyderabad
Mob.: + 91 8096280154
<a href="http://www.capgemini.com">www.capgemini.com</a>
<a href="https://www.capgemini.com/resources/get-the-future-you-want">https://www.capgemini.com/resources/get-the-future-you-want</a>
This message contains information that may be privileged or confidential and is the property of the Capgemini Group. It is intended only for the person to whom it is addressed. If you are not the intended recipient, you are not authorized to read, print, retain, copy, disseminate, distribute, or use this message or any part thereof. If you receive this message in error, please notify the sender immediately and delete all copies of this message.
Comments  Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Comment by Maria Keshwala [ 28/May/25 ]
I will look into this file but last time I check this they matched I will look into this one
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Hi team once again we went over this on our last ticket please check on your end whats is creating extra lines as on our end we showing we are receiving and sending the same Line Items see below please . Thank you
Message ID 44389668
34 Line Items - received 34 line Items sent
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44447-edi-850s-not-going-into-webedi-created-28-may-25-updated-28-may-25-resolved-28-may-25">[CS-44447] EDI 850s Not Going into WebEDI Created: 28/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Andrew Rice Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-https___ww.png      Outlook-https___ww (59dbf669-2bcf-462f-b484-d7b93484049e).png      Outlook-https___ww (9996bb91-1fd6-4cb4-9956-a6b97713e401).png      Outlook-https___ww (be75d51e-9b83-4566-b489-444a7f03b271).png      Outlook-https___ww (e530d24f-5903-404a-bf8f-5932c0142e9f).png      image-********-192840.png      image-********-193730.png      image-********-194513.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Missing documents WebEDI
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello DTS Support,
We have had several instances in the last two weeks where our customers will confirm delivery of an 850 PO to DTS, but it is not visible in WebEDI or sent to us. Usually, if we have them re-send the order, it goes through and is visible in WebEDI. This happened again today: HH Barnum sent PO A87841 which is not visible in WebEDI.
What caused this order to fail processing? Why were we not notified that there was an issue with an inbound order?
Andrew
The content of this email is confidential and intended for the recipient specified in message only. It is strictly forbidden to share any part of this message with any third party, without a written consent of the sender. If you received this message by mistake, please reply to this message and follow with its deletion, so that we can ensure such a mistake does not occur in the future.
Comments
Comment by Andrew Rice [ 28/May/25 ]
Comment by Maria Keshwala [ 28/May/25 ]
Per Andrew trading partner sent the order an 1 1/2 ago PO A87841
SO FAR RECEIVED 4 ORDER THIS morning checking Trading partner sends via SFTP
ZZ,S18114
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Here is the screenshot of the document in the test folder
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
they need to drop the file here
D:\client\5526-barnum\todts
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Andrew Rice [ 28/May/25 ]
Thank you!
And just to confirm, we only need one of those files from today moved to WebEDI and imported since they are duplicates. The others you can ignore for now.
Thanks, Andrew
Comment by Maria Keshwala [ 28/May/25 ]
You’re welcome and yes
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
file in WEBEDI
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Translated file was also sent back to your ERP - I will closed this ticket. Let me know if you need anything else. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44437-connection-credentials-for-mighty-auto-parts-6558-5355-created-28-may-25-updated-28-may-25-resolved-28-may-25">[CS-44437] Connection Credentials for Mighty Auto Parts 6558 - (5355) Created: 28/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: SFTP/FTP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Support,
I’m working on a new project with Mighty Auto Parts and we’re looking to use the existing SFTP connection. For some reason, Mighty Auto can’t locate the details in their system and they’re asking if I can provide the host information, username/pw, IPs for what we have set up; basically all information about the connection.
Could you please help? If you need any additional information from me, please just let me know.
Thanks!
Marianne Kania
Cleo : Implementation Engineer I
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
Comments
Comment by Maria Keshwala [ 28/May/25 ]
Hello Marianne
see below
Host: transfer.datatrans-inc.com Port: 22 Username: dts6558 Password: D6aWbZugVZYzidUbEpPq
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications</em>
<a href="mailto:<EMAIL>"><EMAIL></a>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Thank you!
Marianne Kania
Cleo : Implementation Engineer I
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
Comment by Maria Keshwala [ 28/May/25 ]
you’re welcome
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44432-as2-communication-problem-turck-germany-created-28-may-25-updated-28-may-25-resolved-28-may-25">[CS-44432] AS2 Communication problem - Turck Germany Created: 28/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Michael Hoang
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.gif      image002.jpg      image003.png      image004.png      image005.png      image006.png      image007.png      image008.png      image009.jpg      image010.jpg
Request Type: Emailed request
Request language: English Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Support-Team,
We do have AS2 communication problem to your system. Could you please check on your side.
Best regards,</p>
<ul>
<li>i. A. Edith Hanisch *SAP Competence Center - Team IT</li>
<li>Hans Turck GmbH &amp; Co.* KG Witzlebenstr. 7 | 45472 Mülheim an der Ruhr, Germany T +49 208 4952 4379 <a href="mailto:<EMAIL>"><EMAIL></a> | <a href="http://www.turck.com">www.turck.com</a>
<a href="https://www.linkedin.com/company/turck/">https://www.linkedin.com/company/turck/</a>  <a href="https://www.facebook.com/TurckDE/">https://www.facebook.com/TurckDE/</a>  <a href="https://www.instagram.com/turck/">https://www.instagram.com/turck/</a>  <a href="https://twitter.com/turck">https://twitter.com/turck</a>  <a href="https://www.youtube.com/user/turcktv">https://www.youtube.com/user/turcktv</a> <a href="https://www.xing.com/pages/turck">https://www.xing.com/pages/turck</a> *
Digital Innovation Park *Erfahren Sie mehr über Trends und Innovationen für Industrie 4.0 und IIoT auf <a href="http://www.turck.de/dip">www.turck.de/dip</a> | Learn more about trends and innovations for Industry 4.0 and IIoT at <a href="http://www.turck.com/dip">www.turck.com/dip</a>
Datenschutzerklärung | Privacy Policy Unsere Datenschutzhinweise finden Sie auf <a href="http://www.turck.de/ds">www.turck.de/ds</a> | Our privacy policy can be found at <a href="http://www.turck.com/pp">www.turck.com/pp</a>
Hans Turck GmbH &amp; Co. KG | Sitz: Mülheim an der Ruhr, Deutschland | Amtsgericht Duisburg HRA 8539 | USt-IDNr. DE120353803 | Komplementärin: Hans Turck Verwaltungs-Gesellschaft mit beschränkter Haftung, Mülheim an der Ruhr | Amtsgericht Duisburg HRB 14382 | Geschäftsführung: Dipl.-Wirt.-Ing. Christian Wolf, Dipl.-Oec. Christian Pauli
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Hi Support-Team,
The service is now available.
Best regards,
i. A. Edith Hanisch SAP Competence Center - Team IT</li>
<li>Hans Turck GmbH &amp; Co.* KG Witzlebenstr. 7 | 45472 Mülheim an der Ruhr, Germany T +49 208 4952 4379 <a href="mailto:<EMAIL>"><EMAIL></a> | <a href="http://www.turck.com">www.turck.com</a>
<a href="https://www.linkedin.com/company/turck/">https://www.linkedin.com/company/turck/</a>  <a href="https://www.facebook.com/TurckDE/">https://www.facebook.com/TurckDE/</a>  <a href="https://www.instagram.com/turck/">https://www.instagram.com/turck/</a>  <a href="https://twitter.com/turck">https://twitter.com/turck</a>  <a href="https://www.youtube.com/user/turcktv">https://www.youtube.com/user/turcktv</a> <a href="https://www.xing.com/pages/turck">https://www.xing.com/pages/turck</a> *
Digital Innovation Park *Erfahren Sie mehr über Trends und Innovationen für Industrie 4.0 und IIoT auf <a href="http://www.turck.de/dip">www.turck.de/dip</a> | Learn more about trends and innovations for Industry 4.0 and IIoT at <a href="http://www.turck.com/dip">www.turck.com/dip</a>
Datenschutzerklärung | Privacy Policy Unsere Datenschutzhinweise finden Sie auf <a href="http://www.turck.de/ds">www.turck.de/ds</a> | Our privacy policy can be found at <a href="http://www.turck.com/pp">www.turck.com/pp</a>
Comment by Michael Hoang [ 28/May/25 ]
Hi Edith
Just wanted to follow up with you and make sure everything is taking care of, I see that you replied below and wanted to double check.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Hi Michael,
Yes, the connection works.
Best regards,
i. A. Edith Hanisch SAP Competence Center - Team IT</li>
<li>Hans Turck GmbH &amp; Co.* KG Witzlebenstr. 7 | 45472 Mülheim an der Ruhr, Germany T +49 208 4952 4379 <a href="mailto:<EMAIL>"><EMAIL></a> | <a href="http://www.turck.com">www.turck.com</a>
<a href="https://www.linkedin.com/company/turck/">https://www.linkedin.com/company/turck/</a>  <a href="https://www.facebook.com/TurckDE/">https://www.facebook.com/TurckDE/</a>  <a href="https://www.instagram.com/turck/">https://www.instagram.com/turck/</a>  <a href="https://twitter.com/turck">https://twitter.com/turck</a>  <a href="https://www.youtube.com/user/turcktv">https://www.youtube.com/user/turcktv</a> <a href="https://www.xing.com/pages/turck">https://www.xing.com/pages/turck</a> *
Digital Innovation Park *Erfahren Sie mehr über Trends und Innovationen für Industrie 4.0 und IIoT auf <a href="http://www.turck.de/dip">www.turck.de/dip</a> | Learn more about trends and innovations for Industry 4.0 and IIoT at <a href="http://www.turck.com/dip">www.turck.com/dip</a>
Datenschutzerklärung | Privacy Policy Unsere Datenschutzhinweise finden Sie auf <a href="http://www.turck.de/ds">www.turck.de/ds</a> | Our privacy policy can be found at <a href="http://www.turck.com/pp">www.turck.com/pp</a>
Comment by Michael Hoang [ 28/May/25 ]
Yes if you need anything else I am here for you Edith.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</li>
</ul>
<h4 id="cs-44399-fw-datatrans-856-integration-report-for-1136-exacto-spring-corp-dts690-created-27-may-25-updated-30-may-25-resolved-30-may-25">[CS-44399] FW: DataTrans 856 Integration report for 1136 - Exacto Spring Corp-DTS690 Created: 27/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Highest
Reporter: Brian Wall Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      Email Frequency.png      image-********-134752.png      image-********-135719.png      image-********-135328.png      image-********-135933.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Notification, Missing documents FTP, Dev ticket
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
We keep receiving these blank integration reports. This started last week, we are receiving about 5 per day. Can you please fix this? We are still receiving the actual integration report as well, but we don’t need these blank ones, I am assuming there is an issue somewhere on your end causing these. Let me know if I can provide any additional information.
<em>Best Regards, Brian Wall – IT Manager</em>
EXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Ext: 202 Direct: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a> Website: <a href="http://www.exacto.com">http://www.exacto.com</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 27, 2025 10:46 AM To: EDI <a href="mailto:<EMAIL>"><EMAIL></a> Cc: EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a>; EDI <a href="mailto:<EMAIL>"><EMAIL></a> Subject: DataTrans 856 Integration report for 1136 - Exacto Spring Corp
Partner Code Partner Name Type Reference Status
[EXACTO-SPRING-DISCLAIMER]
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
Comments
Comment by Brian Wall [ 27/May/25 ]
Comment by Brian Wall [ 27/May/25 ]
Hello, we are receiving these more frequently, we have received 9 so far today.
Comment by Brian Wall [ 27/May/25 ]
Comment by Michael Hoang [ 27/May/25 ]
Hey There Brian
I am going to help you and let me dive deeper into this and i will be back shortly , if you have any questions please let me know. I will be taking care of everything . Just read through everything and I am looking into to it currently
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Michael Hoang [ 28/May/25 ]
Good morning Brian
Currently working on this with the team and trying to get things resolved this morning, I wanted to just keep you updated.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Michael Hoang [ 28/May/25 ]
Solutions,
Client when sending a 856 receives an a notification of Pass or Fail when integrating , Have to escalate to Dev Team to have this problem fixed
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Michael Hoang [ 28/May/25 ]
Hi Brian,
I just escalated this to the development team with my manager.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Maria Keshwala [ 28/May/25 ]
Customer was getting erroneous integration report . = two and half weeks of issues - they dont know if they are missing files and not getting any integrations valid reports
not receiving 830 files 05/21/2025 the last time receiving files.
1- Invalid report
05/27/2025-- 3:47 pm CST sent ASN via SFTP customer did not received the integration report for that but customer still receiving this invalid reports .
The other issue is the customer is not getting all the 862s files - it looks like they are being merged into one file
Other issue customer has not received 830s since 05/22/2025 12:15am CST
Customer is receiving the same file over and over again with the same size KB on 05/19/2025 this issue was fixed but it looks like it broke something else they are missing the 05/22/2025
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
<a href="https://dtsprojects.atlassian.net/browse/DS-11725">https://dtsprojects.atlassian.net/browse/DS-11725</a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
The 862 are merged always customer will received files.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hello Brian the have fixed the issue with the 830s Im going to mark them as redownload so you can get the files for the integration reports I will create a dev ticket -
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
I have restaged from date 05/22/2025-05/28/2025
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Dev ticket regarding Integration Report <a href="https://dtsprojects.atlassian.net/browse/DTS-690">https://dtsprojects.atlassian.net/browse/DTS-690</a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hello this issue has been escalated to our development team we will keep you posted with any updates. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
The integration report email notification has been fixed. Please let us know if you still experiencing issues with this . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44396-fw-zf-lifetec-la-laguna-edi-schedule-loaded-release-dates-with-incorrect-transit-time-created-27-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44396] FW: ZF LIFETEC LA LAGUNA - EDI SCHEDULE LOADED RELEASE DATES WITH INCORRECT TRANSIT TIME Created: 27/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image010.png      image011.png      image012.jpg      image013.png      image014.png      image015.png      image016.png      image017.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
Label: Work Authorization CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good morning,
We need to change the transit times and change the Firm versus Planning on Trading partner 4112.
We would like for the weeks 1 – 8 be set to Firm and 9 – xxx be set to Planning
Also, we need add 5 transit days to the requirement Due date. Currently it is coming in as is.
Thank you
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
From: Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 23, 2025 8:22 AM To: Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a>; IT Support <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Val Sova <a href="mailto:<EMAIL>"><EMAIL></a>; Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a> Subject: ZF LIFETEC LA LAGUNA - EDI SCHEDULE LOADED RELEASE DATES WITH INCORRECT TRANSIT TIME
Janet –
The La Laguna (Customer T062, Trading Partner 4112) files loaded to GS but now there is incorrect transit time on the release dates.
The date loading to GS should be one week prior to the EDI schedule date.
Example: EDI release date Monday 06-02-25 should load as Monday 05-26-25
The file that was re-ran loaded as:
EDI scheduled date Monday 06-02-25 loaded to GS as Friday 05-30-25
Can we please get this corrected?
Thank you.
Lynette Page
Customer Service Representative
************ Ext. 213
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, OH 44092
From: Val Sova <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 23, 2025 7:18 AM To: Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a>; Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a>; Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
Thanks Janet, we’ll check it out after it processes.
Thank you,
Valerie Sova
Customer Service Supervisor
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, OH 44092
Phone: ************ X257
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 23, 2025 6:52 AM To: Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a>; Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a>; Val Sova <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
Good morning,
Made a change to the F versus P on this release. I am rerunning it. If still an issue let us know. I also have sent an email to DataTrans to make this change.
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
From: Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 1:35 PM To: Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a>; Val Sova <a href="mailto:<EMAIL>"><EMAIL></a>; Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
Janet,
Should I have these resent again or should Lynette manually enter these?
Ethan Paratto
Ext. 221
IT Technician
Universal Metal Products, Inc. 29980 Lakeland Blvd. Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
From: Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 1:15 PM To: Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a>; Val Sova <a href="mailto:<EMAIL>"><EMAIL></a>; Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
I see that the files ran through Global Shop but now there are no Sales Orders at all in the system for La Laguna….
Not sure what the error is but I need to know if I should enter these manually for now because we are putting together a shipment for tomorrow.
Please advise!!!
Thank you.
Lynette Page
Customer Service Representative
************ Ext. 213
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, OH 44092
From: Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 12:45 PM To: Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a>; Val Sova <a href="mailto:<EMAIL>"><EMAIL></a>; Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
Good afternoon,
Datatrans said they have resent the files. Can you guys confirm this please?
Ethan Paratto
Ext. 221
IT Technician
Universal Metal Products, Inc. 29980 Lakeland Blvd. Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
From: Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 9:39 AM To: Val Sova <a href="mailto:<EMAIL>"><EMAIL></a>; Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
Ethan –
Here are the message id’s I need pushed through:
44354685
44354606
44354675
44354677
44354674
44354670
44354672
Thank you.
Lynette Page
Customer Service Representative
************ Ext. 213
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, OH 44092
From: Val Sova <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 9:26 AM To: Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a>; Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
Ok, I set up trading partner records with no ship to code also. Did not delete the one 4112 412A in case they start sending in the code later.
Lynette,
Please let Ethan know what files are needed.
Thank you,
Valerie Sova
Customer Service Supervisor
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, OH 44092
Phone: ************ X257
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 9:02 AM To: Val Sova <a href="mailto:<EMAIL>"><EMAIL></a>; Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a>
Cc: Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: 34322144B - Need ASap (UM 9881-162) Plant 454A
Hi Val,
If there is no ship to code on the file you can leave it blank. Datatrans will need to push these through.
Please send Ethan the message id’s on the files you need.
Ethan, please send an email to <a href="mailto:<EMAIL>"><EMAIL></a> with those message id’s and ask them to resend. Thank you
From: Val Sova <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 8:57:54 AM To: Janet Dorsey &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Lynette Page &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Ethan Paratto &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
Hi Janet,
I setup the trading partners in all divisions. T061 showed the same ship to code as before 454A, but T062 is blank, used to be 412A. I set up with 412A do I need to set up as just trading partner and no ship code?
In options I also checked Yes to deleting zero qty orders, Trading partner does not send accumulated quantities and force packaging P record.
Can you push these releases through or does Datatrans need to do this? Please let me know if I need to add the T062 trading partner with no ship to code?
Thank you,
Valerie Sova
Customer Service Supervisor
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, OH 44092
Phone: ************ X257
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 21, 2025 2:45 PM To: Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a>; Val Sova <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: 34322144B - Need ASap (UM 9881-162) Plant 454A
Hi Lynette,
I am not in the office. There is a document in lotus notes quality system that shows how to setup EDI Trading partners. Please setup in UMP and whatever division they ship from. I can&#39;t set these up until later tonight.
Janet Dorsey
IT Manager
************ Ext. 242
Universal Metal Products
From: Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 21, 2025 2:16:58 PM
To: Janet Dorsey &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Ethan Paratto &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Criswell, Holly &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
Janet -
I don’t know if you are still pushing EDI files through Global Shop, but these new files did not go through.
Per below Holly is showing the following trading partner IDs for these locations:
(T061) ZF Reynosa 2287
(T062) ZF La Laguna 4112
Is this what we have setup?
Lynette Page
Customer Service Representative
************ Ext. 213
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, OH 44092
From: Criswell, Holly <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 21, 2025 1:32 PM To: Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Janet Dorsey <a href="mailto:<EMAIL>"><EMAIL></a>; Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: 34322144B - Need ASap (UM 9881-162) Plant 454A
Hi Lynette, I am seeing the 830s GSS files were sent to you yesterday for Reynosa. The trading partner ID for Reynosa is 2287. They are being filed in the Reynosa folder in WebEDI as the parameters ar
sophospsmartbannerend
Hi Lynette,
I am seeing the 830s GSS files were sent to you yesterday for Reynosa.
The trading partner ID for Reynosa is 2287.
They are being filed in the Reynosa folder in WebEDI as the parameters are set to place in that folder when TP =
I was able to locate the files for La Laguna, I created a &#34;Smart Folder&#34; in WebEDI so you are easily able to locate them.
I needed to add a map for this location since they are now sending the location with its own UNB ID.
I also see the files have been picked up and sent to your FTP file location with Trading Partner ID: 4112 to load in GSS.
Holly Criswell
Cleo : Senior Implementation Engineer
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a></p>
<ul>
<li>Join us at one of our upcoming events. Check out the list! *
From: Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 21, 2025 11:01 AM To: Criswell, Holly &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Janet Dorsey &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Ethan Paratto &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
The two plants are Reynosa (T061) A464 and La Laguna (T062) A412.
I did see some new files in DataTrans yesterday afternoon for Reynosa, but these did not process through Global Shop and they were filed in the ‘TRW Reynosa’ folder on DataTrans. This folder has not had any files added since 2021 so not sure why that is where they are being filed….
Lynette Page
Customer Service Representative
************ Ext. 213
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, OH 44092
From: Criswell, Holly <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 21, 2025 11:55 AM To: Lynette Page <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: 34322144B - Need ASap (UM 9881-162) Plant 454A
Hi Lynette, Can you please let me know which plants you are not receiving data from? I noticed yesterday they are sending different data then what we have in place to map to you. The 2 Plants I saw th
sophospsmartbannerend
Hi Lynette,
Can you please let me know which plants you are not receiving data from?
I noticed yesterday they are sending different data then what we have in place to map to you. The 2 Plants I saw that were impacted were Reynosa, TOR - Rojas
So, I made some map modifications to ensure the data is passing along.
Holly Criswell
Cleo : Senior Implementation Engineer
Email: + <a href="mailto:<EMAIL>"><EMAIL></a> + | Web: + <a href="http://www.cleo.com">www.cleo.com</a> +
<em>+ Join us at one of our upcoming events. Check out the list! +</em>
From: Damian Sroka CZS RPLP &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Wednesday, May 21, 2025 12:38 AM To: Melissa Iveth Giron REY RPNRL2 &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Humberto David Castrellon REY RPNRL2 &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Lynette Page &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Criswell, Holly &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Subject: RE: 34322144B - Need ASap (UM 9881-162) Plant 454A
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Internal
Hello Melissa,
Based on attached e-mail, I can confirm that 276366 was moved to our external provider Axway on 5th of May. + @Lynette Page please contact + @Criswell, Holly for checking the issue with lack of EDI releases.
Pozdrawiam / Best regards,
Damian Sroka
SC Processes &amp; Digitalization ZF LIFETEC
Operations Supply Chain (RPLP)
From: Melissa Iveth Giron REY RPNRL2 &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Sent: Tuesday, May 20, 2025 5:21 PM To: Humberto David Castrellon REY RPNRL2 &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Damian Sroka CZS RPLP &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Cc: Lynette Page &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Subject: Re: 34322144B - Need ASap (UM 9881-162) Plant 454A
Internal
Hello Damian,
Could you please assist with this
The supplier says that he has not received the EDI. The last update received was on 05-01-25
vendor: 276366
Regards!
From: Humberto David Castrellon REY RPNRL2 &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Tuesday, May 13, 2025 2:47 PM To: Melissa Iveth Giron REY RPNRL2 &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Damian Sroka CZS RPLP &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Cc: Lynette Page &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Subject: FW: 34322144B - Need ASap (UM 9881-162)
Internal</li>
</ul>
<ul>
<li>@Melissa Iveth Giron REY RPNRL2
From: Lynette Page &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Sent: Tuesday, May 13, 2025 2:44 PM To: Humberto David Castrellon REY RPNRL2 &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Subject: RE: 34322144B - Need ASap (UM 9881-162)
Internal
Humberto –
I still have not received an updated EDI schedule.
The last update we saw was received on 05-01-25 and based on that schedule, we are show 3,650 pcs PN 34322144B (UM 9881-162) due for ship date 05-16-25.
I am working with my IT department to see if there is any possible issue on our end as to why we are not seeing your last week’s updated schedule since you said it was sent.
Thank you.
Lynette Page
Customer Service Representative
************ Ext. 213
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, OH 44092
From: Humberto David Castrellon REY RPNRL2 &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Sent: Tuesday, May 13, 2025 2:52 PM To: Lynette Page &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Subject: 34322144B - Need ASap
Internal Good Afternoon, Lynette, Can you please share the shipping you have for this item this week? We need 5800 pcs for this week. 34322144B Please advise. Humberto Castrellon ZF LIFETEC Active &amp;am
sophospsmartbannerend
Internal
Good Afternoon,
Lynette,
Can you please share the shipping you have for this item this week?
We need 5800 pcs for this week.
34322144B
Please advise.
Humberto Castrellon
ZF LIFETEC
Active &amp; Passive Safety Technology Reynosa, Tamps Mexico 88780
NOTE: This message contains confidential information and is intended only for the individual named. If you are not the named addressee, you should not disseminate, distribute or copy this email. Please notify the sender immediately by email if you have received this email by mistake and delete this email from your system. Email transmission cannot be guaranteed to be secure or error-free, as information could be intercepted, corrupted, lost, destroyed, arrive late or incomplete, or contain viruses. The sender, therefore, does not accept liability for any errors or omissions in the contents of this message which arise as a result of email transmission. If verification is required, please request a hard-copy version.
New ZF LIFETEC Data Protection Notice for Customers and Business Partners
Informacje na temat przetwarzania Państwa danych osobowych oraz na temat Państwa praw znajdują się w naszych informacjach o ochronie danych osobowych: + DPN_for_Customers_and_BP_2025_01_28_PL_b6d9abffe9.pdf +
You can find information about how we process your data and your rights in our data protection notice: + DPN_for_Customers_and_BP_2025_01_27_EN_4bdccbd7e7.pdf +
NOTE: This message contains confidential information and is intended only for the individual named. If you are not the named addressee, you should not disseminate, distribute or copy this email. Please notify the sender immediately by email if you have received this email by mistake and delete this email from your system. Email transmission cannot be guaranteed to be secure or error-free, as information could be intercepted, corrupted, lost, destroyed, arrive late or incomplete, or contain viruses. The sender, therefore, does not accept liability for any errors or omissions in the contents of this message which arise as a result of email transmission. If verification is required, please request a hard-copy version. NOTE: This message contains confidential information and is intended only for the individual named. If you are not the named addressee, you should not disseminate, distribute or copy this email. Please notify the sender immediately by email if you have received this email by mistake and delete this email from your system. Email transmission cannot be guaranteed to be secure or error-free, as information could be intercepted, corrupted, lost, destroyed, arrive late or incomplete, or contain viruses. The sender, therefore, does not accept liability for any errors or omissions in the contents of this message which arise as a result of email transmission. If verification is required, please request a hard-copy version.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Comment by Maria Keshwala [ 27/May/25 ]
Hello Janet I will look into this and advise. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
We will also send a work authorization for this as well . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Need a work authorization
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Ok, thank you
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
Comment by Maria Keshwala [ 02/Jun/25 ]
Work authorization sent for this mapping update
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
Thank you
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
Comment by Maria Keshwala [ 02/Jun/25 ]
You’re welcome
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</li>
</ul>
<h4 id="cs-44395-error-in-sending-invoice-5723-po-8682959736-created-27-may-25-updated-28-may-25-resolved-28-may-25">[CS-44395] Error in sending Invoice#5723-PO-8682959736 Created: 27/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Michael Hoang
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  1748358209260blob.jpg
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
Label: PO questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Team
I see Invoice status for #5723 PO -8682959736 color: Color value is invalid
Message ID :
color: Color value is invalid
44406261 color: Color value is invalid
What is the issue can you please check? We have not received any error message
Thanks
Comments  Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Comment by Michael Hoang [ 27/May/25 ]
Hello,
Let me go over your screenshots and I will take care of everything. If you have any other questions please let me know.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Hello Michael I restaged it. And now showing green accepted. Will let you know if again observes any error.
For now I am good.
Thanks
Comment by Michael Hoang [ 27/May/25 ]
Yes that’s great news if you need anything else I am here for you, just let me know!
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</p>
<h4 id="cs-44394-re-datatrans-solutions-caterpillar-l5-oem-project-estimated-due-date-set-created-27-may-25-updated-27-may-25-resolved-27-may-25">[CS-44394] RE: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set Created: 27/May/25  Updated: 27/May/25  Resolved: 27/May/25</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Taylor Long Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.jpg      image002.png      image003.png      image004.png      image005.png      image006.png      image007.png      image008.jpg
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description  * CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Not sure what happened but they are there now for me. Bobbie still cannot see anything from those new trading partners. Her screen just shows no data available in table.
Thank you,
Taylor
Taylor A. Long
Office Administrator
Wolfe &amp; Swickard Machine
1344 South Tibbs Avenue
Indianapolis, IN 46241
(************* x40
(************* fax
CONFIDENTIALITY NOTICE: This E-mail and all attachments transmitted with it are private and confidential and may be protected by legal privilege. If you have received this message in error, please notify the sender immediately by returning the message to the sender and please delete this message and all attachments and all copies and backups thereof. If the reader of this message is not an intended recipient, you are hereby notified that any use, copying, distribution, or disclosure of this E-mail or its attachments is strictly prohibited. Thank you.
The Landed Value Option
Go Beyond the traditional supplier relationship to a true collaborative relationship Wolfe and Swickard will work with your team to ensure you get the parts you need, exactly how you need them Together, we will obtain more value so you can make your customers happier, and your competitors fearful
From: Johnson, Krista <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 27, 2025 10:46 AM To: Taylor Carr <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Anne Wellings <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
Adding support but your admin, Anne, would control access on the WebEDI system.
It all actually looks good to me. Are you just in the Inbox??
Thanks,
Krista
Krista Johnson
Cleo : Implementation Engineer I
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="https://link.edgepilot.com/s/7161e27c/Hv4XwzX1x0KbueTps_TrtA?u=http://www.cleo.com/">https://link.edgepilot.com/s/7161e27c/Hv4XwzX1x0KbueTps_TrtA?u=http://www.cleo.com/</a></p>
<ul>
<li>Join us at one of our upcoming events. Check out the list! *
From: Taylor Carr <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 27, 2025 9:37 AM To: Johnson, Krista &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Anne Wellings &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Please see below. Missed the attachment! Sorry!
Taylor A. Long
Office Administrator
Wolfe &amp; Swickard Machine
1344 South Tibbs Avenue
Indianapolis, IN 46241
(************* x40
(************* fax
CONFIDENTIALITY NOTICE: This E-mail and all attachments transmitted with it are private and confidential and may be protected by legal privilege. If you have received this message in error, please notify the sender immediately by returning the message to the sender and please delete this message and all attachments and all copies and backups thereof. If the reader of this message is not an intended recipient, you are hereby notified that any use, copying, distribution, or disclosure of this E-mail or its attachments is strictly prohibited. Thank you.
The Landed Value Option
· Go Beyond the traditional supplier relationship to a true collaborative relationship
· Wolfe and Swickard will work with your team to ensure you get the parts you need, exactly how you need them
· Together, we will obtain more value so you can make your customers happier, and your competitors fearful
From: Taylor Carr Sent: Tuesday, May 27, 2025 10:31 AM To: Johnson, Krista <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Anne Wellings <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
I show that there are 5 messages in the top right corner (im assuming PO and schedules) but as you can see, I do not have access to see them, so I am unable to send the 810 from the PO. Please fix!! Also please ensure Bobbie Wilbers has access as well!
Thank you,
Taylor
Taylor A. Long
Office Administrator
Wolfe &amp; Swickard Machine
1344 South Tibbs Avenue
Indianapolis, IN 46241
(************* x40
(************* fax
CONFIDENTIALITY NOTICE: This E-mail and all attachments transmitted with it are private and confidential and may be protected by legal privilege. If you have received this message in error, please notify the sender immediately by returning the message to the sender and please delete this message and all attachments and all copies and backups thereof. If the reader of this message is not an intended recipient, you are hereby notified that any use, copying, distribution, or disclosure of this E-mail or its attachments is strictly prohibited. Thank you.
The Landed Value Option</li>
<li>Go Beyond the traditional supplier relationship to a true collaborative relationship</li>
<li>Wolfe and Swickard will work with your team to ensure you get the parts you need, exactly how you need them</li>
<li>Together, we will obtain more value so you can make your customers happier, and your competitors fearful
From: Johnson, Krista <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 27, 2025 10:23 AM To: Taylor Carr <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Anne Wellings <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
Hi...
You are in production already on all. Send the 810s at any time.
Thanks,
Krista
Krista Johnson
Cleo : Implementation Engineer I
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="https://link.edgepilot.com/s/c841cb92/Duziix2ha0mX8hHaX-5Xnw?u=http://www.cleo.com/">https://link.edgepilot.com/s/c841cb92/Duziix2ha0mX8hHaX-5Xnw?u=http://www.cleo.com/</a></li>
<li>Join us at one of our upcoming events. Check out the list! *
From: Taylor Carr <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 27, 2025 8:57 AM To: Johnson, Krista &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fisher, Andrew &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Thurler, Macy &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Anne Wellings &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fischer, Robert &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Thank you for the below information.
Our suppliers are asking if we have an estimate on when we will be able to send their 810 invoices. Specifically, CAT OEM L5. Is there an estimated timeline that you would be able to provide?
Thank you,
Taylor
Taylor A. Long
Office Administrator
Wolfe &amp; Swickard Machine
1344 South Tibbs Avenue
Indianapolis, IN 46241
(************* x40
(************* fax
CONFIDENTIALITY NOTICE: This E-mail and all attachments transmitted with it are private and confidential and may be protected by legal privilege. If you have received this message in error, please notify the sender immediately by returning the message to the sender and please delete this message and all attachments and all copies and backups thereof. If the reader of this message is not an intended recipient, you are hereby notified that any use, copying, distribution, or disclosure of this E-mail or its attachments is strictly prohibited. Thank you.
The Landed Value Option
· Go Beyond the traditional supplier relationship to a true collaborative relationship
· Wolfe and Swickard will work with your team to ensure you get the parts you need, exactly how you need them
· Together, we will obtain more value so you can make your customers happier, and your competitors fearful
From: Johnson, Krista <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 23, 2025 3:20 PM To: Taylor Carr <a href="mailto:<EMAIL>"><EMAIL></a>; Fisher, Andrew <a href="mailto:<EMAIL>"><EMAIL></a>; Thurler, Macy <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Anne Wellings <a href="mailto:<EMAIL>"><EMAIL></a>; Fischer, Robert <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
Hi...
I&#39;ve started the process for CAT - PE and have processed in all past 830s received. They did not process in at the time as CAT - PE didn&#39;t exist in our system.
You may want to start with 830s that begin tomorrow. You might want/need to open each 830 and delete those that are from the past.. Ie keep the most recent.
Thanks,
Krista
Krista Johnson
Cleo : Implementation Engineer I
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="https://link.edgepilot.com/s/7ba585d6/2u9rzVfQwUm7RZ4fJl9Wng?u=http://www.cleo.com/">https://link.edgepilot.com/s/7ba585d6/2u9rzVfQwUm7RZ4fJl9Wng?u=http://www.cleo.com/</a></li>
<li>Join us at one of our upcoming events. Check out the list! *
From: Taylor Carr <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 2:29 PM To: Johnson, Krista &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fisher, Andrew &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Thurler, Macy &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Anne Wellings &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fischer, Robert &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
If it is at all helpful, here is a raw data 830 that they claim was successfully submitted to us. We need this trading partner added.
Thanks,
Taylor
Taylor A. Carr
Office Administrator
Wolfe &amp; Swickard Machine
1344 South Tibbs Avenue
Indianapolis, IN 46241
(************* x40
(************* fax
CONFIDENTIALITY NOTICE: This E-mail and all attachments transmitted with it are private and confidential and may be protected by legal privilege. If you have received this message in error, please notify the sender immediately by returning the message to the sender and please delete this message and all attachments and all copies and backups thereof. If the reader of this message is not an intended recipient, you are hereby notified that any use, copying, distribution, or disclosure of this E-mail or its attachments is strictly prohibited. Thank you.
The Landed Value Option
· Go Beyond the traditional supplier relationship to a true collaborative relationship
· Wolfe and Swickard will work with your team to ensure you get the parts you need, exactly how you need them
· Together, we will obtain more value so you can make your customers happier, and your competitors fearful
From: Johnson, Krista <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 2:46 PM To: Taylor Carr <a href="mailto:<EMAIL>"><EMAIL></a>; Fisher, Andrew <a href="mailto:<EMAIL>"><EMAIL></a>; Thurler, Macy <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Anne Wellings <a href="mailto:<EMAIL>"><EMAIL></a>; Fischer, Robert <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
And... I have my answer. I&#39;m going to send you all the WA for this location.
It will send to Anne in the next few minutes for the $100 cost.
Thanks,
Krista
Krista Johnson
Cleo : Implementation Engineer I
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="https://link.edgepilot.com/s/825beb58/OKYYcEEtwEqPm0683v6ygQ?u=http://www.cleo.com/">https://link.edgepilot.com/s/825beb58/OKYYcEEtwEqPm0683v6ygQ?u=http://www.cleo.com/</a></li>
<li>Join us at one of our upcoming events. Check out the list! *
From: Johnson, Krista <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 1:38 PM To: Taylor Carr &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fisher, Andrew &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Thurler, Macy &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Anne Wellings &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fischer, Robert &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: Re: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
Hi...
Can you advise if you&#39;ve completed a work authorization for the Caterpillar location PE - Reman Drivetrain??
It does appear that we don&#39;t currently have CAT - PE as a trading partner so it may involve more than just turning it on.
I have added Drew/Macy in Sales to assist with the WA per it&#39;s possibly varying from the standard amount. And I&#39;ve copied my Manager in case he can advise on this same point.
Thanks,
Krista
Krista Johnson
Cleo : Implementation Engineer I
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="https://link.edgepilot.com/s/825beb58/OKYYcEEtwEqPm0683v6ygQ?u=http://www.cleo.com/">https://link.edgepilot.com/s/825beb58/OKYYcEEtwEqPm0683v6ygQ?u=http://www.cleo.com/</a></li>
<li>Join us at one of our upcoming events. Check out the list! *
From: Taylor Carr <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 1:29 PM To: <a href="mailto:<EMAIL>"><EMAIL></a> &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Anne Wellings &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set color: Color value is invalid
color: Color value is invalid
| color: Color value is invalid color: Color value is invalid color: Color value is invalid color: Color value is invalid
| color: Color value is invalid color: Color value is invalid
You don&#39;t often get email from <a href="mailto:<EMAIL>"><EMAIL></a>. [ Learn why this is important|https://link.edgepilot.com/s/ae35779c/jgHOU2qkUU2EQ43XRHXHFg?u=https://aka.<a class="g3mark-shortlink" href="https://ms.corp.google.com/LearnAboutSenderIdentification">ms/LearnAboutSenderIdentification</a>] color: Color value is invalid color: Color value is invalid color: Color value is invalid
| color: Color value is invalid color: Color value is invalid color: Color value is invalid color: Color value is invalid
| color: Color value is invalid color: Color value is invalid
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hi Krista,
We just received the below email stating that we are adding 3 new CAT locations. We sent a request the other day to increase this to 4 locations including the “PE” Location that we realized that we are not receiving schedules from either. Can you please ensure that all 4 are being loaded?
L5 – OEM
14 – Joliet
R8- CPP
PE – Cat Reman Drivetrain
Thank you,
Taylor
Taylor A. Carr
Office Administrator
Wolfe &amp; Swickard Machine
1344 South Tibbs Avenue
Indianapolis, IN 46241
(************* x40
(************* fax
CONFIDENTIALITY NOTICE: This E-mail and all attachments transmitted with it are private and confidential and may be protected by legal privilege. If you have received this message in error, please notify the sender immediately by returning the message to the sender and please delete this message and all attachments and all copies and backups thereof. If the reader of this message is not an intended recipient, you are hereby notified that any use, copying, distribution, or disclosure of this E-mail or its attachments is strictly prohibited. Thank you.
The Landed Value Option
· Go Beyond the traditional supplier relationship to a true collaborative relationship
· Wolfe and Swickard will work with your team to ensure you get the parts you need, exactly how you need them
· Together, we will obtain more value so you can make your customers happier, and your competitors fearful
From: Anne Wellings <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 2:23 PM To: Taylor Carr <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
Anne M. Wellings
Office Manager
Wolfe &amp; Swickard Machine
1344 South Tibbs Avenue
Indianapolis, IN 46241
(************* x54
(************* fax
CONFIDENTIALITY NOTICE: This E-mail and all attachments transmitted with it are private and confidential and may be protected by legal privilege. If you have received this message in error, please notify the sender immediately by returning the message to the sender and please delete this message and all attachments and all copies and backups thereof. If the reader of this message is not an intended recipient, you are hereby notified that any use, copying, distribution, or disclosure of this E-mail or its attachments is strictly prohibited. Thank you.
The Landed Value Option</li>
<li>Go Beyond the traditional supplier relationship to a true collaborative relationship</li>
<li>Wolfe and Swickard will work with your team to ensure you get the parts you need, exactly how you need them</li>
<li>Together, we will obtain more value so you can make your customers happier, and your competitors fearful
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 2:21 PM
To: Anne Wellings <a href="mailto:<EMAIL>"><EMAIL></a> Subject: DataTrans Solutions - Caterpillar - L5 OEM Project Estimated Due Date Set
| |
Hi Wolfe &amp; Swickard Machine Co., Inc,
Your Estimated Due Date for Add 3 new CAT locations has been set to 2025-05-22.
Thank you,
DataTrans Solutions Krista Johnson <a href="mailto:<EMAIL>"><EMAIL></a> ************ -
Links contained in this email have been replaced. If you click on a link in the email above, the link will be analyzed for known threats. If a known threat is found, you will not be able to proceed to the destination. If suspicious content is detected, you will see a warning.
Links contained in this email have been replaced. If you click on a link in the email above, the link will be analyzed for known threats. If a known threat is found, you will not be able to proceed to the destination. If suspicious content is detected, you will see a warning.
Links contained in this email have been replaced. If you click on a link in the email above, the link will be analyzed for known threats. If a known threat is found, you will not be able to proceed to the destination. If suspicious content is detected, you will see a warning.
Links contained in this email have been replaced. If you click on a link in the email above, the link will be analyzed for known threats. If a known threat is found, you will not be able to proceed to the destination. If suspicious content is detected, you will see a warning.
Comments
Comment by Taylor Long [ 27/May/25 ]
Comment by Maria Keshwala [ 27/May/25 ]
Hello this is support
Please let us know the issue you are having to further assist you Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Everything on this looks fine now, thanks!
Anne M. Wellings
Office Manager
Wolfe &amp; Swickard Machine
1344 South Tibbs Avenue
Indianapolis, IN 46241
(************* x54
(************* fax
CONFIDENTIALITY NOTICE: This E-mail and all attachments transmitted with it are private and confidential and may be protected by legal privilege. If you have received this message in error, please notify the sender immediately by returning the message to the sender and please delete this message and all attachments and all copies and backups thereof. If the reader of this message is not an intended recipient, you are hereby notified that any use, copying, distribution, or disclosure of this E-mail or its attachments is strictly prohibited. Thank you.
The Landed Value Option
Go Beyond the traditional supplier relationship to a true collaborative relationship Wolfe and Swickard will work with your team to ensure you get the parts you need, exactly how you need them Together, we will obtain more value so you can make your customers happier, and your competitors fearful
Comment by Maria Keshwala [ 27/May/25 ]
Thank you for confirming .
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</li>
</ul>
<h4 id="cs-44393-whirlpool-mx-po-issue-created-27-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44393] Whirlpool MX PO Issue Created: 27/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.png      Errors_20250526_010110.txt      image-********-132017.png      image-********-131936.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Global Shop, Linx map, Work Authorization
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good morning,
We would like to fix where some of the Whirlpool PO# don’t always map through. If you look at attached file some parts have the PO and others don’t. This issue is only happening with Ship To codes M022, M023, ML20, ML21 and ML24.
I think this issue is happening because Whirlpool is putting the PO in 2 different locations, so the mapping is incorrect on some parts.
I think we just need to map the Purchase Order versus the Description. Please let me know your thoughts. Thank you
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
NOTE: This message contains confidential information and is intended only for the individual named. If you are not the named addressee, you should not disseminate, distribute or copy this email. Please notify the sender immediately by email if you have received this email by mistake and delete this email from your system. Email transmission cannot be guaranteed to be secure or error-free, as information could be intercepted, corrupted, lost, destroyed, arrive late or incomplete, or contain viruses. The sender, therefore, does not accept liability for any errors or omissions in the contents of this message which arise as a result of email transmission. If verification is required, please request a hard-copy version.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
<em>Errors_20250526_010110.txt  (427 kB)</em>
Comment by Maria Keshwala [ 27/May/25 ]
I will look into this one also. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Hello
Was the original map request to map from the description? if we are updating the original map we might need a map mod I might need to send a work auth . But let confirmed the segments I will reply back shortly . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Hello Janet I have to send you a work authorization for this change. is $190.00 one hour map mod . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Hi,
It was not defined where to grab the Purchase Order number. We just noticed that some of the ship to codes where not mapping correction. I have been manually fixing them and now I want to automate this process. Thanks
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
No problem. Thank you
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
Comment by Maria Keshwala [ 30/May/25 ]
Need to send a work authorization
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
Good morning,
I didn’t receive the work authorization for the 2 changes. Did you send those yet? Want to make sure they are not stuck in our Spam blocker. Thank you
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
Comment by Maria Keshwala [ 02/Jun/25 ]
Hi Janet
Working on it as we speak
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 02/Jun/25 ]
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 02/Jun/25 ]
Sent the work auth for this mapping update.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
Thank You!
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
Thank you
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business</p>
<h4 id="cs-44385-856-issue-created-27-may-25-updated-30-may-25-resolved-30-may-25">[CS-44385] 856 issue Created: 27/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
We have a new order that just received and 860 change. From my understanding, the change is just the shipping date, and all other line items were good to go. However, when I go to create an 856 and start to set up the packing slips, only a few line items show up.
Please let me know, we need to get this out today.
Comments
Comment by Maria Keshwala [ 30/May/25 ]
Hello Devon Im only keeping one ticket open regarding this issue I will close this one as is the same as the other one we have open. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44382-r-w-sauder-inc-mapping-issue-with-inbound-purchase-order-850-reference-20497-kroger-ralphs-created-27-may-25-updated-30-may-25-resolved-30-may-25">[CS-44382] R.W. Sauder Inc - Mapping issue with inbound purchase order (850) - Reference 20497 - Kroger/ Ralphs Created: 27/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      Kroger_PO_20250523.143536617_Original.xml      Kroger_PO_20250523.143536617_Corrected.xml      image-20250530-133150.png      image-20250530-133609.png      image-20250530-133609 (ec4fb109-6c36-4467-bdca-65cbeedb2cef).png      image-20250530-134211.png      image-20250530-150801.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Mapping Rules, Delta map update
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good morning, We have received an XML file that was unable to be imported into our system due to the repeated use of the element &#34;BuyerID&#34;, which our system is unable to process. Our system is only able to process a single instance of the &#34;BuyerID&#34; element in the XML files.
Currently, the &#34;Reference Identification&#34; entries under the &#34;Text Message&#34; section in the web portal is what is being mapped to the &#34;BuyerID&#34; element.
Would it be possible to instead map the second instance of the &#34;Reference Identification&#34; integer to the text between an &#34;VendorNo&#34; element? The &#34;VendorNo&#34; element would come after the &#34;EdiMsgNo&#34; and &#34;EanComp&#34; elements.
I have attached two example XML files to this email. Please let me know if you have any questions or concerns.
Thank you
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
<em>Kroger_PO_20250523.143536617_Corrected.xml  (0.6 kB)</em>
<em>Kroger_PO_20250523.143536617_Original.xml  (0.6 kB)</em>
Comment by Maria Keshwala [ 27/May/25 ]
We will look into this and advise. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 29/May/25 ]
Good morning
Looking into this
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Hello Michael
To make any mapping update you need a work authorization but before we do that let me ask the Analyst to review this request first . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
In the original no Vendor No is listed and the Buyer ID is the reference Identification
Customer wants the Vendor Numver (VendorNo) from the N9: ZA , N9:02 (10706151) value and also to be the Buyer ID 
Currently is ,mapping the N9: 01 SI THE N9:02 value
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
In the original configuration, no Vendor Number (VendorNo) is provided, and the Buyer ID is currently being derived from the N9 segment where N9:01 = SI and N9:02 = 00601720497.
However, the customer requires the Vendor Number to be extracted from the N9 segment where N9:01 = ZA and N9:02 = 10706151. This same value should also be used to populate the Buyer ID, formatted as:
To summarize:
Current Mapping: N9:01 = SI, N9:02 = 00601720497 → used as Buyer ID. Required Mapping: N9:01 = ZA, N9:02 = 10706151 → used as both VendorNo and Buyer ID ().
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Hello Michael
Following our review of the current mapping configuration, we’ve identified the necessary changes based on your request:</p>
<h4 id="current-mapping">Current Mapping:</h4>
<p>The Buyer ID is currently being derived from the N9 segment where N9:01 = SI and N9:02 = 00601720497.</p>
<h4 id="requested-update">Requested Update:</h4>
<p>The Vendor Number (VendorNo) should be extracted from the N9 segment where N9:01 = ZA and N9:02 = 10706151. This same value should also be used to populate the Buyer ID, formatted as:
We will proceed with implementing this mapping update as specified. Please note that our standard rate for mapping and configuration changes is $190.00 per hour.
A work authorization form will be sent to you shortly for your review and approval. Once we receive the signed authorization, we will begin the implementation.
Please let us know if you have any questions or need further clarification.
Best regards,
Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Hello Michael
Per the Analyst the Buyer ID issue was fixed last week you should not be received the duplicate Buyer ID he is looking into the Vendor ID
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
We were able to fix the issue no work authorization needed at the moment
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
File has been restaged
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Great! Thank you for your assistance with this matter.
Comment by Maria Keshwala [ 30/May/25 ]
You’re welcome
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44329-re-cs-44293-dib-trading-partner-created-23-may-25-updated-28-may-25-resolved-28-may-25">[CS-44329] Re: CS-44293 DIB Trading Partner Created: 23/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Michael Hoang
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  IMG_9873.jpeg
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Michael,
I&#39;ve attached the list of fines including the reference numbers. I am unable to connect the fines with invoices so that I can see where we went wrong. A small brand like ours can&#39;t afford these fees, so thank you for any guidance you could offer.
Sincerely,
On Fri, May 23, 2025 at 3:16 PM Michael Hoang <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
———-— Reply above this line.
Michael Hoang commented:
Hi Andie,
I am Michael I will be taking care of you , Can you give me more details on what you have been fine on like what documents were being rejected so i can get you situated. Just let me know and I am here to help
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
View request · Turn off this request&#39;s notifications Sent on May 23, 2025 2:16:25 PM MDT
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/May/25 ]
Comment by Michael Hoang [ 23/May/25 ]
Hi Michelle
I am working with my manager, which went through your invoice fine. But you will have to contact Do It Best to get details where the fine is coming from. For example is it a specific ASN or PO but all of the codes in the fines we can’t find anything using those numbers . Please let me know when you get in touch with DIB.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</p>
<h4 id="cs-44293-dib-trading-partner-created-23-may-25-updated-28-may-25-resolved-28-may-25">[CS-44293] DIB Trading Partner Created: 23/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Michael Hoang
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi there,
I&#39;m reaching out from Nature&#39;s Willow.
We&#39;ve recently received several fines from DIB in a row, and we&#39;re not sure why. We think there may be an issue with the integration.
Is there a support team that can help us look into this?
Thanks so much, Andie
Comments
Comment by Michael Hoang [ 23/May/25 ]
Hi Andie,
I am Michael I will be taking care of you , Can you give me more details on what you have been fine on like what documents were being rejected so i can get you situated. Just let me know and I am here to help
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</p>
<h4 id="cs-44234-fw-arrival-of-data-from-costco-850-created-22-may-25-updated-23-may-25-resolved-23-may-25">[CS-44234] FW: Arrival of data from Costco-850 Created: 22/May/25  Updated: 23/May/25  Resolved: 23/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Michael Hoang
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-20250523-122229.png      image-20250523-122005.png      image-20250523-122023.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
Label: Delayed document delivery
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Another order missing please advise on why our orders are not coming over?
Jennifer Johnson
Customer Service &amp; Logistics Manager
Mobile: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 2:25 PM To: VOSS Purchase Orders <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Arrival of data from Costco-850
Arrival of data from Costco to Voss USA
This email is to inform you that the following documents have arrived from Costco. If you are a WebEDI Account holder you may log into WebEDI at <a href="http://www.datatrans-inc.com/login">http://www.datatrans-inc.com/login</a>.
Note WebEDI Users: For reports that contain 997 Functional Acknowledgements (FA) please look at the Status column in your Sent Folder to see which messages have been acknowledged.
Date Time Document Interchange Control Reference Message Batch Id
05/22/2025 2:25 pm ET 850 ********* ******** ************ ******** *********
This is an EXTERNAL email. Please exercise caution. DO NOT open attachments or click links from unknown senders or unexpected email. ****
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Comment by Sandy Karidas [ 23/May/25 ]
Costco files come in on ECS02 and server share to ECS03 for processing. I can see both steps occurred and file was delivered to their FTP
File will need to be restaged for delivery. Target Voss USA - OUT - XML
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Michael Hoang [ 23/May/25 ]
Hi Jennifer
I just resent the delivery and made sure everything went through can you double check on your side for the Costco and US Foods PO , if you don’t see it yet please let me know.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</p>
<h4 id="cs-44233-fw-arrival-of-data-from-us-foods-850-created-22-may-25-updated-02-jun-25-resolved-23-may-25">[CS-44233] FW: Arrival of data from US Foods-850 Created: 22/May/25  Updated: 02/Jun/25  Resolved: 23/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Michael Hoang Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-20250523-184548.png      Voss US Food 3.png      image-20250523-184438.png      Voss US Food 1.png      Voss US Food 2.png     image-20250523-184601.png      image-20250523-190532.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
Label: Delayed document delivery
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
The below order has not come over. Is there an issue?
Jennifer Johnson
Customer Service &amp; Logistics Manager
Mobile: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 2:00 PM To: VOSS Purchase Orders <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Arrival of data from US Foods-850
Arrival of data from US Foods to Voss USA
This email is to inform you that the following documents have arrived from US Foods. If you are a WebEDI Account holder you may log into WebEDI at <a href="http://www.datatrans-inc.com/login">http://www.datatrans-inc.com/login</a>.
Note WebEDI Users: For reports that contain 997 Functional Acknowledgements (FA) please look at the Status column in your Sent Folder to see which messages have been acknowledged.
Date Time Document Interchange Control Reference Message Batch Id
05/22/2025 2:00 pm ET 850 ********* 0151 8567674C ******** *********
This is an EXTERNAL email. Please exercise caution. DO NOT open attachments or click links from unknown senders or unexpected email. ****
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Comment by Michael Hoang [ 23/May/25 ]
Goodmorning Jennifer,
I just resent the delivery and made sure everything went through can you double check on your side for the Costco and US Foods PO , if you don’t see it yet please let me know.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/May/25 ]
Orders are not in our system
Jennifer Johnson
Customer Service &amp; Logistics Manager
Mobile: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Michael Hoang [ 23/May/25 ]
Hi Jennifer,
After a thorough investigation into the data flow for the US Foods 850 order, on our side say’s everything has been sent and we restaged everything and resent.
The EDI file for the order in question did successfully arrive and was processed through our servers. Specifically, the file came into the ECSO2 server from LorenData, then was transferred via server share (ECSO3, previously called AS2A). The EDI file was then converted into XML format (as per your system requirements) and queued for delivery to your FTP location. Our logs confirm that the file was placed into the designated directory on your T drive and was available for pickup on May 22nd.
To address this:</p>
<ol>
<li>We have restaged the delivery of the order data to ensure it is resent and actively available on your FTP server.</li>
<li>You should now find the order file in your &#34;From DTS&#34; folder on the T drive, ready for processing. 3. Please confirm whether you are able to locate and retrieve the file. 4. (Below are the screenshots with my manager to make sure everything has been delivered)
Making sure it is the same US FOODS and same date
US Food is on server-03
Making sure the file has been transfer and server-shared to 03
Restaging the file to be delivered
Refresh and Successfully delivered
If you are still having problem Miss Jennifer I can join you on a call and get this situated. Please let me know I will take care of everything.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Sandy Karidas [ 23/May/25 ]
Hi Jennifer,
The Costco and US Foods files are in the fromdts for pick up. Do you see the below files?
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Michael Hoang [ 28/May/25 ]
Hi Jennifer,
I wanted to follow up and see if everything is okay or if you needed help still. Please let me know
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</li>
</ol>
<h4 id="cs-44228-fw-truecommerce-onetime-server-message-edi436387811-10-7-18-143-file-edi-rejected-reason-map-failed-to-run-created-22-may-25-updated-29-may-25">[CS-44228] Fw: TrueCommerce OneTime Server: Message: EDI436387811-***********.FILE.EDI rejected. Reason: Map failed to run Created: 22/May/25  Updated: 29/May/25</h4>
<p>Resolved: 29/May/25
Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  EDI436387811-***********.FILE.EDI      EDI436387811-***********.FILE-Info.CSV      Outlook-qaa1i1bm.png      image-********-165108.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
I received this email from Save a Lot. We sent our first invoice to them yesterday but apparently it was rejected.
Can you please check the email below and let us know what do we need to do?
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> on behalf of noreply <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 8:05:02 PM
To: Lucero Taboada <a href="mailto:<EMAIL>"><EMAIL></a> Subject: TrueCommerce OneTime Server: Message: EDI436387811-***********.FILE.EDI rejected. Reason: Map failed to run
Hello,
A message sent in to the SAVEALOT portal of the TrueCommerce OneTime Server has been rejected. This rejection has been sent to you because you are the nominated contact for the mailbox.
The details of the message and the reason for the rejection are given below:
Mailslot: In (INVOICE) File: EDI436387811-***********.FILE.EDI Sender mailbox ANA: 2107888644 Receiver mailbox ANA: 095484572 Sender reference: 000000006 File generation number: 6 Reason: Map failed to run
Please find attached
A copy of the data file (For file sizes of less than 2Mb only) A CSV file containing details of the message reference number and load information
If you have been granted access to the TrueCommerce OneTime server
Click here to view the message, and view the detailed analysis of the warnings / errors.
If you do not have access to the TrueCommerce OneTime Server or require further assistance then please contact the Service Desk, using the contact details below
Phone: ************ Email: <a href="mailto:<EMAIL>"><EMAIL></a>
Regards, Service Desk TrueCommerce | [www.truecommerce.com|www.truecommerce.com] Phone: ************ | Email: <a href="mailto:<EMAIL>"><EMAIL></a>
(Please do not reply to this email as it has been sent from an unmonitored address)
This message and any attachments are intended only for the individual or company to which it is addressed and may contain information which is privileged, confidential or prohibited from disclosure or unauthorised use. Any form of dissemination, copying, disclosure, distribution and/or publication of this e-mail message or its attachments to third parties is only permitted with the express permission of the sender.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
<em>EDI436387811-***********.FILE-Info.CSV  (1 kB)</em>
<em>EDI436387811-***********.FILE.EDI  (0.9 kB)</em>
Comment by Maria Keshwala [ 23/May/25 ]
Hi Good morning
Please provide your company name , date and invoice number to further assist you thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/May/25 ]
Maria I resend the email we received
Comment by Maria Keshwala [ 23/May/25 ]
Spoke with the customer regarding this issue
I will check the specs from the 810 and make sure the 810s are mapped correctly
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/May/25 ]
Hola Maria ya tendras alguna respuesta del problema que vimos en la maniana con las facturas de Save a Lot????? Avisame
Gracia
Comment by Maria Keshwala [ 23/May/25 ]
No todavia tienes que esperar
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/May/25 ]
Muchas gracias ,el lunes trabajan????
Comment by Maria Keshwala [ 23/May/25 ]
No estamos de fiesta ese dia
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 27/May/25 ]
Hola Claudia como estas me puedes dar el numero de cuenta de nuevo y nombre de la compania ?
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Hola Maria Buenas tardes , Nombre de la Empresa es : USA Import Products &amp; Distribution LLC Numero de Cuenta EDI : 2609
Veo que todavia las facturas que sometimos de Save a Lot no estan recibidas. Vuando podremos tener noticias de que tenemos que corregir ???.
Avisame.
Saludos
Comment by Maria Keshwala [ 28/May/25 ]
Failed Invoices
44382894 message ID
Message ID 44349959
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hello I will schedule a call for tomorrow zoom meeting I compare the other maps with other customers I think you are sending data that they dont need. But Im done for the day I will reach out in the morning . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 29/May/25 ]
Hello Again
We can meet at 3:30pm EST let me know if this works who I should send the meeting link to please provide the email thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Maria,
Yes please send link to me and Claudia.
Comment by Maria Keshwala [ 29/May/25 ]
I sent the invite thank you
Maria Keshwala is inviting you to a scheduled Zoom meeting.
Topic: SAVE A LOT 810 invoice meeting Time: May 29, 2025 03:30 PM Eastern Time (US and Canada) Join Zoom Meeting <a href="https://cleo.zoom.us/j/94499694405">https://cleo.zoom.us/j/94499694405</a>
Meeting ID: 944 9969 4405
One tap mobile +13126266799,,94499694405# US (Chicago) +16465588656,,94499694405# US (New York)
Dial by your location</p>
<ul>
<li>****** 626 6799 US (Chicago)</li>
<li>****** 558 8656 US (New York)</li>
<li>****** 931 3860 US</li>
<li>****** 715 8592 US (Washington DC)</li>
<li>****** 224 1968 US</li>
<li>****** 205 3325 US</li>
<li>****** 473 4847 US</li>
<li>****** 217 2000 US</li>
<li>****** 444 9171 US</li>
<li>****** 900 9128 US (San Jose)</li>
<li>****** 278 1000 US</li>
<li>****** 359 4580 US</li>
<li>****** 205 0468 US</li>
<li>****** 215 8782 US (Tacoma)</li>
<li>****** 248 7799 US (Houston)</li>
<li>****** 209 5623 US</li>
<li>****** 347 5053 US</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free
Meeting ID: 944 9969 4405
Find your local number: <a href="https://cleo.zoom.us/u/afsQANhah">https://cleo.zoom.us/u/afsQANhah</a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 29/May/25 ]
Hey guys
I have fixed the mapping issues - you can go ahead and create new 880s Invoices 🙂 I will closed this ticket I have tested it. But the invoices need to be created brand new from the 850. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</li>
</ul>
<h4 id="cs-44225-order-**********-issue-maness-veteran-medical-6948-created-22-may-25-updated-28-may-25-resolved-28-may-25">[CS-44225] Order ********** - Issue - Maness Veteran Medical - 6948 Created: 22/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Michael Hoang
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250523-135058.png      image-20250523-135136.png      image-20250523-135415.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: WebEDI Questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Good afternoon, I am having an issue in our EDI. I need to invoice for this order – **********. And, I am getting the message below. I did not include it in a batch. I don&#39;t even know how to do that. I also have not ever gotten this message before. Can you please help me, un-batch this order/invoice, so I can invoice properly.
TY! Laura
Message Response Process failed. Unable to create task, already existing as a batch.
--
Comments
Comment by Michael Hoang [ 22/May/25 ]
Good afternoon Laura,
Thank you for reaching out regarding the issue with invoicing order **********.
I am Michael, I will be taking care of you so the error message you are encountering typically means the invoice is currently included in a batch, which prevents making individual changes or processing it separately.
To assist you further, could you please provide more details about the steps you took when you received this message? Specifically:
At what point during invoicing did this message appear? Are you using any batch processing features or workflows? Have you recently created or submitted any batches that might include this invoice? If you can please provide screenshots for me ?
With this information, I can guide you step-by-step on how to un-batch the invoice or resolve the issue so you can proceed with invoicing properly.
In the meantime, I&#39;ll review your order and the current batch status on our end to see if there is anything that can be adjusted.
Please let me know if you have any questions or need assistance along the way. If things still doesn’t work I will help you through a short phone call.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Good afternoon, Thank you for your email. I have never used a batch for anything. When I click on the order and click invoice, it will not take me to the next screen. It immediately pops up an error message in the top right with this…
Message Response Process failed. Unable to create task, already existing as a batch.
TY. Laura
Laura Maness, MBA COO, Maness Veteran Medical, LLC (SDVOSB) (*************
<a href="mailto:<EMAIL>"><EMAIL></a> <a href="http://www.manessveteranmedical.com">www.manessveteranmedical.com</a>
On May 22, 2025, at 4:22 PM, Michael Hoang <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
———-— Reply above this line.
Michael Hoang commented:
Good afternoon Laura,
Thank you for reaching out regarding the issue with invoicing order **********.
I am Michael, I will be taking care of you so the error message you are encountering typically means the invoice is currently included in a batch, which prevents making individual changes or processing it separately.
To assist you further, could you please provide more details about the steps you took when you received this message? Specifically:
At what point during invoicing did this message appear? Are you using any batch processing features or workflows? Have you recently created or submitted any batches that might include this invoice? If you can please provide screenshots for me ?
With this information, I can guide you step-by-step on how to un-batch the invoice or resolve the issue so you can proceed with invoicing properly.
In the meantime, I&#39;ll review your order and the current batch status on our end to see if there is anything that can be adjusted.
Please let me know if you have any questions or need assistance along the way. If things still doesn’t work I will help you through a short phone call.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
View request · Turn off this request&#39;s notifications Sent on May 22, 2025 2:22:44 PM MDT
Comment by Michael Hoang [ 23/May/25 ]
Dear Laura,
Thank you for reaching out regarding the invoice issue for order **********.
To assist you in resolving the &#34;Message Response Process failed. Unable to create task, already existing as a batch&#34; error, please follow these steps carefully:</li>
</ul>
<ol>
<li>Make sure you are logging in through the right URL 2. Log into your EDI account and navigate to the order number Ctrl + F **********. 3. Open the order. 4. Click the &#34;Respond&#34; button. 5. Select the 810 (Invoice) option. 6. This will open the invoice screen; you can then proceed to create the invoice.
Please ensure these exact steps are being followed.</li>
</ol>
<h5 id="screenshots-below">Screenshots Below</h5>
<p>Step 1: Sign into your portal and Ctrl + F **********.
Step 2: Open the order, Click Respond ,Select 810
After you click 810, Below in the screenshot should appear exactly like that.
If you continue to receive the error, kindly confirm whether you are signed in through the correct EDI platform. Sometimes, using an incorrect login URL or environment may cause this issue.
Please let me know if these steps help or if you need further assistance. I am here to help if you are still running into problems Lauren I will help you through phone call just let me know .
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Michael Hoang [ 28/May/25 ]
Hi Laura
Just following up and making sure everything is okay, if you need any help with anything else please reach out.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</p>
<h4 id="cs-44224-re-msc-edi-v-29360-petol-gearench-errored-docs-created-22-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44224] Re: MSC EDI - v# 29360 Petol Gearench <em>Errored Docs</em> Created: 22/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.png      image004.png      image005.png      image006.png      image007.png      image008.png     image009.png      image010.png      image011.png      image012.png      image013.png      image014.png     Document_Processing_Error__MSC_INDUSTRIAL_SUPPLY_2025_05_28_14_00_34.eml      image001 (0b7d7a2c-1a63-4e41-852b-18b6b9188772).png     Document_Processing_Error__MSC_INDUSTRIAL_SUPPLY_2025_05_28_14_00_34 (42175823-5574-42ec-9abc-f66918a19cd2).eml      image-20250530-151509.png     image-20250530-154437.png      image-20250530-163705.png      image-20250530-163407.png      image-20250530-163311.png      image-20250530-163011.png     image-20250530-164531.png      image-20250530-171509.png      image003 (cd809f57-06e7-4b0f-8a8a-eb1a6b20c261).png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: Global Shop, ASN errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Laramie, I&#39;ve looped in @<a href="mailto:<EMAIL>"><EMAIL></a> since they handle all production issues. They will loop us in if they need help with anything.
Hi Support Team, Here is the issue reported by the customer. Please refer to the email below for additional information.
<em>&#34;We are having an issue with 856 transactions erroring. Normally when documents error, they go to the draft folder in WebEDI. However, I do not see all of these in that folder (See below). Is there anything that we can do so that these 856 transactions will be accepted?&#34;</em>
Jennifer Martin
Cleo : Project Manager
Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Web: <a href="http://www.cleo.com">www.cleo.com</a>
Join us at one of our upcoming events. Check out the list!
From: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 10:46 AM To: Martin, Jennifer <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Roberts, Kenrick <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: MSC EDI - v# 29360 Petol Gearench Errored Docs * CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Good Day,
We are having an issue with 856 transactions erroring. Normally when documents error, they go to the draft folder in WebEDI. However, I do not see all of these in that folder (See below). Is there anything that we can do so that these 856 transactions will be accepted?
Also, some of the documents that SPS is showing are errored are showing accepted in WebEDI. Below is one example.
Please advise if there is anything I can change on our end to fix this.
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I [www.petol.com|www.petol.com]
Office: ************
Fax: ************
From: Jaycee Simolin <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 10:36 AM To: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: MSC EDI - v# 29360 Petol Gearench Errored Docs
@Laramie Aars Good morning! Are you able to advise about correcting/resending these errored docs?
Sincerely,
Jaycee Simolin
Associate Product Manager
EDI Fulfillment
MSC Industrial Supply Co.
525 Harbour Place Drive
Davidson, NC 28036
(************* phone</p>
<ul>
<li><a href="http://www.mscdirect.com">www.mscdirect.com</a> +
<a href="http://twitter.com/#!/MSC_Industrial">http://twitter.com/#!/MSC_Industrial</a>  <a href="http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts">http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts</a>  <a href="http://www.linkedin.com/company/24034">http://www.linkedin.com/company/24034</a>
From: Jaycee Simolin Sent: Tuesday, May 13, 2025 10:49 AM To: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: MSC EDI - v# 29360 Petol Gearench Errored Docs
I unfortunately do not as it sounds like you need to find a systematic way to send back “each” for those items. I’m not sure if SPS Support could help?
Sincerely,
Jaycee Simolin
Associate Product Manager
EDI Fulfillment
MSC Industrial Supply Co.
525 Harbour Place Drive
Davidson, NC 28036
(************* phone</li>
<li><a href="http://www.mscdirect.com">www.mscdirect.com</a> +
<a href="http://twitter.com/#!/MSC_Industrial">http://twitter.com/#!/MSC_Industrial</a>  <a href="http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts">http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts</a>  <a href="http://www.linkedin.com/company/24034">http://www.linkedin.com/company/24034</a>
From: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 12, 2025 4:00 PM To: Jaycee Simolin <a href="mailto:<EMAIL>"><EMAIL></a> Subject: EXTERNAL: RE: MSC EDI - v# 29360 Petol Gearench Errored Docs
Thank you! Unfortunately, I have to manually complete those 855 and 856 transactions because the EDI transactions for those parts with an UOM of “set” keep erroring. Do you have any advise on how to fix this so the docs do not error? Laramie
ZjQcmQRYFpfptBannerStart
This Message Is From an External Sender
This e-mail originated outside of MSC. Exercise caution before clicking links or opening attachments
ZjQcmQRYFpfptBannerEnd
Thank you!
Unfortunately, I have to manually complete those 855 and 856 transactions because the EDI transactions for those parts with an UOM of “set” keep erroring. Do you have any advise on how to fix this so the docs do not error?
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I <a href="http://www.petol.com">www.petol.com</a>
Office: ************
Fax: ************
From: Jaycee Simolin <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 12, 2025 2:52 PM To: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: MSC EDI - v# 29360 Petol Gearench Errored Docs
Sending back “each” as the UOM is just fine as that is how we have it set up in our system.
Got that corrected one you sent for PO# 6318660001 and all is good 😊
Sincerely,
Jaycee Simolin
Associate Product Manager
EDI Fulfillment
MSC Industrial Supply Co.
525 Harbour Place Drive
Davidson, NC 28036
(************* phone</li>
<li><a href="http://www.mscdirect.com">www.mscdirect.com</a> +
<a href="https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!2YBZEXnCaN753eb1sqzQx4n1inhWmzbKfC-HHT5JTyWka007WNKfA1A125DJem1kWBVgILrIXliohQ$">https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!2YBZEXnCaN753eb1sqzQx4n1inhWmzbKfC-HHT5JTyWka007WNKfA1A125DJem1kWBVgILrIXliohQ$</a> &lt;<a href="https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts__;!!G_S2aQPGww!2YBZEXnCaN753eb1sqzQx4n1inhWmzbKfC-">https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts__;!!G_S2aQPGww!2YBZEXnCaN753eb1sqzQx4n1inhWmzbKfC-</a>
HHT5JTyWka007WNKfA1A125DJem1kWBVgILr7pv2B2g$&gt;  <a href="https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!2YBZEXnCaN753eb1sqzQx4n1inhWmzbKfC-HHT5JTyWka007WNKfA1A125DJem1kWBVgILrlBsiyTw$">https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!2YBZEXnCaN753eb1sqzQx4n1inhWmzbKfC-HHT5JTyWka007WNKfA1A125DJem1kWBVgILrlBsiyTw$</a>
From: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 12, 2025 12:07 PM To: Jaycee Simolin <a href="mailto:<EMAIL>"><EMAIL></a> Subject: EXTERNAL: RE: MSC EDI - v# 29360 Petol Gearench Errored Docs
Jaycee, I it seems that most of the errors are occurring when a FTS28, FTS212, or FTS1426 is purchased. I think this is due to these items being a “set” and not sold per “each”. It doesn’t give me the option to
ZjQcmQRYFpfptBannerStart
This Message Is From an External Sender
This e-mail originated outside of MSC. Exercise caution before clicking links or opening attachments
ZjQcmQRYFpfptBannerEnd
Jaycee,
I it seems that most of the errors are occurring when a FTS28, FTS212, or FTS1426 is purchased. I think this is due to these items being a “set” and not sold per “each”. It doesn’t give me the option to select “set” in DataTrans. When I asked the EDI analyst about it, they said that SPS does not allow for “set” to be selected. Do you have any advice on this? We cannot change our part number, so it seems this is going to keep happening. I just received another that errored this morning, for what looks like the same reason.
I am going to select “each” when I resend 6318660001.
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I <a href="http://www.petol.com">www.petol.com</a>
Office: ************
Fax: ************
From: Jaycee Simolin <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 12, 2025 7:17 AM To: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: MSC EDI - v# 29360 Petol Gearench Errored Docs
@Laramie Aars Good morning! The 855 for PO# 6318660001 shows errored in SPS’ system – attached is the error notification you were sent on 5/1 – can you check it out please?
Sincerely,
Jaycee Simolin
Associate Product Manager
EDI Fulfillment
MSC Industrial Supply Co.
525 Harbour Place Drive
Davidson, NC 28036
(************* phone
<a href="http://www.mscdirect.com">www.mscdirect.com</a>
<a href="https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!0oUGQHQj1tEkQbpXZR_9qlPK7T87mO_xhjJywN_NxJzlft7hI1GUO5L3ZbLkvZ1_1rliTsqEswX0NA$">https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!0oUGQHQj1tEkQbpXZR_9qlPK7T87mO_xhjJywN_NxJzlft7hI1GUO5L3ZbLkvZ1_1rliTsqEswX0NA$</a> &lt;<a href="https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521">https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521</a>?
ref=ts__;!!G_S2aQPGww!0oUGQHQj1tEkQbpXZR_9qlPK7T87mO_xhjJywN_NxJzlft7hI1GUO5L3ZbLkvZ1_1rliTsoJEjNOSg$&gt; <a href="https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!0oUGQHQj1tEkQbpXZR_9qlPK7T87mO_xhjJywN_NxJzlft7hI1GUO5L3ZbLkvZ1_1rliTsrMheCjzQ$">https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!0oUGQHQj1tEkQbpXZR_9qlPK7T87mO_xhjJywN_NxJzlft7hI1GUO5L3ZbLkvZ1_1rliTsrMheCjzQ$</a>
From: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 9, 2025 5:16 PM
To: Jaycee Simolin <a href="mailto:<EMAIL>"><EMAIL></a> Subject: EXTERNAL: RE: MSC EDI - v# 29360 Petol Gearench Errored Docs
Jaycee, All should now be submitted. The 855 for 6318660001 was already showing as accepted on my end. I did submit the 856 transactions for the three in yellow. Let me know if anything else is needed! Have a good weekend! Laramie Aars Inside
ZjQcmQRYFpfptBannerStart
This Message Is From an External Sender
This e-mail originated outside of MSC. Exercise caution before clicking links or opening attachments
ZjQcmQRYFpfptBannerEnd
Jaycee,
All should now be submitted. The 855 for 6318660001 was already showing as accepted on my end. I did submit the 856 transactions for the three in yellow. Let me know if anything else is needed! Have a good weekend!
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I <a href="http://www.petol.com">www.petol.com</a>
Office: ************
Fax: ************
From: Jaycee Simolin <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 6, 2025 6:26 AM To: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: MSC EDI - v# 29360 Petol Gearench Errored Docs
Hey Laramie! So this is odd. For the ones in GREEN, if I remove the leading zero and just type in the 6 digit number I do see a successful copy of the doc sent. So those are good.
I’m still not seeing anything for those in YELLOW though if you could please advise?
Sincerely,
Jaycee Simolin
Associate Product Manager
EDI Fulfillment
MSC Industrial Supply Co.
525 Harbour Place Drive
Davidson, NC 28036
(************* phone
<a href="http://www.mscdirect.com">www.mscdirect.com</a>
<a href="https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!3w-xUdcPTD-tXjYfY5nP6Z6qd2UkGYwVScSk1NGQHFb_3MEMS0-n7S0ZCTEcYeUDpryfVHBvbQajfg$">https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!3w-xUdcPTD-tXjYfY5nP6Z6qd2UkGYwVScSk1NGQHFb_3MEMS0-n7S0ZCTEcYeUDpryfVHBvbQajfg$</a> &lt;<a href="https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts__;!!G_S2aQPGww!3w-xUdcPTD-tXjYfY5nP6Z6qd2UkGYwVScSk1NGQHFb_3MEMS0-">https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts__;!!G_S2aQPGww!3w-xUdcPTD-tXjYfY5nP6Z6qd2UkGYwVScSk1NGQHFb_3MEMS0-</a>
n7S0ZCTEcYeUDpryfVHCSWbpiuQ$&gt;  <a href="https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!3w-xUdcPTD-tXjYfY5nP6Z6qd2UkGYwVScSk1NGQHFb_3MEMS0-n7S0ZCTEcYeUDpryfVHAfKFyq2g$">https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!3w-xUdcPTD-tXjYfY5nP6Z6qd2UkGYwVScSk1NGQHFb_3MEMS0-n7S0ZCTEcYeUDpryfVHAfKFyq2g$</a>
From: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 5, 2025 6:16 PM To: Jaycee Simolin <a href="mailto:<EMAIL>"><EMAIL></a> Subject: EXTERNAL: RE: MSC EDI - v# 29360 Petol Gearench Errored Docs
Jaycee, Thank you for reaching out! I will go back through and submit these. However, when I look them up in our sent folder, they are all showing as accepted? I also couldn’t tell why it was rejected when I went to restage. Do you have
ZjQcmQRYFpfptBannerStart
This Message Is From an External Sender
This e-mail originated outside of MSC. Exercise caution before clicking links or opening attachments
ZjQcmQRYFpfptBannerEnd
Jaycee,
Thank you for reaching out! I will go back through and submit these. However, when I look them up in our sent folder, they are all showing as accepted? I also couldn’t tell why it was rejected when I went to restage. Do you have any insight?
Thank you,
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I <a href="http://www.petol.com">www.petol.com</a>
Office: ************
Fax: ************
From: Jaycee Simolin <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, May 5, 2025 12:53 PM To: Laramie Aars <a href="mailto:<EMAIL>"><EMAIL></a> Subject: MSC EDI - v# 29360 Petol Gearench Errored Docs
@Laramie Aars Good afternoon! The following documents errored through EDI recently and I don’t show they have been corrected/resent yet. Can you please advise?
Sincerely,
Jaycee Simolin
Associate Product Manager
EDI Fulfillment
MSC Industrial Supply Co.
525 Harbour Place Drive
Davidson, NC 28036
(************* phone
<a href="http://www.mscdirect.com">www.mscdirect.com</a>
<a href="https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!yCGH53FfskPtKeLo-JaC88qk-Voe2P6HEaZ45laq-DoD3q8g3-zLWeAFXWqBk5UNtdYdIkbBmOajzg$">https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!yCGH53FfskPtKeLo-JaC88qk-Voe2P6HEaZ45laq-DoD3q8g3-zLWeAFXWqBk5UNtdYdIkbBmOajzg$</a> &lt;<a href="https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts__;!!G_S2aQPGww!yCGH53FfskPtKeLo-JaC88qk-Voe2P6HEaZ45laq-DoD3q8g3-">https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts__;!!G_S2aQPGww!yCGH53FfskPtKeLo-JaC88qk-Voe2P6HEaZ45laq-DoD3q8g3-</a>
zLWeAFXWqBk5UNtdYdIkZbWUSNRg$&gt;  <a href="https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!yCGH53FfskPtKeLo-JaC88qk-Voe2P6HEaZ45laq-DoD3q8g3-zLWeAFXWqBk5UNtdYdIkaXrWiHXg$">https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!yCGH53FfskPtKeLo-JaC88qk-Voe2P6HEaZ45laq-DoD3q8g3-zLWeAFXWqBk5UNtdYdIkaXrWiHXg$</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Comment by Maria Keshwala [ 23/May/25 ]
Good morning
If they show accepted that means the file had no EDI errors and they sent us a positive 997 if it will failed on SPS most likely they send a rejected 997 , but what is the issue or error as I&#39;m showing these document on the screenshot being accepted?
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Maria,
For some of them I do not know what the error is as, like you said the documents are showing accepted. For some of the errors, the attached is the problem. We have a few items that are sold in a set. SPS does not allow this for the unit on their documents. So each time material that is sold in a set is purchased the documents error. It is becoming quite an issue. Is there anything we can do on our side to resolve the issue.
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I [www.petol.com|www.petol.com]
Office: ************
Fax: ************
<em>Document_Processing_Error__MSC_INDUSTRIAL_SUPPLY_2025_05_28_14_00_34.eml  (17 kB)</em>
Comment by Maria Keshwala [ 28/May/25 ]
Hello
I understand your concern. However, in order for me to assist with an EDI error, I need more specific information. The document appears to have passed EDI validation, meaning there are no visible structural errors. If there is an issue, it may be related to the content or values within the document rather than its format.
To investigate further, please provide the following details:
ASN Message ID Trading Partner Reference Number Description of the Possible Error SPS is Addressing
Without this information, I&#39;m unable to determine the root cause of the issue. Once you provide these details, I’ll be able to assist you more effectively.
Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/May/25 ]
Maria,
The error is due to MSC Industrial Supply not being able to accept a UOM of “ST” which is how our part number is setup for certain products such as the FTS24. I have spoken with Jaycee at MSC about this and there is nothing she can do from her side.
I have attached the error message from SPS with the description of the errors. I cannot provide a message reference as this PO is not showing errored in WebEDI.
Please let me know if any other information is required.
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I [www.petol.com|www.petol.com]
Office: ************
Fax: ************
<em>Document_Processing_Error__MSC_INDUSTRIAL_SUPPLY_2025_05_28_14_00_34 (42175823-5574-42ec-9abc-f66918a19cd2).eml  (17 kB)</em>
Comment by Maria Keshwala [ 29/May/25 ]
Hello
Is the issue for document type 855?
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
We see the rejection for 855 transactions and 856 transactions.
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I [www.petol.com|www.petol.com]
Office: ************
Fax: ************
Comment by Maria Keshwala [ 30/May/25 ]
MSN error with SPS UOM - issue
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
DTS = 6868-
Missing the Unit of Measure ( Checking to see in which record this is expected ) and if this is required per specs the files did not inject into the draft folder files were sent out with the error .
Messag ID 44350044
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Hello
Working on this as we speak , I see that the UOM is missing from your documents I will verify the original file from GSS and see where this value was expected. I will get back as soon as I&#39;m done troubleshooting . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Sample 856 ECS-03 Batch ID 144215154
Linx Map = 1338
<a href="https://admin-prod.datatranswebedi.com/dommap/1338/edit">https://admin-prod.datatranswebedi.com/dommap/1338/edit</a>
Rule 84 the L:10 record did not mapped to any Value
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Hello
This is the 856 mapping issues with the SN1: 03segmen expected in the L:10 record start position 103 you are adding an “ST” value which is not mapping to any UOM code below are the allow codes .
Please review Shipment ID = 0198035-0000 below are the allow UOM for MSC. Thank you - looking into the 855 now .
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
I know this is the issue, but this is how our part number is setup (as a set). I cannot change the part number. I believe that it is the same issue with the 855. Is there anything we can do to fix this issue with MSC?
I appreciate you looking into this.
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I [www.petol.com|www.petol.com]
Office: ************
Fax: ************
Comment by Maria Keshwala [ 30/May/25 ]
But original the Map was setup as the L:10 record with the UOM - you might need a map mod are you always sending the one specific UOM or it changes
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
because the only thing I can think of is hard coding a UOM value once they do the map mod but if this data always changes then you have to fixed it on your end somehow as the UOM is required and the source data is coming from your end .
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
855 - failed at SPS with reference number 6374420001 has the UOM as “Each” - do we know what the error is for this one ? message ID 44243431 -NO EDI errors on this one yet SPS shows this one as errored. can you please let us know what error is SPS saying this one has ? Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
For the 855 I cant determine what the issue is on SPS end as it has the UOM present and the EDI has no errors so please provide more information on the 855 in order to assist .
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Jaycee,
Can you please provide Maria with some insight on the below?
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I [www.petol.com|www.petol.com]
Office: ************
Fax: ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/May/25 ]
Maria,
We have three different UOMs that are tied to our part numbers…ea, st, pr.
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I [www.petol.com|www.petol.com]
Office: ************
Fax: ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
@<a href="mailto:<EMAIL>"><EMAIL></a> Good morning! My apologies but I’m confused by the email below. I’m not seeing a “maria” on the email. What is this in regards to?
Sincerely,
Jaycee Simolin
Associate Product Manager
EDI Fulfillment
MSC Industrial Supply Co.
525 Harbour Place Drive
Davidson, NC 28036
(************* phone</li>
<li><a href="http://www.mscdirect.com">www.mscdirect.com</a> +
<a href="http://twitter.com/#!/MSC_Industrial">http://twitter.com/#!/MSC_Industrial</a>  <a href="http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts">http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts</a>  <a href="http://www.linkedin.com/company/24034">http://www.linkedin.com/company/24034</a>
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
Maria,
The issue is that you sent an UOM of “ST” which is not one of our permittable UOM’s. We an only accept the following per our specs:
Sincerely,
Jaycee Simolin
Associate Product Manager
EDI Fulfillment
MSC Industrial Supply Co.
525 Harbour Place Drive
Davidson, NC 28036
(************* phone</li>
<li><a href="http://www.mscdirect.com">www.mscdirect.com</a> +
<a href="http://twitter.com/#!/MSC_Industrial">http://twitter.com/#!/MSC_Industrial</a>  <a href="http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts">http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts</a>  <a href="http://www.linkedin.com/company/24034">http://www.linkedin.com/company/24034</a>
Comment by Maria Keshwala [ 02/Jun/25 ]
Good morning
Exactly thats what I have explain the customer they need to send the correct allow UOM in order to prevent this error. “ST” Is not allow per MSC I will now closed this ticket as the customer needs to correct this on their end and send the correct code. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
@Laramie Aars Please see below from Maria.
Sincerely,
Jaycee Simolin
Associate Product Manager
EDI Fulfillment
MSC Industrial Supply Co.
525 Harbour Place Drive
Davidson, NC 28036
(************* phone</li>
<li><a href="http://www.mscdirect.com">www.mscdirect.com</a> +
<a href="http://twitter.com/#!/MSC_Industrial">http://twitter.com/#!/MSC_Industrial</a>  <a href="http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts">http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts</a>  <a href="http://www.linkedin.com/company/24034">http://www.linkedin.com/company/24034</a>
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
I do understand this. However, these parts are not sold per each, but in a set. Meaning there is multiple of a certain item contained in the part number. We cannot change this on our end as it would cause issues when customers purchase the material. I need a work around for this from Datatrans, as otherwise these docs will error every time. Or can MSC allow set or ST? EDI is supposed to make things go smoothly and create less work. If the documents error every time a part number with FTS is purchased, this is not creating less work. For MSC or us. Any thoughts?
Laramie Aars
Inside Sales Associate
<a href="mailto:<EMAIL>"><EMAIL></a> I [www.petol.com|www.petol.com]
Office: ************
Fax: ************
Comment by Maria Keshwala [ 02/Jun/25 ]
That&#39;s conversation you need to have with MSC as per their specification they don&#39;t take there is nothing Data trans can do at this point. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Jun/25 ]
@Laramie Aars When you send back the item on the return docs are you sending it as 1 sku number (like we send it over on the 850) or are you sending it back as various part numbers because it’s a set? We need it sent back as 1 line item, like we send it on the 850. With that you would be able to send back UOM of “EA”. There is nothing MSC can do from our side unfortunately I’m very sorry. We can’t send or allow “ST”. Can you use any of the other UOM’s listed below in our spec guide?
Sincerely,
Jaycee Simolin
Associate Product Manager
EDI Fulfillment
MSC Industrial Supply Co.
525 Harbour Place Drive
Davidson, NC 28036
(************* phone</li>
<li><a href="http://www.mscdirect.com">www.mscdirect.com</a> +
<a href="http://twitter.com/#!/MSC_Industrial">http://twitter.com/#!/MSC_Industrial</a>  <a href="http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts">http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts</a>  <a href="http://www.linkedin.com/company/24034">http://www.linkedin.com/company/24034</a></li>
</ul>
<h4 id="cs-44223-new-trading-partner-set-up-created-22-may-25-updated-28-may-25-resolved-28-may-25">[CS-44223] New Trading Partner set up Created: 22/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Michael Hoang
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20240219-012912.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
Label: Add Trading Partner
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
HI,
We need to set up our EDI to work with a new customer – MAURICES.
Could you please help with the set up?
They said we need to contact SPS commerce to get set up with integration testing to ensure information flows from our DataTrans systems to maurices.
New vendor set-up requests: <a href="mailto:<EMAIL>"><EMAIL></a> Customer Support: <a href="mailto:<EMAIL>"><EMAIL></a> or (888) 739-3232
Best regards,
Tais Chaoubah
Operations Coordinator
Avondayle
69 Wingold Ave.
Suite # 110, Mailbox #110
Toronto, ON M6B 1P8
Comments
Comment by Michael Hoang [ 22/May/25 ]
Hey Tals,
To add a new trading partner:</p>
<ol>
<li>Sign onto the WebEDI site at <a href="http://datatrans-inc.com/login">http://datatrans-inc.com/login</a></li>
<li>From the menu at the top click on &#34;Add Trading Partner.&#34; (Screen shot below) 3. Select your trading partner from the drop down list. 4. The contact information is not needed, but the vendor number will be helpful. 5. Click on the Send button.
Once we receive the setup form, an associate will be assigned to the project. That associate will then work with you and the trading partner on both the EDI requirements and any EDI testing.
For any documents you may want mapped, the project analyst assigned will be able to provide you an additional quote.
I am here to help just reach out if you run into anything else.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Michael Hoang [ 28/May/25 ]
HI Tais,
just following up and making sure everything is okay, if you need any help with anything else please reach out.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</li>
</ol>
<h4 id="cs-44217-issue-creating-810-ahn-created-22-may-25-updated-22-may-25-resolved-22-may-25">[CS-44217] Issue creating 810 AHN Created: 22/May/25  Updated: 22/May/25  Resolved: 22/May/25</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Phone call
Request language: English
Request participants: None
Organizations: None
Label: Invoice questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
This is the ticket for the issue creating an 810 FOR AHN
Issue resolved
I was able to create the 810 and the customer was able to create the 810 as well while on the phone.
Comments
Comment by Maria Keshwala [ 22/May/25 ]
Resolved - account 7338
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44214-fw-asn-correction-edi-20974-supplier-e0562j0-created-22-may-25-updated-28-may-25-resolved-28-may-25">[CS-44214] Fw: ASN Correction-EDI # 20974 supplier # E0562J0 Created: 22/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Blanca Gallegos Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      image (d60aaaca-24ba-41f6-bb8f-dae0d9a04fd5).png      image-********-160930.png      image-********-161024.png      image-********-160734.png      image-********-162624.png      image-********-163306.png      image-********-163206.png
Request Type: Emailed request Request language: English
Request participants: Organizations: None
Label: ASN errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello @Support DataTrans
Our customer is reporting that for trading partner Caterpillar - 89 CMSA there is information missing that prevents the ASN to sync. This email thread is the customer report making a comparison to other similar trading partners.
Our review shows that the missing information is not being carried out correctly from the 830 item, see screenshots below:
Incorrect (ASN 856) - Caterpillar - 89 CMSA:
Correct (ASN 856) - Caterpillar - 47 MORTN
Thank you in advance,
From: HARI CHANDANA <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 3:50 AM To: Yuma Shipping <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Roberto Rojo <a href="mailto:<EMAIL>"><EMAIL></a>; Claudia Luevano <a href="mailto:<EMAIL>"><EMAIL></a>; Abigail Kersey <a href="mailto:<EMAIL>"><EMAIL></a>; Vi Ngo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: ASN Correction-EDI # 20974 supplier # E0562J0 Importance: High
WARNING! This email is from an OUTSIDE user. It did not originate from Insultech systems or Insultech users. Use caution and do not click on links or attachments from unknown senders. Notify <a href="mailto:<EMAIL>"><EMAIL></a> if you are unsure.
Hi @Yuma Shipping ,
I have checked latest ASN of facility( 89 Monterey, Mexico), Still Line-item number is not given.
Can you discuss the challenges to implement this.
Since we can see you are including line-item numbers in other facility ASNs. (refer below tables)
ASN PO/RELEASE
ISA<em>00</em> 00 ZZ<em>E0562J0 <em>09</em>00507047989 <em>250521</em>1431</em>U<em>00200</em>000000248<em>0</em>P~
ISA<em>00</em> 00 09<em>00507047989 <em>ZZ</em>E0562J0 <em>250517</em>0102</em>U<em>00200</em>000043718<em>0</em>P{color} | | GS<em>SH</em>E0562J0<em>89</em>250521<em>1431</em>248<em>X</em>003020 GS<em>PS</em>89<em>E0562J0</em>250517<em>0102</em>9742<em>X</em>002003
ST<em>856</em>0248 ST<em>830</em>47340
BSN<em>00</em>AZ217964<em>250521</em>1211 BFR<em>05</em>545<em>SH</em>A<em>250407</em>251029<em>250517**5501204663
DTM</em>011<em>250521</em>1211 N1<em>SU</em>INSULTECH, LLC<em>92</em>E0562J0
DTM<em>017</em>250527<em>1411 PER</em>SR<em>Sean Deshler
HL</em>1<strong>S N1<em>ST</em>Caterpillar Mexico S.A de C.V<em>92</em>89
MEA<em>PD</em>G<em>10</em>LB N3<em>309 Nafta Blvd.
TD1</em>BOX71<em>1 N4</em>Laredo<em>TX</em>78045<em>US
TD5</em>B<em>2</em>ups<em>PD N1</em>PN<em>CATERPILLAR MEXICO S.A. DE C.V</em>92<em>89
REF</em>CN<em>1Z8VF1650369875960 LIN</em>00010BP<em>587-2250</em>EC<em>00</em>PD<em>INSULATION AS</em>DR-<em>RN</em>545 à Here ‘00010’ is the line-item number
REF<em>TN</em>AZ217964 UIT<em>PC
FOB</em>CC PER<em>SC</em>Nydia Galindo Fuan
N1*SF</strong>16<em>85365 SDP</em>Z<em>Z
N1</em>SF<strong>92<em>E0562J0 FST</em>10<em>C</em>D<em>250407
N1</em>SU</strong>91<em>US FST</em>5<em>C</em>D<em>250424
HL</em>2<em>1</em>I FST<em>5</em>C<em>D</em>250424
LIN<em>84</em>SI<em>01</em>CH<em>US</em>EC<em>00</em>GC<em>01</em>BP<em>587-2250 FST</em>5<em>C</em>D<em>250424
SN1**10</em>EA FST<em>20</em>C<em>D</em>250522
SLN<em>1**A</em>10<em>PC</em>56.45 SHP<em>01</em>5<em>035</em>250415
PRF<em>5501204663</em><em><em><em>XXXX à Missing line-item number SHP</em>02</em>435</em>004<em>221210**250415
PID</em>F<em><em><em><em>INSULATION AS CTT</em>1</em>190
REF</em>PK</em>az217964 SE<em>38</em>47340
CLD<em>1</em>10<em>CTN71
REF</em>LS<em>YC18802
CTT</em>2<em>10
SE</em>25<em>0248
GE</em>1<em>248
IEA</em>1<em>000000248
ASN2 ASN3
ISA</em>00* 00 ZZ<em>E0562J0 <em>09</em>005070479LE <em>250521</em>1448</em>U<em>00200</em>000010460<em>0</em>P~
ISA<em>00</em> 00 ZZ<em>E0562J0 <em>09</em>00507047947 <em>250521</em>1440</em>U<em>00200</em>000007541<em>0</em>P~
GS<em>SH</em>E0562J0<em>LE</em>250521<em>1448</em>10460<em>X</em>003020 GS<em>SH</em>E0562J0<em>47</em>250521<em>1440</em>7541<em>X</em>003020
ST<em>856</em>10460 ST<em>856</em>7541
BSN<em>00</em>AZ217949<em>250521</em>1247 BSN<em>00</em>AZ218091<em>250521</em>1238
DTM<em>011</em>250521<em>1247 DTM</em>011<em>250521</em>1238
DTM<em>017</em>250527<em>1447 DTM</em>017<em>250527</em>1438
HL<em>1**S HL</em>1<strong>S
MEA<em>PD</em>G<em>15</em>LB MEA<em>PD</em>G<em>6</em>LB
TD1<em>BOX71</em>1 TD1<em>BOX71</em>1
TD5<em>B</em>2<em>UPS</em>PD TD5<em>B</em>2<em>ups</em>PD
REF<em>CN</em>1Z8VF1650367115801 REF<em>CN</em>1Z8VF1650368042852
FOB<em>CC FOB</em>CC
N1*SF</strong>16<em>85365 N1</em>SF<strong>16<em>85365
N1</em>SF</strong>92<em>E0562J0 N1</em>SF<strong>92<em>E0562J0
N1</em>SU</strong>91<em>US N1</em>SU<strong>91<em>US
HL</em>2<em>1</em>I HL<em>2</em>1<em>I
LIN</em>91<em>BP</em>596-5784<em>EC</em>00<em>CH</em>US LIN<em>242</em>BP<em>338-2266</em>EC<em>02</em>CH*US
SN1</strong>7<em>EA SN1**1</em>EA
SLN<em>1**A</em>7<em>PC SLN</em>1<strong>A<em>1</em>PC
PRF<em>SAMG38521</em><em><em><em>1 PRF</em>HEUC22081</em>217</em></strong>1
PID<em>F</em><strong><em>SHIELD AS-HEAT PID</em>F</strong><em><em>SHIELD AS
REF</em>PK</em>az217949 REF<em>PK</em>AZ218091
CLD<em>1</em>7<em>BOX71 CLD</em>1<em>1</em>BOX71
REF<em>LS</em>YC18783 REF<em>LS</em>yc18894
CTT<em>2</em>7 CTT<em>2</em>1
SE<em>24</em>10460 SE<em>24</em>7541
GE<em>1</em>10460 GE<em>1</em>7541
IEA<em>1</em>000010460 IEA<em>1</em>000007541
Thanks, and Regards
Hari Chandana
Process Solutions Center
Strategic Procurement &amp; Planning Division
Caterpillar Inc
Email Id: <a href="mailto:<EMAIL>"><EMAIL></a>
Desk Phone: +91 44 7170 1131
EDI Enablement Americas &amp; EAME support - <a href="mailto:<EMAIL>"><EMAIL></a>
Click HERE to get our EDI training documentations &amp; X12/EDIFACT guidelines
Comments
Comment by Blanca Gallegos [ 22/May/25 ]
Comment by Maria Keshwala [ 23/May/25 ]
Good morning
We will look into this and advise . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hello Blanca
Can you please give me a call please ? or can we join a zoom meeting sometime today is 11am EST ok for you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Issue Missing Line Item number
830 = Message ID = : 40472498
Line Item 00010
856 =
Line Item Number is missing from the 856
Issue =
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
On the ASN the PRF:02 release number
on the 830 = LIN01
Set up mapping rule
Issue fixed
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hello Blanca
Fixed the assigned Identification under the PO reference PRF05 segment is now populating the Line Item from the 830. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44207-need-asn-raw-data-created-22-may-25-updated-23-may-25-resolved-23-may-25">[CS-44207] Need ASN raw data Created: 22/May/25  Updated: 23/May/25  Resolved: 23/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Michele Hackett Assignee: Michael Hoang
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-4tgxqf4b.png      image-20250522-193921.png      image-20250522-194009.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: ASN Label
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
H Hi,
We sent ASNs to our trading partner, Saks Off Fifth, on May 2. They said 3 of the cartons were not on the ASN however, I do see them on my end. They are asking for ASN raw data. Can you help?
Violet and Brooks 832-373-8763
This what she has sent us:
The ASN we received did not contain these carton label ID&#39;s, please advise: <a href="mailto:<EMAIL>"><EMAIL></a>
00009000075430025414 00009000075430025407 00009000075430025421
Export Results Save Search
More
Refresh
Items Per Page: 10 25 50 100 F P 1 N E 1 - 3 of 3 items
AD Ref No Partner Name Sender Receiver Direction Document Type Primary Key Tracking Date &amp; Time
D-28782905212 VIOLET ZZ:DTS7543 12:6092785376 Inbound 856 VB043025 Friday, May 02, 2025 13:16:43 EDT
D-28782597782 VIOLET ZZ:DTS7543 12:6092785376 Inbound 856 VB043025 Friday, May 02, 2025 11:47:12 EDT
D-28782560088 VIOLET ZZ:DTS7543 12:6092785376 Inbound 856 VB043025 Friday, May 02, 2025 11:36:47 EDT
Comments
Comment by Michele Hackett [ 22/May/25 ]
Comment by Sandy Karidas [ 22/May/25 ]
will need to go to the server and pull the raw data and send it to the customer.
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Michael Hoang [ 22/May/25 ]
Hi Michele
I will be taking care of you , I am diving deeper and going to go through everything and I will be back shortly.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2 Comment by Michael Hoang [ 22/May/25 ]
Dear Michele Hackett,
We are writing to provide a comprehensive explanation of the Advance Shipping Notice (ASN) issue reported by Saks Fifth Avenue concerning 3 carton labels not appearing on the ASNs they received for your recent shipment. O to cross-reference this in your Cleo WebEDI system.
Here’s a step-by-step breakdown of what occurred:</li>
</ul>
<ol>
<li>Initial ASN Creation (Draft ASN - Never Sent):
An initial ASN was created in your system for this shipment. Based on our findings, this specific Draft ASN has Message ID 44097832, was created on 04/29/2025 at 04:31 PM, and has a reference of VB043025. Carton labels were generated and printed based on the data within this Draft ASN (Message ID 44097832). Critical Point: Our records and investigation clearly indicate that this Draft ASN (Message ID 44097832) was never actually transmitted or sent to Saks Fifth Avenue. It remained in a &#34;Draft&#34; status in your Cleo W</li>
<li>Screenshot 1: Unsent Draft ASN in your &#34;Draft&#34; Folder
You can verify this in your Cleo WebEDI system by navigating to your &#34;Draft&#34; folder and looking for Message ID 44097832.</li>
<li>Creation and Transmission of New ASNs (What Saks Received):
Subsequently, three new ASNs were created in your system, all associated with the same reference VB043025, and these were successfully transmitted to and acknowledged by Saks Fifth Avenue on 05/02/2025. T
Message ID 44143287, Sent 05/02/2025 11:32 AM Message ID 44132986, Sent 05/02/2025 11:45 AM Message ID 44143257, Sent 05/02/2025 01:10 PM
These three sent ASNs would have generated a new and different set of carton label IDs than those on the original, unsent Draft ASN (Message ID 44097832). These sent ASNs are visible in your &#34;Sent&#34; folder, as shown in Screenshot 2 below:</li>
<li>Screenshot 2: Transmitted ASNs in your &#34;Sent&#34; Folder (Relevant entries for VB043025)
You can verify these in your Cleo WebEDI system by navigating to your &#34;Sent&#34; folder and looking for messages with the reference VB043025 sent on 05/02/2025.</li>
<li>Shipment Preparation and Labeling Error:
<em>When the physical shipment was being prepared: The majority of the cartons were labeled with the new labels that corresponded to one of the ASNs that was sent to Saks on 05/02/2025. However, 3 cartons retained the old labels that were originally printed from the unsent Draft ASN (Message ID 44097832 from 04/29/2025).</em></li>
<li>Saks Fifth Avenue Receiving Shipment Labels:
When Saks Fifth Avenue received your shipment and scanned the cartons, they processed it against the ASN data they had actually received from you (one of the three ASNs sent on 05/02/2025, with Message IDs 4 Naturally, the labels on those 3 specific cartons (which were from the unsent Draft ASN, Message ID 44097832) did not match any information on the ASNs Saks had received and processed. This is why they reporte
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Michele Hackett [ 22/May/25 ]
Thank you, this helps a lot. Now, what action should I take? What should I send them? The Unsent ASNs in the draft folder?
Comment by Michael Hoang [ 22/May/25 ]
Hi Michele,
No worries just to clarify the situation with Saks Fifth Avenue and help them reconcile their records, I suggest is</li>
<li>Provide All ASN Data to Saks: You should provide Saks with the EDI data for all relevant ASNs:
The three ASNs sent on 05/02/2025 (Message IDs 44143287, 44132986, and 44143257). The original unsent Draft ASN (Message ID 44097832, created 04/29/2025).</li>
<li>Explain the Sequence: Let Saks fifth that an initial ASN (Message ID 44097832) was drafted and labels printed from it, but this specific draft was not sent. New ASNs (IDs 44143287, 44132986, 44143257) were then crea This transparency will help them understand the origin of the discrepancy.
Michele if you need anything else please let me know. You can also send them my response to you using the screenshots so they would understand what happened in detail as well.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Michele Hackett [ 22/May/25 ]
Thank you, Michael! I just sent everything over and hopefully that will be the solution!
Comment by Michael Hoang [ 22/May/25 ]
No worries I am here to help just let me know Michele.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</li>
</ol>
<h4 id="cs-44165-turck-855-not-delivered-created-21-may-25-updated-23-may-25-resolved-21-may-25">[CS-44165] Turck 855 Not Delivered Created: 21/May/25  Updated: 23/May/25  Resolved: 21/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Andrew Rice Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-https___ww.png      Outlook-https___ww (8ef67f31-b6f0-4c58-8762-9357390bf6a3).png      Outlook-https___ww (873d0ec1-0ac8-4eb7-90a6-40a9184b5ea9).png     Outlook-https___ww (0d597c43-c2af-4fc6-b72f-8f8a67b15b2b).png      Outlook-https___ww (f58dbe93-b25c-48da-8d27-35935dde15b2).png      image-********-
211040.png      image-********-210858.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello DTS Support,
I am looking into an issue brought to us by Turck Germany, who did not receive an 855 document from DTS yesterday. The file was sent by Banner at 4:30PM yesterday. I have also re-uploaded the file today in the hopes that it will process on the 2nd attempt. If there is some sort of translation error can you please send me the details? The hermes report email shows &#34;SUCCESS&#34;.
The 855 document had acknowledgment data for these POs:
4501050257 4501050333 4501050329 4501050328 4501050331 4501050338 4501050348 4501050380 4501050358 4501050266 4501050356 4501050272 4501050070 4501050258 4501050276 4501050337 4501050390 4501050571 4501050396 4501050410 4501050413 4501050419 4501050561 4501050527 4501050580 4501050583 4501035898 4501050578 4501050588 4501050593
Thanks, Andrew The content of this email is confidential and intended for the recipient specified in message only. It is strictly forbidden to share any part of this message with any third party, without a written consent of the sender. If you received this message by mistake, please reply to this message and follow with its deletion, so that we can ensure such a mistake does not occur in the future.
Comments
Comment by Andrew Rice [ 21/May/25 ]
Comment by Maria Keshwala [ 21/May/25 ]
Documents did not failed translation - checking delivery = files are stuck
Queuing data for delivery to output queue: &#39;0 - WebEDI - Out - Sent Folder&#39; to:
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 21/May/25 ]
There was an issue with the files injecting into the WEBEDI portal our development team is working on it they pushed the files this morning Manually please let us know if they still not seeing them but I see here they were delivered.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Andrew Rice [ 21/May/25 ]
Hello Maria,
Thank you for confirming the Turck files were manually processed, I will confirm with Turck that they received the data. I was not aware that there was a services outage yesterday, is this still in effect or has it been resolved?
Andrew
Comment by Maria Keshwala [ 23/May/25 ]
This issue was fixed Andrew
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44164-cat-chillicothe-asn-103276-created-21-may-25-updated-21-may-25-resolved-21-may-25">[CS-44164] CAT Chillicothe ASN 103276 Created: 21/May/25  Updated: 21/May/25  Resolved: 21/May/25</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Michelle Rice Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-********-205825.png      image-********-205740.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: ASN questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
I am trying to send an ASN and received this message. Message ID ********. The 830 came in so the trading partner should be ok. Can you help?
Thanks
Michelle Rice
Vee Engineering/Air Side Systems
<a href="mailto:<EMAIL>"><EMAIL></a>
765.778.7895 ext. 27
Comments
Comment by Michelle Rice [ 21/May/25 ]
Comment by Maria Keshwala [ 21/May/25 ]
Hello Michelle
ASNs for CAT Locations have different ISA IDs it does not use your DTS account such as DTS5035 all our webedi customers when creating a document the ISA ID is the DTS account number even though you created or trying to send is not going to send due to this issue we have integrated hard coded these ISA for CAT they will not reflect when creating an ASN Manually , this TP is added in your account and it has been but it does not use the DTS account as an ISA it uses the one assigned by CAT which will not reflect on manually entered ASNs. now if you have the file created on your end I can drop it manually on my end and have it process for this CAT location so it goes to the mapping process and reflects the correct ISAID. It does not work
ISA ID for CAT is
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44162-san-juan-pr-***********-has-left-you-a-message-7-second-s-long-created-21-may-25-updated-22-may-25-resolved-22-may-25">[CS-44162] SAN JUAN PR (+***********) has left you a message 7 second(s) long Created: 21/May/25  Updated: 22/May/25  Resolved: 22/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  message.wav
Request Type: Emailed request Request language: English
Request participants: None
Organizations: None
Label: ASN Label, ASN Creation
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Dear Voicemail to support : You received a message from SAN JUAN PR (+***********). The message is in mailbox 140, dated Wednesday, May 21, 2025 at 2:41:28 PM. You can get this message via the phone, or use the links below to download the message. Download the message and mark it as read Download the message and delete it Important: This will completely delete the message and this link will no longer work. Thank you!
Terms of Use | Privacy Policy | ©2025 Sangoma All Rights Reserved
<a href="https://www.facebook.com/sangoma">https://www.facebook.com/sangoma</a>  <a href="https://www.twitter.com/sangoma">https://www.twitter.com/sangoma</a>  <a href="https://www.linkedin.com/company/sangoma/">https://www.linkedin.com/company/sangoma/</a>  <a href="https://www.sangoma.com/">https://www.sangoma.com/</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
<em>message.wav  (120 kB)</em>
Comment by Maria Keshwala [ 22/May/25 ]
worked with this customer yesterday
no issues only questions regarding the ASN and also Labels I was able to assist
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44156-can-attachments-be-added-to-invoices-created-21-may-25-updated-28-may-25-resolved-28-may-25">[CS-44156] Can attachments be added to invoices Created: 21/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Michael Hoang
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Support
Request language: English
Request participants: None
Organizations: None
Label: WebEDI Questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
I need to attach our work order to the service invoices we submit. Where can I do this?
Comments
Comment by Michael Hoang [ 23/May/25 ]
Hello there
I wanted to clarify is this your first time sending attachments through EDI because Unfortunately, only the invoice can be sent through EDI. This does not have the capability to send with any attachments. If you sent attachments before can you show me screenshots too see how you did but as of right now it is not possible. Please let me know if you need any help .
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</p>
<h4 id="cs-44091-re-trafalgar-rp-created-21-may-25-updated-28-may-25-resolved-28-may-25">[CS-44091] Re: Trafalgar RP Created: 21/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Brad Rusin Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  ~WRD0001.jpg
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description  Hello, Antonio.
Unfortunately, we have not seen any of those POs on our end. Are you able to resend all of them? I reached out to Datatrans/Cleo and they confirmed they have not received a Bloomingdale&#39;s PO for our company since April 25. They have, however, received many other Bloomingdale&#39;s POs as recently as yesterday. Please let me know when they are resent so we can keep an eye out for them.
Datatrans/Cleo support is also copied to this email. The missing PO numbers and original transmission dates are below.
7809158 CM TRAFALGAR 05-05-25
7812810 TRAFALGAR 05-05-25
7926220 TRAFALGAR 05-12-25
7934833 CM TRAFALGAR 05-12-25
Thank you.
On Wed, May 21, 2025 at 8:16 AM Tom Mintal <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
fyi
---------- Forwarded message ---------From: Antonio Martinez <a href="mailto:<EMAIL>"><EMAIL></a> Date: Wed, May 21, 2025 at 8:14 AM Subject: RE: Trafalgar RP To: Tom Mintal <a href="mailto:<EMAIL>"><EMAIL></a>
Hi Tom,
Thank you, I hope all is well with you too! I am seeing the below POs have been transmitted in our system over the past two weeks and you can expect to receive POs this week as well.
7809158 CM TRAFALGAR 05-05-25
7812810 TRAFALGAR 05-05-25
7926220 TRAFALGAR 05-12-25
7934833 CM TRAFALGAR 05-12-25
Thanks!
Antonio Martinez | Senior Assistant Buyer | Men’s Tailored Clothing &amp; Dress Furnishings
Bloomingdale’s | 28-07 Jackson Avenue 19th FL, Long Island City, NY 11101
From: Tom Mintal <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 21, 2025 8:35 AM To: Antonio Martinez <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Trafalgar RP
⚠ EXT MSG:
Hi Antonio,
Hope you&#39;re having a good week.
I&#39;m just checking in regarding the replenishment purchase (RP) order. We haven&#39;t received one since the week of April 28th, and I wanted to see if an order is scheduled to be sent this week.
Thanks for the update!
Best regards,
Tom
<em>| Tom Mintal Trafalgar National Sales Manager <a href="mailto:<EMAIL>"><EMAIL></a> : 224.301.8361 Phoenix Leather Goods : <a href="http://www.phoenixleathergoods.com">www.phoenixleathergoods.com</a> [www.trafalgarstore.com|https://urldefense.com/v3/<em>http:/www.trafalgarstore.com</em>;!Unable to render embedded object: File (EkHEP60!DEXEp6EIoIz99cFbKPIHJqYnTHiWOpINqL-HSuSJ3Pi1bzQNYf3bejFbaFCgVYbTw1NspZ35UaZ4sc4_3dd7TDmUKiql3yEMkWOk$] : [ nuorder.com/trafalgar) not found.!EkHEP60!DEXEp6EIoIz99cFbKPIHJqYnTHiWOpINqL-HSuSJ3Pi1bzQNYf3bejFbaFCgVYbTw1NspZ35UaZ4sc4_3dd7TDmUKiql3x7P0UHT$] |</em></p>
<ul>
<li>This is an EXTERNAL EMAIL. Stop and think before clicking a link or opening attachments.
Comments
Comment by Brad Rusin [ 21/May/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
Hi Brad,
I re-transmitted the below you should see this by tomorrow morning the latest, please let me know if you do not see by then. For the two transmitted on 5-5, we can cancel these as anything generated by our system this week will take this into account, and you should receive this weeks orders by EOW with 5-19 in the name.
7926220 TRAFALGAR 05-12-25
7934833 CM TRAFALGAR 05-12-25
Thanks,
Antonio Martinez | Senior Assistant Buyer | Men’s Tailored Clothing &amp; Dress Furnishings
Bloomingdale’s | 28-07 Jackson Avenue 19th FL, Long Island City, NY 11101
Comment by Maria Keshwala [ 21/May/25 ]
Good morning
I will look into this and advise . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Brad Rusin [ 21/May/25 ]
Thanks, Antonio.
I see them already. I&#39;m not sure what happened with the previous transmissions, but we will fill these ASAP.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
Perfect, thank you for confirming!
Antonio Martinez | Senior Assistant Buyer | Men’s Tailored Clothing &amp; Dress Furnishings
Bloomingdale’s | 28-07 Jackson Avenue 19th FL, Long Island City, NY 11101
Comment by Nicholas Sanchez [ 28/May/25 ]
<a href="https://datatrans-inc.atlassian.net/browse/CS-44299">https://datatrans-inc.atlassian.net/browse/CS-44299</a>
Nicolas Sanchez Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ul>
<h4 id="cs-44089-edi-missing-852-867-files-for-tazverik-05-21-2025-dts6580-ipsen2-created-21-may-25-updated-28-may-25-resolved-28-may-25">[CS-44089] EDI Missing 852 &amp; 867 files for Tazverik - 05/21/2025 -DTS6580-Ipsen2 Created: 21/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.gif
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: Missing documents FTP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi DTS team,
Good morning,
We haven’t received Tazverik EDI 852 &amp; 867 files for 05/21/2025. We are seeing the files in data trans server. But we haven’t received any files in the SFTP.
Could you please reshare the files.
Issue: Missing files 852 , 867
Date Impacted: 05/21/2025(report Start date 05/21/2025 Report end date 05/21/2025)
Filetype: 852- Product Activity Report, 867- Transfer Reports
Distributer impacted :ABSG Speciality , Cardinal Health , McKesson Plasma, McKesson Specialty Health.
Brand impacted: Tazverik
DTS connection: DTS6580-Ipsen2
Thanks &amp; Regards,
Vantapati Venkata Madhavi
Capgemini India
<a href="http://www.capgemini.com">www.capgemini.com</a>
This message contains information that may be privileged or confidential and is the property of the Capgemini Group. It is intended only for the person to whom it is addressed. If you are not the intended recipient, you are not authorized to read, print, retain, copy, disseminate, distribute, or use this message or any part thereof. If you receive this message in error, please notify the sender immediately and delete all copies of this message.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
Comment by Maria Keshwala [ 21/May/25 ]
We will look into this and advise. Thank you
Comment by Maria Keshwala [ 28/May/25 ]
we showed that all your files were delivered on 05/21/2025 at 10:30 PM Mountain Time. if you have not received them please let me know thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44088-edi-852-867-missing-files-05-21-2025-dts6357-ipsen1-created-21-may-25-updated-28-may-25-resolved-28-may-25">[CS-44088] EDI 852 &amp; 867 Missing Files - 05/21/2025 - DTS6357-Ipsen1 Created: 21/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.gif
Request Type: Emailed request
Request language: English Request participants: Organizations: None
Label: Missing documents FTP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi DTS team,
Good morning,
We haven’t received EDI 852 &amp; 867 files for 05/21/2025. We are seeing the files in data trans server. But we haven’t received any files in the SFTP.
Could you please reshare the files.
Issue: Missing files 852 &amp; 867
Date Impacted: 05/21/2025 (report Start date 05/21/2025 Report end date 05/21/2025)
Filetype: 852- Product Activity Report , 867- Transfer Reports
Distributer impacted :ABSG Speciality , Cardinal Health , CuraScript, McKesson Plasma, McKesson Specialty Health,
Cesar Castillo, Metro Medical , Morris &amp; Dickson.
Brand impacted: Dysport/Somatuline/Onivyde
DTS connection: DTS6357-Ipsen1
Thanks &amp; Regards,
Vantapati Venkata Madhavi
Capgemini India
<a href="http://www.capgemini.com">www.capgemini.com</a>
This message contains information that may be privileged or confidential and is the property of the Capgemini Group. It is intended only for the person to whom it is addressed. If you are not the intended recipient, you are not authorized to read, print, retain, copy, disseminate, distribute, or use this message or any part thereof. If you receive this message in error, please notify the sender immediately and delete all copies of this message.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
Comment by Maria Keshwala [ 21/May/25 ]
We will look into this and advise. Thank you
Comment by Maria Keshwala [ 28/May/25 ]
Good morning did you guys received your files I was out of the office . Please let me know if this issue has been resolved so I can closed this ticket thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
we show the files were delivered if you have not received them please let me know thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44085-lowes-loads-missing-created-21-may-25-updated-22-may-25-resolved-22-may-25">[CS-44085] Lowes Loads missing Created: 21/May/25  Updated: 22/May/25  Resolved: 22/May/25</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-125748.png      image-********-125636.png      image-********-125839.png      image001.png
Request Type: Phone call
Request language: English
Request participants: None
Organizations: Label: Delayed document delivery
CSAT Comment: {{webhookResponse.body.value.comment}}
Description  Ticket for the missing documents for Lowes
Comments
Comment by Maria Keshwala [ 21/May/25 ]
Lowes sent the document at 6:am EST
Files came in in ECS-02 around 4:01 AM MT
Files are in the T drive fromdts folder waiting to be picked up
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 21/May/25 ]
Hello Donna
Files are in the fromdts folder waiting to be picked up see below
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 21/May/25 ]
Hello Donna you should be all set.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 21/May/25 ]
it was a permission issue you should be able to grab the files please let me know
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
They still are not in my system will let you know when they come over.
Comment by Maria Keshwala [ 21/May/25 ]
I still see them on my end have the IT on your end to run a pick up so you guys can get it
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 21/May/25 ]
if that doesnt work please get back to me
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/May/25 ]
I still don’t see anything on my side.
Donna Reed
Home Run Inc.
Operations Manager
800.543.9198 ext. 1001
937.376.1379 fax
<em><em>+ <a href="mailto:<EMAIL>"><EMAIL></a> +</em></em>
Comment by Maria Keshwala [ 22/May/25 ]
Hi Donna they were picked up from your side you should of received them thank you let me know if there still an issue . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44084-missing-granger-clark-services-855-in-data-trans-portal-created-21-may-25-updated-23-may-25-resolved-23-may-25">[CS-44084] Missing Granger &amp; Clark services 855 in Data Trans portal Created: 21/May/25  Updated: 23/May/25  Resolved: 23/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Vinothkumar Narayanamoorthy Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      image (c60a1256-b338-49ea-8e06-ac275bf42a49).png      image-20250523-202619.png      image-20250523-202658.png    Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: Missing documents WebEDI
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Sandy, Good morning.
Below Grainger &amp; Clark Services 855&#39;s are missing in Data Trans portal. Kindly check the same.
Thank you Vinoth
Comments
Comment by Vinothkumar Narayanamoorthy [ 21/May/25 ]
Comment by Maria Keshwala [ 21/May/25 ]
We will look into this and advise. Thank you
Comment by Vinothkumar Narayanamoorthy [ 21/May/25 ]
Thank you Maria,
Regards Vinoth
Comment by Maria Keshwala [ 23/May/25 ]
855 processed on 05/21/2025
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
Good evening
All the 855s were processed and acknowledged on 05/21/2025 we were experiencing an issue with the sent folder that was fixed by the developers. . Have a nice weekend
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44075-asn-signal-correction-cat-cq-created-20-may-25-updated-28-may-25-resolved-28-may-25">[CS-44075] ASN Signal correction - CAT CQ Created: 20/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Blanca Gallegos Assignee: Maria Keshwala
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      image (6e0c95b0-a46f-46f3-a0a9-9fc734fc1916).png      image-********-153748.png      image-********-154703.png      image-********-155913.png      image-********-160349.png      image (ba212224-1fdf-462e-9032-570e33d6221a).png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: ASN errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello @Sandy Karidas
After feedback from our customer and a positive synch with their system, we found out that the problem is related to the following field:
Trading Parter: CAT-CQ // ASN856) // Supplier Manufacturer // Additional Name : US
I made the changes in the template, but when I want to create a new ASN, the value is overwritten with &#34;E0562J0&#34; (see second screenshot). Can you help us removing this overwriting please?
ASN that passed thru the customer system: Message ID 44338969
Template:
Draft #44344379:
Thank you in advance,
Comments
Comment by Blanca Gallegos [ 20/May/25 ]
Comment by Maria Keshwala [ 21/May/25 ]
Good morning
We will review and advise thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hello Blanca
I sent you a email on the other ticket to have a zoom meeting and take care of the errors you are facing. Let me know if 11am EST today works thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Blanca Gallegos [ 28/May/25 ]
Hello @<a href="mailto:<EMAIL>"><EMAIL></a>!
Yes, if you can set up a Zoom meeting that will work. Send me the invitation to this email: <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Maria Keshwala [ 28/May/25 ]
Hello I will send you the invite to meet at 11:30 am EST
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
I just sent you the invitation
Maria Keshwala is inviting you to a scheduled Zoom meeting.
Topic: Issues with ASN CAT -Time: May 28, 2025 11:30 AM Eastern Time (US and Canada) Join Zoom Meeting <a href="https://cleo.zoom.us/j/96070907372">https://cleo.zoom.us/j/96070907372</a>
Meeting ID: 960 7090 7372
One tap mobile +16465588656,,96070907372# US (New York) +16469313860,,96070907372# US
Dial by your location</p>
<ul>
<li>****** 558 8656 US (New York)</li>
<li>****** 931 3860 US</li>
<li>****** 715 8592 US (Washington DC)</li>
<li>****** 224 1968 US</li>
<li>****** 205 3325 US</li>
<li>****** 626 6799 US (Chicago)</li>
<li>****** 205 0468 US</li>
<li>****** 215 8782 US (Tacoma)</li>
<li>****** 248 7799 US (Houston)</li>
<li>****** 209 5623 US</li>
<li>****** 347 5053 US</li>
<li>****** 473 4847 US</li>
<li>****** 217 2000 US</li>
<li>****** 444 9171 US</li>
<li>****** 900 9128 US (San Jose)</li>
<li>****** 278 1000 US</li>
<li>****** 359 4580 US</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free
Meeting ID: 960 7090 7372
Find your local number: <a href="https://cleo.zoom.us/u/aLOR2ZggW">https://cleo.zoom.us/u/aLOR2ZggW</a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Customer creates the ASN from the 830 by clicking on response
once is created is grabbing an invalid assigned by buyer , customer added US as a default but still grabbing the wrong one for this case I think hard coding the value in the map will work .
Document default coded with value US
Reason why is overwriting the value is because on the 830 the TO sends the code “E0562J0” not “US” per customer the file does not process when sending the value E0562J0 but when sending “US” FILE PROCESS . at this point the best approach is to hard code the value within the DOM map
as this will be hardcoded specific for this customer the map has been cloned
<a href="https://admin-prod.datatranswebedi.com/dom/8714">https://admin-prod.datatranswebedi.com/dom/8714</a>
Even thought we hard coded the value is not reflecting on the document still coming as the 830 document
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hello Blanca
The value “ E0562J0” is coming from the 830 I have hard coded and is not working as this is coming from the 830 the best is to reach out to Caterpella CQ and have them fixed the 830 on the N1:01 SU - the value in the N104 change to &#39;US” Instead of “E0562J0”
CAT’s Email
<a href="mailto:<EMAIL>"><EMAIL></a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Blanca Gallegos [ 28/May/25 ]
Hello @<a href="mailto:<EMAIL>"><EMAIL></a> The value mentioned and shown below relates to the &#34;Assigned by Buyer&#34; field, which is correct. The field needed to be changed is the one called &#34;Assigned by Seller&#34;:
I know that we just had a call about this, so I appreciate your support to have this finally fixed. Thanks in advance,
Comment by Maria Keshwala [ 28/May/25 ]
Hello Blanca
as mentioned I cant corrected we work with codes that was a mis typo the name I worked what was behind the scenes they need to send the correct code .
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</li>
</ul>
<h4 id="cs-44067-810s-showing-total-0-in-the-sent-folder-and-open-document-created-20-may-25-updated-21-may-25-resolved-20-may-25">[CS-44067] 810s showing total 0 in the Sent folder - and open document Created: 20/May/25  Updated: 21/May/25  Resolved: 20/May/25</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Michelle Ostrowski Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250520-200828.png      image-20250520-202456.png      image-20250520-201610.png      image-20250520-201738.png      image-20250520-201513.png     image-20250520-210748.png      image-20250520-210950.png
Request Type: Phone call
Request language: English
Request participants: None
Organizations: Label: WebEDI Questions, Invoice errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
This ticket is for the 0 totals on your invoices - Thank you
Comments
Comment by Maria Keshwala [ 20/May/25 ]
Totals are not showing in the portal – checking to see if the EDI documents are displaying the totals in the invoices
confirmed that the files are going out with their totals checked batch IDs below
shwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
281-292-868
Comment by Maria Keshwala [ 20/May/25 ]
1- requested Invoice numbers to Match the PO numbers from the TP only for UNFI already set up for Meijer
Need a Mapping Rule .
2- Hard code or add a default for the DUNS number = 1208600130000 - allowed default in the Global DOM
3 -
4-
5 allow default UOM , weight Shipped units
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 20/May/25 ]
850 = BEG03 segment
810= BIG02=segment
Mapping setup for the 810 to reflect the invoice number from the PO number
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 20/May/25 ]
Hello Michelle
Mapping rule to auto populate the 850 PO number as the Invoice number into the 810 has been setup . I have tested and is working. Please let me know if you need anything else. If you are experiencing another issue not related this please create a new ticket. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Michelle Ostrowski [ 20/May/25 ]
Thank you! Have a fabulous day! Sent from my iPhone
On May 20, 2025, at 3:15 PM, Maria Keshwala <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Comment by Maria Keshwala [ 21/May/25 ]
Thank you 🙂
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44065-loads-timing-out-despite-responding-within-2-hours-created-20-may-25-updated-22-may-25-resolved-22-may-25">[CS-44065] Loads Timing Out Despite Responding within 2 hours Created: 20/May/25  Updated: 22/May/25  Resolved: 22/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Jada Johnson Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-2qkqu3zi.png      image-********-152627.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: Delayed document delivery
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello,
Please advise and assist with shipments timing out with AAFES even though we cancelled or committed to the load within 2 hours. There&#39;s either a delay in DataTran receiving/sending the status update or it&#39;s not being received at all.
3183489/123264823: DataTran Received EDI 204: 11:04 AM Created in Tai: 11:16 AM
Cancelled in Tai: 12:36 PM Timed Out by AAFES: 2:03 PM
3183488/123264822: DataTran Received EDI 204: 11:04 AM Created in Tai: 11:16 AM Committed in Tai: 12:30 PM Timed Out by AAFES: 2:03 PM Thank you,
Comments  Comment by Jada Johnson [ 20/May/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 20/May/25 ]
Thanks Jada!
Confirmed the EDI file is created and available for pickup within 3 seconds of the load being updated:
3183489/123264823: Filename: TMZC5010_001695568P_990_123264823_20250520_4645442.edi
3183488/123264822: Filename: TMZC5010_001695568P_990_123264822_20250520_9726135.edi
Thanks!
Need more info? Please check out our Knowledge Base.
For more visibility on your tickets, please check out our Ticket Portal.
<a href="https://www.tai-software.com">https://www.tai-software.com</a> Alisha Graves-Sermons Project Manager
color: Color value is invalid
************ Option: 5 - Voicemail Only &lt;tel:************ Option: 5 - Voicemail Only&gt;
color: Color value is invalid
<a href="mailto:<EMAIL>"><EMAIL></a> color: Color value is invalid
color: Color value is invalid
<a href="http://www.tai-software.com">www.tai-software.com</a> color: Color value is invalid
color: Color value is invalid
Status IO color: Color value is invalid
401 Main St, Suite 201, Huntington Beach, CA 92648
On Tue, May 20, 2025 at 12:44 PM, Jada Johnson <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Hello,
Please advise and assist with shipments timing out with AAFES even though we cancelled or committed to the load within 2 hours. There&#39;s either a delay in DataTran receiving/sending the status update or it&#39;s not being received at all.
3183489/123264823: DataTran Received EDI 204: 11:04 AM Created in Tai: 11:16 AM Cancelled in Tai: 12:36 PM Timed Out by AAFES: 2:03 PM
3183488/123264822: DataTran Received EDI 204: 11:04 AM Created in Tai: 11:16 AM Committed in Tai: 12:30 PM Timed Out by AAFES: 2:03 PM Thank you,
!<a href="https://c-Ym-04.na1.hs-service-engage.com/Cto/UA+23284/c-Ym-04/R5R8b42-yN9cfkNG2fJj6W1_khn53z7rSqW22Wkg_1XnxxgW1GdphP1T-J33W3LFJ-53H59wPW1N5Vzx1V2Sskn1V0Nqb4W1">https://c-Ym-04.na1.hs-service-engage.com/Cto/UA+23284/c-Ym-04/R5R8b42-yN9cfkNG2fJj6W1_khn53z7rSqW22Wkg_1XnxxgW1GdphP1T-J33W3LFJ-53H59wPW1N5Vzx1V2Sskn1V0Nqb4W1</a>!
Comment by Maria Keshwala [ 20/May/25 ]
We will look into this and advise
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Jada Johnson [ 21/May/25 ]
Good morning, We are continuing to experience issues with loads Timing Out, even though we are rating and working them on time. This not only disrupts our workflow but also reflects poorly with our customer. Please advise on a resolution as soon as possible. 3182807/123270701 DataTran Received the 204: 05/20/2025 02:43 PM Created in Tai: 05/20/2025 02:56 PM Cancelled in Tai: 05/20/2025 03:22 PM Cancellation/Timed Out Received in DataTran: 05/21/2025 09:46 AM
Thank you,
Comment by Maria Keshwala [ 21/May/25 ]
<a href="https://datatrans-inc.atlassian.net/browse/CS-40667">https://datatrans-inc.atlassian.net/browse/CS-40667</a> (Nico&#39;s ticket )
3184491= 05/20/2025
204 - received from AFFESS 8:10:00 CST
204 sent Tramazon Tai 8:10:02 am CST
ECS-03 990 received from Tramazon to AAFES 990 pickup
990 came in at 05/21/2025 08:26:07 am CST
990 sent to AFFES At 08:26:13 am CST
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 22/May/25 ]
Good morning
We are experiencing a delay from the Tramazon Tai Team as shown in the timeline below. Please note that we at DataTrans/Cleo are sending files in a timely manner. Thank you
Details:
Document Reference Number: 3184491 Date: 05/20/2025 204 Received from AFFESS: 05/20/2025 at 08:10:00 AM CST 204 Sent to Tramazon Tai: 05/20/2025 at 08:10:02 AM CST 990 Received from Tramazon Tai: 05/21/2025 at 08:26:07 AM CST
990 Sent to AFFESS A: 05/21/2025 at 08:26:13 AM CST
As shown, there was nearly a one-day delay in receiving the 990 from Tramazon Tai, which is impacting the overall processing timeline.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44064-braas-810-missing-lines-ds-11452-created-20-may-25-updated-23-may-25-resolved-23-may-25">[CS-44064] Braas 810 Missing Lines- DS-11452 Created: 20/May/25  Updated: 23/May/25  Resolved: 23/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Andrew Rice Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-https___ww.png      Outlook-https___ww (1038eb1b-1856-4447-a2ac-8583a7e7388f).png      Outlook-https___ww (1fff9ab5-0134-401d-ab87-d322bd122d95).png      Outlook-https___ww (8b4b1610-9454-48da-a765-bbd24eeca331).png      Outlook-https___ww (bc55b911-427c-43d2-bed8-ce05de95cfbf).png     <strong>us_ascii_Q_Banner_810_Data_Report___2025_04_16_08_00_10_to_2025_04_16_0</strong> _<strong>us_ascii_Q_9_00_10</strong>.eml      20250416090012_braas_810_O.dat      image-20250523-142156.png      image-20250523-145703.png      image-20250523-145845.png      image-20250523-145914.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Dev ticket, Invoice errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello DTS Support,
On April 16th we sent an 810 file for Braas / MOTION-AI with invoice 5394736, which is for 123 line items. However, the translated file is incomplete with only 112 line items. I&#39;ve attached the Hermes 810 report email from that day which shows &#34;SUCCESS&#34; for all outbound 810s, along with a copy of the original dat file.
Please look into this ASAP and let us know what caused these 11 lines to fail translation.
Andrew The content of this email is confidential and intended for the recipient specified in message only. It is strictly forbidden to share any part of this message with any third party, without a written consent of the sender. If you received this message by mistake, please reply to this message and follow with its deletion, so that we can ensure such a mistake does not occur in the future.
Comments
Comment by Andrew Rice [ 20/May/25 ]
<em>20250416090012_braas_810_O.dat  (3.40 MB)</em>
<em>[^_us_ascii_Q_Banner_810_Data_Report_2025_04_16_08_00_10_to_2025_04_16_0_ __us_ascii_Q_9_00_10.eml] _(34 kB)</em>
Comment by Maria Keshwala [ 20/May/25 ]
Hi Andrew
I will look into this I will keep you posted . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 20/May/25 ]
Andrew just confirming I checked on my end this invoice was sent with other invoices correct
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Andrew Rice [ 21/May/25 ]
Hi Maria,
That is correct, it was one of many in the 810 file.
Thanks, Andrew
Comment by Maria Keshwala [ 23/May/25 ]
Removed the other invoices from this batch only 5394736 in this file redrop the file in the Split Outbox
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
Hi Andrew
I have Isolated this invoice and redrop it for reprocessing I will add this TP issue to the dev ticket we have open . I will keep you posted once the file processed . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
passed Delta -
File sent with the 123 Line Items
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
Hi Andrew
I have sent the file with the 123 Line Items to Brass .
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
<a href="https://dtsprojects.atlassian.net/browse/DS-11452">https://dtsprojects.atlassian.net/browse/DS-11452</a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44061-asn-created-20-may-25-updated-28-may-25-resolved-28-may-25">[CS-44061] ASN Created: 20/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Candie Rogers Assignee: Michael Hoang Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: ASN rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good Afternoon,
Can you please verify that the ASN below was accepted? All the others show accepted and this one is still in yellow and showing SENT. Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) 419.947.2647 ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comments
Comment by Candie Rogers [ 20/May/25 ]
Comment by Lauren Cohn [ 20/May/25 ]
Reach out to the customer
“Hi Candie, I am looking into this for you and will get back to you shortly”
Next, check the servers, check to see if this did infact send outbound and if any 997’s were received yesterday that relate to this
From there, if a 997 was not received you can offer her the below:
” Unfortunately, I am not showing that a positive 997 acknowledgement was returned. We have two options for this scenario. Option 1, we can reach out to your partner on your behalf to confirm if this document was received or not and then retransmit if this was a no. Option 2, I can go ahead (with your permission) with resending the document to trigger the 997 if the partner has already confirmed with you that the document was not received. Please let me know how you would like to proceed.”
Lauren Cohn | Technical Support Representative Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************
If urgent, please call us with the ticket number and details of the issue.
Comment by Michael Hoang [ 20/May/25 ]
HI Candi,
I am here to help you, I am just going to dive deeper and will get back to you shortly!
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Michael Hoang [ 21/May/25 ]
Hi Candie,
Thanks for reaching out. I’ve reviewed ASN 44326760 for O’Reilly Automotive (PO SO105373), and as of now it shows a status of SENT in WebEDI.
This means the ASN was successfully transmitted from our system, but we have not yet received a 997 Functional Acknowledgment from the trading partner confirming receipt.
Would you like me to:</p>
<ol>
<li>Reach out to the partner on your behalf to confirm if they’ve received the file 2. Resend the ASN directly to trigger a new 997 response
Let me know how you’d like to proceed, and I’ll take care of it right away. I will take care of everything.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</li>
</ol>
<h4 id="cs-44058-invoices-adding-extra-lines-created-20-may-25-updated-23-may-25-resolved-23-may-25">[CS-44058] Invoices Adding Extra Lines Created: 20/May/25  Updated: 23/May/25  Resolved: 23/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Michael Hoang Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: Invoice questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello,
When we first setup our account, we made a slight customization so that EDI would not bring in the blank lines or the FedEx tracking line from the QuickBooks invoice, into the EDI invoice. It seems like that customization has been reversed and we are now having to delete the blank lines and tracking info line before we are able to send the invoice from ‘draft’. Can you please help with this issue?
Kendall Reidmiller
| Kendall Reidmiller Office: 817-483-9883 Email: <a href="mailto:<EMAIL>"><EMAIL></a> Pipettesupplies.com  <a href="https://www.facebook.com/PipetteSuppliesInc">https://www.facebook.com/PipetteSuppliesInc</a> <a href="https://www.linkedin.com/company/pipette-supplies-inc">https://www.linkedin.com/company/pipette-supplies-inc</a> |
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 20/May/25 ]
Comment by Michael Hoang [ 21/May/25 ]
Hi Kendall
I am going to be taking care of this, let me dive deeper for you and make sure I get this situated. I will be back shortly, if you need any help with anything else please let me know.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Michael Hoang [ 21/May/25 ]
Hi Kendall,
Prior to any changes, This is important. could you please provide two sample files for comparison. Because I wanted too see what is happening on your screen. So I can help you.</li>
</ul>
<ol>
<li>QuickBooks - Screenshot of the data file showing the fields in invoice. Prior to you making any changes. 2. WebEDI version – the corresponding draft invoice in WebEDI (Message ID screenshot).
Reviewing both versions side by side will help us pinpoint where the extra lines originate and ensure the adjustment is accurate. Thank you!
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2</li>
</ol>
<h4 id="cs-44046-dee006-john-deere-worldwide-po-pdm44504667-asn-error-item-number-created-20-may-25-updated-30-may-25-resolved-30-may-25">[CS-44046] DEE006 JOHN DEERE WORLDWIDE - PO PDM44504667 - ASN Error - ITEM NUMBER Created: 20/May/25  Updated: 30/May/25  Resolved: 30/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Glen Houghtaling Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      ********041510_AE856MCT0273496.txt      edi_D20250520T090112.edi      image001 (b3b302c9-877a-418d-b445-e07fe3013cce).png     DEE006_JOHN_DEERE_WORLDWIDE___PO_PDM44504667____ASN_Error___ITEM _NUMBER.eml     DEE006_JOHN_DEERE_WORLDWIDE___PO_PDM44504667____ASN_Error___ITEM _NUMBER (7649cb17-d2f8-4656-8651-e1d0c8ed68a1).eml      image-20250527-194849.png      image-********-130935.png      image-********-131744.png      image-********-133254.png      image004.png     ********041510_AE856MCT0273496 (c0f6a610-8c02-4d31-be78-55e8c7f69917).txt      ********034508_AE856MCT0275084.txt     ********034508_AE856MCT.********15431591      image004 (0d15af1f-8d03-44a7-88a6-0c88c34282b0).png      image001 (bc92bf34-7b3f-4bb2-b878-4817938f2fbf).png      image-20250530-125717.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Global Shop, ASN rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Datatrans,
Milcut received an 824 file indicating the product number was blank. Upon review, It appears the product number is being excluded from the ASN, and the ASN only lists one box (two boxes were shipped). Can you review the data, and let us know how to correct this problem? Thank you.
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
Comments
Comment by Glen Houghtaling [ 20/May/25 ]
<em>********041510_AE856MCT0273496.txt  (6 kB)</em>
<em>edi_D20250520T090112.edi  (0.5 kB)</em>
Comment by Michael Hoang [ 20/May/25 ]
Hi Glen, Thanks for bringing this to our attention. I’m pulling the ASN and 824 details now and will circle back shortly with findings or next steps. I will take care of things for you.
Michael Hoang
Cleo Communications
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>"><EMAIL></a> [************|tel:**********] opt. 2
Comment by Maria Keshwala [ 22/May/25 ]
Hi Glen Im working on this as we speak
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 27/May/25 ]
824 Message ID ********
Application Error Condition Code
Invalid Combination
Free Form Message
ITEM NUMBER IS SPACES AND CANNOT BE PROCESSED
Data Element Reference Number
ASN Shipment ID =0273496-0000
date 05/19/2025
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 27/May/25 ]
Hi Glen whats the ASN message ID in WEBEDI and shipment ID I cant find it with the information given on the 824 please provide me this information to further investigate . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Glen Houghtaling [ 27/May/25 ]
Hello,
See message below. Have a great day!
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
<em>[^DEE006_JOHN_DEERE_WORLDWIDE__PO_PDM44504667__ASN_Error__ITEM NUMBER.eml] _(30 kB)</em>
Comment by Maria Keshwala [ 27/May/25 ]
hi GLen thats the 824 whats the 856
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Glen Houghtaling [ 27/May/25 ]
Hello,
This is the EDI data I found when I learned of the issue. I don’t know what happened on the portal, it does not seem to be there anymore. I used reference 0273496 to get the data below. I did not delete the message, nor did I try to process a correction.
ST<em>856</em>0402
BSN<em>00</em> 0273496-0000 <em>********</em>160223<em>0003
DTM</em>011<em>********</em>1627<em>CD
DTM</em>017<em>********</em>1627<em>CD
HL</em>1<strong>S
MEA<em>PD</em>G<em>0</em>LB
TD5<em>B</em>2<em>UPSN</em>LT
REF<em>2I</em>1Z5814160373720712
N1<em>SF</em>MILCUT INC.<em>92</em>3212
N1*ST</strong>92<em>*********
HL</em>2<em>1</em>P
HL<em>3</em>1<em>P
CLD</em>1<em>1
REF</em>98<em>PALLE
REF</em>LA<em>********************
HL</em>4<em>1</em>O
PRF<em>PDM44504667
CLD</em>1<em>50
REF</em>98<em>BOX
REF</em>LA<em>********************
CTT</em>4<em>0
SE</em>22<em>0402
GE</em>1<em>899
IEA</em>1**********
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
<em>[^DEE006_JOHN_DEERE_WORLDWIDE__PO_PDM44504667__ASN_Error__ITEM NUMBER (7649cb17-d2f8-4656-8651-e1d0c8ed68a1).eml] _(30 kB)</em>
Comment by Maria Keshwala [ 27/May/25 ]
yeah is not coming up for me not sure if it was deleted
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 27/May/25 ]
it was deleted it was on restage mode I will restore this file again
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications</em>
<a href="mailto:<EMAIL>"><EMAIL></a>
************
Comment by Glen Houghtaling [ 27/May/25 ]
Can’t you possibly duplicate the error it by entering the Milcut data from Global Shop?
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
Comment by Maria Keshwala [ 27/May/25 ]
Yeah Im doing testing on this the line Item number is missing so I will run the file you sent us originally and see
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 27/May/25 ]
Original File in ECS-03 BATCH ID *********
Linx Map = 502 <a href="https://admin-prod.datatranswebedi.com/dommap/502/edit">https://admin-prod.datatranswebedi.com/dommap/502/edit</a>
No pack level on the ASN still missing
checking a good file and see which record should display the pack level
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Message ID ******** same ASN structure from 03/27/2025 as the rejected ASN
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hello Glen this is a tricky one I have to escalate this to an analyst to see what the issue is .
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hi Glen you said two boxes were sent but on the ASN it shows a line Item for PALLE and the other for BOX two different shipment container
I think this might be the issue -
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hi Glen
Please check the structure of your ASN it looks a bit off the T record has the shipment container ending in 201 same is at the end of the document and in the middle you have the T record with a different serial number and both ending in 01 one has “PALLE” the other one “BOX”
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
I also have asked Holly to take a look at this file I will advise once she works on this matter. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Asked Holly
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hi Glen per the analyst it appear that you guys change your 856 for this trading partner this map has not been updated since 2018 and this has been happening all the way since February you been sending this structure flat file , to fixed this issue it needs a map mod which it will cost $550.00 to modify the mapping for this ASN let me know to send the work authorization . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Glen Houghtaling [ 28/May/25 ]
Hi Maria,
I don’t think there was any change to the output from Global Shop. My research shows this error occurs when there are multiple boxes on the order. I was under the impression that ASN’s (856) for all Deere locations processed in a like fashion.
It appears ASN’s (856) files were processing properly when there was a single box shipped. Why would the map be bad for multiple boxes being shipped?
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
Comment by Maria Keshwala [ 28/May/25 ]
Sending this back to the analyst
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hi Glen,
Could you please provide an ASN from a similar location that includes multiple shipments within the file? We’d like to use it for comparison against the current mapping.
If possible, please also share the Message ID of a document that processed successfully without any issues. That would be very helpful.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Glen Houghtaling [ 29/May/25 ]
Hi Maria,
I have attached a file for you to compare. Reference 0275084-0000 shipped with many more boxes than the location in question. As I stated earlier, Milcut EDI output from Global Shop has not changed to my knowledge.
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
<em>********041510_AE856MCT0273496 (c0f6a610-8c02-4d31-be78-55e8c7f69917).txt  (6 kB)</em>
<em>********034508_AE856MCT0275084.txt  (21 kB)</em>
Comment by Maria Keshwala [ 29/May/25 ]
Thank you Glen
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Glen Houghtaling [ 29/May/25 ]
Hi Maria,
This file is all Deere. I can see there was an issue with 0274213-0000. It looks like there is different data being supplied for location DEE001 vs DEE006. I am not sure that is a problem.
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
<em>********034508_AE856MCT.********15431591  (57 kB)</em>
Comment by Maria Keshwala [ 29/May/25 ]
could be thats why we said if there was a change on your end map mod is required or you guys can see the structure of the flat file and determine whats different but I can check in a bit.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 30/May/25 ]
Hi Glen
You guys completely change you flat file for this TP H2999 see the structure of the old file then see the new file please check this on your end and let us if you need a map mod or you guys can look into this on your end and fixed it back to how it was. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-44044-asn-sent-status-not-delivered-dts-680-created-20-may-25-updated-29-may-25-resolved-28-may-25">[CS-44044] ASN SENT STATUS NOT DELIVERED - DTS-680 Created: 20/May/25  Updated: 29/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250523-125224.png      image-20250523-140425.png      Violation for no ASN - where they took out violation.pdf      Violation for no ASN.xls      image-20250527-173805.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Document not delivered to TP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Good Morning,
The following ASN was sent on 5/14 to our trading partner. It shows sent and in yellow. Our trading partner said they did not receive. I do not see an error. Can you find out why they would not have received? They fined us $500.
Thanks,
Amanda
ISA Control #: 000004198 Group Control #: 4198 Notes
Sent Date/Time: 05/14/2025 03:39 PM Last Modified Date/Time: 05/14/2025 03:39 PM Ack Status: Ack Date:
Associated Documents: (850) Ref: VVRS39 Ack Control: Ack Group: Ack Document:
Staples
Sincerely,
Amanda Gentry
Controller/CFO
BSP Filing Solutions
<a href="http://www.bspfiling.com">www.bspfiling.com</a>
800-356-3494
&#34;Great things are done by a series of small things brought together.&#34;</li>
</ul>
<ul>
<li>Vincent van Gogh
Comments
Comment by Maria Keshwala [ 20/May/25 ]
I will look into this and advise and send all the necessary proof of delivery if we show there was an issue with the delivery I will present this to my manager to discuss the $500.00 fined . I will be in touch. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Hello Maria,
I am following up on this.
Thanks,
Amanda
Comment by Maria Keshwala [ 22/May/25 ]
Hello please allow us more time regarding this issue I will get back to you as soon as I can sorry for the delay and thank you for your patience.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
Working on this matter as we speak . Thank you for your patience
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
The ASN status is sent no 997s received checking backend and see if the file was delivered
This message ID did not inject into ECS-03 for delivery
Only the 810 made it to Staples .
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
Hello Amanda
I haven&#39;t seen this file delivered yet, so I&#39;ll create a development ticket to investigate the root cause of the issue. In the meantime, could you please send us the fine from Staples showing the $500.00 charger ? so I can share it with my manager. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
I asked Sandy If I should create a dev ticket to discover the root cause of this delivery issue waiting to hear back
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/May/25 ]
Good Morning Maria,
Attached are the violation details from Staples website and the actual payment remittance where they deducted the $500 from our payment.
Thanks for your attention to this matter.
Amanda
<em>Violation for no ASN - where they took out violation.pdf  (983 kB)</em>
<em>Violation for no ASN.xls  (26 kB)</em>
Comment by Maria Keshwala [ 27/May/25 ]
<a href="https://dtsprojects.atlassian.net/browse/DTS-680">https://dtsprojects.atlassian.net/browse/DTS-680</a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 27/May/25 ]
Hello,
I have escalated this issue to our development team to determine the root cause for internal tracking and resolution.
For future reference, please use the UnAck Outbound tab in WebEDI. If this feature had been used on 5/14, it would have shown that the ASN was not acknowledged, allowing for earlier detection.
Let us know if you have any further questions.
Thank you.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 28/May/25 ]
Hello Amanda
I have sent this to your account manager Macy Thurler so see what can be done about the $500.00 violation fee. I will go ahead and closed this ticket
below is her contact information
<a href="mailto:<EMAIL>"><EMAIL></a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Thanks Maria. We did not have that tab until this became an issue. Is this something new?
Comment by Maria Keshwala [ 29/May/25 ]
you’re welcome , is been there all the customers have it I think a year ago or so
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/May/25 ]
Just wondering. I looked at the screen when I first had this happen. That tab was not there. I remember seeing errors but it was not in there. The tab catalog is definitely new also. It was not on our screen the other day.
I have cleaned out old messages on those and we will check daily.
Comment by Maria Keshwala [ 29/May/25 ]
Those were implemented globally last year .
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</li>
</ul>
<h4 id="cs-43989-issue-walmart-invoices-sent-incorrectly-missing-information-asn-tracking-number-issue-created-19-may-25-updated-19-may-25-resolved-19-may-25">[CS-43989] Issue- Walmart- Invoices sent incorrectly missing information /ASN Tracking number issue Created: 19/May/25  Updated: 19/May/25  Resolved: 19/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-203755.png      maria.docx      ISA00 00 ZZDTS596.txt      image-********-204355.png      ISA00 00 ZZDTS596 (cd37fcff-28f0-4660-8985-1886783e630d).txt
Request Type: Phone call
Request language: English
Request participants: None
Organizations: Label: Invoice errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello this is the ticket for the Walmart Issue -
Comments
Comment by Maria Keshwala [ 19/May/25 ]
Customer looked through the Invoices with me and figured it out their error is customer error.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
Checking 856s Tracking Number Bill of Lading
Message ID : 44325726
Walmart is receiving extra 00 per customer after the tracking number
Two Bill Of Lading Number
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 19/May/25 ]
Jim Sipe
************
<em>maria.docx  (347 kB)</em>
Comment by Maria Keshwala [ 19/May/25 ]
Data that its been sent to Walmart -
ISA00 00 ZZDTS596.txt
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
ISA00 00 ZZDTS596 (cd37fcff-28f0-4660-8985-1886783e630d).txt
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
Hello Jim
Hope I was able to assist you today please let me know if you any anything else . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-43970-default-fields-in-invoice-810-document-for-trading-partner-tractor-supply-created-19-may-25-updated-23-may-25-resolved-23-may-25">[CS-43970] Default Fields in Invoice (810) Document for Trading Partner Tractor Supply Created: 19/May/25  Updated: 23/May/25  Resolved: 23/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Bob Ballengee Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250523-151219.png      image-20250523-151533.png      image-20250523-151252.png      image-20250523-151024.png      image-20250523-151957.png     image-20250523-152744.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: Quickbooks
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello,
There are two fields in blue in the 810 document for Tractor Supply that should be populating automatically when I run the DTS-QB Integrator to Transfer Quickbooks Invoices to WebEDI. Those two fields are: Invoice Number
U.P.C. Consumer Package Code (1-5-5-1)
Since these numbers will vary (depending on the invoice number and the products sold), I cannot use the Default feature in WebEDI. These fields should be populated when the Integrator is used.
Is this something that you can correct? Or something that you can direct me on how to do?
Please advise as soon as possible.
Thank you,
Comments
Comment by Maria Keshwala [ 20/May/25 ]
Good morning
I will review this and advise. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Bob Ballengee [ 22/May/25 ]
Hi,
It&#39;s been a few days since I sent my request as referenced in this acknowledgement email. Would you please let me know the status of my request?
Thanks,
Comment by Maria Keshwala [ 22/May/25 ]
We apologized for this delay we were facing some internal issues that were urgent matter I will get to this as soon as I can Thank you for your patience.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 22/May/25 ]
Hello
Again I apologized lets setup a zoom meeting tomorrow to go over the mapping issues with Quickbooks let me know if 11am EST works for you please advise thank you I will send you the zoom link.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Bob Ballengee [ 22/May/25 ]
Hi Maria,
Yes, I can do a Zoom meeting tomorrow at 11 am EST. Please send me a zoom link or invite.
Thank you,
Comment by Maria Keshwala [ 23/May/25 ]
Good morning
I have sent the invite
During the meeting we will go over the issue -
Maria Keshwala is inviting you to a scheduled Zoom meeting.
Topic: Quickbooks Mapping Issue Time: May 23, 2025 11:00 AM Eastern Time (US and Canada) Join Zoom Meeting <a href="https://cleo.zoom.us/j/93795742417">https://cleo.zoom.us/j/93795742417</a>
Meeting ID: 937 9574 2417
One tap mobile +16469313860,,93795742417# US +13017158592,,93795742417# US (Washington DC)
Dial by your location</li>
<li>****** 931 3860 US</li>
<li>****** 715 8592 US (Washington DC)</li>
<li>****** 224 1968 US</li>
<li>****** 205 3325 US</li>
<li>****** 626 6799 US (Chicago)</li>
<li>****** 558 8656 US (New York)</li>
<li>****** 900 9128 US (San Jose)</li>
<li>****** 278 1000 US</li>
<li>****** 359 4580 US</li>
<li>****** 205 0468 US</li>
<li>****** 215 8782 US (Tacoma)</li>
<li>****** 248 7799 US (Houston)</li>
<li>****** 209 5623 US</li>
<li>****** 347 5053 US</li>
<li>****** 473 4847 US</li>
<li>****** 217 2000 US</li>
<li>****** 444 9171 US</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free</li>
<li>************ US Toll-free
Meeting ID: 937 9574 2417
Find your local number: <a href="https://cleo.zoom.us/u/aeGXNXtXNa">https://cleo.zoom.us/u/aeGXNXtXNa</a>
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
Hello Bob
Im in the meeting thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
UPC .Comsumer package Code (1-5-5-1)
not mapping over the 810 when it comes from Quickbooks checking the catalog to see if the data is present
the customer only received 3 orders so far from Tractor Supply and the 3 orders received so far the customer is experiencing this issue
The Description in the catalog is different from the description that is coming over from Quickbooks to Webedi customer needs to update this
Once the customer receives a PO they import the PO into Quickbooks as a sales order not as Invoice therfore that option is left uncheck
Partners are mapped
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
2 issue = Invoice number not pulling over into Webedi
The Invoice number is generated automatically through QuickBooks Once the invoice is created the customer runs the integrator -
We tested Invoice number is populating as expected
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 23/May/25 ]
Hello Bob
We were able to address the issues and ran a test both problems are no longer present. If you encounter the same issue again, please don’t hesitate to reach out; I’ll be happy to assist you.
Regarding the tracking number, if you experience any issues, please create a new support ticket so we can isolate and handle it separately.
Thank you, and have a great weekend!
Best
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</li>
</ul>
<h4 id="cs-43951-fw-urgent-re-reminder-2-51918-4173-4285-4296-8359-8508-volvo-migration-from-x12-to-edifact-format-created-19-may-25-updated-22-may-25">[CS-43951] FW: <strong>URGENT</strong> -- RE: REMINDER 2: 51918 - 4173, 4285, 4296, 8359, 8508 VOLVO Migration from X12 to EDIFACT format. Created: 19/May/25  Updated: 22/May/25</h4>
<p>Resolved: 22/May/25
Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.jpg      image003.gif
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Project, Work Authorization
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
I have a customer requesting some things (bottom of the email chain). It’s all Greek to me. Is this something you guys can help with?
Doug Eichenberg
Quality Manager
Milesrubber.com
Facebook
“ For God so loved the world, that he gave his only begotten Son, that whosoever believeth in him should not perish, but have everlasting life.” -John 3:16
From: Larry Lemke <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 16, 2025 1:52 PM To: Doug Eichenberg <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: <em>URGENT</em> – RE: REMINDER 2: 51918 - 4173, 4285, 4296, 8359, 8508 VOLVO Migration from X12 to EDIFACT format. Importance: High
Doug,
Do you know what this is about? Apparently, this was sent to Rich’s email…
Larry
From: Administration SVO EDI <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 16, 2025 1:40 PM To: Kristi Hamilton <a href="mailto:<EMAIL>"><EMAIL></a>; Magdalena Lotz <a href="mailto:<EMAIL>"><EMAIL></a>; Doug Eichenberg <a href="mailto:<EMAIL>"><EMAIL></a>; Sandi Buchanan-Biltz <a href="mailto:<EMAIL>"><EMAIL></a>; Larry Lemke <a href="mailto:<EMAIL>"><EMAIL></a>; Hillary Osgood <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Louis Glindmyer <a href="mailto:<EMAIL>"><EMAIL></a>; Function EDI SML NA communication <a href="mailto:<EMAIL>"><EMAIL></a>; David Ross <a href="mailto:<EMAIL>"><EMAIL></a>; Mark Herschman <a href="mailto:<EMAIL>"><EMAIL></a>; Info Mack ASN <a href="mailto:<EMAIL>"><EMAIL></a>; Info VPNA EDI <a href="mailto:<EMAIL>"><EMAIL></a>; Zachary Peaslee <a href="mailto:<EMAIL>"><EMAIL></a>; Function EDI SML NA communication <a href="mailto:<EMAIL>"><EMAIL></a>; Administration SVO EDI <a href="mailto:<EMAIL>"><EMAIL></a>; Lorenzo Ingram <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: <em>URGENT</em> – RE: REMINDER 2: 51918 - 4173, 4285, 4296, 8359, 8508 VOLVO Migration from X12 to EDIFACT format.
+@Hillary Osgood
Thanks,</li>
</ul>
<hr>
<p>Lauren Olson (Young)
Supply Chain Manager
Mack Medium Duty
Roanoke Valley Operations
6450 Technology Drive
Salem, VA 24153
E-mail: <a href="mailto:<EMAIL>"><EMAIL></a>
Phone: ************
From: Kristi Hamilton <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 16, 2025 7:46 AM To: Magdalena Lotz <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; Sandi Buchanan-Biltz <a href="mailto:<EMAIL>"><EMAIL></a>; Larry Lemke <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Louis Glindmyer <a href="mailto:<EMAIL>"><EMAIL></a>; Function EDI SML NA communication <a href="mailto:<EMAIL>"><EMAIL></a>; David Ross <a href="mailto:<EMAIL>"><EMAIL></a>; Mark Herschman <a href="mailto:<EMAIL>"><EMAIL></a>; Info Mack ASN <a href="mailto:<EMAIL>"><EMAIL></a>; Info VPNA EDI <a href="mailto:<EMAIL>"><EMAIL></a>; Zachary Peaslee <a href="mailto:<EMAIL>"><EMAIL></a>; Function EDI SML NA communication <a href="mailto:<EMAIL>"><EMAIL></a>; Administration SVO EDI <a href="mailto:<EMAIL>"><EMAIL></a>; Hillary Osgood <a href="mailto:<EMAIL>"><EMAIL></a>; Lorenzo Ingram <a href="mailto:<EMAIL>"><EMAIL></a> Subject: <em>URGENT</em> – RE: REMINDER 2: 51918 - 4173, 4285, 4296, 8359, 8508 VOLVO Migration from X12 to EDIFACT format. Importance: High
Adding additional contacts for Miles Rubber.
Miles Rubber Team – Please, we urgently require your response to the below mentioned request.
There has been no response provided by the supplier to date.
The project team is in the process of supporting the move of suppliers from X12 to Edifact. In order to successfully complete tasks, the suppliers participation is crucial.
Please respond to this email as requested.
KR,
Kristi Hamilton Supply Chain Compliance Manager
Volvo Group Truck Operations
North America Supplier Compliance
Supplier Compliance Support - <a href="mailto:<EMAIL>"><EMAIL></a>
O. 540-731-5478 | M: 540-505-2417
4833 Cougar Trail Road | Dublin, VA 24084 | US
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.volvogroup.com">www.volvogroup.com</a>
From: Magdalena Lotz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 16, 2025 7:29 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Louis Glindmyer <a href="mailto:<EMAIL>"><EMAIL></a>; Function EDI SML NA communication <a href="mailto:<EMAIL>"><EMAIL></a>; David Ross <a href="mailto:<EMAIL>"><EMAIL></a>; Mark Herschman <a href="mailto:<EMAIL>"><EMAIL></a>; Info Mack ASN <a href="mailto:<EMAIL>"><EMAIL></a>; Info VPNA EDI <a href="mailto:<EMAIL>"><EMAIL></a>; Zachary Peaslee <a href="mailto:<EMAIL>"><EMAIL></a>; Function EDI SML NA communication <a href="mailto:<EMAIL>"><EMAIL></a>; Administration SVO EDI <a href="mailto:<EMAIL>"><EMAIL></a>; Hillary Osgood <a href="mailto:<EMAIL>"><EMAIL></a>; Lorenzo Ingram <a href="mailto:<EMAIL>"><EMAIL></a>; Kristi Hamilton <a href="mailto:<EMAIL>"><EMAIL></a> Subject: REMINDER 2: 51918 - 4173, 4285, 4296, 8359, 8508 VOLVO Migration from X12 to EDIFACT format.
Reminder 2 **
Hello All,
I urgently need a response to the matter below. If I do not receive a reply within 5 working days, I will have to escalate the issue to the Volvo buyer and inform Volvo plants.
If you are not the correct contact, please provide a responsible person for the EDI implementation.
In case of any questions, please do not hesitate to contact me.
BR/ Magda
______________________________________________ Magdalena Lotz
Professional Solution Engineer
Volvo Group Digital &amp; IT
Mydlana 2, building WA 2:1
51-502 Wrocław, Poland
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.edi.volvogroup.com">www.edi.volvogroup.com</a>
From: Magdalena Lotz Sent: Tuesday, April 22, 2025 3:54 PM To: <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Louis Glindmyer <a href="mailto:<EMAIL>"><EMAIL></a>; Function EDI SML NA communication <a href="mailto:<EMAIL>"><EMAIL></a>; David Ross <a href="mailto:<EMAIL>"><EMAIL></a>; Mark Herschman <a href="mailto:<EMAIL>"><EMAIL></a>; Info Mack ASN <a href="mailto:<EMAIL>"><EMAIL></a>; Info VPNA EDI <a href="mailto:<EMAIL>"><EMAIL></a>; Zachary Peaslee <a href="mailto:<EMAIL>"><EMAIL></a>; Function EDI SML NA communication <a href="mailto:<EMAIL>"><EMAIL></a>; Administration SVO EDI <a href="mailto:<EMAIL>"><EMAIL></a> Subject: 51918 - 4173, 4285, 4296, 8359, 8508 VOLVO Migration from X12 to EDIFACT format.
Hello All,
I am contacting you from the EDI Team as VOLVO GROUP would like to start the migration of the EDI messages from X-12 Standard to EDIFACT Standard with 51918 MILES RUBBER &amp; PACKING CO.
We would like to start the transition to EDIFACT as soon as possible . Please reply to this e-mail within 7 working days.
In the attached presentation you can find more details about EDIFACT format.
Please see below the EDIFACT EDI migration plan:
EDI IMPLEMENTATION PLAN with links to the VOLVO EDI WEBPAGE and the specifications and guidelines:
STEP 1 COMMUNICATION
STEP 2 DELFOR D04A
STEP 3 DESADV D07A [ ODETTE TRANSPORT LABEL 1.4.
https://www.edi.volvogroup.com/content/dam/volvo-group/markets/edi/specifications-and-guidelines/transport-label-and-packaging/label-1.4.pdf]
STEP 4 INVOICE D07A</p>
<ol>
<li>Fill out the communication form attached and send it back to me. 2) Set up the connection on your side. 3) Test the connection Please note: This STEP can be skipped in case if the existing EDI communication channel with the Volvo Group remains the same as used today for the X12 messages. Please inform me.</li>
<li>The DELFOR (EDIFACT) can be set up to run in parallel with the current 830 (X-12) that you are receiving from Volvo Group today. Once the migration is completed then X12 will be switched off. 2) Please find in the attached document a cross-reference between 830 and DELFOR.</li>
<li>Please create the DESADV and the ODETTE TRANSPORT LABEL based on the DELFOR. The DESADV can be validated with our Validation Portal: VALIDATION PORTAL : Volvo Group. 2) Please find in the attached document a cross-reference between 856 and DESADV. 3) Once your DESADV is approved, you will be asked to provide the printed ODETTE TRANSPORT LABEL for the corresponding DESADV. 4) When the DESADV &amp; Odette Transport LABEL are approved you will receive a separate production email as confirmation.</li>
<li>For each VOLVO plant, one Invoice must be checked against a DESADV live file. 2) Your EDI INVOICE will not reach the VOLVO accountancy during the testing.</li>
<li>When the EDI INVOICE is approved you will receive a separate production email as confirmation.
Please set up the following UNB address (Sender/Receiver IDs) on your end, in order to receive the files from the Volvo Plants that your Company is connected with:
VOLVO plant parma number VOLVO plant name UNB address
4173 Volvo Group NA, LLC - 573 094200005560139700004173:OD
4285 Mack Trucks Inc MMD 094200005560139700004285:OD
4296 Mack Trucks Inc CKD 094200005560139700004296:OD
8359 Volvo Group NA,LLC - 124 094200005560139700008359:OD
8508 SVO, LLC 094200005560139700008508:OD
In case of any questions please contact me, we can also have a meeting to discuss details.
For more information, visit our VOLVO EDI WEBPAGE.
BR/ Magda
OoO 1.05 – 4.05
______________________________________________ Magdalena Lotz
Professional Solution Engineer
Volvo Group Digital &amp; IT
Mydlana 2, building WA 2:1
51-502 Wrocław, Poland
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.edi.volvogroup.com">www.edi.volvogroup.com</a>
<em>This email message (including its attachments) is confidential and may contain privileged information and is intended solely for the use of the individual and/or entity to whom it is addressed. If you are not the intended recipient of this e-mail you may not disseminate, distribute or copy this e-mail (including its attachments), or any part thereof. If this e-mail is received in error, please notify the sender immediately by return e-mail and make sure that this e-mail (including its attachments), and all copies thereof, are immediately deleted from your system. Please further note that when you communicate with us via email or visit our website we process your personal data. See our privacy policy for more information about how we process it: <a href="https://www.volvogroup.com/en-en/privacy.html">https://www.volvogroup.com/en-en/privacy.html</a></em>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 19/May/25 ]
Comment by Maria Keshwala [ 19/May/25 ]
Hello
We will work on this and advise . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
Sandy will ask Jason if this is billable .
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 21/May/25 ]
Good Afternoon
I will be sending over a work authorization for 4 maps 190x 3= 570 .00 to covert them from X12 to DELFOR EDIFACT . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 22/May/25 ]
Good morning
Work authorization has been sent once paid an analyst will be assigned to work on this project. They will contact you and your trading partner for any further request . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</li>
</ol>
<h4 id="cs-43949-failed-810s-created-19-may-25-updated-19-may-25-resolved-19-may-25">[CS-43949] failed 810s Created: 19/May/25  Updated: 19/May/25  Resolved: 19/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-********-201650.png      image-********-201752.png      image-********-201920.png      image-********-213210.png      image-********-212918.png      image-********-213502.png      image-********-213502 (0ec4ff90-d0ff-4c2a-9cad-2b66ec659139).png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Invoice errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
I have 15 invoices that are showing in the error folder due to a validation error. Could you review? The account I’m logging in with to see these is 3978
Tim Davis
IT Director
voestalpine Roll Forming Corporation
1070 Brooks Industrial Road
Shelbyville, KY 40065, United States
T. +1/502/633/4435 ext. 304
F. +1/502/633/5824
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.voestalpine.com/rfc">www.voestalpine.com/rfc</a>
voestalpine - One step ahead.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 19/May/25 ]
Comment by Maria Keshwala [ 19/May/25 ]
Hello Tim
I will look into this and advise. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
I don&#39;t see any errors with the files not sure why they end up in draft I will do some troubleshooting on my end and advise.
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
Review the files there are any errors on the invoices not sure why they end up in draft sending one document out
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
Product Service ID required error
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
Check Invoice 126565 PO number line is missing checking to see if it was listed on your file
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
Invoice = 126565= missing Purchase Order Line Number under the Item level
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
The IT112 = PL = Purchase’s Order Line Number
Value expected in the IT113 Segment
Linx Map = 153
Bad File - Missing the data
Good file Invoice number = 126088 date 04/30/2025
Testing Good file -
Value is expected = Record L23 start position 366
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
Hello Tim
Please review your files they are missing data values this is why they failed for Invoice Number Invoice = 126565= missing Purchase Order Line Number under the Item level- is missing in the L23 Record start position 366 see b you can add the value in WEBEDI or resend a new file but its easier just to fixed the error in WEBEDI and send the file . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************
Comment by Maria Keshwala [ 19/May/25 ]
Also review the rest of the files all missing PO line number . Thank you I will closed this ticket let me know if you need anything else . Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-43935-orders-with-pickup-dates-in-the-next-48-hours-created-18-may-25-updated-22-may-25-resolved-22-may-25">[CS-43935] Orders with pickup dates in the next 48 hours Created: 18/May/25  Updated: 22/May/25  Resolved: 22/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  48HourReport.xlsx
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
Label: Delayed document delivery
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Orders with pickup dates in the next 48 hours
The orders below have not been fulfilled completely and have a Ship Date within the next 48 hours.
Supplier Supplier Name Order Number Pick-up Date Fulfilled Line Item # Item Code Supplier Item Code Description Requested Remaining UOM
858000 UNIVERSAL PRODUCTS D002182 05/20/2025 None 1 311477 U35286-01 DECAL,CAUTION,LOOSE LUG NUTS 200 200 EA
You are receiving this email as your organization has listed you as the contact for Open Order notifications.
Comments  Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 18/May/25 ]
<em>48HourReport.xlsx  (4 kB)</em>
Comment by Maria Keshwala [ 22/May/25 ]
This was sent to the customer to respond on the documents
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-43934-active-account-created-18-may-25-updated-21-may-25-resolved-21-may-25">[CS-43934] Active Account Created: 18/May/25  Updated: 21/May/25  Resolved: 21/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: Keli Bacon Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  invoice-********.pdf
Request Type: Support
Request language: English
Request participants: None
Organizations: None
Label: Billing/accounting
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Support Team
I just paid an invoice still my account is deactivated kindly activate my account.
Comments
Comment by Jira Service Management Widget [ 18/May/25 ]
<em>invoice-********.pdf  (45 kB)</em>
Comment by Maria Keshwala [ 21/May/25 ]
Hello Keli
Can you please check this account they paid their invoice. Thank you
Maria Keshwala
<em>Senior Support Engineer I Cleo Communications <a href="mailto:<EMAIL>"><EMAIL></a></em>
************</p>
<h4 id="cs-43928-prod-sedc-edi-997-functional-acknowledgement-exception-report-for-edi_860-purchaseorderchange-may-16-2025-created-18-may-25-updated-22-may-25">[CS-43928] PROD | SEDC EDI 997 Functional Acknowledgement Exception Report for EDI_860-PurchaseOrderChange |May 16,2025 Created: 18/May/25  Updated: 22/May/25</h4>
<p>Resolved: 22/May/25
Status: Resolved
Project: Customer Support
Components: None
Affects versions: None Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image003.png      image001.png      image004.png      image002.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: Missing 997
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi WORTHY PROMOTIONAL PRODUCTS LLC Team,
We have not received functional acknowledgements for the following EDI_860-PurchaseOrderChange transactions.
Please find the below table for reference.
HTML Table Header
SEDC EDI 997 Functional Acknowledgement Exception Report for EDI_860-PurchaseOrderChange transactions
TRANSACTION_TYPE PO_NUMBER EDI_CONTROL_NUMBER PARTNER_NAME TRANSACTION_DATE
PC 3551298 000000596 WORTHY PROMOTIONAL PRODUCTS LLC
May 16,2025 09:45:19 AM
Regards, webMethods B2B Team
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 19/May/25 ]
This one has been re-acknowledged.
Thanks,
Cody Worthy Operations Engineer
1700 Central Plank Rd
Wetumpka, AL 36092</p>
