# New Client EDI Onboarding Checklist

## Pre-Onboarding Requirements

### 1. Business Information Collection
- [ ] Company legal name
- [ ] DBA (Doing Business As) names
- [ ] Tax ID / EIN
- [ ] Primary contact information
- [ ] Technical contact information
- [ ] Billing contact information

### 2. Trading Partner Details
- [ ] List of all trading partners
- [ ] Document types per partner (850, 810, 856, etc.)
- [ ] Current EDI capability (if any)
- [ ] Expected transaction volumes
- [ ] Go-live timeline requirements

### 3. Technical Assessment
- [ ] Current ERP/WMS system
- [ ] Integration preferences (API, FTP, manual)
- [ ] IT infrastructure overview
- [ ] Security requirements
- [ ] Compliance needs (HIPAA, etc.)

## WebEDI Account Setup

### 1. Create Company Profile
- [ ] Set up company in WebEDI Admin 3.5
- [ ] Configure company identifiers
- [ ] Set default parameters
- [ ] Enable appropriate modules
- [ ] Configure billing settings

### 2. User Account Creation
- [ ] Create primary admin user
- [ ] Set up additional users as needed
- [ ] Configure user permissions
- [ ] Set up notification preferences
- [ ] Enable two-factor authentication

### 3. Portal Configuration
- [ ] Customize WebEDI Portal 4.0 interface
- [ ] Set up document folders
- [ ] Configure smart folders
- [ ] Set up saved searches
- [ ] Enable required features

## Trading Partner Configuration

### 1. Partner Profile Setup
For each trading partner:
- [ ] Create partner profile
- [ ] Enter ISA/GS identifiers
- [ ] Configure communication method
- [ ] Set up document types
- [ ] Map partner codes

### 2. Communication Setup

#### AS2 Configuration
- [ ] Generate certificates
- [ ] Exchange with partner
- [ ] Configure AS2 profile
- [ ] Set encryption preferences
- [ ] Test connectivity

#### SFTP Setup
- [ ] Create SFTP account
- [ ] Generate SSH keys
- [ ] Share with partner
- [ ] Configure directories
- [ ] Set polling schedule

#### VAN Setup
- [ ] Obtain VAN mailbox
- [ ] Configure interconnect
- [ ] Set up routing
- [ ] Test mailbox access
- [ ] Verify billing setup

### 3. Document Configuration
- [ ] Map required fields
- [ ] Configure validation rules
- [ ] Set up acknowledgments
- [ ] Configure archiving
- [ ] Enable monitoring

## Integration Development

### 1. Data Mapping
- [ ] Analyze client's data format
- [ ] Create field mappings
- [ ] Handle special requirements
- [ ] Configure transformations
- [ ] Document mapping logic

### 2. Business Rules
- [ ] Set up validation rules
- [ ] Configure auto-responses
- [ ] Create routing rules
- [ ] Set up alerts
- [ ] Implement error handling

### 3. Testing Phase

#### Internal Testing
- [ ] Unit test each document type
- [ ] Test validation rules
- [ ] Verify data accuracy
- [ ] Check error scenarios
- [ ] Validate acknowledgments

#### Partner Testing
- [ ] Send test files to partner
- [ ] Receive test files from partner
- [ ] Verify end-to-end flow
- [ ] Test error conditions
- [ ] Confirm compliance

## Training and Documentation

### 1. User Training
- [ ] Schedule training sessions
- [ ] Cover portal navigation
- [ ] Demonstrate document processing
- [ ] Show reporting features
- [ ] Practice common tasks

### 2. Documentation Delivery
- [ ] Provide user guide
- [ ] Create quick reference cards
- [ ] Document support contacts
- [ ] Share troubleshooting guide
- [ ] Deliver integration specs

### 3. Support Setup
- [ ] Add to support system
- [ ] Configure monitoring alerts
- [ ] Set up escalation path
- [ ] Document special requirements
- [ ] Schedule follow-up calls

## Go-Live Preparation

### 1. Final Validation
- [ ] Complete UAT sign-off
- [ ] Verify all mappings
- [ ] Confirm partner readiness
- [ ] Check production access
- [ ] Review error procedures

### 2. Cutover Planning
- [ ] Schedule go-live date
- [ ] Plan cutover sequence
- [ ] Prepare rollback plan
- [ ] Notify all parties
- [ ] Arrange support coverage

### 3. Production Launch
- [ ] Enable production flow
- [ ] Monitor first transactions
- [ ] Verify successful processing
- [ ] Check acknowledgments
- [ ] Address any issues

## Post Go-Live

### 1. Stabilization (Week 1)
- [ ] Daily monitoring
- [ ] Quick issue resolution
- [ ] User support
- [ ] Fine-tune settings
- [ ] Document lessons learned

### 2. Optimization (Week 2-4)
- [ ] Review performance
- [ ] Optimize processing
- [ ] Enhance automation
- [ ] Add new features
- [ ] Plan phase 2

### 3. Transition to BAU
- [ ] Handoff to support team
- [ ] Document all customizations
- [ ] Set up regular reviews
- [ ] Plan future enhancements
- [ ] Close project

## Common Onboarding Challenges

### Technical Issues
- Certificate problems
- Firewall configurations
- Data format mismatches
- Communication failures

### Business Process
- Changed requirements
- New trading partners
- Document additions
- Timeline pressures

### Solutions
- Maintain clear communication
- Document all decisions
- Test thoroughly
- Have contingency plans

---
*This checklist ensures comprehensive client onboarding. Customize based on specific client needs and complexity.*