# CS-44852: Re: Fw: Document Processing Error: Pool Warehouse 2025-05-27 20:07:56

## Ticket Information
- **Key**: CS-44852
- **Status**: Pending
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON>
- **Created**: 2025-06-02T15:18:09.638-0600
- **Updated**: 2025-06-16T06:31:48.310-0600
- **Customer**: Commerce Inc

## Description
*{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}
 
 
 
 
{color:#174e86} I am including Cleo Support.{color} 
{color:#174e86} There is something that is not correct between the communication of <PERSON>S and <PERSON><PERSON> regarding Pool Warehouse.{color}  
{color:#174e86} Please communicate how we can fix this matter with all these errors that are populating.{color} 
{color:#174e86} Pool Warehouse is expecting one number to be the Purchase Order number, however that this not the number that <PERSON><PERSON> is recognizing as the PO. So that could be some of the issue.{color} 
{color:#174e86} There is no ability to add charge for shipping on an invoice{color}{color:#174e86}{color}  
  
{color:#174e86} Please work with each other solve the ongoing issues that are arrising.{color} 
----
 
 {color:#172B4D}*From:* Support <<EMAIL>>
  *Sent:* Monday, June 2, 2025 5:00 PM
  *To:* accounting <<EMAIL>>
  *Cc:* Carol <<EMAIL>>
  *Subject:* RE: Fw: Document Processing Error: Pool Warehouse 2025-05-27 20:07:56{color}    
 
Hi Team,   
Good day. As per our internal team, the invoice for PO203022 is failing due to missing REF segment with the VN value. This should be sent if received in PO. Kindly check and resend as needed.
 
 Regards, 
 
 SPS Commerce Infinite Retail Power 
 Lance Adrian Valdez 
 Technical Support Engineer 
 Customer Support Resolution 
 P: 888-739-3232 
 <EMAIL> 
 Case: ********       
--------------- Original Message ---------------
  *From:* Support [<EMAIL>]
  *Sent:* 5/29/2025 2:47 PM
  *To:* <EMAIL>
  *Cc:* <EMAIL>
  *Subject:* RE: Fw: Document Processing Error: Pool Warehouse 2025-05-27 20:07:56
 
   

Hi Team, 

After investigating, I found that I'll need to send this over to our Implementation team for further review. Requests of this nature typically take around 2-3 business days to complete. I'll pass along any updates I receive from the team; thank you for your patience while we investigate further. 
 
 Regards, 
 
 SPS Commerce Infinite Retail Power 
 Lance Adrian Valdez 
 Technical Support Engineer 
 Customer Support Resolution 
 P: 888-739-3232 
 <EMAIL> 
 Case: ********       
--------------- Original Message ---------------
  *From:* Support [<EMAIL>]
  *Sent:* 5/29/2025 12:16 PM
  *To:* <EMAIL>
  *Cc:* <EMAIL>
  *Subject:* RE: Fw: Document Processing Error: Pool Warehouse 2025-05-27 20:07:56
 
  
Hi Team,   
Good day. I am currently investigating on the issue.
 
 Regards, 
 
 SPS Commerce Infinite Retail Power 
 Lance Adrian Valdez 
 Technical Support Engineer 
 Customer Support Resolution 
 P: 888-739-3232 
 <EMAIL> 
 Case: ********       
--------------- Original Message ---------------
  *From:* accounting [<EMAIL>]
  *Sent:* 5/28/2025 3:57 PM
  *To:* <EMAIL>
  *Cc:* <EMAIL>
  *Subject:* Fw: Document Processing Error: Pool Warehouse 2025-05-27 20:07:56
 
  
{color:#174e86} Hello,{color}{color:#174e86}{color}   
{color:#174e86} There is a breakdown with the PO that is being used by Pool Warehouse. Our System can only look for the PO that is highlighted. I am not sure why we are continuing to get this error.{color}{color:#174e86}{color} 
| {color:#3d6999}
 {color}{color:#3d6999}*Michelle Sadro* \\  *Office Manager/ Accounting*{color}{color:#3d6999} {color}{color:#3d6999}{color} | {color:#3d6999}  {color}{color:#3d6999}*phone:*{color} {color:#3d6999}************** ext:117{color}{color:#3d6999} \\  *email:* <EMAIL> \\{color} {color:#3d6999} {color}{color:#3d6999}{color} | 
| {color:#040404}
{color}
!C2_signature_untitled-2copy_7fd183eb-f79e-4758-8c6e-343a260d131f.jpg|thumbnail!{color:#040404} {color}{color:#040404}{color} | 
| {color:#3d6999}Pool Rails• Custom Rails• Slides• Lifts• Games• Diving Boards• Tables & Seating• Loungers \\  \\{color} | 
| {color:#040404}
 {color}{color:#040404}{color}[{color:#040404} *www.global-poolproducts.com*{color}|https://global-poolproducts.com/]{color:#040404}{color} {color:#040404} {color}{color:#040404}{color} | {color:#3b6d9e}{color}{color:#3b6d9e}{color}!C2_signature_linkedin_32x32_121f123a-5dbb-4697-8420-1acd4afcf12e.png|thumbnail!{color:#3b6d9e} <[https://www.linkedin.com/company/global-pool-products/mycompany/?viewAsMember=true]>{color} {color:#040404}{color}{color:#3b6d9e}{color}{color:#3b6d9e}{color}!C2_signature_facebook_32x32_81fada7e-01f0-4112-9aa8-833f7c8fdf75.png|thumbnail!{color:#3b6d9e} <[https://www.facebook.com/globalpoolproducts]>{color} {color:#040404}{color}{color:#3b6d9e}{color}{color:#3b6d9e}{color}!C2_signature_instagram_32x32_491cab4a-cf03-48e9-898c-81520fd9ea21.png|thumbnail!{color:#3b6d9e} <[https://www.instagram.com/globalpool.products/]>{color} {color:#040404}{color}{color:#3b6d9e}{color}{color:#3b6d9e}{color}!C2_signature_youtube_32x32_a19c4949-bc3a-48b7-8dd3-592519d2ea65.png|thumbnail!{color:#3b6d9e} <[https://www.youtube.com/channel/UC7v37IPsFuoMPZUhLE7FCfw]>{color} {color:#040404}{color} | 
| {color:#040404}{color}{color:#8c8888} \\ This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. If you have received this email in error, please notify us immediately and delete the message from your system.{color}{color:#040404} {color}{color:#040404}{color} |{color:#174e86}{color} 
----
 
 {color:#172B4D}*From:* accounting <<EMAIL>>
  *Sent:* Tuesday, May 27, 2025 4:36 PM
  *To:* Cody M <<EMAIL>>; Carol <<EMAIL>>
  *Subject:* Fw: Document Processing Error: Pool Warehouse 2025-05-27 20:07:56{color}    
{color:#174e86} Hi Cody,{color}{color:#174e86}{color}   
{color:#174e86} Still having this continue issue with the EDI System.{color} 
{color:#174e86} Shipping is still not added as a charge and we are still getting these errors regarding EDI due to PO #.{color}{color:#174e86}{color}   
{color:#174e86} I know that Pool Warehouse use to have another Purchase Order number, but that Number is not the one being loaded into the PO field.{color} 
{color:#174e86} I think Pool Warehouse's po needs to be swapped with the PO for the customer. Please see highlighted.{color}  
{color:#174e86} We cannot search by the Vendor Order Number for Orders only the PO Number that is highlighted.{color}{color:#174e86}{color} 
 !image.png|thumbnail!{color:#174e86}{color} 
| {color:#3d6999}
 {color}{color:#3d6999}*Michelle Sadro* \\  *Office Manager/ Accounting*{color}{color:#3d6999} {color}{color:#3d6999}{color} | {color:#3d6999}  {color}{color:#3d6999}*phone:*{color} {color:#3d6999}************** ext:117{color}{color:#3d6999} \\  *email:* <EMAIL>{color}{color:#3d6999} {color}{color:#3d6999}{color} | 
| {color:#040404}
{color}
!C2_signature_untitled-2copy_7fd183eb-f79e-4758-8c6e-343a260d131f.jpg|thumbnail!{color:#040404} {color}{color:#040404}{color} | 
| {color:#3d6999}Pool Rails• Custom Rails• Slides• Lifts• Games• Diving Boards• Tables & Seating• Loungers \\  \\{color} | 
| {color:#040404}
 {color}{color:#040404}{color}[{color:#040404} *www.global-poolproducts.com*{color}|https://global-poolproducts.com/]{color:#040404}{color} {color:#040404} {color}{color:#040404}{color} | {color:#3b6d9e}{color}{color:#3b6d9e}{color}!C2_signature_linkedin_32x32_121f123a-5dbb-4697-8420-1acd4afcf12e.png|thumbnail!{color:#3b6d9e} <[https://www.linkedin.com/company/global-pool-products/mycompany/?viewAsMember=true]>{color} {color:#040404}{color}{color:#3b6d9e}{color}{color:#3b6d9e}{color}!C2_signature_facebook_32x32_81fada7e-01f0-4112-9aa8-833f7c8fdf75.png|thumbnail!{color:#3b6d9e} <[https://www.facebook.com/globalpoolproducts]>{color} {color:#040404}{color}{color:#3b6d9e}{color}{color:#3b6d9e}{color}!C2_signature_instagram_32x32_491cab4a-cf03-48e9-898c-81520fd9ea21.png|thumbnail!{color:#3b6d9e} <[https://www.instagram.com/globalpool.products/]>{color} {color:#040404}{color}{color:#3b6d9e}{color}{color:#3b6d9e}{color}!C2_signature_youtube_32x32_a19c4949-bc3a-48b7-8dd3-592519d2ea65.png|thumbnail!{color:#3b6d9e} <[https://www.youtube.com/channel/UC7v37IPsFuoMPZUhLE7FCfw]>{color} {color:#040404}{color} | 
| {color:#040404}{color}{color:#8c8888} \\ This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. If you have received this email in error, please notify us immediately and delete the message from your system.{color}{color:#040404} {color}{color:#040404}{color} |{color:#174e86}{color} 
----
 
  {color:#172B4D}*From:* SPS Commerce <<EMAIL>>
  *Sent:* Tuesday, May 27, 2025 4:14 PM
  *To:* accounting <<EMAIL>>; <EMAIL> <<EMAIL>>
  *Subject:* Document Processing Error: Pool Warehouse 2025-05-27 20:07:56{color} 
| 
| |   !https://cdn.dev.spsc.io/web/framework/assets/20.09.01/images/sps-logo-blue.png! 
| | 
{color:#172B4D} {color} 
|{color:#172B4D} {color}{color:#172B4D}SPS Commerce was unable to successfully process the following document: \\  \\ Serial Number: PCL60730778793 \\  \\ Document Originator: Global Pool Products \\ Document Recipient: Pool Warehouse \\ Document Type: 810 \\  \\ Source Filename: todts~SPSCAS2~*********.edi \\  \\ InvoiceDate: 2025-05-27 \\ VendorNumber: 548984 \\ InvoiceNumber: 49043 \\ ShipToLocation: Matt Szmyd \\ InvoiceTotalAmount: 660.52 \\ PurchaseOrderNumber: PO203022 \\  \\ InvoiceNumber:49043 Unable to find internalId with Purchase Order Number PO203022 \\  \\  \\ An error has occurred while processing the document referenced above. If you would like assistance to resolve the error or need additional information, please visit our Support Center and choose one of our convenient contact channels to connect with a support representative. \\  \\ For complimentary expert training visit https://trainingcenter.spscommerce.com/ and for complimentary support visit https://www.spscommerce.com/customer-support/support/ \\ Kind regards, \\ SPS Customer Operations Team \\  \\{color} {color:#172B4D} {color}{color:#172B4D} {color}| 
{color:#172B4D}{color} 
| {color:#172B4D} *SPS Commerce Inc.*{color} {color:#172B4D}333 S 7th St #1000, Minneapolis, MN 55402{color} {color:#172B4D}Having issues? {color}[{color:#172B4D} {color}{color:#172B4D} +Contact our support team+{color}|https://url9207.spscommerce.com/ls/click?upn=u001.1BcK1iWLIsTdqxmExt4XF4Otr-2BLsuFn-2FxwYDU95jSNI1X1Fb9UTy8hk1BuQqAILHvobnxo-2B0ykwDT1nbVjQ0gQ-3D-3DgXgC_0T32ClFdYnPySZLQz4syRouywzAnbk7g8xJ-2F5c66YzojgCoLYOq5-2FfpJrkJbKgsSkuqxgf-2F6Kue5AmBI9H81zrdHDFdMc3Sw-2FsReD1YQJhUcfuaZDJ0N-2Bg4022GZVDTw1-2FgBd7-2Fk4s6BLnZjwhDDl-2F6rv-2BwfNOOH9voQ5W4UpXEUW1ITliWdolfGooV15qN8sYrotg4-2BaOBqvwJC-2BQuVLfW3nq658hoDKEJK7v70jbUWvnqv6ZTN1eDVmwmJ-2Blsw]{color:#172B4D} {color}{color:#172B4D}{color} {color:#172B4D}Message Reference ID: SPS-CLW-JA-L9F{color} | 
  
 !https://url9207.spscommerce.com/wf/open?upn=u001.5Bv-2BhbcTZd2TYeqtCeQF2dpI-2F4IXkgyzWdKnREO4RTmBg8JyzbKs1W1kinQKXBE2tQHRynTk1-2BzM0z5vctOMup2jgvbLQc2vbL97BbY7Kiu-2F1Z9-2BAuRiHBGymuNjzjC3At1sCYfc57hXit257jUoFWjEMwJV-2BeqOqVddqQBr7GAlgvyART7hnkqvQvTns6UGm3eCk3W-2B-2FmdPZ5jDBMd-2Fesp2fQ88Ud2CPT8al45mB3Xet0UYk59d4FujbEkR9yVB!  
!https://spscommerce.my.salesforce.com/servlet/servlet.ImageServer?oid=00D300000000bzv&esid=018Nt00000LDcmZ&from=int!
 
 thread::URvT0GROcMy_eY8ceAQp-ww:: 
!https://spscommerce.my.salesforce.com/servlet/servlet.ImageServer?oid=00D300000000bzv&esid=018Nt00000LECus&from=int! !https://spscommerce.my.salesforce.com/servlet/servlet.ImageServer?oid=00D300000000bzv&esid=018Nt00000LLqIr&from=ext!{adf}{"type":"expand","content":[{"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"default"},"content":[{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Michelle Sadro","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"text","text":" ","marks":[{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"hardBreak"},{"type":"text","text":"  ","marks":[{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"text","text":"Office Manager/ Accounting","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"text","text":"  "}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"phone:","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"text","text":" "},{"type":"text","text":"************** ext:117","marks":[{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"hardBreak"},{"type":"text","text":"  ","marks":[{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"text","text":"email:","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"text","text":" <EMAIL> ","marks":[{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"hardBreak"},{"type":"text","text":"  "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"file","id":"UNKNOWN_MEDIA_C2_signature_untitled-2copy_7fd183eb-f79e-4758-8c6e-343a260d131f.jpg","collection":"","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":" "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Pool Rails• Custom Rails• Slides• Lifts• Games• Diving Boards• Tables & Seating• Loungers ","marks":[{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"hardBreak"},{"type":"text","text":"  ","marks":[{"type":"textColor","attrs":{"color":"#3d6999"}}]},{"type":"hardBreak"},{"type":"text","text":" "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"www.global-poolproducts.com","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#040404"}},{"type":"link","attrs":{"href":"https://global-poolproducts.com/"}}]},{"type":"text","text":" "},{"type":"hardBreak"},{"type":"text","text":" ","marks":[{"type":"textColor","attrs":{"color":"#040404"}}]},{"type":"text","text":" "}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"file","id":"UNKNOWN_MEDIA_C2_signature_linkedin_32x32_121f123a-5dbb-4697-8420-1acd4afcf12e.png","collection":"","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":"https://www.linkedin.com/company/global-pool-products/mycompany/?viewAsMember=true","marks":[{"type":"link","attrs":{"href":"https://www.linkedin.com/company/global-pool-products/mycompany/?viewAsMember=true"}},{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":" "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"file","id":"UNKNOWN_MEDIA_C2_signature_facebook_32x32_81fada7e-01f0-4112-9aa8-833f7c8fdf75.png","collection":"","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":"https://www.facebook.com/globalpoolproducts","marks":[{"type":"link","attrs":{"href":"https://www.facebook.com/globalpoolproducts"}},{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":" "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"file","id":"UNKNOWN_MEDIA_C2_signature_instagram_32x32_491cab4a-cf03-48e9-898c-81520fd9ea21.png","collection":"","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":"https://www.instagram.com/globalpool.products/","marks":[{"type":"link","attrs":{"href":"https://www.instagram.com/globalpool.products/"}},{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":" "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"file","id":"UNKNOWN_MEDIA_C2_signature_youtube_32x32_a19c4949-bc3a-48b7-8dd3-592519d2ea65.png","collection":"","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":"https://www.youtube.com/channel/UC7v37IPsFuoMPZUhLE7FCfw","marks":[{"type":"link","attrs":{"href":"https://www.youtube.com/channel/UC7v37IPsFuoMPZUhLE7FCfw"}},{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#3b6d9e"}}]},{"type":"text","text":"  "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"hardBreak"},{"type":"text","text":" This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. If you have received this email in error, please notify us immediately and delete the message from your system.","marks":[{"type":"textColor","attrs":{"color":"#8c8888"}}]},{"type":"text","text":"  "}]}]}]}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

