# CS-45308: Responsible Disclosure of a Subdomain Takeover issue on: alt-ftp.datatrans-inc.com

## Ticket Information
- **Key**: CS-45308
- **Status**: Canceled
- **Priority**: Medium
- **Assignee**: Unassigned
- **Reporter**: <EMAIL>
- **Created**: 2025-06-12T06:03:05.137-0600
- **Updated**: 2025-06-12T07:07:18.138-0600
- **Customer**: <EMAIL>

## Description
+Please forward this email to your IT/Security team for their immediate attention.+

 Dear IT/Security Team, 
 I am a responsible bug bounty hunter and I am writing to notify you about a  *High-severity* vulnerability in your organization’s domain. 

  *+The vulnerability & Technical details:+*
  *I have found a Subdomain Takeover on  +alt-ftp.datatrans-inc.com+*. The affected DNS record is an A record (**************), on AWS, in the us-west-2 region.
 
 *+The Impact:+*
 1.  *Account Takeover*: Cookies set for  +.datatrans-inc.com/datatrans-inc.com+ are shared with this subdomain ( +alt-ftp.datatrans-inc.com+) can let attackers steal session data and impersonate users.
 2.  *Stored XSS and JavaScript Exploitation*: Malicious JavaScriptcode can be injected, compromising user data or spreading malware.
 3.  *Denial of Service (DoS)*: A cookie bomb attack can overload the system and make its subdomains inaccessible.
 4.  *Phishing*: The subdomain could host fake pages to trick users into sharing sensitive information.
 5.  *Malicious Content Hosting*: The subdomain could be used to distribute harmful content, risking user safety and damaging your reputation. 

 +*PoC:*+
 
 A screenshot demonstrating a successful subdomain takeover is provided below. 
image2 
An example of POC for Denial of Service (DoS) and JavaScript (JS) code execution, will be sent upon request.
 

 *+Mitigation:+* 
To resolve this issue, we recommend reviewing your DNS records and ensuring all inactive subdomains are either removed or properly configured. 

 +*Severity:*+ 
 *High - Score 8.2* - [https://www.first.org/cvss/calculator/3.1#CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:N/I:H/A:L|https://www.first.org/cvss/calculator/3.1#CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:N/I:H/A:L]
Network - no VPN needed 
Attack complexity is low. 
No privileges needed. 
User interation is required. 
Scope is changed and affects other subdomains. 
Confidentiality: None (Potentially High, for stealing session tokens) 
Integrity:  *High* (Attacker can manipulate website content the user visits) 
Availability:  *Low* (Cookie Bomb) 
 
 +*Payment and future collaboration together:*+ 
If you found this report valuable, we accept payment via PayPal or bank transfer. Invoices are available upon request. 
For PayPal, please use the email  *<EMAIL>* - [https://paypal.me/omriman067|https://paypal.me/omriman067]
 
 +In addition we offer the following services to  *all your domains*:+ 
# Penetration Testing. 
# Continuous Monitoring for Subdomain Takeovers. 
# Continuous Active/Passive Attack Surface Assessment. 
# Leaked Credentials Discovery on the Darknet. 
# ISO and GDPR Compliance Reviews. 
# Licenses for various cyber security products. 
 Please let us know if you are interested in this service, and we will provide you with all the details.

 We assure you that our services are conducted with utmost professionalism and respect for your organization’s confidentiality. Therefore, I no longer manage that subdomain. Upon discovering your DNS record linking it to IP **************, I promptly released my control over it. 
 
Sincerely, Omri. 
image1

 If you prefer not to receive notifications regarding potential vulnerabilities in the future, reply back with  *Unsubscribe*

## Components


## Labels

