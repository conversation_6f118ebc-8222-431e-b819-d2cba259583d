<p>hello and welcome to our demonstration on the basics of datatrans web edi the industry&#39;s number one cloud-based edi and e-commerce platform datatran&#39;s web edi solution provides our users with a simple and affordable way to become edi capable whether you are brand new to the edi world or have years of experience data trans web edi provides an intuitive and powerful solution to address your edi requirements data trans reliable cloud-based infrastructure provides maximum uptime so that your edi solution is always available webdi requires no special software or computer knowledge the only requirement for using webei is a connection to the internet thus you can become edi capable quickly at minimal cost with very little training data trans webdi can seamlessly integrate with any application such as shipping services accounting software erp or any warehouse management system and it allows users to manage all of their edi transactions from a single easy to use web portal like we always say if you can use email you can use data trans web edi by the end of this demonstration you will be more familiar with web edi&#39;s user interface how to quickly complete basic functions such as receiving purchase orders and generating response documents including po acknowledgements advanced shipping notices and invoices we will wrap it up with a brief overview of some of the additional tools that are available with data trans web edi so let&#39;s get started upon receiving a document from your trading partners such as a purchase order you will be notified immediately by email logging into your web edi account will take you directly to your inbox all webidi messages are stored in folders new documents received from your trading partners can be found in your inbox the draft folder contains response documents that you&#39;re currently working on and the sent folder contains all documents that have been sent to your trading partners smart folders provide the ability to organize documents based on specific criteria set by the user for example if you wish to organize all incoming purchase orders from a single trading partner into a specific folder this can easily be accomplished with web edi and the archive keeps your active folders less cluttered by storing older documents that have already been processed archive documents are still fully functional and can be used to generate response documents or print ucc 128 shipping labels active messages indicate which documents you are currently working on for easy reference looking now at the inbox window incoming messages received from your trading partners are identified by the data trans message id the document type the trading partner the reference or po number when the document was received when the order is expected to ship the total amount of the order the store number and the document which shows the fulfillment cycle to sort the columns click on the column header the column will sort in ascending order to reverse the order click the header again easily search for documents by message id reference number store number or total amount priority flags allow you to set visual cues to alert users of items that need to be addressed expandable rows allow quick access to all documents associated with a parent document the alert tabs provide immediate visibility to documents that need to be addressed such as approaching ship dates purchase order changes and trading partner messages to view an incoming purchase order click twice to open the document datatrans webidi documents are designed to look like traditional paper documents so there&#39;s no need to learn the complexities of edi to be fully edi capable your trading partner&#39;s logo is always located on the top left with basic document information such as document type and reference numbers found on the top right beneath the company and ship to information can be found the item detail which in this instance includes the seller&#39;s product information quantities units of measure and totals after receiving a po your trading partner may require a purchase order acknowledgement apo acknowledgment is used by sellers to confirm the receipt of a purchase order and communicates whether the po is accepted rejected or what changes have been made to generate a po acknowledgement click the respond button and select purchase order acknowledgement 855 from the drop-down menu datatranswebedi generates response documents that are automatically pre-populated with all relevant information from the incoming po this saves time and avoids costly mistakes often brought about by human error for the sake of this demonstration we will select item accepted for each line items and select acknowledge with details no change as the acknowledgement type this lets the buyer know that they can expect the shipment as ordered once we have confirmed all fields are properly filled out send the po acknowledgement off to the trading partner when we are ready to fulfill the order we will respond with an advanced shipment notice or asn as the name suggests the advanced shipment notice is a document that is sent in advance of the shipment arriving at your trading partner&#39;s facility and is utilized to communicate the contents of the shipment to generate an asn select the purchase order click the respond button and select advanced shipment notice 856 from the drop down menu the new asn packing wizard simplifies the packing process and helps to ensure that accurate information is being sent to your trading partner we will briefly address auto pack in a moment but for now we&#39;ll focus on the pick and pack packing editor first we are asked to enter the number of cartons that we will be shipping with this asn for the sake of this demo let&#39;s say 2 and click next on the asn packing editor items ordered on the po are displayed on the left to pack your cartons hold down the left mouse button and drag and drop from the left window to the packs we&#39;ve created on the right after releasing the mouse over the destination pack an item quantity box will pop up for us to enter the number of items we wish to go into the pack notice the quantity defaults to the number of items on the left side if needed change the quantities to reflect what is being packed as we continue to pack our items notice that the original quantity decreases on the left to reflect how many items are remaining if we were sending a partial asn we would simply not include those particular items on the left to pack on the right once we are satisfied that our items are correctly packed and reflect what our trading partner expects to receive click finish once generated our new packing detail will now be reflected on the asn after ensuring that all company and shipping information is correct and all required fields are properly filled out to our trading partner specifications we can now print our ucc 128 labels and automatically generates a ucc 128 label for each pack you are shipping you can print the labels on any quality printer no specialty thermal printer is needed in addition to the ucc 128 label you can print additional shipping documents such as packing slips packing lists vix billa lading and shipment summaries when you&#39;re ready send the asn to your training partner now comes the good part once order has been fulfilled it&#39;s time to get paid let&#39;s make an invoice first we will open the purchase order we wish to invoice once again click the respond button and select invoice 810 from the drop down menu just as before data trans web edi automatically pre-populates the invoice with all relevant information from the original purchase order if you&#39;re sending a partial invoice you can change the quantity or you can select the line item and delete it all together if necessary form validation ensures you are sending accurate data to your trading partner if you would like to save the invoice and send it at a later time it will be located in the draft folder once all required fields are filled out and you&#39;re ready to send the invoice send the invoice off to your trading partner congratulations we&#39;ve successfully received a purchase order from our trading partner responded with a po acknowledgement and asn and we send back an invoice datatranswebidi is just that easy additional features that extend the power of datatranswebedi include a catalog management system that simplifies the order process when combined with the auto pack asn functionality that brings automation to the advanced shipment notice also you can maintain specific product information that is easily uploaded from a csv file document defaults allow you to easily preset fields in your response documents to reduce data entry you can batch process documents and keep track of their status in the batch queue as well as batch print your ucc 128 labels graphical reports and dashboards provide real-time visibility of all transactions being sent and received within your supplier community my webdi provides user management tools that offer fully customizable permission sets allowing the administrator to manage user access to all edi data and response documents expand your business by easily adding trading partners to your network within webedi finally a true benefit of edi is realized when it&#39;s integrated to your existing business processes datatrans webvdi can integrate with any erp system and shipping service api this saves you time money and virtually eliminates the need for manual data entry this concludes our demonstration on the basics of datatranswebedi though we hope this brief overview has been informative it is by no means exhaustive if you have specific conditions that were not covered by this demo or any other questions about how datatrans solutions can address your edi implementation and integration requirements please give us a call at 800-469-0877 or check us out at <a href="http://www.datatransdashing.com">www.datatransdashing.com</a> thank you for watching</p>
