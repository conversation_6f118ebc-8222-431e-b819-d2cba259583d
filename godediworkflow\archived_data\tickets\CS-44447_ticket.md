# CS-44447: EDI 850s Not Going into WebEDI

## Ticket Information
- **Key**: CS-44447
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON>
- **Created**: 2025-05-28T10:44:08.248-0600
- **Updated**: 2025-05-28T13:47:31.322-0600
- **Customer**: Engineering Corp

## Description
{color:#172B4D}Hello DTS Support,{color}{color:#172B4D}{color}  
  
{color:#172B4D} We have had several instances in the last two weeks where our customers will confirm delivery of an 850 PO to DTS, but it is not visible in WebEDI or sent to us. Usually, if we have them re-send the order, it goes through and is visible in WebEDI. This happened again today: <PERSON><PERSON>num sent PO A87841 which is not visible in WebEDI.{color}{color:#172B4D}{color}   
  
{color:#172B4D} What caused this order to fail processing?{color}  
{color:#172B4D} Why were we not notified that there was an issue with an inbound order?{color}{color:#172B4D}{color}  
  
{color:#172B4D} Andrew{color}{color:#172B4D}{color}{color:#172B4D}{color}  
   
  The content of this email is confidential and intended for the recipient specified in message only. It is strictly forbidden to share any part of this message with any third party, without a written consent of the sender. If you received this message by mistake, please reply to this message and follow with its deletion, so that we can ensure such a mistake does not occur in the future.{adf}{"type":"expand","content":[{"type":"paragraph","content":[{"type":"text","text":" "}]},{"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"default"},"content":[{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"file","id":"UNKNOWN_MEDIA_Outlook-https___ww.png","collection":"","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":"http://www.bannerengineering.com/","marks":[{"type":"link","attrs":{"href":"http://www.bannerengineering.com/"}},{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":" "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"file","id":"UNKNOWN_MEDIA_Outlook-https___ww (59dbf669-2bcf-462f-b484-d7b93484049e).png","collection":"","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":"https://www.facebook.com/BannerEngineering","marks":[{"type":"link","attrs":{"href":"https://www.facebook.com/BannerEngineering"}},{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#d1a316"}}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"file","id":"UNKNOWN_MEDIA_Outlook-https___ww (9996bb91-1fd6-4cb4-9956-a6b97713e401).png","collection":"","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":"https://twitter.com/BannerSensors","marks":[{"type":"link","attrs":{"href":"https://twitter.com/BannerSensors"}},{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#d1a316"}}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"file","id":"UNKNOWN_MEDIA_Outlook-https___ww (be75d51e-9b83-4566-b489-444a7f03b271).png","collection":"","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":"https://www.linkedin.com/company/banner-engineering","marks":[{"type":"link","attrs":{"href":"https://www.linkedin.com/company/banner-engineering"}},{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#d1a316"}}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"file","id":"UNKNOWN_MEDIA_Outlook-https___ww (e530d24f-5903-404a-bf8f-5932c0142e9f).png","collection":"","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":"https://www.youtube.com/user/BannerEngineering","marks":[{"type":"link","attrs":{"href":"https://www.youtube.com/user/BannerEngineering"}},{"type":"textColor","attrs":{"color":"#d1a316"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#d1a316"}}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Andrew Rice","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":"Senior Application Developer","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]}]}]}]}]},{"type":"paragraph","content":[{"type":"text","text":"----"},{"type":"hardBreak"},{"type":"text","text":"Banner Engineering Corp.","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"hardBreak"},{"type":"text","text":"9714 10th Avenue North, Minneapolis, MN 55441","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"hardBreak"},{"type":"text","text":"E","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":" "},{"type":"text","text":"<EMAIL>","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}},{"type":"link","attrs":{"href":"mailto:<EMAIL>"}}]},{"type":"text","text":"P","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":" 763.513.3054  ","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":"C","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":" 763.398.9386","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":" "}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

