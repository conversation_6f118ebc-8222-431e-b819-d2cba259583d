#!/usr/bin/env python3
"""
Analyze EDI support tickets from PDF HTML files to extract valuable information.
"""

import re
import os
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import json

class Ticket:
    """Represents a support ticket."""
    def __init__(self, ticket_id: str, title: str):
        self.id = ticket_id
        self.title = title
        self.status = ""
        self.type = ""
        self.priority = ""
        self.reporter = ""
        self.assignee = ""
        self.resolution = ""
        self.created = ""
        self.updated = ""
        self.resolved = ""
        self.description = ""
        self.comments = []
        self.is_edi_related = False
        self.edi_types = []
        self.has_complete_resolution = False
        self.troubleshooting_steps = []
        self.root_cause = ""
        self.resolution_details = ""
        
    def analyze_edi_content(self):
        """Analyze if ticket is EDI related and extract EDI types."""
        edi_patterns = {
            '850': r'\b850\b|purchase\s*order|PO\s+850',
            '856': r'\b856\b|ASN|advance\s*ship|ship\s*notice',
            '810': r'\b810\b|invoice',
            '997': r'\b997\b|functional\s*ack',
            'AS2': r'\bAS2\b|AS\s*2',
            'SFTP': r'\bSFTP\b|secure\s*ftp',
            'FTP': r'\bFTP\b(?![\s-]*stuck)',
            'X12': r'\bX12\b|ANSI',
            'EDI': r'\bEDI\b',
            'Mapping': r'mapping|translation|transform',
            'Communication': r'connection|timeout|transmission|delivery'
        }
        
        full_text = f"{self.title} {self.description} {' '.join(self.comments)}".lower()
        
        for edi_type, pattern in edi_patterns.items():
            if re.search(pattern, full_text, re.IGNORECASE):
                self.is_edi_related = True
                self.edi_types.append(edi_type)
                
    def analyze_resolution_quality(self):
        """Analyze if ticket has complete troubleshooting and resolution."""
        full_text = f"{self.description} {' '.join(self.comments)}".lower()
        
        # Look for troubleshooting indicators
        troubleshooting_patterns = [
            r'check\w*\s+(?:the\s+)?(?:file|connection|settings|configuration)',
            r'verified?\s+(?:that|the)',
            r'test\w*\s+(?:the\s+)?(?:connection|file|transmission)',
            r'found\s+(?:that|the\s+)?(?:issue|problem|error)',
            r'root\s+cause',
            r'caused?\s+by',
            r'issue\s+was',
            r'problem\s+was',
            r'error\s+(?:was|occurred)',
            r'resent|reprocess|restage',
            r'updated?\s+(?:the\s+)?(?:configuration|settings|mapping)',
            r'fixed?\s+(?:the\s+)?(?:issue|problem|error)'
        ]
        
        for pattern in troubleshooting_patterns:
            matches = re.findall(pattern, full_text, re.IGNORECASE)
            self.troubleshooting_steps.extend(matches)
            
        # Look for root cause
        root_cause_patterns = [
            r'root\s+cause[:\s]+([^.]+\.)',
            r'caused?\s+by[:\s]+([^.]+\.)',
            r'issue\s+was[:\s]+([^.]+\.)',
            r'problem\s+was[:\s]+([^.]+\.)',
            r'(?:the\s+)?error\s+(?:was|occurred)[:\s]+([^.]+\.)'
        ]
        
        for pattern in root_cause_patterns:
            match = re.search(pattern, full_text, re.IGNORECASE)
            if match:
                self.root_cause = match.group(1).strip()
                break
                
        # Look for resolution
        resolution_patterns = [
            r'resolv\w*[:\s]+([^.]+\.)',
            r'fix\w*[:\s]+([^.]+\.)',
            r'solution[:\s]+([^.]+\.)',
            r'(?:i\s+)?(?:have\s+)?(?:resent|reprocessed|updated|fixed|changed)([^.]+\.)'
        ]
        
        for pattern in resolution_patterns:
            match = re.search(pattern, full_text, re.IGNORECASE)
            if match:
                self.resolution_details = match.group(1).strip() if len(match.groups()) > 0 else match.group(0).strip()
                break
                
        # Determine if resolution is complete
        if self.status == "Resolved" and (self.troubleshooting_steps or self.root_cause or self.resolution_details):
            self.has_complete_resolution = True

def parse_ticket_html(content: str) -> List[Ticket]:
    """Parse HTML content and extract tickets."""
    tickets = []
    
    # Split by ticket headers (h4 tags with ticket IDs)
    ticket_sections = re.split(r'<h4[^>]*id="[^"]*"[^>]*>', content)
    
    for section in ticket_sections[1:]:  # Skip first empty section
        # Extract ticket ID and title from header
        header_match = re.match(r'\[([^\]]+)\]\s*(.+?)(?:\s*Created:|$)', section)
        if not header_match:
            continue
            
        ticket_id = header_match.group(1)
        title = header_match.group(2).strip()
        
        ticket = Ticket(ticket_id, title)
        
        # Extract metadata
        status_match = re.search(r'Status:\s*(\w+)', section)
        if status_match:
            ticket.status = status_match.group(1)
            
        type_match = re.search(r'Type:\s*(\w+)', section)
        if type_match:
            ticket.type = type_match.group(1)
            
        priority_match = re.search(r'Priority:\s*(\w+)', section)
        if priority_match:
            ticket.priority = priority_match.group(1)
            
        reporter_match = re.search(r'Reporter:\s*(?:<[^>]+>)?([^<\s]+)', section)
        if reporter_match:
            ticket.reporter = reporter_match.group(1)
            
        assignee_match = re.search(r'Assignee:\s*([^\n]+)', section)
        if assignee_match:
            ticket.assignee = assignee_match.group(1).strip()
            
        resolution_match = re.search(r'Resolution:\s*(\w+)', section)
        if resolution_match:
            ticket.resolution = resolution_match.group(1)
            
        # Extract dates
        created_match = re.search(r'Created:\s*(\d+/\w+/\d+)', section)
        if created_match:
            ticket.created = created_match.group(1)
            
        updated_match = re.search(r'Updated:\s*(\d+/\w+/\d+)', section)
        if updated_match:
            ticket.updated = updated_match.group(1)
            
        resolved_match = re.search(r'Resolved:\s*(\d+/\w+/\d+)', section)
        if resolved_match:
            ticket.resolved = resolved_match.group(1)
            
        # Extract description
        desc_match = re.search(r'Description\s*\n(.+?)(?=Comments|$)', section, re.DOTALL)
        if desc_match:
            ticket.description = desc_match.group(1).strip()
            
        # Extract comments
        comments_section = re.search(r'Comments?\s*\n(.+?)(?=</ul>|$)', section, re.DOTALL)
        if comments_section:
            comment_blocks = re.findall(r'Comment by[^[]+\[([^\]]+)\]\s*([^<]+(?:<[^>]+>[^<]+)*)', comments_section.group(1))
            for date, comment in comment_blocks:
                ticket.comments.append(comment.strip())
                
        # Analyze ticket
        ticket.analyze_edi_content()
        ticket.analyze_resolution_quality()
        
        tickets.append(ticket)
        
    return tickets

def analyze_files(file_paths: List[str]) -> Dict:
    """Analyze multiple ticket files and generate comprehensive report."""
    all_tickets = []
    file_stats = {}
    
    for file_path in file_paths:
        print(f"\nAnalyzing {os.path.basename(file_path)}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            tickets = parse_ticket_html(content)
            all_tickets.extend(tickets)
            
            # File-specific stats
            edi_tickets = [t for t in tickets if t.is_edi_related]
            complete_tickets = [t for t in edi_tickets if t.has_complete_resolution]
            
            file_stats[os.path.basename(file_path)] = {
                'total_tickets': len(tickets),
                'edi_tickets': len(edi_tickets),
                'complete_resolutions': len(complete_tickets),
                'edi_percentage': (len(edi_tickets) / len(tickets) * 100) if tickets else 0
            }
            
            print(f"  Total tickets: {len(tickets)}")
            print(f"  EDI tickets: {len(edi_tickets)} ({file_stats[os.path.basename(file_path)]['edi_percentage']:.1f}%)")
            print(f"  Complete resolutions: {len(complete_tickets)}")
            
        except Exception as e:
            print(f"  Error processing file: {e}")
            
    # Overall analysis
    edi_tickets = [t for t in all_tickets if t.is_edi_related]
    complete_tickets = [t for t in edi_tickets if t.has_complete_resolution]
    
    # EDI type distribution
    edi_type_counter = Counter()
    for ticket in edi_tickets:
        for edi_type in ticket.edi_types:
            edi_type_counter[edi_type] += 1
            
    # Common issues
    issue_patterns = defaultdict(list)
    keywords = ['connection', 'timeout', 'mapping', 'format', 'validation', 'authentication', 
                'certificate', 'duplicate', 'missing', 'failed', 'error', 'stuck']
    
    for ticket in edi_tickets:
        full_text = f"{ticket.title} {ticket.description}".lower()
        for keyword in keywords:
            if keyword in full_text:
                issue_patterns[keyword].append(ticket.id)
                
    # High-value tickets (complete resolution with clear troubleshooting)
    high_value_tickets = []
    for ticket in complete_tickets:
        if len(ticket.troubleshooting_steps) >= 2 or (ticket.root_cause and ticket.resolution_details):
            high_value_tickets.append({
                'id': ticket.id,
                'title': ticket.title,
                'edi_types': ticket.edi_types,
                'root_cause': ticket.root_cause,
                'resolution': ticket.resolution_details,
                'troubleshooting_steps': len(ticket.troubleshooting_steps)
            })
            
    # Generate report
    report = {
        'summary': {
            'total_tickets_analyzed': len(all_tickets),
            'total_edi_tickets': len(edi_tickets),
            'edi_percentage': (len(edi_tickets) / len(all_tickets) * 100) if all_tickets else 0,
            'complete_resolutions': len(complete_tickets),
            'resolution_rate': (len(complete_tickets) / len(edi_tickets) * 100) if edi_tickets else 0,
            'high_value_tickets': len(high_value_tickets)
        },
        'file_breakdown': file_stats,
        'edi_type_distribution': dict(edi_type_counter.most_common()),
        'common_issue_keywords': {k: len(v) for k, v in sorted(issue_patterns.items(), 
                                                               key=lambda x: len(x[1]), 
                                                               reverse=True)[:10]},
        'high_value_tickets': high_value_tickets[:20]  # Top 20 high-value tickets
    }
    
    return report

def main():
    """Main function to analyze ticket files."""
    # Define file paths
    file_paths = [
        "/mnt/c/Users/<USER>/OneDrive - Cleo/Desktop/Sources/0001-1000.pdf(1).html",
        "/mnt/c/Users/<USER>/OneDrive - Cleo/Desktop/Sources/1001-2000.pdf(1).html",
        "/mnt/c/Users/<USER>/OneDrive - Cleo/Desktop/Sources/2001-3000.pdf(1).html",
        "/mnt/c/Users/<USER>/OneDrive - Cleo/Desktop/Sources/3001-3498.pdf.html"
    ]
    
    print("EDI Support Ticket Analysis")
    print("=" * 50)
    
    report = analyze_files(file_paths)
    
    # Print summary report
    print("\n" + "=" * 50)
    print("ANALYSIS SUMMARY")
    print("=" * 50)
    
    print(f"\nTotal tickets analyzed: {report['summary']['total_tickets_analyzed']}")
    print(f"EDI-related tickets: {report['summary']['total_edi_tickets']} ({report['summary']['edi_percentage']:.1f}%)")
    print(f"Tickets with complete resolutions: {report['summary']['complete_resolutions']} ({report['summary']['resolution_rate']:.1f}% of EDI tickets)")
    print(f"High-value tickets identified: {report['summary']['high_value_tickets']}")
    
    print("\nEDI Type Distribution:")
    for edi_type, count in report['edi_type_distribution'].items():
        print(f"  {edi_type}: {count}")
        
    print("\nMost Common Issue Keywords:")
    for keyword, count in list(report['common_issue_keywords'].items())[:10]:
        print(f"  {keyword}: {count} tickets")
        
    print("\nTop High-Value Tickets (showing first 10):")
    for i, ticket in enumerate(report['high_value_tickets'][:10], 1):
        print(f"\n{i}. {ticket['id']}: {ticket['title']}")
        print(f"   EDI Types: {', '.join(ticket['edi_types'])}")
        if ticket['root_cause']:
            print(f"   Root Cause: {ticket['root_cause'][:100]}...")
        if ticket['resolution']:
            print(f"   Resolution: {ticket['resolution'][:100]}...")
            
    # Save detailed report
    output_file = "/home/<USER>/edi_knowledge_base/support_tickets/analysis_report.json"
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)
    print(f"\nDetailed report saved to: {output_file}")

if __name__ == "__main__":
    main()