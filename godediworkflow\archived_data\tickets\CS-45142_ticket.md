# CS-45142: (5780) Bollin Label Systems Invoice total not correct. 

## Ticket Information
- **Key**: CS-45142
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: h<PERSON><PERSON>@bollin.com
- **Created**: 2025-06-09T12:09:10.545-0600
- **Updated**: 2025-06-12T09:32:45.227-0600
- **Customer**: <EMAIL>

## Description
Heather

5780 *Bollin Label Systems*

Reporting an issue with incoming purchase orders from her trading partner, *<PERSON> Plus*. The pricing on these purchase orders is not populating correctly. Instead of showing the calculated price for the items, the total amount is listed as *"unavailable on her side but for cleo support engineers we see the total and unit prices , nothing is missing.* <PERSON> mentioned that she believes this is a recurring problem that support has been able to fix for her in the past. 

## Components


## Labels

