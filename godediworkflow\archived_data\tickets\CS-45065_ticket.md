# CS-45065: New Labels for RC Taylor DTS-696

## Ticket Information
- **Key**: CS-45065
- **Status**: Escalated
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-06-06T08:36:08.136-0600
- **Updated**: 2025-06-06T13:13:52.374-0600
- **Customer**: Texas Roasting Company

## Description
Hi Support, 

  

<PERSON> Taylor is needing new labels created. As our processes are changing, could you please submit a ticket to dev? Attached is our label template for dev to use, along with the only document <PERSON> sent as a spec. 

  

The Excel sheet has three tabs, one for each of three labels that need to be built: Carton label (856C DOM 8621), GTIN label (856C DOM 8621), and the Pallet label (856 DOM 8643). 

  

Trading Partner: <PERSON> Taylor 7117 - (5918) 

Customer: Texas Roasting Company 7634 - (6437) 

  

856C Test Message ID: 43987241 

856 Test Message ID: 44299702 

  

One thing to note is that there is an open ticket related to one of the fields on the 856C for the MAN*AI field, which will need to print on the carton label. That ticket is ttps://dtsprojects.atlassian.net/browse/DS-12089. I’m not sure if it’s been transferred over to your jira instance yet or what the status is since my access has been removed. 

  

Please let me know if anything else is needed or if there are any questions. 

  

Thanks! 
|
|
|
|{color:#000001}{color}
|{color:#000001}Marianne{color}{color:#000001}{color}|{color:#000001}  \\{color}{color:#000001}{color}|{color:#000001}Kania{color}{color:#000001}{color}|{color:#000001}{color}
|{color:#000001}{color}
|{color:#000001}Cleo{color}{color:#000001}{color}|{color:#000001} :  \\{color}{color:#000001}{color}|{color:#000001}Implementation Engineer I{color}{color:#000001}{color}|{color:#000001}{color}
|
|
|
| | | {color:#000001}{color}
|{color:#000001}{color}{color:#000001}
{color}{color:#000001}{color}
|{color:#000001}Email: {color}{color:#000001}{color}|{color:#000001}{color}[{color:#000001}{color}{color:#000001}<EMAIL>{color}|mailto:<EMAIL>]{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}|{color:#000001}{color}
{color:#000001}{color}{color:#000001}{color}{color:#000001} | {color}{color:#000001}{color}{color:#000001}
Web: {color}{color:#000001}{color}{color:#000001}{color}[{color:#000001}{color}{color:#000001}www.cleo.com{color}|https://www.cleo.com/]{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}
| | | {color:#0083CA}{color}
|{color:#0083CA}{color}{color:#0083CA}{color}
|{color:#0083CA}{color}[{color:#0083CA}{color}{color:#0083CA}Join us at one of our upcoming events. Check out the list!{color}|https://www.cleo.com/events]{color:#0083CA}{color}{color:#0083CA} \\{color}{color:#0083CA}{color}|{color:#0083CA}{color}

## Components


## Labels

