<p>Okay, I want to share and I want to share. Yeah, screen one. Okay, perfect. Okay, so all right, move this out of the way. Okay, so um All right, so now Like I said, we&#39;re going to search for the purchase order, right? Okay. So, save a lot is the sender and the receiver is uh USA inputs. Okay. So, here&#39;s the purchase order received from save a lot. And as you can see, the source is zero van Lauren data, which is our event because it comes from Lauren data and it injects into the server. Right? Once we receive it, as you can see down here, the target is zero rabbit MQ webd injection. This is our target uh um file uh file to inject into webd. This is what triggers to inject the file into the webi portal where the customer where the customer views the document, right? The pretty view of it. Um, so this is the target and I&#39;ll show you where that is, how it is there and all of that. Okay. So, I&#39;m going to look at this and see it, you know, why. So, an A75 is a purchase order for grocery stores. Okay. An A50 are is a purchase order for retail, right? But like clothing uh right uh uh furniture, whatever, right? Um and uh and then I think 8:30s usually um like Caterpillar uses 830s which is like you know schedulers and stuff like that. But this one an A75 is used for a grocery store. So when you see an A7 an A75, you know it&#39;s coming for a grocery store. Okay. All right. So what they did here, they send two purchase orders. Okay. And it&#39;s okay. They can send 10, fifth, it doesn&#39;t matter in one batch. We make sure that those get separated and they inject individually within the WebDI portal. Okay. All right. So uh Now, I forgot one thing. Um, I forgot one thing. Let me open another thing and I&#39;ll show you.</p>
<p>Yes. Yes. Yes. Yes. You know, it&#39;s for food.</p>
<p>Yeah. Okay. So, Portugal is for food</p>
<p>for consumable. Okay. So, um All right. And let me go here. and do my sender USA imports. I just want to save a lot. Just want to view the 997 against this. What is this? You can open two windows at the same at the same time. Okay. By just double clicking the icon, you just right click it and then, you know, click on it and it&#39;ll open a new one in case you want to have to to open All right. So, how do I know for which one for which purchase order is this for? Um, well, hold on. Okay. Let me look at so that all right it says control 26 control 001 and control 002. So we send the 997 for those two um orders. Okay. All right. So let me see. Okay. So you see the controls 101 102. So the 987 reflects the the control numbers that are from the purchase order, right? So a reference. Oh, we accepting this documents from this control that you send this. Okay. All right. So I am going to see if I see anything weird. Um because this number, right? You know what I noticed? Okay, let me There you are. All right. So, we got I&#39;m looking at a number that goes after the uh TA1 which is right here. This is what that TA1 is sending. So, we are generating a control on that tier one. How do I know that? Because you see the console up there. So, let me see what happen. I&#39;m going to also check all their 997s. You can send them the pass and see, you know, what&#39;s going on. All right, let me go back here. No, no, Okay, it looks like Okay, because the A75 has the control before the key. Let me check. too. Can I close the yellow? We always new things every day. Some things I already know, some things is like, what&#39;s going on here? Yes. Which is so cool, right? Stay alive. I I like that because I I don&#39;t get bored, you know? I don&#39;t like uh monogamy. Um, so let&#39;s see. Um, but all right, let me see if I can replicate the problem. We got there for now. Let me let me generate a 97. So I go to tools. Um sorry I need to do Let me save the file. If it works this way. Here is my deliveries, right? It&#39;s saying that it went to WebDI like I show you, right? I injected the file there. And this out thing is the uh 997 that was autogenerated. Okay, here&#39;s my 997 in conver in conversations. Okay, you can also view it down here. Okay. So, under the envelope, we go to tools and I&#39;m going to say create a 987. Where did it go? It should create a 987. Let me go to the one that I saved here. See if it&#39;ll do it. I didn&#39;t know. Why? How many things? See what I can do. Never seen this before and it doesn&#39;t let me to create it on i7 which is weird. Usually I I would do that. Let me put a no on the team. Let me see what I&#39;m gonna do. Let me see if we have sent any good 97s before. You know, that&#39;s a good thing to check too. Um because I can&#39;t replicate. All right. Um Okay. Um I&#39;m going to do Michael. I&#39;m gonna go back like I don&#39;t know why they became affected but okay. So we do some let me just see how we send it because I can what I can do is everyone And they sound I guess the first time we I guess they became active with Save the Lab starting May 1st. Um because what I&#39;m seeing here, we&#39;ve been sending the Emergency sending that T1 control there. Let me check some active law since April 2025. It appears that the 817 has been sent on the 97 since um I just like to add notes as I go, you know. Um, so now I&#39;m going to check on Now I&#39;m going to check in general. You can do the document types here. Let me say and um and I&#39;m going to just go back a little The thing is nocal has their DTS as their ISA, you know, but USA imports has a different kind of sender um AISA ID. So I mean obviously this has the correct information. You see they have the GS after the P and it says FA sender. I mean I can fix that. But um I have to ask a robber how we altered. Let me check one more thing. So you see how I&#39;m analyzing. So I&#39;m checking. Okay. So now I&#39;m going to check new cows A75s versus USA imports A75s if they&#39;re sending the same structure cuz for some reason we are sending that at1 for this particular customer and not for the other one. You know before I go to somebody I have to like do all my diligence, you know. like all my re my discoveries and that no is not international.</p>
<p>It&#39;s a just a just a grocery store uh purchase order. It&#39;s like when they ask for like food stuff, you know, like no probably sends I don&#39;t know chocolate or whatever and then save a lot wants their chocolate and they&#39;re ordering chocolates. I don&#39;t know. USA might sell different things to save a lot and they might order you know I don&#39;t know something but it has to be food it&#39;s food related as but and you know um Google this is how I learned to I did my own research on documents like I google them and I&#39;m like okay can you explain to me what this document really is right what it&#39;s really for and It literally explains you why you can search it too which is yeah it helps you know like all the document types but A10 is always invoice basically you say and I you know and you might find reverse relationship. What is a reverse relationship? Meaning when when we Oh s*** sorry and you recorded that. the meeting. This is not a regular setup because they already are relationship to check the internal testing to see whether to use the STP. Okay, I will jump in and call that you have to do natural migration. Thank you. All right. So, I got off track. Uh, what was I saying? Okay. Uh, let me see something. Um, let me check the A75s now. So, So far imports and distribution. A75 see look like so Okay. So, I&#39;m going to check save. Oh, no. I&#39;m sorry. A Oh, I&#39;m gonna do Notepad++. You have that one? Oh, good thing to I&#39;m going to say no. Good thing to consider. Don&#39;t load or update anything in the server. All right. Now, in your desktop, yes. But here, do I&#39;m telling you this because Tyler once did that and it broke a lot of things. So, when you get those like you see, they asked me to update new version, whatever. I said, &#34;No, I don&#39;t want I don&#39;t want any trouble.&#34; Okay. So, I&#39;m gonna open this because I want to convert the the two files. So, I&#39;m going to minimize this and I&#39;m going to save a S75 out save as we save A75 and you saying for I say okay.</p>
<p>All right. So, all right. Let me open this up. I&#39;m going to say one. Okay. Wow. Um, One second. Um I know there is a I always forget this. Uh there is um sorry stop here. I want to make it look straight. One second. Hello. Sorry. Hello. I have this screenshot, but to get to that screen, I&#39;m trying to find where Oh, it&#39;s pretty. Got it. I know how to do the X and stuff. I&#39;m making great prints for me. Oh man, I just do and then I&#39;ll do it like this. Sorry about that. Give me one second. I&#39;m going to do it like this. Let me see if I can do that. You want to install the recording so it doesn&#39;t, you know, ruin your your recordings. And I&#39;ll tell you when to start recording.</p>
