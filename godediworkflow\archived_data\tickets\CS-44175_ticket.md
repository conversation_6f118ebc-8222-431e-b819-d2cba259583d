# CS-44175: ALDI US | 810 | EDI Error Notification | Adams Flavors Foods&Ingredients LLC (1121395-US-1) |  12556795

## Ticket Information
- **Key**: CS-44175
- **Status**: Canceled
- **Priority**: Medium
- **Assignee**: <PERSON><PERSON> Thakur
- **Reporter**: <EMAIL>
- **Created**: 2025-05-21T16:40:07.442-0600
- **Updated**: 2025-05-22T04:41:12.234-0600
- **Customer**: Ingredients LLC

## Description
| !https://visibility.inovis.com/dataquality/xclientimage/aldibp/ALDI_email_header.png! <[#]>
 *ALDI/HOFER Error Notification* |
| \\Dear Sir or Madam,
The EDI message you sent us contains errors which are either soft errors, hard errors and/or MIG validation errors. Our EDI system can successfully process EDI messages with soft or MIG validation errors. However, we recommend that you fix soft and MIG validation errors in your EDI messages.
Any EDI message containing hard errors is rejected by the system. As a result, your EDI message will not be processed in our EDI system. Please adjust your message to match our requirements regarding electronic data exchange with business partners. The current version of the corresponding Message Implementation Guideline is available for download in the onboarding portal ([ +https://visibility.inovis.com/dataquality/xclient/ALDIBP+|https://visibility.inovis.com/dataquality/xclient/ALDIBP]).
 *{color:#FF0000} *+Important:+*{color}* In case of hard error(s) within the message, the complete interchange is rejected! For the avoidance of doubt, if a document in a specific interchange includes hard error(s) all other documents within this interchange are rejected as well. Please resend the interchange after correcting the errors within all documents.
Please find a summary of the errors at the end of this e-mail. Please also refer to our EDI Guideline for further information, which is also available for download in the onboarding portal ([ +https://visibility.inovis.com/dataquality/xclient/ALDIBP+|https://visibility.inovis.com/dataquality/xclient/ALDIBP]). 
The EDI message contains the following errors: \\Error Count: 1 \\Hard Error Count: 0 \\Soft Error Count: 0 \\MIG Validation Error Count: 1 \\Message Number:  *12556795  \\* \\Value must come from X12 specified code list. \\  \\  \\
If you have any further questions, please do not hesitate to contact your relevant national contact person at ALDI/HOFER.
Regards
 *OpenText Team, on behalf of ALDI/HOFER* \\[ {color:#0000ff}+<EMAIL>+{color}|mailto:<EMAIL>]|
| |
| |
| |

## Components


## Labels

