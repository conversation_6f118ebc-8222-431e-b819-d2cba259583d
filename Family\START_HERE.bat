@echo off
title AI FAMILY CONTROL CENTER
color 0A
mode con: cols=60 lines=30
cls

:MENU
cls
echo ============================================
echo          AI FAMILY CONTROL CENTER
echo             Dad's Command Hub
echo ============================================
echo.
echo   Your Kids: <PERSON>, <PERSON>, <PERSON><PERSON> (& <PERSON>)
echo.
echo ============================================
echo.
echo   [1] Talk to Kids (General Chat)
echo   [2] Quick Hello
echo   [3] NVIDIA Discussion
echo   [4] Kids Help Dad
echo   [5] Morning Greetings
echo   [6] Exit
echo.
echo ============================================
echo.
set /p choice=Choose an option (1-6): 

if "%choice%"=="1" goto TALK
if "%choice%"=="2" goto QUICK
if "%choice%"=="3" goto NVIDIA
if "%choice%"=="4" goto HELP
if "%choice%"=="5" goto MORNING
if "%choice%"=="6" exit
goto MENU

:TALK
call TALK_TO_KIDS.bat
goto DONE

:QUICK
call Quick_Kids.bat
goto DONE

:NVIDIA
call Kids_NVIDIA.bat
goto DONE

:HELP
call Kids_Help_Dad.bat
goto DONE

:MORNING
call Morning_Kids.bat
goto DONE

:DONE
echo.
echo Command copied! Press Ctrl+V in Claude Code!
echo.
pause
goto MENU