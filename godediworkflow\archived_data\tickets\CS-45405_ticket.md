# CS-45405: Galderma | SD | QC Failures - 20250610

## Ticket Information
- **Key**: CS-45405
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-06-13T07:02:12.879-0600
- **Updated**: 2025-06-16T10:45:38.133-0600
- **Customer**: <EMAIL>

## Description
Hi Team,
 
 We are receiving the SD and SH as new transaction type. Could you please let us know the reason.
 As of now we are failing it. 

  

We have completed the analysis for the qc column which got failed for SD shipment files dated 20250610. Please find the below in detail and Field validation FIA screenshot as well. 

The  *report end date* for the below is 06/09/2025. 
|  *{color:#172B4D} *Client & sd name*{color}*{color:#172B4D}{color} |  *{color:#172B4D} *File Name*{color}* |  *{color:#172B4D} *Column name*{color}* |  *{color:#172B4D} *Column value*{color}* |  *{color:#172B4D} *Comments*{color}* | 
| {color:#172B4D}Galderma ABSG{color} | {color:#172B4D}GALDERMA_SD_ABSG_DISPENSE_867_DLV_20250610021217_99999999.CSV{color} | {color:#172B4D}Transaction Type{color} | {color:#172B4D}SH, SD{color} | {color:#172B4D}We have verified the FIA. The list of values are returns, return, sales, sale. Column values are not present in the list.{color} | 
| {color:#172B4D}Galderma ABSG{color} | {color:#172B4D}GALDERMA_SD_ABSG_DISPENSE_867_DLV_20250610021217_99999999.CSV{color} | {color:#172B4D}SUM867QTYSOLDPU{color} | {color:#172B4D}{color} | {color:#172B4D}We have verified the FIA. This Column is dependent on the column transaction type. But the values present in the Transaction type Column are not present in the list.{color} |
 

  

  

!image001.png|thumbnail!
 
 !image002.png|thumbnail!
 
  

  

 *{color:#2F5496} *Thanks & Regards,*{color}* 

 *{color:#2F5496} *Suhani Yadav*{color}*{color:#2F5496}{color}
  *{color:#215E99} *Software Engineer*{color}*{color:#646464}{color} 

 *+{color:#0C64C0} *++91 9271250045+*{color}+**{color:#0C64C0} *|  +suhani-ashok.yadav+*{color}[{color:#0C64C0} {color}{color:#0C64C0} *+@+*{color}{color:#0C64C0} {color}|mailto:<EMAIL>]{color:#0C64C0} {color}[{color:#0C64C0} {color}{color:#0C64C0} *+capgemini.com+*{color}{color:#0C64C0} {color}|mailto:<EMAIL>]{color:#0C64C0} {color}*{color:#1F497D}{color} 

!image003.gif!{color:#1F497D}{color}{color:#1F497D}{color} 

  

   This message contains information that may be privileged or confidential and is the property of the Capgemini Group. It is intended only for the person to whom it is addressed. If you are not the intended recipient, you are not authorized to read, print, retain, copy, disseminate, distribute, or use this message or any part thereof. If you receive this message in error, please notify the sender immediately and delete all copies of this message.

## Components


## Labels

