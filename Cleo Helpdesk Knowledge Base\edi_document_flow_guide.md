# EDI Document Flow: A Comprehensive Guide

## 1. Introduction
This guide provides a clear explanation of the fundamental concepts of Electronic Data Interchange (EDI) document flows. Understanding the distinction between "Inbound" and "Outbound" documents is crucial for effective EDI implementation, management, and troubleshooting.

## 2. Core Concepts: Inbound vs. Outbound
The terms "Inbound" and "Outbound" are always relative to your company's perspective.

*   **Inbound:** Documents you **receive** from your trading partners (e.g., customers, suppliers, warehouses).
*   **Outbound:** Documents you **send** to your trading partners.

A document's direction is determined by the business process. For example, a Purchase Order (EDI 850) is **Inbound** when a customer sends it to you to buy your products, but it is **Outbound** when you send it to a supplier to buy their materials.

## 3. Visualizing the Order-to-Cash Flow

Here is a vertical flowchart illustrating a typical "Order-to-Cash" cycle from a seller's perspective.

```mermaid
graph TD
    Start([Start: Customer Places Order]) --> PO{850 Purchase Order};
    PO --> Receive_PO[Receive 850 in Your System];
    Receive_PO --> Send_997_PO[Send 997 Ack for 850];
    Receive_PO --> Send_855[Send 855 PO Acknowledgment];
    Send_855 --> Ship[Prepare & Ship Order];
    Ship --> Send_856[Send 856 ASN to Customer];
    Ship --> Send_810[Send 810 Invoice to Customer];
    Send_810 --> Receive_Payment[Receive 820 Payment Advice];
    Receive_Payment --> End([End: Order Complete]);

    style Start fill:#D4EDDA,stroke:#155724
    style End fill:#F8D7DA,stroke:#721C24
    style PO fill:#D1ECF1,stroke:#0C5460
```

## 4. Common EDI Documents

The following tables categorize common EDI documents by their most frequent direction for a company that sells goods.

### Common Inbound Documents (Received by Your Company)
These are documents typically sent to you by your customers or partners.

| Transaction # (ID) | Document Name | Description / Purpose |
| :--- | :--- | :--- |
| **850** | Purchase Order | A customer sends this to order goods or services. This triggers the order-to-cash cycle. |
| **820** | Payment Order / Remittance Advice | A customer sends this to indicate payment has been made and which invoices it covers. |
| **860** | Purchase Order Change Request | A customer sends this to change details on a previously sent Purchase Order (850). |
| **824** | Application Advice | A partner's system sends this to report errors or issues with a document you sent them. |
| **856** | Advance Ship Notice (ASN) | (As a Buyer) A supplier sends this to inform you that a shipment is on its way. |
| **997** | Functional Acknowledgment | An automated receipt confirming a document you sent was received. |

### Common Outbound Documents (Sent by Your Company)
These are documents typically sent from you to your customers or partners.

| Transaction # (ID) | Document Name | Description / Purpose |
| :--- | :--- | :--- |
| **810** | Invoice | You send this to a customer to request payment for goods or services provided. |
| **855** | Purchase Order Acknowledgment | You send this to confirm receipt of a customer's PO (850) and that you can fulfill it. |
| **856** | Advance Ship Notice (ASN) | (As a Seller) You send this to your customer to inform them you have shipped their order. |
| **865** | PO Change Acknowledgment | You send this to confirm or reject changes requested in a PO Change Request (860). |
| **846** | Inventory Inquiry / Advice | You send this to partners (like retailers) to provide them with current inventory levels. |
| **850** | Purchase Order | (As a Buyer) You send this to a supplier to order raw materials or products. |
| **997** | Functional Acknowledgment | An automated receipt your system sends after receiving any EDI document. |

## 5. Key Takeaway

The direction of a document completely depends on the business context. The most common documents that can be both inbound and outbound are the **850 (Purchase Order)** and the **856 (Advance Ship Notice)**, as their direction depends on whether your company is the buyer or the seller in that specific transaction.