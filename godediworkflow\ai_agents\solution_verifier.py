"""
Solution Verification Engine - Validates and Scores EDI Solutions
Ensures proposed solutions actually work based on historical data
"""

import re
import json
import sqlite3
import logging
from typing import Dict, List, Optional, Tuple, Set
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from dataclasses import dataclass
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer

from config import settings, DATABASE_PATH

logger = logging.getLogger(__name__)


@dataclass
class SolutionScore:
    """Comprehensive scoring of a proposed solution"""
    solution_text: str
    quality_score: float  # 0-10 scale
    success_rate: float  # 0-1 probability
    confidence: float  # 0-1 confidence in the assessment
    similar_successes: int
    similar_failures: int
    average_resolution_time: Optional[timedelta]
    prerequisites: List[str]
    risks: List[str]
    alternative_solutions: List[Dict]


@dataclass
class VerificationResult:
    """Result of solution verification"""
    is_valid: bool
    effectiveness_score: float
    implementation_steps: List[str]
    expected_outcome: str
    potential_issues: List[str]
    historical_evidence: List[Dict]


class SolutionVerifier:
    """
    Verifies and scores proposed EDI solutions based on historical success
    """
    
    # Solution quality indicators
    QUALITY_INDICATORS = {
        'specific': {
            'positive': ['specific', 'exact', 'precise', 'detailed', 'step-by-step'],
            'negative': ['generic', 'vague', 'unclear', 'maybe', 'possibly']
        },
        'actionable': {
            'positive': ['update', 'change', 'modify', 'configure', 'set', 'enable'],
            'negative': ['investigate', 'check', 'look into', 'research']
        },
        'complete': {
            'positive': ['first', 'then', 'finally', 'verify', 'test'],
            'negative': ['partial', 'temporary', 'workaround', 'bandaid']
        },
        'validated': {
            'positive': ['confirmed', 'tested', 'verified', 'proven', 'works'],
            'negative': ['untested', 'experimental', 'might work', 'should work']
        }
    }
    
    # Common prerequisites by solution type
    SOLUTION_PREREQUISITES = {
        'mapping_update': ['Access to mapping tool', 'Test environment available', 'Partner specifications'],
        'certificate_renewal': ['Admin access', 'Certificate authority account', 'Approval from security team'],
        'firewall_change': ['Network team approval', 'Change request submitted', 'IP addresses verified'],
        'partner_config': ['Partner contact information', 'Partner portal access', 'Current configuration backup'],
        'data_fix': ['Database access', 'Data validation rules', 'Rollback plan']
    }
    
    def __init__(self, db_path: str = None):
        """Initialize the Solution Verifier"""
        self.db_path = db_path or str(DATABASE_PATH)
        self.vectorizer = TfidfVectorizer(max_features=300)
        self.solution_cache = {}
        self._load_solution_history()
    
    def verify_solution(self, problem: str, proposed_solution: str, 
                       context: Optional[Dict] = None) -> SolutionScore:
        """
        Verify and score a proposed solution
        
        Args:
            problem: Description of the problem
            proposed_solution: The proposed solution text
            context: Optional context (partner, transaction type, etc.)
            
        Returns:
            SolutionScore with comprehensive assessment
        """
        # Check cache first
        cache_key = f"{problem[:50]}:{proposed_solution[:50]}"
        if cache_key in self.solution_cache:
            return self.solution_cache[cache_key]
        
        # Find similar historical solutions
        similar_solutions = self._find_similar_solutions(problem, proposed_solution)
        
        # Calculate success rate
        success_rate = self._calculate_success_rate(similar_solutions)
        
        # Score solution quality
        quality_score = self._score_solution_quality(proposed_solution)
        
        # Identify prerequisites
        prerequisites = self._identify_prerequisites(proposed_solution)
        
        # Identify risks
        risks = self._identify_risks(proposed_solution, similar_solutions)
        
        # Find alternatives
        alternatives = self._find_alternative_solutions(problem, similar_solutions)
        
        # Calculate average resolution time
        avg_time = self._calculate_average_resolution_time(similar_solutions)
        
        # Calculate confidence
        confidence = self._calculate_confidence(len(similar_solutions), success_rate, quality_score)
        
        score = SolutionScore(
            solution_text=proposed_solution,
            quality_score=quality_score,
            success_rate=success_rate,
            confidence=confidence,
            similar_successes=sum(1 for s in similar_solutions if s['success']),
            similar_failures=sum(1 for s in similar_solutions if not s['success']),
            average_resolution_time=avg_time,
            prerequisites=prerequisites,
            risks=risks,
            alternative_solutions=alternatives
        )
        
        # Cache the result
        self.solution_cache[cache_key] = score
        
        return score
    
    def validate_implementation(self, solution: str, 
                              ticket_data: Dict) -> VerificationResult:
        """
        Validate that a solution can be implemented successfully
        
        Args:
            solution: The solution to validate
            ticket_data: Current ticket data
            
        Returns:
            VerificationResult with validation details
        """
        # Extract implementation steps
        steps = self._extract_implementation_steps(solution)
        
        # Check if solution addresses the problem
        is_valid = self._validates_against_problem(solution, ticket_data)
        
        # Calculate effectiveness score
        effectiveness = self._calculate_effectiveness(solution, ticket_data)
        
        # Identify potential issues
        potential_issues = self._identify_potential_issues(solution, ticket_data)
        
        # Get historical evidence
        evidence = self._get_historical_evidence(solution)
        
        # Predict outcome
        expected_outcome = self._predict_outcome(solution, ticket_data)
        
        return VerificationResult(
            is_valid=is_valid,
            effectiveness_score=effectiveness,
            implementation_steps=steps,
            expected_outcome=expected_outcome,
            potential_issues=potential_issues,
            historical_evidence=evidence
        )
    
    def track_solution_outcome(self, ticket_key: str, solution: str, 
                             success: bool, resolution_time: timedelta):
        """
        Track the outcome of a solution for future learning
        
        Args:
            ticket_key: The ticket identifier
            solution: The solution that was applied
            success: Whether the solution worked
            resolution_time: Time taken to resolve
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Store solution outcome (in production, this would be a dedicated table)
            cursor.execute("""
                INSERT INTO solution_outcomes 
                (ticket_key, solution, success, resolution_time, timestamp)
                VALUES (?, ?, ?, ?, ?)
            """, (ticket_key, solution, success, resolution_time.total_seconds(), 
                 datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
            # Clear cache to reflect new data
            self.solution_cache.clear()
            
        except Exception as e:
            logger.error(f"Error tracking solution outcome: {e}")
    
    def _find_similar_solutions(self, problem: str, solution: str) -> List[Dict]:
        """Find similar solutions from historical data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Search for tickets with similar problems and resolutions
            query = """
            SELECT ticket_key, summary, description, resolution, 
                   created, updated, status
            FROM jira_tickets
            WHERE status = 'Done' 
            AND resolution IS NOT NULL
            AND resolution != ''
            ORDER BY updated DESC
            LIMIT 1000
            """
            
            cursor.execute(query)
            
            all_solutions = []
            for row in cursor.fetchall():
                all_solutions.append({
                    'ticket_key': row[0],
                    'summary': row[1],
                    'description': row[2],
                    'resolution': row[3],
                    'created': row[4],
                    'updated': row[5],
                    'status': row[6]
                })
            
            conn.close()
            
            # Calculate similarity scores
            if not all_solutions:
                return []
            
            # Vectorize problems and solutions
            problem_solution = f"{problem} {solution}"
            corpus = [problem_solution] + [f"{s['summary']} {s['resolution']}" 
                                          for s in all_solutions]
            
            try:
                vectors = self.vectorizer.fit_transform(corpus)
                similarities = cosine_similarity(vectors[0:1], vectors[1:])[0]
                
                # Get top similar solutions
                similar_indices = similarities.argsort()[-20:][::-1]
                similar_solutions = []
                
                for idx in similar_indices:
                    if similarities[idx] > 0.3:  # Similarity threshold
                        sol = all_solutions[idx].copy()
                        sol['similarity'] = similarities[idx]
                        sol['success'] = self._determine_success(sol)
                        similar_solutions.append(sol)
                
                return similar_solutions
                
            except Exception as e:
                logger.error(f"Error calculating similarities: {e}")
                return []
                
        except Exception as e:
            logger.error(f"Error finding similar solutions: {e}")
            return []
    
    def _calculate_success_rate(self, similar_solutions: List[Dict]) -> float:
        """Calculate success rate from similar solutions"""
        if not similar_solutions:
            return 0.5  # No data, assume 50%
        
        successes = sum(1 for s in similar_solutions if s['success'])
        return successes / len(similar_solutions)
    
    def _score_solution_quality(self, solution: str) -> float:
        """Score the quality of a solution based on indicators"""
        score = 5.0  # Base score
        solution_lower = solution.lower()
        
        for category, indicators in self.QUALITY_INDICATORS.items():
            # Check positive indicators
            positive_matches = sum(1 for word in indicators['positive'] 
                                 if word in solution_lower)
            # Check negative indicators
            negative_matches = sum(1 for word in indicators['negative'] 
                                 if word in solution_lower)
            
            # Adjust score
            score += (positive_matches * 0.5) - (negative_matches * 0.5)
        
        # Check for specific technical details
        if re.search(r'\b[A-Z]{2,}\d{3,}\b', solution):  # Transaction codes
            score += 0.5
        if re.search(r'\d+\.\d+\.\d+\.\d+', solution):  # IP addresses
            score += 0.5
        if re.search(r'[A-Z]{3,}_[A-Z]{3,}', solution):  # Config parameters
            score += 0.5
        
        # Length bonus (detailed solutions score higher)
        word_count = len(solution.split())
        if word_count > 50:
            score += min(word_count / 100, 1.0)
        
        return min(max(score, 0), 10)  # Clamp to 0-10
    
    def _identify_prerequisites(self, solution: str) -> List[str]:
        """Identify prerequisites for implementing the solution"""
        prerequisites = []
        solution_lower = solution.lower()
        
        # Check for solution type and add relevant prerequisites
        for solution_type, prereqs in self.SOLUTION_PREREQUISITES.items():
            if solution_type.replace('_', ' ') in solution_lower:
                prerequisites.extend(prereqs)
        
        # Check for specific mentions
        if 'admin' in solution_lower or 'administrator' in solution_lower:
            prerequisites.append('Administrator access required')
        if 'partner' in solution_lower and 'contact' in solution_lower:
            prerequisites.append('Partner contact required')
        if 'backup' in solution_lower:
            prerequisites.append('Current configuration backup required')
        if 'test' in solution_lower:
            prerequisites.append('Test environment required')
        
        return list(set(prerequisites))  # Remove duplicates
    
    def _identify_risks(self, solution: str, similar_solutions: List[Dict]) -> List[str]:
        """Identify potential risks with the solution"""
        risks = []
        
        # Check failure rate of similar solutions
        if similar_solutions:
            failure_rate = 1 - self._calculate_success_rate(similar_solutions)
            if failure_rate > 0.3:
                risks.append(f"Similar solutions have {failure_rate:.0%} failure rate")
        
        # Check for risky keywords
        risky_words = {
            'delete': 'Data loss risk',
            'remove': 'Potential data loss',
            'restart': 'Service interruption',
            'reboot': 'System downtime',
            'production': 'Production impact',
            'all': 'Wide-ranging changes',
            'force': 'May cause unexpected behavior'
        }
        
        solution_lower = solution.lower()
        for word, risk in risky_words.items():
            if word in solution_lower:
                risks.append(risk)
        
        # Check for missing test mentions
        if 'production' in solution_lower and 'test' not in solution_lower:
            risks.append('No testing mentioned for production change')
        
        return list(set(risks))
    
    def _find_alternative_solutions(self, problem: str, 
                                  similar_solutions: List[Dict]) -> List[Dict]:
        """Find alternative solutions that have worked for similar problems"""
        alternatives = []
        seen_solutions = set()
        
        for sol in similar_solutions:
            if sol['success'] and sol['resolution'] not in seen_solutions:
                seen_solutions.add(sol['resolution'])
                alternatives.append({
                    'solution': sol['resolution'],
                    'success_rate': 1.0,  # This specific solution succeeded
                    'ticket_reference': sol['ticket_key']
                })
        
        # Sort by uniqueness and return top 3
        return alternatives[:3]
    
    def _calculate_average_resolution_time(self, similar_solutions: List[Dict]) -> Optional[timedelta]:
        """Calculate average time to resolution for similar solutions"""
        if not similar_solutions:
            return None
        
        resolution_times = []
        for sol in similar_solutions:
            try:
                created = datetime.fromisoformat(sol['created'].replace('Z', '+00:00'))
                updated = datetime.fromisoformat(sol['updated'].replace('Z', '+00:00'))
                resolution_times.append((updated - created).total_seconds())
            except:
                continue
        
        if not resolution_times:
            return None
        
        avg_seconds = np.mean(resolution_times)
        return timedelta(seconds=avg_seconds)
    
    def _calculate_confidence(self, sample_size: int, success_rate: float, 
                            quality_score: float) -> float:
        """Calculate confidence in the solution assessment"""
        # Base confidence on sample size
        if sample_size == 0:
            size_confidence = 0.3
        elif sample_size < 5:
            size_confidence = 0.5
        elif sample_size < 10:
            size_confidence = 0.7
        else:
            size_confidence = 0.9
        
        # Factor in success rate consistency
        rate_confidence = success_rate if success_rate > 0.5 else (1 - success_rate)
        
        # Factor in solution quality
        quality_confidence = quality_score / 10
        
        # Weighted average
        confidence = (size_confidence * 0.4 + 
                     rate_confidence * 0.4 + 
                     quality_confidence * 0.2)
        
        return min(confidence, 0.95)  # Cap at 95%
    
    def _extract_implementation_steps(self, solution: str) -> List[str]:
        """Extract step-by-step implementation from solution text"""
        steps = []
        
        # Look for numbered steps
        numbered_pattern = r'(\d+[\.\)])\s*([^0-9\n]+)'
        numbered_matches = re.findall(numbered_pattern, solution)
        if numbered_matches:
            steps = [match[1].strip() for match in numbered_matches]
        
        # Look for bullet points
        if not steps:
            bullet_pattern = r'[-•]\s*([^\n]+)'
            bullet_matches = re.findall(bullet_pattern, solution)
            if bullet_matches:
                steps = [match.strip() for match in bullet_matches]
        
        # Look for action words at start of sentences
        if not steps:
            sentences = solution.split('.')
            for sentence in sentences:
                sentence = sentence.strip()
                if any(sentence.lower().startswith(word) for word in 
                      ['update', 'change', 'modify', 'create', 'delete', 'configure']):
                    steps.append(sentence)
        
        return steps if steps else [solution]
    
    def _validates_against_problem(self, solution: str, ticket_data: Dict) -> bool:
        """Check if solution addresses the stated problem"""
        problem_keywords = set(ticket_data.get('summary', '').lower().split())
        solution_keywords = set(solution.lower().split())
        
        # Check keyword overlap
        overlap = len(problem_keywords & solution_keywords)
        return overlap >= min(3, len(problem_keywords) * 0.3)
    
    def _calculate_effectiveness(self, solution: str, ticket_data: Dict) -> float:
        """Calculate predicted effectiveness of the solution"""
        base_score = 0.5
        
        # Specific solutions score higher
        if len(solution.split()) > 20:
            base_score += 0.1
        
        # Technical details increase score
        if re.search(r'[A-Z]{2,}\d{3,}', solution):  # Transaction codes
            base_score += 0.1
        if re.search(r'\b\d+\b', solution):  # Specific values
            base_score += 0.1
        
        # Action words increase score
        action_words = ['update', 'change', 'fix', 'configure', 'set']
        if any(word in solution.lower() for word in action_words):
            base_score += 0.1
        
        # Testing mention increases score
        if 'test' in solution.lower():
            base_score += 0.1
        
        return min(base_score, 1.0)
    
    def _identify_potential_issues(self, solution: str, ticket_data: Dict) -> List[str]:
        """Identify potential issues with implementing the solution"""
        issues = []
        
        # Check for missing information
        if 'TBD' in solution or 'TODO' in solution:
            issues.append('Solution contains placeholders')
        
        # Check for assumptions
        if 'assume' in solution.lower() or 'probably' in solution.lower():
            issues.append('Solution based on assumptions')
        
        # Check complexity
        steps = self._extract_implementation_steps(solution)
        if len(steps) > 10:
            issues.append('Complex multi-step solution - higher risk of error')
        
        return issues
    
    def _get_historical_evidence(self, solution: str) -> List[Dict]:
        """Get historical evidence supporting the solution"""
        # This would query the solution history database
        # For now, return empty list
        return []
    
    def _predict_outcome(self, solution: str, ticket_data: Dict) -> str:
        """Predict the likely outcome of implementing the solution"""
        quality_score = self._score_solution_quality(solution)
        
        if quality_score >= 8:
            return "High likelihood of success - solution is specific and comprehensive"
        elif quality_score >= 6:
            return "Good chance of success - solution addresses main issues"
        elif quality_score >= 4:
            return "Moderate success probability - solution may need refinement"
        else:
            return "Low confidence - solution lacks specificity"
    
    def _determine_success(self, solution_data: Dict) -> bool:
        """Determine if a historical solution was successful"""
        # Simple heuristic - ticket was closed and stayed closed
        return solution_data['status'] == 'Done'
    
    def _load_solution_history(self):
        """Load historical solution data"""
        # In production, this would load from a dedicated solution tracking table
        logger.info("Solution history loaded")


def demonstrate_solution_verification():
    """Demonstrate the Solution Verifier"""
    verifier = SolutionVerifier()
    
    print("Solution Verification Engine Demonstration\n")
    print("=" * 60)
    
    # Test case 1: Good solution
    problem1 = "Walmart 856 ASN failing with invalid date format"
    solution1 = """Update the Walmart 856 mapping as follows:
    1. Open the mapping tool and navigate to Walmart_856_4010.map
    2. Locate the DTM segment at line 234
    3. Change the date format from YYMMDD to YYYYMMDD
    4. Save and deploy to test environment
    5. Send test 856 to verify format is accepted
    6. Deploy to production after successful test"""
    
    print(f"\nProblem: {problem1}")
    print(f"Proposed Solution: {solution1[:100]}...")
    
    score1 = verifier.verify_solution(problem1, solution1)
    print(f"\nVerification Results:")
    print(f"Quality Score: {score1.quality_score:.1f}/10")
    print(f"Success Rate: {score1.success_rate:.1%}")
    print(f"Confidence: {score1.confidence:.1%}")
    print(f"Prerequisites: {', '.join(score1.prerequisites)}")
    print(f"Risks: {', '.join(score1.risks) if score1.risks else 'None identified'}")
    
    # Test case 2: Vague solution
    print("\n" + "=" * 60)
    problem2 = "Connection timeout with partner"
    solution2 = "Check the connection and see if it's working"
    
    print(f"\nProblem: {problem2}")
    print(f"Proposed Solution: {solution2}")
    
    score2 = verifier.verify_solution(problem2, solution2)
    print(f"\nVerification Results:")
    print(f"Quality Score: {score2.quality_score:.1f}/10")
    print(f"Success Rate: {score2.success_rate:.1%}")
    print(f"Confidence: {score2.confidence:.1%}")
    print(f"Prerequisites: {', '.join(score2.prerequisites)}")
    print(f"Risks: {', '.join(score2.risks) if score2.risks else 'None identified'}")


if __name__ == "__main__":
    demonstrate_solution_verification()