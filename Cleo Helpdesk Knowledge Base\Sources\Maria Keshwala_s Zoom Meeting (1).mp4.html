<p>Depending on the delimter is Oh, depending on the delimiter. Okay. Okay. Oh, that&#39;s why I need to know what the limit is. Gotcha. All right. Thank you. So, my delimiter. Let me start over. Now, let me go and file open. Let&#39;s try this one more. Okay. And then my little right here is this the little things are my delh can you hear me? Okay, my delimiter is this star thing and and then here is my replace. So that does not work. Yeah, I&#39;m trying to figure it out. I just want to make it pretty. You know what I mean? Like I I just want to make it look pretty. Um, let me try this. You got it. For some reason, it&#39;s not working. Um, I don&#39;t want to bother, you know, we don&#39;t have our physical, but we&#39;re good at sometime for things. Sometimes we forget things. Oh s***. Can I give you yours? So, what&#39;s my delimiter? My delimiter here start. Okay. The limiter here. Okay. So, let me ask them. I know. So one more time replace And I&#39;m going to replace my needle this with that. And then wrap around. That&#39;s selected. And that&#39;s going to be And then replace. Oh. Oh. What? Oh. Oh, it worked. Oh, no. doesn&#39;t look the way I want it to look. I&#39;m almost there. Let me do Let me figure something and that&#39;s my delimiter separated. And I want to replace with this. And yeah, this is selected. Extend it. This is selected. And we I replace all. This does not look the way I want it to look. But I honestly know what I mean. How I want it. I want it to like cuz that&#39;s the best way to convert files. Oh gosh. Wow. I mean this is why he showed me. Look at the screenshot. He said the limiter here and do that and then replace all and my delimiter separator of the document is an aster as you saw right. So I will find what this actually let me see actually so okay let me see something by that I will say that. Oh, and then after that we got My little separator is actually the That&#39;s what it looks like. I figure it out. I got it. Thank you. You know why? Because I was reading the document to see where it ends and then I see you see this ends with that. So that&#39;s my separator. So I got it now. Okay. All right. Perfect. Now, let&#39;s find the other one. This one is for save new cow. So, let me open the other file. Now, we&#39;re Well, now we&#39;re in track. Um, and I&#39;m going to do the same thing. That&#39;s my little tilted That means and then from Noal they buying hazelnut spr I&#39;m just making sure everything Now, let me double check that that they all have a zero after that cuz I&#39;m trying to find anything that can trigger that. You know what I mean? After the uh the ISA. So, I&#39;m just trying to see what you call that. Let me get from all the cloud there. Z one. I think that&#39;s a problem. I think that&#39;s why maybe, you know, I&#39;m trying to find a difference. All right, I&#39;m trying to find a difference and to explain you the uh document here. Down here is the body, the line item level. I don&#39;t care about that. I care about the ISA and receiver. That&#39;s the envelope, right? That&#39;s what says who&#39;s the sender who&#39;s receiving the control number of that um file. So, what I&#39;m noticing here, I&#39;m just comparing the two. So, this is the same. This is the sender. That&#39;s correct. I&#39;m just checking every little detail. Uh this says 22 because it&#39;s just sending files are brand new. So, it&#39;s okay. Uh the the version of the document is correct. But what I notice here, as you can see, you see this is the this is the receiver, right? This is the receiver. But if you scroll here, we got the dates, right? We got this data, we got the you uh for uh I&#39;ll show you where you can see that. That&#39;s a version of the document. And then this is the version type. This is the control. And after the control, they&#39;re adding a one. I&#39;m pretty sure if they send us a a uh one with the zero with zero here, it would generate the 97. Let me see if I can test that myself. I&#39;m gonna try. Let&#39;s give it a try. Right. All right. Let me take a screenshot. This is what I can identify that looks different on the A75 because the A75 I mean the 997 generated automatically. We don&#39;t control that. So I&#39;m going to manipulate that inbound document.</p>
<p>Um and say this is the difference. This is the difference. And I&#39;m going to check what setting is this because I need to tell um that segment is Let&#39;s check that out. Let&#39;s go into edit mode and business. Yeah. Yeah, that one. Okay. The acknowledgement one. So I think the acknowledgement type that&#39;s what&#39;s causing the creating the uh as you can see you see the seven terminators are is this and this is the elephant separator which is the aster and then The separator is this the like so uh it&#39;s called soup element separator okay and the repeat character is this too there is a back we set up that I usually work on part it&#39;s called partner configurations the configurations that&#39;s what you set up this but we don&#39;t set this up anyways but there is an issue they&#39;re saying oh you&#39;re sending us a wrong version wrong Then I go in and but you see the acknowledgements as one. I think that&#39;s my problem. I swear. So I&#39;m going to test that out and see if I need to check that out. Oh, sorry. Can you hear me now? Can you hear me now? Okay. Sorry. Sorry. Um, let me check if it&#39;s the same thing. Yeah, you see that? Yeah. Now it&#39;s zero. Let me see if we can play with it. Um, I would just have to delete that P. the customers. Now if I say injecting I&#39;m gonna say plus or something like that. Um okay. Okay. So this one is new cows 875. with the lady I show my not okay with it and I&#39;m going to give you the length of this ticket you can see my notes okay all right uh as value of zero and that USA imports a nullishment with a value of one. Okay. So in this you just have to think out of the box into any you know any possibilities right just confer the documents. So I&#39;m like why are we sending back a response from this source document a value of an ATA1 right so that&#39;s why I was like let me check two different files from two different people it&#39;s the same trading partner right so they&#39;re sending the same document the same specification for the A75. So, I&#39;m like, okay, it has to be something in there different that is generating two separate different 997s. And I&#39;m pretty sure that might be the problem. But to confirm that, I&#39;m going to see if I can test it. Um, let me see. So, I&#39;m going to do I&#39;m going to grab one of the uh USA imports A75s, right? And I&#39;m going to say Copy to new batch. I&#39;m going to edit the data and I&#39;m going to go in there and edit this to zero. And I&#39;m going say okay. I&#39;m just going to make sure it&#39;s not showing anywhere else. The envelope um and let me take a look at the class version. Oh, perfect. You see it&#39;s the zero is there. All righty. I&#39;m going to delete this um PO because I don&#39;t want that PO there. You know, I don&#39;t want to like inject two POS and WDI because I&#39;m pretty sure it&#39;s inject. So I will log into their account right now just to get rid of that PO and I&#39;m going to call it I&#39;m going to edit this. I&#39;m going to edit version and uh actually let me do it this version. Can I do this version? No. Okay, I know where the purchase order is. Um where&#39;s my purchase? has to be up here some my reference number. Where are you? Where are you? That&#39;s the ability to ship to. Oh, this is my purchase order. Sorry. I&#39;m going to do test. Don&#39;t touch. Uh, I&#39;m going to do uh yeah, I just do test and say okay, right? Uh, let me see. I&#39;ll see if that would generate 97 just to test it out. Okay,</p>
<p>you&#39;re ready.</p>
<p>Okay,</p>
<p>but they don&#39;t say it though. That&#39;s the thing. Um, what&#39;s the account number? Uh, so to find an account number, if I don&#39;t know it, you know, you either go to where I show you, but I also go here. Great. How do we know? Okay, let me go in here and type USA imports. USA. So, the account is 2609. I just saw it in the bottom right here. Mhm. All right, I have the their account open just to delete that a A75 inject, you know, because I don&#39;t want to confuse. All right, so I think we&#39;re good to go. I am going to uh say save everything that I did and uh I&#39;m going to close and then I&#39;m going to say okay. Okay. And I&#39;m going to I change the date because it&#39;s when you copy a batch, it will reflect in today&#39;s date, just so you know. Okay. Okay. There&#39;s my file. So, a generator 987. So, fingers crossed. That was the problem. Damn. Nice. You see that? It has on. Mhm. So, what I&#39;m going to do is uh I don&#39;t know. She will do it or I will do it. The lady from Sable. Let me uh let me take a copy of this and put it in my notes. So that&#39;s the problem. There you go. All right. All of that. Well, find the problem. Okay. Um but because we don&#39;t control the inbounds, so I cannot change every inbound. to a zero value to generate the correct acknowledgement, right? The training partner has to do it. So, um let me let them know. So, by adding the zero value it will on on the A75 it will generate the I will generate the correct 997. Um with the G7 you know that&#39;s not our problem right now. If they want nice 997s then send us a a good a good document. Right. So let me take a copy of this. this edit here. I&#39;m going to delete the file. It probably injected in my video. Let me see. Let me refresh everything. Yeah, that test injected. I just delete it. All right. I literally just clicked on it, right click and delete, you know. But I&#39;ll show you in my other screen now what I&#39;m going to do. So, what I&#39;m going to do here, I don&#39;t want to confuse anybody. Let me keep this one open until I get on my findings and then I&#39;m going to go ahead and delete this because I don&#39;t need it. All right. So, let me go into Let me reshare my screen. Okay. At least you we both learned something new today. All right. Huh? Are you you still confused or it&#39;s basic to make it look pretty? But you you&#39;ll know I&#39;ll show you more about notepad when you play with it. It&#39;s it&#39;s good when you&#39;re like doing it hands on. Um you know it&#39;s easier. But all right. So all right. These are all my notes right? As you can see I was identifying what the problem is. Okay. Oh, you can. Oh, uh, well, it Okay, that&#39;s a trick one. So, if you have like an issue, uh, I don&#39;t because everybody&#39;s different, Michael. Everybody&#39;s different. Unless you have a client that has an issue that they had it in the past, you can search past tickets of the client and the search here by the account number or you know and uh maybe pull that up. But if you want if you&#39;re taking notes right now right for this particular scenario which is an invalid setment on the 997 what you need to do right what we did was to check the inbound compare with other inbound that would that was generating a correct 997s versus this one right and And uh you can I&#39;m going to give you this link. You can copy this link on your notes and your e notes and reference to my ticket. Uh let me go. So if that&#39;s helpful, you know, so every time we work on a ticket, you can copy that link and always go back to it and read it. Okay. All right. Uh I want to show Nico this problem. Uh, let me see. Are you available to use? I mean, Um, send Nico the link I sent you to join this meeting. So, oh, actually I want to invite Lauren, too. I like, you know, when I find something new, I like people to learn. to if she wants to join and and because I you know when I find something new a new problem and I discover and I found the solution I like to share and you guys get the same uh problem uh is Lauren in here or or I don&#39;t know she cannot um he&#39;s not in</p>
<p>Michael All right, I just gonna go ahead and jump in and and show show you. So, let me share. Am I you guys looking at my ticket? Both of you right now, right? So, the issue here was True Commerce, right? The ADI provider sent an email to Marian should should have come to us saying, &#34;Hey, we receive we&#39;re receiving invalid 9 sevens from you guys right here with an unexpected TA1 segment after the after the I messed up this and I save it shouldn&#39;t be a GS segment right instead</p>
<p>I&#39;m like okay we generated 97s automatically right we don&#39;t control that mapping</p>
<p>and how do we fix that right So my, you know, I did several troubleshooting, which was first comparing a 997 from uh new cow because we only have two clients that send uh 97s do save a lot, right?</p>
<p>Yeah.</p>
<p>And and documents do save a lot. And I was checking the new cows. New cows lose good. They don&#39;t they don&#39;t have that AT1 segment. They they have the uh the uh sorry they have the GS on it right as you can see here it has a GS and then here has a AT1 with a control number right</p>
<p>so</p>
<p>so okay then I&#39;m like okay so there has to be a problem because we generate the response based on a source right and the source is the 75 right so the 75s I was like okay let me compare new cows and USA import 75 when I did my comparing the only difference that I can I don&#39;t care what happens in the bottom and the line item level is the envelope right which</p>
<p>yeah</p>
<p>that um ISA and all of that so uh the only difference I discovered was that new cow had a zero after the the the um this data here which is not the control after the control number sorry and this one had a one to it which I thought this might be the problem then I went in and I&#39;m going to then I went in and checked the envelope part here inside to see where that zero was belonging to and this is the acknowledgement so and no had a zero and they have a one, right?</p>
<p>So, what I did was to test it out and and confirm that the inbound document the A75 they&#39;re sending us is invalid with an invalid interchange acknowledgement change setup to one is creating this acknowledgement uh uh version with an ATA submitting. Right? So, I went in copy the batch. Let me go back to let me share because I want to share everything. Um, where&#39;s my thing? Share. Okay. I want to share here. All right. Then I went in and I copied the A75 to a new batch. Change my reference to text. So that way the customer doesn&#39;t get confused in case it injects quickly and they&#39;re looking at our POS, right? Change it to test</p>
<p>because it was going to inject to WebDI and I change it to the zero value within the acknowledgement, right? I save it and then they generated the the 997, right? And great after it generated the 997 and I go here delivery conversion I went in and I and I saw that my test 97 sorry that&#39;s the same generated with the GS. Let&#39;s see. Oh no, that&#39;s a that&#39;s a bad one. We&#39;re looking at the bad one. Uh where&#39;s my Google? Where is the good one? So we saw right here I think this this one. Oh no that&#39;s 141 should be this. Should I say</p>
<p>was it sent?</p>
<p>Yeah. I mean just Yeah, I just I just literally did it. Um uh let me change the I literally did it. Hold on. Let me show you the copy of it, right? Um uh we opened it and it looked good. So here, let me share this screen because I think I I got rid of it. Hold on. Where are you? I We did Zoom, me and um Michael, because he wanted to record, you know, and you can&#39;t record in Slack. Um here. Okay. Share. All right. This is the copy that I took the ISA generated, right?</p>
<p>Yeah.</p>
<p>So, this is says, you see it says test the A75, but it did generate it without without uh the ATA. Oh my god, Michael, didn&#39;t we look at it just now?</p>
<p>Why don&#39;t you find it in the send folder?</p>
<p>Yeah. No, no, no. Not in the send with that. You remember, we don&#39;t send 997s from WebDI. Um,</p>
<p>no, not not not in WebDI and ECS like that.</p>
<p>Let me go back. Uh, let me go back there and go into I wonder if it was still generating, but it did generate it. I literally would say, &#34;Are you guys looking at my my my server?&#34;</p>
<p>Yeah, ECSO2.</p>
<p>Okay, cool. All right, let&#39;s do this. Um, let me do USA. No, let me do No. Hold on. Hi Oh, let me send this to you. Okay, one second. Bye. All right. Sorry. Let me send this confirmation code. Okay. All right. So, USA this What the heck am I doing? This is the one we did today, right? So, I went in and I removed and I change it to a zero. And then It did the outer thing which is the 97. I wonder if it will create an This is an A75. Where&#39;s my 97? Where&#39;s my 97? Nico, does that work for you to to um create a 97 from the 850? I put a no on the team. Nobody responded. It&#39;s not working for me. I see Sandy doing doing it.</p>
<p>Let me go check. But I know that&#39;s a problem. That&#39;s the fix. Hold on. Give me one second. Hold on. Hold on guys. It is X Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z Z as in zebra 7 F as in Frank 9 8 S is in Sam. Okay. Um, so did you are you able to Nico?</p>
<p>I&#39;m checking right now.</p>
<p>Oh,</p>
<p>but I know that&#39;s the problem because the acknowledgement that type that creates</p>
<p>Yeah, I can create it.</p>
<p>Oh, can I see it? Why I cannot? That&#39;s bugging me. Uh, I&#39;m going to stop sharing you. can share. Anybody here can share. How can I stop? I&#39;ll stop sharing. Yeah. Okay. Share. Nico. I want to see if that will fix it.</p>
<p>So, tools create FA.</p>
<p>Yeah,</p>
<p>there it is. Can you click on the 97? And can you click on test version?</p>
<p>Okay.</p>
<p>Um, can you go after the B? What do you see?</p>
<p>GS.</p>
<p>Perfect. I fixed it. Okay. Can you send me that? screenshot.</p>
<p>Uh I want you to actually uh copy the data, not a screenshot. Oh, you can send screenshot too. It&#39;s okay.</p>
<p>Send it to me. You can slap</p>
<p>forward. Yeah, slap. So yeah, that fixed the problem. So that acknowledgement thing on the inbounds is it it does it does make a difference for the acknowledgement type in case you get something like that that somebody&#39;s getting like an invalid with the invalid segment. Nico, that&#39;s where you go. Yeah. I&#39;m going to send that to True Commerce and I&#39;ll be like, well, when they send us correct A75s,</p>
<p>well, yeah, it&#39;s tell them specifically the ISA14.</p>
<p>ISA14, right? Okay. Cuz I can&#39;t see it.</p>
<p>The the specs.</p>
<p>Yeah.</p>
<p>Oh, right.</p>
<p>Let me see. You have the effects. Hold on. Yeah, that&#39;s an that&#39;s the A75 in link you&#39;re looking at.</p>
<p>Yep.</p>
<p>Yeah. Let me take a screenshot of that. Say that. Yep.</p>
<p>Oh, no. Go to lunch. Yeah, go to lunch. Yeah. Yeah, I&#39;ll take lunch, too. So that way we kind of come back. at the same time. Okay, I&#39;ll see you later.</p>
<p>Peace out.</p>
<p>Bye.</p>
<p>Hi.</p>
<p>Yeah.</p>
