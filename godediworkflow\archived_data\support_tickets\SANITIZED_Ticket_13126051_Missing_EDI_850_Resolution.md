# Support Ticket: Missing EDI 850 Purchase Orders - Resolution Example

## Ticket Information
- **Ticket ID**: 13126051
- **Issue Type**: Missing EDI Documents
- **Document Type**: 850 Purchase Orders
- **Trading Partner**: [RETAIL_PARTNER]
- **Integration Type**: EDI via FTP
- **Resolution Time**: Same day
- **Root Cause**: Multiple POs combined in single file

## Problem Description
Customer reported not receiving EDI 850 Purchase Orders from their retail partner. The orders were expected but not appearing in their WebEDI system.

## Troubleshooting Steps

### 1. Initial Investigation (P4.0 - WebEDI Portal)
- Checked WebEDI Portal 4.0 for incoming transactions
- Confirmed no 850s visible in the system
- Verified customer permissions and access settings

### 2. File System Check (ECS - Server Level)
- Connected to ECS server to check raw file directories
- Located FTP incoming directory: `/var/log/edi/incoming/[partner_folder]`
- Found EDI files present but not processed

### 3. File Analysis (EDI Notepad)
- Downloaded suspicious file for analysis
- Opened in EDI Notepad for segment inspection
- **Key Finding**: File contained multiple ISA-IEA envelopes in a single transmission

### 4. Root Cause Identification
The issue was caused by the trading partner sending multiple 850 Purchase Orders within a single file, but without proper batch headers. The system expected:
- One file = One transaction
- Or proper batch envelope (multiple GS-GE within single ISA-IEA)

Instead received:
- One file = Multiple complete transmissions (multiple ISA-IEA pairs)

### 5. Resolution Applied
1. **Immediate Fix**: 
   - Manually split the multi-transaction file into individual files
   - Reprocessed each file separately through the system
   - All 850s successfully appeared in WebEDI

2. **Long-term Solution**:
   - Configured file splitter rules in ECS
   - Set up monitoring for similar file patterns
   - Notified trading partner about preferred file format

## Validation Steps
- ✓ Verified all 850s now visible in WebEDI Portal
- ✓ Confirmed customer can access and process orders
- ✓ Set up automated splitting for future files
- ✓ Created monitoring alert for multi-envelope files

## Key Learnings
1. Always check raw file structure when documents are missing
2. Trading partners may send non-standard file formats
3. EDI Notepad is essential for diagnosing file structure issues
4. File splitter configuration can prevent recurring issues

## Prevention Measures
- Added file format requirements to partner onboarding checklist
- Created automated detection for multi-envelope files
- Documented standard vs non-standard file formats

## Tools Used
- **P4.0**: WebEDI Portal 4.0 - Initial investigation
- **ECS**: Server access - Raw file retrieval
- **EDI Notepad**: File structure analysis
- **File Splitter**: Automated file separation

## Customer Communication Template
"I've identified the issue with your missing 850 Purchase Orders. Your trading partner is sending multiple orders in a single file, which our system wasn't processing correctly. I've manually processed all pending orders, and they're now available in your WebEDI portal. I've also configured our system to automatically handle this format going forward, so you won't experience this issue again."

---
*This ticket demonstrates proper EDI troubleshooting methodology: systematic investigation, root cause analysis, immediate resolution, and preventive measures.*