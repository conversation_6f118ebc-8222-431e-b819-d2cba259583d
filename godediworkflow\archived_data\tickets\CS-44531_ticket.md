# CS-44531: RE: CS-44388 810 rejection question

## Ticket Information
- **Key**: CS-44531
- **Status**: In Progress
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON> Long
- **Created**: 2025-05-29T09:40:13.766-0600
- **Updated**: 2025-06-13T14:35:02.082-0600
- **Customer**: <PERSON> Long

## Description
Please see attached where the information that I submitted originally did include the decimal. Will you please see if it is a formatting issue internally within the document. I wouldn’t have submitted without a decimal. I don’t want to submit again and have the same issue… 

!image001.png|thumbnail! 

  

  

Thank you, 

<PERSON>  

   

{color:#0070C0}Taylor A. Long{color} 

{color:#0070C0}Office Administrator{color} 

{color:#0070C0}Wolfe & Swickard Machine{color} 

{color:#0070C0}1344 South Tibbs Avenue{color}  

{color:#0070C0}Indianapolis, IN 46241{color} 

{color:#0070C0}(************* x40{color} 

{color:#0070C0}(************* fax{color}{color:#0070C0}{color} 

  

{color:#172B4D}CONFIDENTIALITY NOTICE: This E-mail and all attachments transmitted with it are private and confidential and may be protected by legal privilege. If you have received this message in error, please notify the sender immediately by returning the message to the sender and please delete this message and all attachments and all copies and backups thereof. If the reader of this message is not an intended recipient, you are hereby notified that any use, copying, distribution, or disclosure of this E-mail or its attachments is strictly prohibited. Thank you.{color}{color:#1F497D}{color} 

!image002.jpg|thumbnail!{color:#1F497D}{color}!image003.png|thumbnail!{color:#1F497D}{color}!image004.png|thumbnail!{color:#1F497D}{color}!image005.png|thumbnail!{color:#1F497D}{color}!image006.png|thumbnail!{color:#1F497D}{color}{color:#1F497D}{color} 

  

{color:#172B4D}The{color} {color:red}L{color}{color:#172B4D}anded{color} {color:red}V{color}{color:#172B4D}alue{color} {color:red}O{color}{color:#172B4D}ption{color} 
* Go Beyond the traditional supplier relationship to a true  *collaborative relationship*
* Wolfe and Swickard will work  *with your team*  to ensure you get the parts you need, exactly how you need them
* Together, we will obtain  *more value* so you can make your customers happier, and your competitors fearful
  

  
  

 *From:* Michael Hoang <<EMAIL>> 
  *Sent:* Wednesday, May 28, 2025 5:08 PM
  *To:* Taylor Long <<EMAIL>>
  *Subject:* CS-44388 810 rejection question   

   

{color:#999999}—-—-—-—{color}    

{color:#999999}Reply above this line.{color}   
  

{color:#333333}Michael Hoang commented:{color} 

{color:#333333}Hi Taylor,{color} 

{color:#333333}We finally figured out what is wrong, I reviewed the 824 rejection and the 810 invoice data you provided. The rejection was due to a  *“total amount error”*, which appears to stem from the formatting of the charge values in your invoice, specifically the  *decimal placement* in the raw data.{color} 

{color:#333333}Here’s what we found:{color} 

 {color:#333333}·{color} {color:#333333}The  *total dollar amount for the charge was not formatted correctly*; the raw data lacked the necessary decimal point, causing the system to interpret the amount incorrectly.{color} 

 {color:#333333}·{color} {color:#333333}The total amounts should reflect decimal places to match the expected format. For example, a charge amount like 292.30 must be entered with the decimal and not as 29230 or 2923.{color} 

 {color:#333333}·{color} {color:#333333}Additionally, there was an invalid code in the allowance/charge segment (SC code), but this is secondary to the decimal formatting issue.{color} 

 {color:#333333}·{color} {color:#333333}The rule to remember is: If IT07 is present with either IT06 or IT08, IT07 takes precedence, and a dollar amount must be correctly entered with the decimal placement.{color} 

 *{color:#333333} *To resolve this:*{color}*{color:#333333}{color} 

 {color:#333333}·{color} {color:#333333}Please create a  *new invoice with the corrected decimal formatting* in the charge amount fields.{color} 

 {color:#333333}·{color} {color:#333333}Avoid restaging and resubmitting the same invoice, as the system will not pick up those changes on an existing document.{color} 

 {color:#333333}·{color} {color:#333333}Ensure that the charge and allowance values follow the correct syntax as per the invoice requirements.{color} 

{color:#333333}If you need assistance creating the new invoice or have additional questions, feel free to reach out. I am here to help !{color} 

{color:#333333}Michael Hoang{color} 

{color:#333333}Cleo Communications{color} 

{color:#333333}<EMAIL>{color}{color:#333333}{color} 

[{color:#333333}<EMAIL>{color}|mailto:<EMAIL>]{color:#333333}
 [************|tel:2812928686] opt. 2{color}{color:#333333}{color}   

[{color:#333333}{color}{color:#3572B0}View request{color}{color:#333333}{color}|https://link.edgepilot.com/s/aab9d2f3/wEz-4nGUrUuxpd4H9Gjjeg?u=https://help.desk.datatrans-inc.com/servicedesk/customer/portal/1/CS-44388?sda_source=notification-email]{color:#333333} ·{color} [{color:#333333} {color}{color:#3572B0}Turn off this request's notifications{color}{color:#333333}{color}|https://link.edgepilot.com/s/9218fb4e/sxbxzG5-mUW5tVWXKLyXww?u=https://help.desk.datatrans-inc.com/servicedesk/customer/portal/1/CS-44388/unsubscribe?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0Z3QiOiJhbm9ueW1vdXMtbGluayIsInFzaCI6ImFhNWMwNzg4MDUxZmMwMTMyYmM5YzZmY2QzNzliOTRjYmIwYzI5MWE5ZTcyMTI3MDI0NjViMDU1MzY4YzUyNmEiLCJpc3MiOiJzZXJ2aWNlZGVzay1qd3QtdG9rZW4taXNzdWVyIiwiY29udGV4dCI6eyJ1c2VyIjoicW06ZGI2YWEzMGUtM2E3MS00YjNiLTlkZTMtYTY1MTI3Yzk5ZjQxOjgxZmI0NTE0LTgwNTctNGZlZC1hNmJhLThlZWEwZWMxZDIzMCIsImlzc3VlIjoiQ1MtNDQzODgifSwiZXhwIjoxNzUwODg1NjgzLCJpYXQiOjE3NDg0NjY0ODN9.2sBRjd2AoxRJ9Vonmoyr_VKLGUtfmjO3j4-qSsDOjbc]{color:#333333}{color}  

{color:#333333}Sent on May 28, 2025 3:08:03 PM MDT{color}{color:#333333}{color}   

!~WRD0000.jpg|thumbnail!{color:#333333}{color}
 
{color:#333333} Links contained in this email have been replaced. If you click on a link in the email above, the link will be analyzed for known threats. If a known threat is found, you will not be able to proceed to the destination. If suspicious content is detected, you will see a warning.{color}

## Components


## Labels

