<p>So this is my ticket that I created with her. Um so you know she said oh the A10s are showing total zeros in the column and then I open it and then you know something funky was doing the web portal. I saw it with my own eyes and I&#39;m like what the heck you know like it was showing zero totals when they actually have totals. send them right. Uh I I and what I did was I went to the server just to make sure that raw data we sent out was actually um sending it with total so she can get paid. Right. Uh another thing she requested was the first request was I want my my my invoice number I want to match with the PO number from the trading partner. So that way it autopop populates for her. Right. Um, only for Unifi because for me your is already set up and I put a note for me. I need a mapping code too. She said I want hardcode or add a default the Dun&#39;s number. This this nine is the Dun number code from from the map and I&#39;m going to walk you there to show you what I did. So I&#39;m going to show you how to do open documents default uh when the customer asks you Uh, hey, can you allow me to autosave so it autopop populates a data always for me? Okay. So, I&#39;m going to show you that. Uh, let me one second. I&#39;m going to stop sharing. Share again. Um, this one. So, when a customer calls and say, &#34;Hey, I want my my aense to reflect my remit to address right which is their address to get paid right my this is their company name the Dun&#39;s number the address city blah blah blah so I don&#39;t I don&#39;t want every time I create a document I don&#39;t want to manually to keep this info in all the time right which makes sense so I always said to the customer if the data is always the same repeatedly go ahead and add it as a default. That way you don&#39;t have to manually put that data in all the time, right? But if it varies, no, that&#39;s not a good idea, right? Uh because because then it will populate the same information. So right here there is default and the trading part I was working was unify and this is the A10, right? The customer can do this, but I usually do it because for some reason when the customer does is not saving it, but if I do it saves it, which is so weird. Um I I don&#39;t know if it&#39;s because when they refresh, it doesn&#39;t reflect to me. I I just do it just to make sure it&#39;s done, you know. So here, so she entered this and I entered this. I I told her I asked her, &#34;What&#39;s your Dun&#39;s number?&#34; Okay, we&#39;ll put it in. I save it. Meaning once she creates an A10 for this specific trading partner, it will always populate this information. Does that make sense? Okay. Um, also she say Maria for the additional information terms of sale the description discount amount is is always net 45. Put net 45 there. I did put net4. Then she said, oh, for the method payments of origin shipping point is always fed as ground. So I put fed as ground. Then she said, then in the bottom where it says invoice shipment summary I want you to default is always each is always this weight and it&#39;s pound right okay before Michael it was grayed out like these fields right here they&#39;re grayed out when they&#39;re grayed out it means that in the map in the dump the check of uh to allow to default is off and we need to turn that switch on so that field populates in here. Does that make sense? So for instance uh okay so let me explain. So let&#39;s just let&#39;s pretend uh these ones right here were off. Right? I know which one those are because I&#39;ve been doing this for a while. If you don&#39;t know which one. No, if you don&#39;t know whi which one those are, I always say in the beginning, export NA10 that&#39;s being sent. Okay, see where this data is entered, which segment. Okay, that way you know which one you&#39;re going to open. But I&#39;ll show you. And sometimes they&#39;re plain and simple. I know the codes for your measure or weight or right. So it&#39;s You&#39;re going to get it when once you get used to it. So, I&#39;m going to share my other screen to show you the dome. Okay. Or what we do. So, let me stop sharing and reshare again. And yes, oh, I have to. All right. So, this is this is the global dump. In the global dump, we have to be very careful to make changes because Global means a lot of customers might use this dub as well. Okay. Uh we got to be careful when we make changes the time and stuff because some of the trading partners are huge like Amazon, Walmart, Target and we have a lot of customer constantly sending documents to us. If the dumb is open for editing and the cost customer sends data that that file might failed. Okay? Because the DOM is open for editing. It&#39;s not saved. Okay? It&#39;s not if it&#39;s in edit mode, it&#39;s going to kick down the document that is coming in for translation, if that makes sense. So, always ask me or Sandy or you know, you know, usually Sandy and I I have a a good eye when we can because even Nico calls Noria I need to make a change in the dumb. Can I do it or I need to make this kind of change. He always checks you know because this is you know doms are a little again this is what literally controls document flow right um so some of the maps are integrated okay I&#39;m gonna I&#39;m gonna give you this this good uh You know, I&#39;m happy that I&#39;m that we get to this because integrated maps, they will have some red line up here something and and you you&#39;ll know is integrated, right? Um those maps are linked with the links map which is this right here. Those are the customers that are global shop, right? They are integrated and they might use this dump as their target map. Okay. Um so my source will be the flat file, right? L record or Tre or whatever and my target will be this map. So if I have it open, you know, could cause issues, right? So integrated ones are the most tedious one to be honest. Um yeah, stay away. So So if you need again even Laura asked Sandy you can ask me okay you say Maria this customer wants this in the map change always ask okay just to be in the safe zone okay sometimes I know we&#39;re not allowed to create uh clones but the project team does it I clone when the customer is asking for two many specific customizations. Not everybody&#39;s the same. Like I said, um some customer might say, &#34;Hey, I want this hard code. I want this this hard code and this and that.&#34; And I&#39;m like, &#34;You know what? Let me clone this and assign this map just to you, right? And do all my customizations.&#34; That&#39;s what I do just to be on the safe side. Now, If they are integrated and they need mapping changes when we already completed a project, it requires a work authorization because integrated customer have to pay any mapping changes. Okay. Um All right. So unless it&#39;s something like that&#39;s why I told Lauren to take to sent me that Global Shop one that you have because Global Shop is a little tedious. Okay. And mail cup is tedious. So, sign that we work at it together. Okay. Well, John Deere if if if I mean they sent 830s, you know, it was our complex the and and caterpillar, but you can take it because you don&#39;t want to stay away from anything. Yeah, you want to learn, right? But for now, you want to take baby steps. Yeah. You don&#39;t want to run before you walk. Okay. But I eventually we want you to learn everything. Okay. All right. We just don&#39;t want to overwhelm you. Uh, and we want you to learn the baby steps. Okay. So, all right. So, for instance, like we mentioned, like she says, Maria, I want the invoice shipment summary. I know those are in the bottom is the ISS segment, which is this one right here. Um, this is one here. So, if I I want to It it it was grayed out before. It was grayed out. So what I do, I come here, go to the segment right here. Right. Go click and I click allow the Oh, can you hear me now? Oh, why it goes and goes? It&#39;s zoom. All right. So I click on the one. She you know, she say, &#34;Oh, Maria, I want a default.&#34; I&#39;m like, &#34;Okay, you want to allow default?&#34; This switch right here here will allow to reflect the field in the default session in the web portal. That way you can key in the the value. We do this because the customer is hard coding it within their own portal that will not affect anybody else. Right now if the customer have their own mapping and there is certain things where our coding or whatever. I can come here and hardcode that. Okay. Uh but in this case, this is a global DOM. Allowing a default will not affect anybody, but it will affect everybody in the terms of that this will be available for anybody who would like to enter the data to save it for default, but it&#39;s not going to break anything if that makes sense. Right? So once I&#39;m Done. I save it. It takes a little bit, right? I just open the default units of measure so you can see visually how it works the whole process. Okay. Um, it takes a bit, you know, I did this while she was on the phone just to, you know, have her take a look at it. But mapping rules, I used to do it while the customers on the phone. I used to do mapping and stuff. Not anymore. Take a take, get in line. Dude, I have other customers that were first, right? They send the ticket. But some customers are clever and they like, &#34;Oh, you know, let me call.&#34; Right? Because I&#39;ll get somebody right away. No. Sometimes if if it&#39;s um holding production, meaning they have an emergency, they need to send as poss. I&#39;m like right on the phone taking care of that, right? Uh, but if it&#39;s gonna take forever, you&#39;re gonna know as you go, right? If it&#39;s something that I need to address with Sandy, we need to get to it, right? I tell the customer, I&#39;m going to create a ticket for you. I&#39;m going to set it up as as high and as soon as I&#39;m done with you in the call, I&#39;m going to get with my manager and we&#39;re gonna get this done. I&#39;m gonna call it and try to figure it out what&#39;s going on, right? And get back to you. I do that. you know when something you can do right and then something that you cannot do right and then fall okay because I would take all your time you have to use your time very wisely so one thing I do a lot I multitask a lot and you&#39;re gonna become a pro at multitasking here you can you can be working on a mapping problem me I could be working on delta and then a phone call uh every like today you were with Lauren Nico was At lunch, I was in the middle working with Global Shop and I have to answer the phone for two customers and this one took forever because I was doing all this, right? So, I have to stop what I was doing in and uh with Milka and focus on this. So, you&#39;re gonna you manage that. All right. So, I&#39;m going to stop sharing and show you how it reflects and uh webd. So, first of all, once you make an update, Okay. In the dome, you always need to re refresh because it might not reflect, you know, life like while you&#39;re having it open like that. You have to refresh. Um, what was I going to say? I was going to say something else about the D. Oh, any dumb changes you make. Let&#39;s say the customer has a invoice error, ASN error, and we need to fix the sec or add a sec, you know, update the the DOM. Uh, they need to recreate a brand new document. They cannot use the ones they sent out and edit them because editing them, which is restaged, is not going to reflect the mapping changes. Okay. All right. So, let&#39;s go to default. And here 810. All right. So, see it&#39;s available now. So, I can say uh I&#39;m, you know, if I&#39;m always chip 10, I can chip 10. Save it. Always going to populate 10. But that can change, right? We we&#39;re not going to set up a unit ship. And that can change anytime. Things like that change, amounts change. So, that you&#39;re not going to settle for default. You will know as you as as you go why it&#39;s not defaulting you know like addresses can be default right things like this so you&#39;ll know all right so that&#39;s that&#39;s this so you know the things I have to do enter the data save it so always going to populate my next step request was that we&#39;re going to do now is set up a mapping group right so first I go to the inbox and And she wants Oh, this is not my corner. Hold on. Okay. Right here. I didn&#39;t want to open it. Okay. So, she wants this number to once she create an 810 to populate on the as an invoice number. Does that make sense? No. A10. The invoice. She wants She wants the purchase order number to be the invoice number for her A10. Does that make sense? So once she creates a respond, that will automatically ically populate on that. Okay. From the doc. If she comes in here to create a new document manually, meaning not from the PO and comes in here and creates an A10, that&#39;s not going to autopop populate because it doesn&#39;t have a source to to reference from. Does that make sense? All right, I&#39;m going explain it to you again. When you create a document from a A50, a purchase order, I receive a purchase order. Now, I need to build them, right? I&#39;m going to create an invoice, right? Some of the fields from this purchase order, I&#39;m going to autopop populate on this eight time because I&#39;m creating a document based on this source, which is my A50. So the data values, some of them will come over. Okay. And I can set up rules. I can even have a lot of things coming over to my A10. That way I save a manual entry. Does that make sense? Now the customer has two choices to create documents. They can create it from the A50 or they can create it from the from the file new but it&#39;s manual it&#39;s blank there is nothing there they have to complete everything the only thing that will populate is the data that we set up as default on that document type for that specific trading partner okay are you following me now okay let me know when you&#39;re confused And I&#39;ll go back because I go ahead, right? And another thing you you&#39;ll learn as you go. Uh we&#39;re talking in in this scenario is the A50, even the A75, same thing, right? The A75, which is the grocery store ones, they can do the same thing. They can also create an invoice from the A56. Okay, you see they can um I recommend always to do it from day 50. That&#39;s my choice. And the reason why is the data is more accur You never know the customer is doing this one right and some data might be required from the A50 rather than the A56. So I would like to do it from here. You know it is what it is. Whatever pre preference some customer like to do it from the A56 to be honest you know. You know now Yeah. Because an A56 is is the advanced shipment notice. So the A6, as you know, is a document that tells the purchaser, hey, this is the items you&#39;re going to get when the truck arrives, right? So pretty much once the customer receives an 850, they respond with this, right? And what this is is actually showing how they&#39;re packaging the shipment level by carton, how many pounds, is it by pallet, right? Um, who&#39;s the carrier, right? I think this is FedEx. Yeah, Fredd. Um, uh, the routing number, they get this information, the carrier details, they get it from the carrier. Okay. They need to get this from the carrier. Um, so you know, they ship from from ship to ship from is their warehouse, right? And ship to the warehouse that&#39;s going from uh to Unifi, right? Um and this is a purchase order detail the day, whatever. Uh this is generated automatically the UCCC um that&#39;s comes in the labels. Okay? And and this is where the customer prints the labels from before they ship the boxes. You know, the ones you get at home when you order something, right? You get a label. This is the same thing. So, um um this goes electronically. Let&#39;s say if it&#39;s, you know, it&#39;s going to UniFi, UniFi gets it in the warehouse. Uh every, you know, the ASN and looks like, oh, we&#39;re getting 90 uh quantities of this order. When they, you know, they scan it and they&#39;re like, okay, once the truck arrives, they need to have this document at hand. Otherwise, they&#39;ll be fined. Okay? That&#39;s why some of them find the customer if they don&#39;t receive this on time and the truck arrives without this information. Okay? So, that way you have an idea. Okay? Um because this helps them a lot. Like they have the order number, they have everything and they know what&#39;s coming. They have the routing number of the shipment, you know. Um it helps them. And here, as you know, this is where they they print the labels, right? Um, actually, I guess they don&#39;t print labels for this one. Let me see what they print. Maybe they print it in house. Some customers, they print this in house. Oh, they have a Yeah. Yeah, they&#39;re printing this in house. Okay. Yeah, they have their own labels. It looks like when you don&#39;t see that, cuz we had to set up their labels for them, that means they&#39;re printing them in house. Their lab Okay. Okay. So, let&#39;s settle the mapping rules. So, how do I know? Okay. We uh let&#39;s go to the</p>
<p>Yeah, it might be that they&#39;re printing an alpha. Yep. And or or if they&#39;re brand new customer, maybe, you know, they might Some customer, don&#39;t get me wrong, some customer might call you and say, &#34;Hey, I want I want labels.&#34; I heard from Sandy because, you know, after we migrated with Cleo, I don&#39;t know if we&#39;re going to start charging for that, if they pay for that upfront with sales. So, we need to check with sales if they actually pay for it before we we create a development ticket to create the labels. Okay. Uh but that is something else that when it gets to it, we&#39;ll walk you through and help and you know, help you help you. Um, okay. So, right now we have the purchase order, but I in order for me to create a rule, I need the segment, you know, I need the raw data. So, I&#39;m going to go back to inbox and I&#39;m going to export a document. And my purchase order number is this one I believe. Yep. You see it&#39;s the BG BE37. Okay. So, right. So, what I&#39;m going to do, I&#39;m adding those as I go. Right. EO. B G BGO3 segment. Okay. All right. And now I want to know my 810 my 810. Oh, where did I go? I feeling exhausted. Like I had a good lunch though. Uh Let me see. This is a 10. Okay. And it will be my BGO2. Okay. BG I B2. Sorry. B I G02 set. Okay. All right, I&#39;m going to share my other screen and the DOM uh to show you how we set up mapping groups. Okay. All right. Sharing. All right. Are you seeing my the D right? Okay, cool. All right. So, we go to mapping rules. Give me one second. something that I have here that will help me. Okay. Okay. All right. So, we going to find the customer. Actually, never mind. Just click on new. We set up the customer, right? The customer is name uh venture Venture outdoor venture. Okay. Trading partner is five natural. Document type is my A50 and the target segment is uh hold on a minute. Oh no, my document type is a time. Sorry about that. My target segment is my A10. I&#39;m sorry. The segment BG B I G. Oh my god, why? So, B I G and then asteris two. I don&#39;t know if I would take the two or one, but I don&#39;t I think I&#39;ll think 02. Let me try go two and see if that works. And then my source is the 850. And then my source is BG PG3. Let me double check. B uh B E G. Okay. Got it. B E G B E G Oh. Okay. And then I guess that&#39;s that. Um hopefully it takes the um Oh yeah, it removes the zero. So let me double check now. Let me go back to where we are. I might, you know, might be wrong or right. Let&#39;s just double check sometimes. Let me just take it. Stop sharing. share my other screen. All right, I&#39;m going to test it out by going to inbox. Uh, I&#39;m going to refresh my my thing first and I&#39;m going to click on one of the UniFi 850s. It doesn&#39;t matter if they send a document or not. This is just testing. When I create a respon, I will know immediately if it worked. It worked. Okay. So, you see how it populated the PO. I know it worked. So, I&#39;m going to do this. I&#39;m gonna take a screenshot of that because I add any proof that I did and you take it that way, you know, something comes back and be like, &#34;Hey, I&#39;m I did it. I did it.&#34; All right. Okay. set up for the AL to reflect the invoice number as the No. Okay. So, what I&#39;ll do, I&#39;m going to the test saved and then I&#39;ll go back to once it&#39;s saved I&#39;ll go to draft and I&#39;ll delete my test. I&#39;m going to reshare my screen um to show you my ticket. So The sa took me. O. So, okay. Let me see. I answered the call uh she called at this is Oh, so 40 minutes was the call. 40 and look onto now. Yep. Some tickets might take you very long. I have tickets that have taken me oh my god when I create dev tickets you know mapping troubleshooting my hair here for a couple of hours. Um all right so so b pretty much you see my take a right literally added intern I don&#39;t know for me you know I put the VG se what I did mapping rule, you know, mapping rule set up, blah blah. I save it. I reply to the customer. Say, &#34;Hello, what&#39;s her name?&#34; Michelle. Hello, Michelle. think I&#39;m their designated person. It&#39;s not like that. Uh I do have some designated customers, right? But uh anybody can work on anything, right? They just they just like some people and and what they do is they will reopen the same email or the same ticket we had previously and open something else. I don&#39;t like that. And this is why it&#39;s not good for you or or anybody. One you already closed a ticket related to an issue, right? And if they have a separate um case scenario which is totally different and it requires a debt ticket once you create a ticket plus adding this ticket to the dev ticket that might confuse the developers being like what the heck is this? Is it this the problem or that problem? You know what I mean? So I don&#39;t like combining problems in one. Now in this situation I knew I&#39;m not going to need a developer. So that&#39;s why I kept on the phone where she was asking me for as if they&#39;re on the phone with me and they had different problems, I&#39;ll add them all in one ticket. Okay? But if I already address their issues, worked it, and you have something new, you have to create a new ticket. I&#39;m already done with this. You know what I&#39;m saying? Because that can create problems. All right. So, I just say thank you. Save. And then I&#39;ll close this. Do resolve. And that&#39;s it. Done. But you see how long it took. Do you have any tickets open that you want me to address or that you need to know or we can go? Mhm.</p>
<p>Okay. Do you have any Oh, Andrew. Oh,</p>
<p>banner. Banner. It&#39;s either Sandy or I take Veron. Stay away from Andrew uh on April 16 we file for brass motion AI with invoice data which is for 120 I however the translated file is incomplete I I attached Herman&#39;s A10 report for that day which show success for all of the ideals they&#39;re going to be breaking in. So let me tell you a little story. You&#39;re not recording anymore, right? Okay, good.</p>
