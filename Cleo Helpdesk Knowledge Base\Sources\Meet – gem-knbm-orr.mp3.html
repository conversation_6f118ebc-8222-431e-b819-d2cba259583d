<p>Sure. Take your time. Uh, they&#39;re not here, right?</p>
<p>No, not yet. This is what</p>
<p>is this phone call at 3 p.m. my time?</p>
<p>Yeah.</p>
<p>Okay.</p>
<p>Did you hear what I said earlier or I was in mute?</p>
<p>Uh, you were probably on mute.</p>
<p>Oh, I was saying that the relationship we have with UNMP is that they actually pick up files from us and and drop files. It&#39;s not like we&#39;re pushing it in a schedule basis. You know what I mean?</p>
<p>So, yeah, they actually pick it up. So, Yeah, we&#39;ll see what local shop is going to say cuz I told <PERSON> there&#39;s no visual, you know, that I can see that something is wrong. Unless they say so, then I don&#39;t.</p>
<p>Weird.</p>
<p>I know, right? That just happened two weeks ago.</p>
<p>Mhm.</p>
<p>That&#39;s weird. And you know what? The first thing we should do is drop one of the files, you know, the ones in the backup file</p>
<p>or or restage and see if it shows up in the from DTS.</p>
<p>Yeah, I like that one better,</p>
<p>right? Let&#39;s do that to see if that Let me just let me pull it up. Um, let me pull up No target. They didn&#39;t have files, right? So, yeah. All right. I had their stuff up and decimal equals Okay, you know what? Let me test something. What? Before he goes in, let me do it. Let me click that open back here. BTS. Let me go. So my screen should share my screen. All right, Darren is here.</p>
<p>Okay,</p>
<p>close your screen.</p>
<p>Yeah, hold on.</p>
<p>Darien.</p>
<p>Hey there, Nick. How you doing?</p>
<p>How are you doing today, bro?</p>
<p>Not too bad, man. Not too bad. This uh other She&#39;s kind of crazy. Um,</p>
<p>yeah, this one is this one seems Oh, by the way, uh, I have Maria and Michael with me, Darren. I don&#39;t know if you met them before. Maria and Michael, we&#39;ll be joining this call just to see if maybe they we could figure this out quicker than than uh than possible. So,</p>
<p>yeah.</p>
<p>Maria, Darien,</p>
<p>hey there y&#39;all.</p>
<p>I&#39;m gonna go ahead and take us to the universal metal server.</p>
<p>Yep.</p>
<p>Um I don&#39;t know unfortunately anything preemptive that was done. Uh what I I mean I just restarted the service. Um but don&#39;t know any other kind of background. She was kind of upset off the bat and I</p>
<p>unfortunately couldn&#39;t get a lot of information out.</p>
<p>All right. It&#39;s all right. We&#39;re going to touch all bases today. Basically Darren, what I want to do Um, I want to make sure that they&#39;re their current setup. So, for example, they&#39;re using the services, right? But do can you open up the scheduler because if they&#39;re using the new setup, the services should have been shut down. Remember, we had that issue with another customer.</p>
<p>Oh, we did,</p>
<p>right? So, we we need to we need to shut down the the services.</p>
<p>What is the service again?</p>
<p>Uh, FDTS file transfer. DTSD I</p>
<p>Yeah. No, I I I saw it in your list.</p>
<p>Uh</p>
<p>no, no, I saw it in the list by f if you uh range if you organize it. There it is at the top there.</p>
<p>Oh, DTS FTS. Oh, it&#39;s disabled already.</p>
<p>Okay, so that&#39;s disabled. That&#39;s good. Um the theuler is up and running, right? Right.</p>
<p>Mhm.</p>
<p>Okay. So, that&#39;s up and running. Let&#39;s Let&#39;s How about we test connection really quick?</p>
<p>Okay.</p>
<p>Um, the credentials should be in the script.</p>
<p>Yeah.</p>
<p>Yep. Yep. In that script. Exactly. Schedule task.</p>
<p>Now, Was any of them</p>
<p>this the second tab?</p>
<p>Yeah.</p>
<p>Yeah.</p>
<p>So that script yep should be somewhere in the try. Um there 30. Yeah. From 32 to 33. 31 to 33. No. No. The host name is the uh the URL</p>
<p>this Yeah.</p>
<p>Yep.</p>
<p>Maria restaging the file probably is going to take a couple minutes, so give it some time.</p>
<p>Yeah, I I&#39;ll wait for until you guys do this and then I did a whole line. It seems that works.</p>
<p>Okay. So there is connection. So you can you can connect, right? That&#39;s perfect.</p>
<p>All right. So what does the script say about the from DTS for uh location? So that should be in line 7 something. Let me go to my notesp. I mean it exists. Go to line 58 for me. Why do I have 58? That&#39;s weird. This is an old script from 2023.</p>
<p>Yeah,</p>
<p>that helps.</p>
<p>All right, so 50 police 48 49 are good. 74 74 is ABS global DTS from DTS. Okay. So</p>
<p>that&#39;s where it&#39;s going.</p>
<p>Yeah.</p>
<p>All right. Cool. So go to that folder. U Maria, did you restage the file?</p>
<p>I can do it now again. But I did before. So</p>
<p>you did. You did already, right?</p>
<p>Let me let me do it again. Okay, I chose stage one.</p>
<p>Okay,</p>
<p>let me and let sure. Oh my god, the lab is not. All right, let me check the folder. Let&#39;s see again. Nico, I reag you guys. Not saying I want to stage another one.</p>
<p>No, no, no, not yet. Not yet. Don&#39;t do that yet.</p>
<p>Don&#39;t do it.</p>
<p>Um, no, not yet.</p>
<p>Okay.</p>
<p>Um, hold up. Give me a second.</p>
<p>I&#39;m checking to see if there&#39;s another UMP. Here it is. All right. Uh Darren, do me a favor. Use uh instead of using the Win SCP client, let me go straight to the file in the file explorer.</p>
<p>Mhm.</p>
<p>Where am I going?</p>
<p>Yeah. From DTS. Open it up. All right. Give me one second. Stay there for me. Okay. All right.</p>
<p>Yeah, I was going to say, Nico, to drop a test file and see if it out because they&#39;re reststaging. I don&#39;t see it moving. So, let&#39;s see.</p>
<p>All right. Refresh the the the folder. Nothing.</p>
<p>They have They haven&#39;t pick it up yet. It&#39;s still</p>
<p>um run schedule. Yeah.</p>
<p>Yeah. I don&#39;t see anything.</p>
<p>Okay. All right.</p>
<p>Why did it change all of a sudden? Um,</p>
<p>yeah.</p>
<p>Go to go to the C drive. Yeah.</p>
<p>Go to DTS.</p>
<p>Not the SCP log me.</p>
<p>No, no. Go to DTS and then uh edit or open up in a notepad that DTS.config. Yeah. All right. So, that from DTS is the same. Is that the same file location in this script? This is the old script. This doesn&#39;t this shouldn&#39;t even be running. It is right.</p>
<p>Uh for the most part, minus the looks like the passwords are different and also</p>
<p>yeah, of course, but mostly importantly the location file path from DTS global Um, let me see. Let me see your script. And don&#39;t close this.</p>
<p>Are you talking about this one?</p>
<p>Yeah, that one. And go to lines. What was it? 48. No, 50. Close it. 74. 74. This the from DTS. Yeah. Okay. So, Yeah. Okay. So, we have from DTS apps. global DTS from DTS. And here we have Glo is basically the D drive apps global DTS from DTS. I mean, give me a second. Let me open up your original 74 from DTS. Yeah. I mean,</p>
<p>everything looks good on his end, Nico, right?</p>
<p>Yeah. Everything is is as it should be on his end.</p>
<p>Well, I think it&#39;s the problem is on our end that we have to check because when I restaged the file, it&#39;s not moving into the folder and the test file is still there waiting to be picked up.</p>
<p>Yeah. Well, at at least he should be able to see the test file, the restage file. I mean, yeah, we could look into that, but at least at the moment, he should be able to see my</p>
<p>Right. to see it. Okay. Because it&#39;s already there for waiting for them.</p>
<p>Exactly. Exactly.</p>
<p>Okay.</p>
<p>Um,</p>
<p>so I guess take care of that, I guess.</p>
<p>Passwords are good, right? And the users username is the same. Yeah, I don&#39;t think anything would have changed. Um,</p>
<p>I mean, we can get in to it on the 1 SCP side.</p>
<p>Exactly. When SCP is working, so the connection is good. I&#39;ll open up this from DTS in here.</p>
<p>Oh, Nico, I know what&#39;s going on.</p>
<p>1969.</p>
<p>Nico,</p>
<p>their path, their path,</p>
<p>is is not a D drive. It&#39;s D client. So the target path is Dclient /2751. Let me check. Let me</p>
<p>All paths go to the D drive. Well, most go to the D drive first then get they get sent to the T drive.</p>
<p>The T. Okay, let me just go there and see if anything is stopping. Um, no, nothing is there. Uh, let me go to and go to the D drive. So maybe it&#39;s worth checking um the D client. Let me see if anything is there. What&#39;s the account to what&#39;s the DTS account? 2751</p>
<p>27 or 2751. Yeah.</p>
<p>Okay, perfect. So there&#39;s nothing on it from DTS stage from this. There&#39;s nothing. H2DTS there&#39;s nothing in 2DS there&#39;s nothing</p>
<p>I&#39;m confused though too because on their D drive it looks like stuff has been moving uh if I go to GPS 519 102 something was in there 519</p>
<p>yeah</p>
<p>exactly this is exactly the this is exactly what we were looking at before you got on the code Dar</p>
<p>we know We noticed that there was files being sent over to you and moved to your to your um</p>
<p>backup</p>
<p>to your backup drive, but for some reason you&#39;re not seeing them. That&#39;s why we&#39;re trying to figure out why you don&#39;t see them.</p>
<p>Yeah. I don&#39;t know if they&#39;re doing it manually. Um yeah,</p>
<p>but you only have three. We should have sent</p>
<p>No, we sent uh Let me go to the background. There&#39;s quite a few for quite a few. We send a lot actually. Yeah, we send a lot of files starting at at 126 a.m.</p>
<p>Oh, there&#39;s an error as well. There they are.</p>
<p>So, you&#39;re ging error file.</p>
<p>Oh, yes. Yeah, they are. Um, I don&#39;t know. Why are they&#39;re converting into error files? They don&#39;t look like that in our end though.</p>
<p>They don&#39;t.</p>
<p>Yeah. I couldn&#39;t tell you. I don&#39;t know anything in correlation to at least that. Um there are they coming in as error files? I mean we&#39;re the file name is normal the same that has not changed and let me see um so we send files every 60 minutes nothing has been updated in our and that I can see Nicole. Um I&#39;m just worried about the stage portion. So you getting the files are there ending in your backup instead. Correct.</p>
<p>That&#39;s what it looks like at least.</p>
<p>Why? Is that</p>
<p>you know what? Let me let me try one more thing. I&#39;m going to drop this test file in the D drive just to see what happens.</p>
<p>Okay.</p>
<p>Are you manually dropping it in or you like doing every stage and checking the D drive?</p>
<p>No, I&#39;m manually dropped.</p>
<p>Okay. There&#39;s stuff in here.</p>
<p>Do they go through stage or that&#39;s a different thing like stage from DPS?</p>
<p>No, that&#39;s different.</p>
<p>Oh, okay. Oh, so there are three file Now there Oh, there there is the file. Uh there&#39;s two files from Yeah, just now. The ones you dropped, they&#39;re there. Let me do a run now and see.</p>
<p>Yeah, you want me to run the schedule task or No, I&#39;m</p>
<p>Yeah, you can run the schedule test.</p>
<p>Yeah, do that. Yeah, because we don&#39;t control the schedule.</p>
<p>Open open up the from DTS.</p>
<p>Nothing.</p>
<p>What the? Yeah, they&#39;re not moving. Let me do a brown stage stage. I think the script looks good. That&#39;s correct.</p>
<p>Yeah, the script looks exactly as it should. Okay. I was wondering because I I even reage one from data admin recently and I don&#39;t see it in the D drive. That file from 222 is that you Nico and 219.</p>
<p>I just dropped the test file. I didn&#39;t drop anything else.</p>
<p>You haven&#39;t dropped regular files?</p>
<p>No.</p>
<p>Oh. So that&#39;s mean. So they&#39;re when I restaged.</p>
<p>Yes.</p>
<p>Okay. They just haven&#39;t moved,</p>
<p>right?</p>
<p>Oh, so after this they go to the T drive, correct?</p>
<p>Yep.</p>
<p>From DTS. All right. Um H Okay, we&#39;re going to do something different. Um, let me let me see your your script. Uh, Darien, I think I think I found something odd. All right. Uh, that script. Go to line. Go to line. Where&#39;s my script at? 34 34. Okay. So, that ends in GZI.</p>
<p>Oh, G7i. Okay. All right. So, do me a favor. Check your win SCP. Go to from DTS.</p>
<p>On which side?</p>
<p>On that side. The right side. All right. Um, something is wrong. Log off. Close the session.</p>
<p>Okay. And then open it up. Open up Win SCP again because I logged into Win SCP and I see the file there. Oh, I see what&#39;s wrong. I see what&#39;s wrong.</p>
<p>What? isn&#39;t</p>
<p>use the use the</p>
<p>the host name isn&#39;t correct if you look at line 30 32</p>
<p>oh maybe that&#39;s why</p>
<p>oh my gosh and then I use the password in in line 34.</p>
<p>There&#39;s my test drive. There&#39;s my test file. So,</p>
<p>but he can see the other two that are there.</p>
<p>No, because they&#39;re not in that in the T drive just yet.</p>
<p>So, our our side hasn&#39;t processed the D the stuff in D drive just yet. our our stuff the the files you restaged</p>
<p>it should be automatic. If I&#39;m looking at the if I&#39;m looking at the ECS manager you um universal metal products out is scheduled immediate send. So it should be within a minute or two. Um so you You should be able to see that test file that you see in Win SCP in your regular directory.</p>
<p>Yeah, in this one.</p>
<p>Oh, my files disappear.</p>
<p>Okay. Check the T drive.</p>
<p>Go to the T drive. Okay. Yeah, they&#39;re in the T drive.</p>
<p>So, we&#39;re looking good over here so far in our end. Uh fronts. Okay, files are in the front DTS waiting to be picked up.</p>
<p>Yeah, exactly.</p>
<p>So, we&#39;re our file cycle is working as expected.</p>
<p>All right. So, do me a favor, Darren, and forgive me for being such a pain. Go back to the go back to the script. Yep. Go back to the script. Grab line. Grab the path that you have in this fold in this script for from the TS which is in path 74 I think it is.</p>
<p>Yeah.</p>
<p>Yeah. Grab that path and then go straight to that path in your in your file directory.</p>
<p>Yeah, I already I did that earlier.</p>
<p>You already did that, right?</p>
<p>Yeah. Okay.</p>
<p>So, that that goes to here.</p>
<p>Mhm. Um then why is the file not here?</p>
<p>You can&#39;t see the file, right? To</p>
<p>neither my test file nor the two files that you restaged.</p>
<p>Oh, I mean they&#39;re still sitting here. So</p>
<p>yeah, exactly. Just be the mere fact that you can see them there, Maria,</p>
<p>and he can&#39;t. That&#39;s a problem. I know.</p>
<p>Um, this is a a pickup issue. Um, okay. One last thing. Uh, open task manager. Let&#39;s see if services is still for some reason running.</p>
<p>Oh, wait. The rights here. Isn&#39;t that isn&#39;t is that an issue? I think that&#39;s an issue. Nico, no. You know where it says rights? So, there&#39;s name.</p>
<p>Isn&#39;t Isn&#39;t that like a That&#39;s how the permission issue used to look like by I know you said there&#39;s no permission issue in this one, but that&#39;s how it used to look like the N. Is that a NW something? It&#39;s so small I can barely read.</p>
<p>Yeah, it&#39;s like a R</p>
<p>R something. Um, I think that might be the problem. Nico, uh, how to fix that. No, no, I&#39;m not not convinced that that&#39;s the problem. We have to like Yeah.</p>
<p>Um, it&#39;s probably the name naming convention is it not?</p>
<p>Um, no. We don&#39;t have naming conventions uh rules in our side for you</p>
<p>for outbound. Yeah, for outbound files.</p>
<p>Outbound. For inbound, yes. But wait, I are the inbounds working. Let me just double check. Let me go into webd and go to the account. So, I know outbound we having a problem, but let me check inbound. Um let me just check the server here and then check. Okay, the push shows up in I think they just pick up files from us. I don&#39;t see a hold on UMP. Oh, yeah. UNMP in let me check. Uh 856. We&#39;re getting files from them because we just processed today in the morning some ASN. So,</p>
<p>okay. So, the inbound is working.</p>
<p>Yep. So, let I just checked um where Oh, not only does something Nico, where is your uh ticket? I can&#39;t add the inbound that way. You know, we cross that one that is chat. Um, where</p>
<p>where is my what?</p>
<p>Your ticket. Your chair. Let me find it. I&#39;ll get it. I think I sent it to you this morning, right? Yeah. Okay. No, that&#39;s not Let me double check like the other eights are coming. Okay, so So they go in is good. Why is this not working on Darren&#39;s cloud?</p>
<p>Um, so if we are able to grab files, you&#39;re not able to pick up. Did you u check the script? Nico, you said that they match what they have.</p>
<p>Yeah, the script is good. Um,</p>
<p>the path is good.</p>
<p>Yeah. Where&#39;s the um what line is the uh yeah there&#39;s something wrong with picking up because we are receiving 9&#39;s 32 33 34 and 40. Okay,</p>
<p>Darren, let me let me see your the script again. Go to line</p>
<p>uh 58. Why does it Why do I have 58 as the backup path? I don&#39;t think 58 is a backup path line. No, that&#39;s</p>
<p>six. 60. Yeah. Okay. We fixed that. Okay. So, that&#39;s okay. Sorry. I&#39;m looking at the brackets, making sure I&#39;m not missing any Nico, I have a question. If if they were having issues getting files like she said for two weeks, she said two weeks, wouldn&#39;t we have worth of two weeks of files sitting in the fronts folder?</p>
<p>We theoretically, yes. I don&#39;t understand why she&#39;s saying there&#39;s two weeks worth of data that she&#39;s not getting when</p>
<p>Yeah. They were picked up, right? Um,</p>
<p>oh man, I&#39;m running out of ideas here. Do you have school DTS? Two DTS and then hit the hit the play button here, Darren. Okay. So, no errors.</p>
<p>But your path is still not showing my file. Why? Did you check the logs?</p>
<p>It&#39;s love.</p>
<p>We could do that.</p>
<p>Go to DTS logs.</p>
<p>DTS or 1 SCP?</p>
<p>Uh, it should be</p>
<p>log files. No,</p>
<p>I mean they&#39;re both kind of there. Just because this one will have logs.</p>
<p>Yeah.</p>
<p>No, I guess not.</p>
<p>No, I think it should be your old setups. Oh, so look, you see the p path right there to your logs right there in line 43.</p>
<p>Can you check that from DTS? really quick before you open up a log.</p>
<p>Uh, what do you mean?</p>
<p>Go up one that check that from DTS.</p>
<p>There they are.</p>
<p>Oh my gosh. What? DTS.</p>
<p>That took forever to get there.</p>
<p>Those are the files.</p>
<p>Yeah.</p>
<p>What&#39;s Can you show me the path file? The path name here. Yeah, they updated two minutes ago.</p>
<p>Okay. So, it&#39;s a timing issue. It takes time for you to get them. I guess</p>
<p>that Yeah.</p>
<p>Can you check your schedule cuz you guys pull files from us or and drop files.</p>
<p>I wonder if it&#39;s a scheduled task. So, because also</p>
<p>you guys have a schedule task set up.</p>
<p>Oh, look at this. I wonder if it&#39;s this Uh,</p>
<p>also the user I don&#39;t know if it&#39;s supposed to be system.</p>
<p>Well, I&#39;ll tell you what, it it it picked up when you hit play and</p>
<p>Yeah, that&#39;s what I&#39;m saying. Yeah.</p>
<p>Uh, schedule and train. Yeah.</p>
<p>I didn&#39;t We didn&#39;t mess with these these settings before, did we, Dar?</p>
<p>No, we didn&#39;t.</p>
<p>Okay.</p>
<p>This is me messing with it just because if it worked from there, then it should work from here.</p>
<p>Mhm.</p>
<p>And it looks like one we actually clicked the play button. That&#39;s when it actually went through.</p>
<p>Well, maybe whatever we did here, it overwrote what&#39;s already in set up in um</p>
<p>Sure. Um let&#39;s uh try one more file.</p>
<p>One more test file. I got you.</p>
<p>Yes. Yeah, I&#39;m dropping um All right, we&#39;re going to drop another test another test file. Okay, so I just dropped another test file. You should be able to pick it up if you run if you run thisuler. That should that should pick it up. I don&#39;t want to I don&#39;t want to depend on hitting the play button, right?</p>
<p>Yeah.</p>
<p>Okay. Okay, let me run the I don&#39;t mean There it is.</p>
<p>Yeah. Coming up with the schedule.</p>
<p>You can you can delete it. Run the run the hit the play. button and it&#39;ll reinstall it.</p>
<p>Oh, that&#39;s fair.</p>
<p>Yeah, let&#39;s just do that.</p>
<p>Okay. So, Now it should it should be in your Well, not yet. It It&#39;s still going there. Now it should be in your Now it should be in your schedule task. There it is. I sort of wonder if this is an issue. I guess send one more thing.</p>
<p>Okay.</p>
<p>How many have</p>
<p>we&#39;ll do?</p>
<p>Okay. Run it. That&#39;s weird. Now you always change it to to Windows Server 2019.</p>
<p>I have in the past. Yeah, I&#39;ll try this. Oh, there&#39;s the second one, right?</p>
<p>Mhm.</p>
<p>There we go. All right. Uh,</p>
<p>try one more time. All right. Good. Nico, can you have them drop a file for us just to make sure? I know those files came this morning. I just want to make sure whatever you guys update is still working for inbound.</p>
<p>Got it.</p>
<p>All right, run it.</p>
<p>All right. Oh, there we go.</p>
<p>Okay. All right. Uh, cut a cut one of these tests and drop it in the 2DS file.</p>
<p>Okay.</p>
<p>And then run it.</p>
<p>Uh, yeah.</p>
<p>So, it sends. Yeah. And it should be gone.</p>
<p>Got it. Yeah, we got it. Omari.</p>
<p>Nice. I just deleted it from the from the folder, but we got it. All right, dude. I mean, Darren, I don&#39;t know what else we can do. Um,</p>
<p>I mean, it looked like it was a schedule task thing more than anything. Maybe it was an account thing, but I don&#39;t know why it happened in the first place.</p>
<p>All right. Um, you have all of the files in your in your history and you if they need to access any files that they don&#39;t have, I woulduggest just asking them to reach into their history or their backup folder first.</p>
<p>If they don&#39;t find what they&#39;re looking for in their backup, then they can reach out to us and then we can restage any file they&#39;re missing.</p>
<p>Yeah. Can you go ahead and let them know in the email that we&#39;re we&#39;re all in together?</p>
<p>Yeah. Yeah, I&#39;ll do that.</p>
<p>Yeah. Nico will email Janet um and let her know</p>
<p>because I don&#39;t think she believes me at this point.</p>
<p>I work with her a lot.</p>
<p>Something Yeah. So I was like, uh, I mean, I could say that she&#39;s like, &#34;Are you sure?&#34; Like, I that&#39;s what they told me.</p>
<p>Sometimes she&#39;s not right. But, you know.</p>
<p>Yeah. No, it&#39;s a whole And I was like, I don&#39;t blame you. I&#39;m just I just don&#39;t know what&#39;s wrong. I&#39;m sorry. I can&#39;t like tell you</p>
<p>Yeah.</p>
<p>exactly what it is. It&#39;s just a two-way street. It just goes</p>
<p>Yeah. So, I was like, &#34;Okay.&#34;</p>
<p>So, you send us a file work perfectly, right?</p>
<p>Yes.</p>
<p>And receive the files now.</p>
<p>Yeah. Awesome. I think we&#39;re we&#39;re good. You guys are awesome.</p>
<p>What we&#39;ve been doing trying to troubleshoot these one one day at a time,</p>
<p>right? We troubleshoot so many at a time every</p>
<p>Yeah, it&#39;s it&#39;s crazy.</p>
<p>I know. I know. Crazy. It was. So,</p>
<p>all right then.</p>
<p>Are we good to go?</p>
<p>We&#39;re good to go.</p>
<p>Awesome. Thank You have a wonderful day.</p>
<p>Thank you, Derek. You have a good one.</p>
<p>Bye.</p>
<p>Bye.</p>
<p>All right, guys. What did you guys learn?</p>
<p>So, that was good. Um, no, I mean that was pretty straight for me, you know, like straightforward. I know he has to do some stuff and he&#39;s end up.</p>
<p>To to say the truth,</p>
<p>setting up the SFTP is not as complicated as he made it made it out to be because he was changing a lot of stuff in the uh</p>
<p>in the scheduler</p>
<p>and the scheduleuler in the in the condition settings. Yeah,</p>
<p>exactly. All of those things we never touch. Every call I&#39;ve had with anybody else, we don&#39;t have to touch that. It just works as expected. Right. Right.</p>
<p>But I guess he needed to do some extra stuff on his end to what to get it working. But we found the issue. It was auler. Deleted it, reinstalled it, got it to work.</p>
<p>Um I do believe I do have more calls tomorrow. If you guys care to join, let me know and I&#39;ll send you the link</p>
<p>tomorrow. Yeah, cuz I I think I want to be in one of those where you are uh migrated in. Yeah, that&#39;ll be great to to have that, you know, visibility.</p>
<p>Yep.</p>
<p>Um All righty. Well, thank you, Michael. I&#39;ll call you back in in Slack. Okay.</p>
<p>Oh, okay. Do that. No, no, no. Yeah, you don&#39;t want to miss that. Okay. Bye.</p>
