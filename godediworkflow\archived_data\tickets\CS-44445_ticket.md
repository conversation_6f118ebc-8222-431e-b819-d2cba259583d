# CS-44445: RE: Missing POs / PHOENIX LEATHER GOODS LLC

## Ticket Information
- **Key**: CS-44445
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-05-28T10:10:10.792-0600
- **Updated**: 2025-05-30T09:38:35.614-0600
- **Customer**: <EMAIL>

## Description
Hi Brad, 

  

We could see both the POs  *{color:#333333} *8042105*{color}*{color:#333333} and  *8043700*{color}  were transmitted successfully. 

  

!image001.png|thumbnail! 

  

  

 *Thanks & regards,* 

 *Shanthi* 

 *EDI Analyst | Macy's, Inc.* 

 *145 Progress Place | Springdale, OH 4524*6 

   

 *From:* Brad <PERSON> <<EMAIL>> 
  *Sent:* Wednesday, May 28, 2025 12:36 AM
  *To:* EDISupport <<EMAIL>>
  *Cc:* <PERSON> <<EMAIL>>
  *Subject:* Missing POs  

   

{color:red}⚠{color}{color:red} EXT MSG:{color}   
  

Hello Macy's support. I'm not sure if this email address has remained CCed to the case we have open with Datatrans/Cleo. The email header is CS-44299. We are missing several Bloomingdale's POs in Datatrans. Datatrans claims to have not received them on their server. They are asking for an MDN. Is that something you can provide? The latest response from Datatrans support is below.    

    

Thank you for any information you can provide.    

    

{color:#333333}Hello Antonio,{color} 

{color:#333333}Our transmission protocol used to connect with Bloomingdales is via AS2. An MDN (Message Disposition Notification) is an encrypted file we send to your system for the AS2 message we received. It appears the screenshots you provided are of the software you use to send those POs but unfortunately they do not show if an MDN was received on your end indicating your transmission was successful. We still do not see POs 8042105 and 8043700 in our system. The last POs we received were 7926220 and 7934833 on 5/21 in one batch transmission (EDI Control Number: 000000326).{color} 

{color:#333333}Nicolas Sanchez
 Senior Support Engineer I
 Cleo Communications{color}
 [<EMAIL>|mailto:<EMAIL>]{color:#333333}{color} 
| 
!~WRD0002.jpg|thumbnail! |  *{color:#053972} *Brad Rusin*{color}* {color:#172B4D}Vice President, Ecommerce{color} [{color:#053972}<EMAIL>{color}|mailto:<EMAIL>] {color:#172B4D}{color}{color:#053972}:{color} {color:#172B4D}{color}{color:#053972}************{color}{color:#172B4D}{color} {color:#172B4D}Phoenix Leather Goods{color} {color:#BFBFBF}:{color} {color:#172B4D}{color}[{color:#053972}www.phoenixleathergoods.com{color}|https://urldefense.com/v3/__http:/www.phoenixleathergoods.com__;!!EkHEP60!Gr5gQ0Ypv5QfLSl-V05DFJuXG6d5NfH9uTdOEUqZtoLGsecWvm2Fbv_sDP3P-95elf_Uxzdp7dD8lIy0sH5mSZ8y1UaIgb0$]{color:#172B4D}{color} |
     

{color:red}* This is an EXTERNAL EMAIL. Stop and think before clicking a link or opening attachments.{color}   

!~WRD0002.jpg|thumbnail!

## Components


## Labels

