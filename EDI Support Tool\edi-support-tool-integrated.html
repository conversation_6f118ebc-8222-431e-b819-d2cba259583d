<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EDI Support Tool - Integrated with GODediworkflow</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
        }

        /* API Status Indicator */
        .api-status {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .api-status.connected {
            background: #48bb78;
            color: white;
        }

        .api-status.disconnected {
            background: #e53e3e;
            color: white;
        }

        .api-status.connecting {
            background: #ed8936;
            color: white;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-container {
            display: flex;
            gap: 20px;
            padding: 20px;
            max-width: 1800px;
            margin: 0 auto;
        }

        .ticket-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            padding: 30px;
            width: 400px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .flow-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            padding: 30px;
            flex: 1;
            max-height: 90vh;
            overflow-y: auto;
        }

        h1 {
            color: #2a5298;
            margin-bottom: 20px;
            font-size: 2em;
        }

        h2 {
            color: #2a5298;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .ticket-input {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 14px;
            resize: vertical;
            font-family: inherit;
        }

        .analyze-btn {
            width: 100%;
            padding: 15px;
            margin-top: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .analyze-btn:hover {
            transform: translateY(-2px);
        }

        .analyze-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .ticket-analysis {
            margin-top: 20px;
            padding: 20px;
            background: #f7fafc;
            border-radius: 10px;
            display: none;
        }

        .search-results {
            margin-top: 20px;
            padding: 20px;
            background: #e6f2ff;
            border-radius: 10px;
            display: none;
        }

        .search-result-item {
            background: white;
            border: 1px solid #cbd5e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .search-result-item:hover {
            border-color: #5a67d8;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .ticket-id {
            font-weight: bold;
            color: #5a67d8;
            margin-bottom: 5px;
        }

        .ticket-summary {
            font-size: 14px;
            color: #4a5568;
            line-height: 1.4;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #5a67d8;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .detected-docs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .doc-tag {
            background: #5a67d8;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
        }

        .doc-tag:hover {
            background: #4c51bf;
        }

        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .error-message {
            background: #fed7d7;
            border: 1px solid #fc8181;
            color: #742a2a;
            padding: 12px;
            border-radius: 8px;
            margin-top: 10px;
            display: none;
        }

        .success-message {
            background: #c6f6d5;
            border: 1px solid #68d391;
            color: #22543d;
            padding: 12px;
            border-radius: 8px;
            margin-top: 10px;
            display: none;
        }

        /* File Upload Styles */
        .file-upload-section {
            margin-bottom: 20px;
        }

        .file-upload-label {
            display: block;
            border: 2px dashed #5a67d8;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f7fafc;
        }

        .file-upload-label:hover {
            background: #e9ecef;
            border-color: #4c51bf;
        }

        .upload-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .file-upload-label small {
            display: block;
            margin-top: 5px;
            color: #666;
        }

        .uploaded-files {
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
        }

        .file-item {
            background: #e6f2ff;
            border: 1px solid #3182ce;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* Document categories styles */
        .doc-categories {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .doc-category {
            background: #f7fafc;
            border-radius: 12px;
            padding: 20px;
        }

        .category-title {
            font-weight: bold;
            color: #2a5298;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .document-grid {
            display: grid;
            gap: 10px;
        }

        .document-node {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .document-node:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border-color: #5a67d8;
        }

        .document-node.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }

        .doc-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .doc-number {
            font-size: 1.4em;
            font-weight: bold;
        }

        .doc-direction {
            font-size: 0.8em;
            padding: 2px 8px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
        }

        .doc-name {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .flow-details {
            background: #f7fafc;
            border-radius: 12px;
            padding: 30px;
            margin-top: 20px;
            display: none;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <!-- API Status Indicator -->
    <div class="api-status disconnected" id="apiStatus">
        <span class="status-dot"></span>
        <span id="statusText">Offline Mode</span>
    </div>

    <div class="main-container">
        <div class="ticket-panel">
            <h2>📋 Ticket Analyzer</h2>
            
            <!-- File Upload Section -->
            <div class="file-upload-section">
                <label for="fileInput" class="file-upload-label">
                    <div class="upload-icon">📎</div>
                    <span>Drop files here or click to upload</span>
                    <small>Supports: Text, PDF, Images, Videos, Excel, CSV, EDI files</small>
                </label>
                <input type="file" id="fileInput" multiple accept="*" onchange="handleFileUpload(event)" style="display: none;">
                
                <div id="uploadedFiles" class="uploaded-files"></div>
            </div>
            
            <textarea class="ticket-input" id="ticketInput" placeholder="Paste your support ticket here or upload files above...

Example:
Customer: Walmart
Issue: Missing 850 Purchase Orders
Error: No documents received since yesterday"></textarea>
            
            <button class="analyze-btn" id="analyzeBtn" onclick="analyzeTicket()">Analyze Ticket</button>
            
            <div class="loading" id="loadingIndicator">
                <div class="spinner"></div>
                <p>Analyzing ticket...</p>
            </div>
            
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
            
            <div class="ticket-analysis" id="ticketAnalysis">
                <h3>Analysis Results</h3>
                <div id="analysisContent"></div>
            </div>
            
            <div class="search-results" id="searchResults">
                <h3>Related Tickets Found</h3>
                <div id="searchResultsContent"></div>
            </div>
        </div>
        
        <div class="flow-panel">
            <h1>EDI Document Flow Map</h1>
            
            <input type="text" class="search-box" id="searchBox" placeholder="Search EDI documents (e.g., 850, Invoice, Purchase Order)..." onkeyup="searchDocuments(event)">
            
            <div class="doc-categories" id="docCategories">
                <!-- Document categories will be populated here -->
            </div>
            
            <div class="flow-details" id="flowDetails">
                <!-- Flow details will be displayed here -->
            </div>
        </div>
    </div>

    <script>
        // API Configuration
        const API_BASE_URL = 'http://127.0.0.1:8000/api';
        let apiConnected = false;
        let uploadedFiles = [];

        // Document data (embedded for offline mode)
        const documentData = {
            '850': {
                name: 'Purchase Order',
                type: 'inbound',
                category: 'orders',
                flow: ['Buyer creates PO', 'Transmits via EDI', 'Seller receives', 'Seller sends 855 acknowledgment'],
                dependencies: ['Trading partner setup', 'Valid product catalog'],
                generates: ['855 PO Acknowledgment', '856 ASN', '810 Invoice'],
                troubleshooting: [
                    'Check trading partner mailbox',
                    'Verify connection status',
                    'Validate EDI format',
                    'Check for 997 acknowledgments'
                ],
                commonIssues: ['Missing segments', 'Invalid product codes', 'Date format errors']
            },
            '810': {
                name: 'Invoice',
                type: 'outbound',
                category: 'financial',
                flow: ['Shipment completed', 'Generate invoice', 'Transmit to buyer', 'Buyer processes payment'],
                dependencies: ['850 Purchase Order', '856 ASN (if required)'],
                generates: ['820 Payment Order/Remittance'],
                troubleshooting: [
                    'Verify PO reference exists',
                    'Check calculation accuracy',
                    'Validate tax information',
                    'Ensure ASN was sent if required'
                ],
                commonIssues: ['Math errors', 'Missing PO reference', 'Tax calculation issues']
            },
            '856': {
                name: 'Advance Ship Notice (ASN)',
                type: 'outbound',
                category: 'shipping',
                flow: ['Order picked/packed', 'Generate ASN', 'Transmit before shipment', 'Physical shipment follows'],
                dependencies: ['850 Purchase Order', 'Shipping system integration'],
                generates: ['810 Invoice'],
                troubleshooting: [
                    'Check shipment timing',
                    'Verify carton details',
                    'Validate tracking numbers',
                    'Ensure hierarchical structure'
                ],
                commonIssues: ['Late transmission', 'Missing carton details', 'Invalid tracking numbers']
            },
            '855': {
                name: 'Purchase Order Acknowledgment',
                type: 'outbound',
                category: 'orders',
                flow: ['Receive 850 PO', 'Validate order', 'Send acknowledgment', 'Process order'],
                dependencies: ['850 Purchase Order'],
                generates: ['856 ASN', '810 Invoice'],
                troubleshooting: [
                    'Check PO was received',
                    'Verify product availability',
                    'Validate pricing',
                    'Review acknowledgment codes'
                ],
                commonIssues: ['Missing original PO', 'Product unavailable', 'Price mismatches']
            },
            '997': {
                name: 'Functional Acknowledgment',
                type: 'both',
                category: 'acknowledgments',
                flow: ['EDI document received', 'System validates', 'Generate 997', 'Send to originator'],
                dependencies: ['Any EDI document'],
                generates: [],
                troubleshooting: [
                    'Review acknowledgment codes',
                    'Check for syntax errors',
                    'Validate segment requirements',
                    'Verify compliance levels'
                ],
                commonIssues: ['Syntax errors reported', 'Missing mandatory segments', 'Invalid values']
            }
        };

        // Initialize the application
        async function init() {
            await checkAPIConnection();
            renderDocumentCategories();
            setupEventListeners();
        }

        // Check API connection status
        async function checkAPIConnection() {
            const statusEl = document.getElementById('apiStatus');
            const statusTextEl = document.getElementById('statusText');
            
            statusEl.className = 'api-status connecting';
            statusTextEl.textContent = 'Connecting...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/status`);
                if (response.ok) {
                    const data = await response.json();
                    apiConnected = true;
                    statusEl.className = 'api-status connected';
                    statusTextEl.textContent = 'Connected to GODediworkflow';
                    console.log('API connected:', data);
                }
            } catch (error) {
                apiConnected = false;
                statusEl.className = 'api-status disconnected';
                statusTextEl.textContent = 'Offline Mode';
                console.log('API not available, running in offline mode');
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            // Drag and drop
            const dropZone = document.querySelector('.file-upload-label');
            
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#4c51bf';
                dropZone.style.background = '#e9ecef';
            });
            
            dropZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#5a67d8';
                dropZone.style.background = '#f7fafc';
            });
            
            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#5a67d8';
                dropZone.style.background = '#f7fafc';
                handleFiles(e.dataTransfer.files);
            });
        }

        // Handle file upload
        function handleFileUpload(event) {
            handleFiles(event.target.files);
        }

        function handleFiles(files) {
            for (let file of files) {
                const fileInfo = {
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    content: null
                };
                
                // Read file content
                const reader = new FileReader();
                reader.onload = (e) => {
                    fileInfo.content = e.target.result;
                    uploadedFiles.push(fileInfo);
                    displayUploadedFiles();
                };
                
                if (file.type.startsWith('text/') || file.name.endsWith('.edi') || file.name.endsWith('.x12')) {
                    reader.readAsText(file);
                } else {
                    reader.readAsDataURL(file);
                }
            }
        }

        function displayUploadedFiles() {
            const container = document.getElementById('uploadedFiles');
            container.innerHTML = '';
            
            uploadedFiles.forEach((file, index) => {
                const fileEl = document.createElement('div');
                fileEl.className = 'file-item';
                fileEl.innerHTML = `
                    <div>
                        <div style="font-weight: bold;">${file.name}</div>
                        <div style="font-size: 0.8em; color: #666;">${formatFileSize(file.size)}</div>
                    </div>
                    <button onclick="removeFile(${index})" style="background: #e53e3e; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer;">Remove</button>
                `;
                container.appendChild(fileEl);
            });
        }

        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            displayUploadedFiles();
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }

        // Analyze ticket function
        async function analyzeTicket() {
            const ticketInput = document.getElementById('ticketInput').value;
            const analyzeBtn = document.getElementById('analyzeBtn');
            const loadingEl = document.getElementById('loadingIndicator');
            const errorEl = document.getElementById('errorMessage');
            const successEl = document.getElementById('successMessage');
            const analysisEl = document.getElementById('ticketAnalysis');
            const searchResultsEl = document.getElementById('searchResults');
            
            if (!ticketInput.trim() && uploadedFiles.length === 0) {
                showError('Please enter ticket information or upload files');
                return;
            }
            
            // Reset UI
            errorEl.style.display = 'none';
            successEl.style.display = 'none';
            analysisEl.style.display = 'none';
            searchResultsEl.style.display = 'none';
            
            // Show loading
            analyzeBtn.disabled = true;
            loadingEl.style.display = 'block';
            
            try {
                if (apiConnected) {
                    // Use API for analysis
                    const results = await analyzeWithAPI(ticketInput);
                    displayAPIResults(results);
                } else {
                    // Use offline analysis
                    const results = analyzeOffline(ticketInput);
                    displayOfflineResults(results);
                }
                
                showSuccess('Analysis completed successfully');
                
            } catch (error) {
                console.error('Analysis error:', error);
                showError('Failed to analyze ticket: ' + error.message);
            } finally {
                analyzeBtn.disabled = false;
                loadingEl.style.display = 'none';
            }
        }

        // Analyze with API
        async function analyzeWithAPI(ticketContent) {
            // First, search for related tickets
            const searchResponse = await fetch(`${API_BASE_URL}/search`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    query: ticketContent,
                    limit: 5,
                    use_cache: true
                })
            });
            
            if (!searchResponse.ok) {
                throw new Error('Search API failed');
            }
            
            const searchData = await searchResponse.json();
            
            // Then analyze the ticket
            const analyzeResponse = await fetch(`${API_BASE_URL}/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ticket_id: 'NEW',
                    ticket_content: ticketContent,
                    extract_patterns: true,
                    find_root_cause: true
                })
            });
            
            if (!analyzeResponse.ok) {
                throw new Error('Analysis API failed');
            }
            
            const analyzeData = await analyzeResponse.json();
            
            return {
                search: searchData,
                analysis: analyzeData
            };
        }

        // Display API results
        function displayAPIResults(results) {
            // Display search results
            if (results.search && results.search.results && results.search.results.length > 0) {
                const searchResultsEl = document.getElementById('searchResults');
                const contentEl = document.getElementById('searchResultsContent');
                
                contentEl.innerHTML = '';
                results.search.results.forEach(ticket => {
                    const resultEl = document.createElement('div');
                    resultEl.className = 'search-result-item';
                    resultEl.innerHTML = `
                        <div class="ticket-id">${ticket.id || 'Unknown'}</div>
                        <div class="ticket-summary">${ticket.content || ticket.description || 'No description'}</div>
                    `;
                    contentEl.appendChild(resultEl);
                });
                
                searchResultsEl.style.display = 'block';
            }
            
            // Display analysis
            if (results.analysis) {
                const analysisEl = document.getElementById('ticketAnalysis');
                const contentEl = document.getElementById('analysisContent');
                
                let html = '';
                
                if (results.analysis.patterns && results.analysis.patterns.length > 0) {
                    html += '<h4>Detected Patterns:</h4><ul>';
                    results.analysis.patterns.forEach(pattern => {
                        html += `<li>${pattern}</li>`;
                    });
                    html += '</ul>';
                }
                
                if (results.analysis.root_causes && results.analysis.root_causes.length > 0) {
                    html += '<h4>Possible Root Causes:</h4><ul>';
                    results.analysis.root_causes.forEach(cause => {
                        html += `<li>${cause}</li>`;
                    });
                    html += '</ul>';
                }
                
                if (results.analysis.recommendations && results.analysis.recommendations.length > 0) {
                    html += '<h4>Recommendations:</h4><ul>';
                    results.analysis.recommendations.forEach(rec => {
                        html += `<li>${rec}</li>`;
                    });
                    html += '</ul>';
                }
                
                contentEl.innerHTML = html;
                analysisEl.style.display = 'block';
            }
        }

        // Offline analysis
        function analyzeOffline(ticketContent) {
            const content = ticketContent.toLowerCase();
            const detectedDocs = [];
            const issues = [];
            const recommendations = [];
            
            // Detect EDI documents mentioned
            Object.keys(documentData).forEach(docType => {
                if (content.includes(docType.toLowerCase()) || 
                    content.includes(documentData[docType].name.toLowerCase())) {
                    detectedDocs.push(docType);
                }
            });
            
            // Detect common issues
            if (content.includes('missing')) {
                issues.push('Missing documents detected');
                recommendations.push('Check trading partner connection status');
                recommendations.push('Verify document was sent from source');
            }
            
            if (content.includes('reject') || content.includes('997')) {
                issues.push('Document rejection detected');
                recommendations.push('Review 997 functional acknowledgment for errors');
                recommendations.push('Check document format compliance');
            }
            
            if (content.includes('error')) {
                issues.push('Error condition detected');
                recommendations.push('Check system logs for detailed error messages');
                recommendations.push('Verify data mapping configuration');
            }
            
            return {
                detectedDocuments: detectedDocs,
                issues: issues,
                recommendations: recommendations
            };
        }

        // Display offline results
        function displayOfflineResults(results) {
            const analysisEl = document.getElementById('ticketAnalysis');
            const contentEl = document.getElementById('analysisContent');
            
            let html = '<h4>Detected EDI Documents:</h4>';
            if (results.detectedDocuments.length > 0) {
                html += '<div class="detected-docs">';
                results.detectedDocuments.forEach(doc => {
                    html += `<span class="doc-tag" onclick="showDocumentFlow('${doc}')">${doc} - ${documentData[doc].name}</span>`;
                });
                html += '</div>';
            } else {
                html += '<p>No specific EDI documents detected</p>';
            }
            
            if (results.issues.length > 0) {
                html += '<h4>Identified Issues:</h4><ul>';
                results.issues.forEach(issue => {
                    html += `<li>${issue}</li>`;
                });
                html += '</ul>';
            }
            
            if (results.recommendations.length > 0) {
                html += '<h4>Recommendations:</h4><ul>';
                results.recommendations.forEach(rec => {
                    html += `<li>${rec}</li>`;
                });
                html += '</ul>';
            }
            
            contentEl.innerHTML = html;
            analysisEl.style.display = 'block';
        }

        // Show error message
        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            setTimeout(() => {
                errorEl.style.display = 'none';
            }, 5000);
        }

        // Show success message
        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            setTimeout(() => {
                successEl.style.display = 'none';
            }, 3000);
        }

        // Render document categories
        function renderDocumentCategories() {
            const categoriesEl = document.getElementById('docCategories');
            const categories = {
                orders: { title: '📦 Orders', docs: [] },
                shipping: { title: '🚚 Shipping', docs: [] },
                financial: { title: '💰 Financial', docs: [] },
                acknowledgments: { title: '✅ Acknowledgments', docs: [] }
            };
            
            // Categorize documents
            Object.keys(documentData).forEach(docType => {
                const doc = documentData[docType];
                if (categories[doc.category]) {
                    categories[doc.category].docs.push({ type: docType, ...doc });
                }
            });
            
            // Render categories
            categoriesEl.innerHTML = '';
            Object.keys(categories).forEach(catKey => {
                const category = categories[catKey];
                if (category.docs.length === 0) return;
                
                const catEl = document.createElement('div');
                catEl.className = 'doc-category';
                
                let html = `<div class="category-title">${category.title}</div>`;
                html += '<div class="document-grid">';
                
                category.docs.forEach(doc => {
                    html += `
                        <div class="document-node" onclick="showDocumentFlow('${doc.type}')" id="doc-${doc.type}">
                            <div class="doc-header">
                                <div class="doc-number">${doc.type}</div>
                                <div class="doc-direction">${doc.type === 'both' ? '↔️' : doc.type === 'inbound' ? '📥' : '📤'}</div>
                            </div>
                            <div class="doc-name">${doc.name}</div>
                        </div>
                    `;
                });
                
                html += '</div>';
                catEl.innerHTML = html;
                categoriesEl.appendChild(catEl);
            });
        }

        // Show document flow details
        function showDocumentFlow(docType) {
            const doc = documentData[docType];
            if (!doc) return;
            
            // Update active state
            document.querySelectorAll('.document-node').forEach(el => {
                el.classList.remove('active');
            });
            document.getElementById(`doc-${docType}`).classList.add('active');
            
            // Display flow details
            const flowEl = document.getElementById('flowDetails');
            
            let html = `
                <h2>${docType} - ${doc.name}</h2>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-top: 20px;">
                    <div style="background: white; padding: 20px; border-radius: 10px; border: 1px solid #e2e8f0;">
                        <h4 style="color: #2a5298; margin-bottom: 15px;">📊 Document Flow</h4>
                        <ol>
                            ${doc.flow.map(step => `<li>${step}</li>`).join('')}
                        </ol>
                    </div>
                    
                    <div style="background: white; padding: 20px; border-radius: 10px; border: 1px solid #e2e8f0;">
                        <h4 style="color: #2a5298; margin-bottom: 15px;">🔗 Dependencies</h4>
                        <ul>
                            ${doc.dependencies.map(dep => `<li>${dep}</li>`).join('')}
                        </ul>
                    </div>
                    
                    <div style="background: white; padding: 20px; border-radius: 10px; border: 1px solid #e2e8f0;">
                        <h4 style="color: #2a5298; margin-bottom: 15px;">🔧 Troubleshooting Steps</h4>
                        <ul>
                            ${doc.troubleshooting.map(step => `<li>${step}</li>`).join('')}
                        </ul>
                    </div>
                    
                    <div style="background: white; padding: 20px; border-radius: 10px; border: 1px solid #e2e8f0;">
                        <h4 style="color: #2a5298; margin-bottom: 15px;">⚠️ Common Issues</h4>
                        <ul>
                            ${doc.commonIssues.map(issue => `<li>${issue}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
            
            flowEl.innerHTML = html;
            flowEl.style.display = 'block';
        }

        // Search documents
        function searchDocuments(event) {
            const query = event.target.value.toLowerCase();
            
            document.querySelectorAll('.document-node').forEach(node => {
                const docType = node.id.replace('doc-', '');
                const doc = documentData[docType];
                
                const matches = docType.toLowerCase().includes(query) ||
                               doc.name.toLowerCase().includes(query) ||
                               doc.flow.some(f => f.toLowerCase().includes(query)) ||
                               doc.commonIssues.some(i => i.toLowerCase().includes(query));
                
                node.style.display = matches ? 'block' : 'none';
            });
        }

        // Initialize on page load
        window.addEventListener('DOMContentLoaded', init);
        
        // Periodic API connection check
        setInterval(checkAPIConnection, 30000);
    </script>
</body>
</html>