<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EDI Support Tool - Debug Version</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .connected {
            background-color: #4CAF50;
            color: white;
        }
        .disconnected {
            background-color: #f44336;
            color: white;
        }
        .debug-log {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        textarea {
            width: 100%;
            min-height: 150px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
        }
        button {
            background-color: #2196F3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #1976D2;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #e3f2fd;
            border-radius: 4px;
            display: none;
        }
        .error {
            background-color: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            display: none;
        }
    </style>
</head>
<body>
    <h1>EDI Support Tool - Debug Version</h1>
    
    <div class="container">
        <h2>API Connection Status</h2>
        <div id="status" class="status disconnected">Checking connection...</div>
        <button onclick="checkConnection()">Check Connection</button>
        <div id="connectionDebug" class="debug-log"></div>
    </div>
    
    <div class="container">
        <h2>Test Ticket Analysis</h2>
        <textarea id="ticketInput" placeholder="Enter ticket content here...">Customer: Walmart
Issue: Missing 850 Purchase Orders
Error: No documents received since yesterday</textarea>
        <br>
        <button id="analyzeBtn" onclick="analyzeTicket()">Analyze Ticket</button>
        <div id="error" class="error"></div>
        <div id="results" class="results"></div>
        <div id="debugLog" class="debug-log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:8000/api';
        let debugLog = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] ${message}`);
            document.getElementById('debugLog').innerHTML = debugLog.join('<br>');
            console.log(message);
        }
        
        function logConnection(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEl = document.getElementById('connectionDebug');
            logEl.innerHTML += `[${timestamp}] ${message}<br>`;
            console.log(message);
        }
        
        async function checkConnection() {
            logConnection('Starting connection check...');
            const statusEl = document.getElementById('status');
            statusEl.textContent = 'Checking connection...';
            statusEl.className = 'status disconnected';
            
            try {
                logConnection(`Fetching ${API_BASE_URL}/status`);
                const response = await fetch(`${API_BASE_URL}/status`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });
                
                logConnection(`Response status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    logConnection(`Response data: ${JSON.stringify(data)}`);
                    statusEl.textContent = 'Connected to GODediworkflow API';
                    statusEl.className = 'status connected';
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                logConnection(`Error: ${error.message}`);
                statusEl.textContent = 'Disconnected - API not available';
                statusEl.className = 'status disconnected';
                return false;
            }
        }
        
        async function analyzeTicket() {
            log('Starting ticket analysis...');
            
            const ticketInput = document.getElementById('ticketInput').value;
            const analyzeBtn = document.getElementById('analyzeBtn');
            const errorEl = document.getElementById('error');
            const resultsEl = document.getElementById('results');
            
            // Reset UI
            errorEl.style.display = 'none';
            resultsEl.style.display = 'none';
            errorEl.textContent = '';
            resultsEl.innerHTML = '';
            
            if (!ticketInput.trim()) {
                errorEl.textContent = 'Please enter ticket content';
                errorEl.style.display = 'block';
                return;
            }
            
            analyzeBtn.disabled = true;
            analyzeBtn.textContent = 'Analyzing...';
            
            try {
                // First try search
                log('Sending search request...');
                const searchPayload = {
                    query: ticketInput,
                    limit: 5,
                    use_cache: true
                };
                
                log(`Search payload: ${JSON.stringify(searchPayload)}`);
                
                const searchResponse = await fetch(`${API_BASE_URL}/search`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify(searchPayload)
                });
                
                log(`Search response status: ${searchResponse.status}`);
                
                let searchData = null;
                if (searchResponse.ok) {
                    searchData = await searchResponse.json();
                    log(`Search results: ${searchData.count} found`);
                } else {
                    log(`Search failed: ${searchResponse.statusText}`);
                }
                
                // Then analyze
                log('Sending analyze request...');
                const analyzePayload = {
                    ticket_id: 'NEW',
                    ticket_content: ticketInput,
                    extract_patterns: true,
                    find_root_cause: true
                };
                
                log(`Analyze payload: ${JSON.stringify(analyzePayload)}`);
                
                const analyzeResponse = await fetch(`${API_BASE_URL}/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify(analyzePayload)
                });
                
                log(`Analyze response status: ${analyzeResponse.status}`);
                
                if (!analyzeResponse.ok) {
                    throw new Error(`Analysis failed: ${analyzeResponse.statusText}`);
                }
                
                const analyzeData = await analyzeResponse.json();
                log(`Analysis complete: ${JSON.stringify(analyzeData)}`);
                
                // Display results
                let html = '<h3>Analysis Results</h3>';
                
                if (searchData && searchData.results && searchData.results.length > 0) {
                    html += '<h4>Related Tickets:</h4><ul>';
                    searchData.results.forEach(ticket => {
                        html += `<li>${ticket.id || 'Unknown'}: ${ticket.content || ticket.description || 'No description'}</li>`;
                    });
                    html += '</ul>';
                }
                
                if (analyzeData.patterns && analyzeData.patterns.length > 0) {
                    html += '<h4>Patterns:</h4><ul>';
                    analyzeData.patterns.forEach(pattern => {
                        html += `<li>${pattern}</li>`;
                    });
                    html += '</ul>';
                }
                
                if (analyzeData.root_causes && analyzeData.root_causes.length > 0) {
                    html += '<h4>Root Causes:</h4><ul>';
                    analyzeData.root_causes.forEach(cause => {
                        html += `<li>${cause}</li>`;
                    });
                    html += '</ul>';
                }
                
                if (analyzeData.recommendations && analyzeData.recommendations.length > 0) {
                    html += '<h4>Recommendations:</h4><ul>';
                    analyzeData.recommendations.forEach(rec => {
                        html += `<li>${rec}</li>`;
                    });
                    html += '</ul>';
                }
                
                resultsEl.innerHTML = html;
                resultsEl.style.display = 'block';
                
            } catch (error) {
                log(`Error: ${error.message}`);
                errorEl.textContent = `Error: ${error.message}`;
                errorEl.style.display = 'block';
            } finally {
                analyzeBtn.disabled = false;
                analyzeBtn.textContent = 'Analyze Ticket';
            }
        }
        
        // Check connection on load
        window.addEventListener('DOMContentLoaded', () => {
            checkConnection();
        });
    </script>
</body>
</html>