# CS-45184: KeHE 855 Purchase Order 3104965

## Ticket Information
- **Key**: CS-45184
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON>
- **Created**: 2025-06-10T08:07:05.940-0600
- **Updated**: 2025-06-10T09:43:32.843-0600
- **Customer**: Adams Extracts

## Description
Customer: Adams Extracts 

ID: 6061 

Trading Partner: KeHE Distributors 

Document: 855 PO Ack 

Number: 3104965 

  

I received the email below from Genius Central regarding PO Acknowledgement 3104965 that was sent to KeHE on 6/9/25. Perhaps the error message can explain why the PO Ack was not acknowledged. Can you please investigate? 

  

  

!image001.png|thumbnail! 

  

  
  

 *From:* <EMAIL> <<EMAIL>> 
  *Sent:* Monday, June 9, 2025 5:31 PM
  *To:* <PERSON>, <PERSON> <C<PERSON><EMAIL>>
  *Subject:* Document error - 855 Purchase Order - 3104965   

   

 {color:#172B4D}EDI Document Error Notification: Action Required{color} 
| 
| !http://geniuscentral.com/files/email/2017west/gc-logo_b.png! <[http://url6300.geniuscentral.com/ls/click?upn=u001.7yP8kVZ7a3dCLkCt3iS4edgJ7-2BUQ-2BXqMf3KSCvtptjHycN8jDrqNeC7KxRcwc5BNRMqd_6u6vDCkK85wo1bmjII8yQu3W-2FBReWt9HnB5WjuZXDORV2mwARc6zuwKEwR0CHWEmepyen9t36oIzaX-2FpRhPgh7KOHTQksIKUinHQqG6KfXRaEDgti3Zgzk-2BYJKDrIZve4qezJsW2GMaGQmW36hi59zWmPIfenA2sJW2TeGNbs2Ugc7b0cOLa0W6l8ghJBR-2FazGkwiN1AnkGz9FoT2XuJiw-3D-3D]> | 
| {color:#555555} \\  *EDI Document Error Notification: Action Required*{color} | 
| 
 *{color:#555555} *Date:*{color}* {color:#555555}2025-06-09T21:08:54+00:00{color} 
 *{color:#555555} *Document Type:*{color}* {color:#555555}855{color} 
 *{color:#555555} *Sender:*{color}* {color:#555555}ADAMS EXTRACT - DTS6061{color} 
 *{color:#555555} *Receiver:*{color}* {color:#555555}KeHE Buyer - 0569813430000{color} {color:#555555}{color} 
 *{color:#555555} *Group Control Number:*{color}* {color:#555555}253{color} 
 *{color:#555555} *Transaction Control Number:*{color}* {color:#555555}253{color} {color:#555555}{color} 
 *{color:#555555} *PO #:*{color}* {color:#555555}3104965{color} 
 *{color:#555555} *GC Order ID:*{color}* {color:#555555}0{color} 
 *{color:#555555} *Transaction Key:*{color}* {color:#555555}130f8e3f-b20e-4e2e-a945-1c7713dd5635{color} {color:#555555}{color} 
 *{color:#555555} *Error:*{color}* {color:#555555}Message was not sent successfully or completely. Call to remote http server failed (Remote server answered with http return code 400 (Bad Request).)..{color} | 
| 
| 
{color:white}Please correct the error listed above.{color} 
{color:white}If you need additional help, please contact the GC Support Team.{color} {color:white}{color}[{color:white}{color}{color:white}geniuscentral.com{color}{color:white}{color}|http://url6300.geniuscentral.com/ls/click?upn=u001.7yP8kVZ7a3dCLkCt3iS4edgJ7-2BUQ-2BXqMf3KSCvtptjH6YoUnlnAexZqvX6BI-2FAknI4aJ_6u6vDCkK85wo1bmjII8yQu3W-2FBReWt9HnB5WjuZXDORV2mwARc6zuwKEwR0CHWEmXsjQPBxeKTfmI4r7oSSCsV-2FOufdoFCNEXkdbahc8S5My9TfxRZQPfZM1lHyTuLR5kQxOU2tsdUbzQkYKLisZu5iEF0fBWoPei3OxK-2FlLjlgdueAne4nzZ1QJIgTMRWyDRxuObouXmgMrvVa40uH6cg-3D-3D]{color:white} : {color}{color:white}************{color}{color:white} : {color}[{color:white}{color}{color:white}<EMAIL>{color}{color:white}{color}|mailto:<EMAIL>]{color:white}{color} | 
| | 
   

!http://url6300.geniuscentral.com/wf/open?upn=u001.o53znndtEotsMzGQnRM-2FBi1JYSm83CAFahXLxZL-2FbRGWwozP2NlyOshQDPIDv8XBsuyd6mmKbUQBnmZyzIPZUy1M73tbtQsRaWybDfVtd0MlGIsNXF-2FM7A5SKAhlYI5hy4pFCLvxyMh9ctWXWKzvOdryHpuIVy6SpkIFK43SfK-2BBtwszNfpg4vfhSZXYGDYWKJnIDemnak1Dn-2FDh-2FYbq54ynKbhi-2F9ACpklSnt1K5Js-3D!

## Components


## Labels

