# CS-44973: Re: CS-44831 Quickbooks Integrator view in the Portal missing Setup tab

## Ticket Information
- **Key**: CS-44973
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON>
- **Created**: 2025-06-05T05:30:08.411-0600
- **Updated**: 2025-06-05T11:19:37.126-0600
- **Customer**: Group LLC

## Description
GM, 
I tried moving data into QB using the the guide provided, but I don’t have enough information to do it yet. Once we have more, I’ll try again. 
  
Thanks 
<PERSON> 
----
 
 {color:#172B4D}*From:* <PERSON> <<EMAIL>>
  *Sent:* Wednesday, June 4, 2025 5:11:23 PM
  *To:* RILY Group LLC <<EMAIL>>
  *Subject:* CS-44831 Quickbooks Integrator view in the Portal missing Setup tab{color}     
 
{color:#333333}{color}{color:#999999}—-—-—-—{color} {color:#333333} 
{color}{color:#999999}Reply above this line.{color} {color:#333333} 
  

<PERSON> commented: 

were you guys able to follow the guide and set the QB online ? please let me know thank you 

 _<PERSON> <PERSON>shwala_ 

 _Senior Support Engineer I_
  _Cleo Communications_{color}
 [{color:#333333} _webedi-support@cleo.com_{color}|mailto:<EMAIL>]{color:#333333} 

 _281-292-8686_{color}   

[{color:#333333}{color}{color:#3572b0}View request{color}|https://help.desk.datatrans-inc.com/servicedesk/customer/portal/1/CS-44831?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0Z3QiOiJhbm9ueW1vdXMtbGluayIsInFzaCI6IjZiOTg1NTlkZTUwYmE4YmVmMmY2ODA1OGNhMWE1Mzg1OGVlNGE1NmM0MjkxN2Q4M2QyMGIwNTdmOTEyYWU3NmIiLCJpc3MiOiJzZXJ2aWNlZGVzay1qd3QtdG9rZW4taXNzdWVyIiwiY29udGV4dCI6eyJ1c2VyIjoiMTMzNzUiLCJpc3N1ZSI6IkNTLTQ0ODMxIn0sImV4cCI6MTc1MTQ5MDY4MiwiaWF0IjoxNzQ5MDcxNDgyfQ.heDYUEF8ZGS9hd8dcVFnC-BGUjLufaLepbnMZcg6-3U&sda_source=notification-email]{color:#3572b0}{color}{color:#333333} ·{color} [{color:#333333} {color}{color:#3572b0}Turn off this request's notifications{color}|https://help.desk.datatrans-inc.com/servicedesk/customer/portal/1/CS-44831/unsubscribe?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0Z3QiOiJhbm9ueW1vdXMtbGluayIsInFzaCI6ImQ3NGE2NDVmNDU3MzU0YTczNWJkMDgwYTFkMWZkMTIwMTI4YjA1MzFkZTgyNGNmNWYyMjM4M2Q4OTgzODdkOGQiLCJpc3MiOiJzZXJ2aWNlZGVzay1qd3QtdG9rZW4taXNzdWVyIiwiY29udGV4dCI6eyJ1c2VyIjoicW06ZGI2YWEzMGUtM2E3MS00YjNiLTlkZTMtYTY1MTI3Yzk5ZjQxOjZhODdkZGExLTFkMDQtNDYyNS04ZTc3LWJlNzUxZGUwZWIxYyIsImlzc3VlIjoiQ1MtNDQ4MzEifSwiZXhwIjoxNzUxNDkwNjgyLCJpYXQiOjE3NDkwNzE0ODJ9.pr7RqS0zGqSOORf4e3SFYaikZp67q8pISA-p8ikupL0]{color:#3572b0}{color}{color:#333333} 
 Sent on June 4, 2025 3:11:22 PM MDT{color}  !http://atlassian-trk.prd.msg.ss-inf.net/CI0/010101973cc8d0e5-73c4b371-56db-4697-9c27-b2b67eafd62c-000000/rY-Q1dsVCp1TkBjHM9vgR4K2a0jmBp269jv_dI9gzow=408!{color:#333333}{color}{adf}{"type":"expand","content":[{"type":"paragraph","content":[{"type":"text","text":"Get "},{"type":"text","text":"Outlook for iOS","marks":[{"type":"link","attrs":{"href":"https://aka.ms/o0ukef"}}]}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

