<h3 id="my-meeting">My Meeting</h3>
<p>Meeting started: May 21, 2025, 2:52:19 PM Meeting duration: 20 minutes Meeting participants: <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="transcript">Transcript</h4>
<p>00:00 <PERSON>.: Yes, <PERSON><PERSON>, yes. 00:06 <PERSON>.: Okay, can you hear me? Okay, so for the first ticket. 00:08 Michael <PERSON>.: Yes. 00:13 Sandy K.: Um. The first thing they need to do, because they&#39;re looking… it sounds like they&#39;re looking to add… this is a new Trading partner, I saw you asked him. 00:26 <PERSON>.: Yes. 00:26 <PERSON>.: Um… So, what they need to do, then, is they need to fill out the form inside WebEDI, to add a new trading partner. 00:34 <PERSON>.: Yes. That&#39;s… yes. 00:39 <PERSON>.: Once… I&#39;m sorry, go ahead. 00:42 <PERSON>.: No, that what I was, um, that was my solution, was telling them to go back to their portal and click on Add Trading Partners. And everything, and um… once they do that. Um, to confirm it with me. 00:57 <PERSON>.: Right, and so right inside of… responses, there are these canned responses. And so there is one in here. For ad trading partner. 01:03 <PERSON>.: Oh, okay. 01:09 <PERSON>.: And if you… you can just… right there, you can just, you know, put in, you know, hi, whoever, to add your trading partner, and it walks them step-by-step what they need to do. 01:09 <PERSON> H.: Oh, okay, yes. 01:18 <PERSON> K.: Once it&#39;s assigned to a project analyst. They will… be able to
answer any questions for For them. 01:28 <PERSON> H.: Oh, okay, yes. 01:29 <PERSON> K.: So, that&#39;s all they have to do. They have to actually do this. We won&#39;t fill out the form, but once the analyst is assigned to it, they&#39;ll fill it out. But they have to… In order to initiate that, they need to complete this first. 01:41 Michael H.: Yes. Okay, I want to figure out that one. Okay. 01:44 Sandy K.: That one&#39;s super easy. Okay. 01:46 Michael H.: Yes. 02:02 Sandy K.: Okay, so when they did their first setup, they made a slight customization so that EDI would not bring in the blank lines or FedEx tracking lines from QuickBooks. Into the invoice, okay. It seems like their customization has been reversed, and we are now having to delete blank lines and tracking info. Of course it was. Why wouldn&#39;t it be? This is pipette, right? 02:39 Michael H.: Yes. This is the same person. 02:45 Sandy K.: This is the same one that is asked about adding Ulta? Both those tickets are the same customer? 02:55 Michael H.: Um, let me see… no, this is not the same. This is… oh, sorry about that. Or 4058. Let me edit. 03:08 Sandy K.: All right, so they had a customization That would not bring in blank lines. For the FedEx tracking line from QuickBooks. Invoice to EDI invoice. No… We have to look through the development tickets. So… Oh, you want this recording? Sorry. No, it&#39;s not even letting me record. It&#39;s not letting me record because it says… I have to join, and I am joined. 03:52 Michael H.: Oh. 04:01 Sandy K.: Um, okay. So… Oh, okay, alright. Alright, so pipette… said they had some customizations. Customizations would always be done by our development team. 04:03 Michael H.: Yes, I&#39;m recording on my screen. Yes. 04:15 Sandy K.: Um, so I went to our JIRA. I&#39;ll have to get you access to JIRA, but, um… 04:27 Sandy K.: Um… to see here, I just typed in pipette. So, the first one here… what is this customization for? Okay, contains every PWK segments. Awesome. That&#39;s not… Here&#39;s an enhancement. Line item shipping discount, and can it be
added to their current integration? Nope. I&#39;m just wrapping the show. 05:32 Michael H.: Because I&#39;m not… for this one, I was on track, but then I couldn&#39;t understand the QuickBook integrators. Uh, I went to the portal to try to find what she&#39;s, like, talking about. 05:42 Sandy K.: Well, as soon as she said that she had a customization. So, any customizations would not have anything to do with the portal. 05:51 Michael H.: Okay, so… 05:52 Sandy K.: So she had a customization done within the integrator. To not bring over the FedEx tracking. Or any blank lines that were in the QuickBooks invoice to the EDI invoice. So that means when they run the integrator. They had some customization. 06:07 Michael H.: Yeah. 06:12 Sandy K.: That was not bringing over this from inside QuickBooks. 06:18 Michael H.: Okay. 06:18 Sandy K.: But now, according to her. It appears that that is… um, happening that it&#39;s bringing over blank lines, and it is bringing over And she didn&#39;t provide any examples, but um… Which is fine. 06:41 Sandy K.: So, I&#39;m just looking here to see… Any customizations or anything here for pipette. 06:49 Michael H.: Yes. 06:49 Sandy K.: Um… A shipping charge as a line item on the invoice. The invoice will not integrate. Nope, it&#39;s not it either. This was the enhancement… that everybody looked at. Customers have the contact name pulled from the PER segment and order number in the REF replace since the QB shipped. Nope, it&#39;s not it either. 08:30 Sandy K.: I agree there? Let&#39;s see what you&#39;re 09:15 Sandy K.: These are the only developments because I see. And I don&#39;t see anything… That relates to… 09:17 Michael H.: Yeah. 09:54 Sandy K.: Oops, reverse flow… QuickBooks Partner Configs… QuickBooks prior companies. Just those. 10:30 Sandy K.: Welcome to this enhancement one again? This all has to do with the SEC segment, not…
11:46 Sandy K.: It&#39;s taking that long to load? I&#39;m waiting to see if the QuickBooks integrator flag is going to populate here. 11:54 Michael H.: Yes. 11:55 Sandy K.: So I can tell if… okay, there it goes. I&#39;m gonna assume that she&#39;s already fixed these. With whatever information she didn&#39;t want on them. And all the notes on this would be… in our old ticketing system, which we no longer have. 12:28 Sandy K.: Accessed. 12:28 Michael H.: Yes. 12:43 Sandy K.: I&#39;m… I&#39;m gonna assume… there was another line here. Like a line 2? Can you ask her if she can, um… Send over an example, because I know the developer&#39;s gonna ask for it because I can&#39;t find a ticket for it. 13:07 Michael H.: Okay. 13:08 Sandy K.: So, can you just ask her if she could please send over an example of an invoice. Um, that&#39;s pulling over that information. Inside Web EDI. So before she corrects it and removes the information she doesn&#39;t want. Could you please send over an example to us? Um, from both QuickBooks, so what she sees in QuickBooks. And what&#39;s coming into WebEDI, I need to know the segments and the elements, and she doesn&#39;t have to tell me that, but The example that comes over into WebEDI will give me everything I need, so… I&#39;m going to need an example invoice that is bringing over this information again. 13:55 Sandy K.: Before she makes any changes. Because if once, like, if she says here, use this one, but she&#39;s already made corrections to it, that won&#39;t help me. 13:55 Michael H.: Yeah. 14:04 Sandy K.: I need it before she would make any changes. 14:58 Michael H.: I said, uh, hi, Kendall, um… Before we make any changes, could you provide two examples for… two examples. The first one is decrypt… Books, export, and the second one is the Web EDI versions before… Uh… yes, before… 15:16 Sandy K.: Right, yeah, if she could provide a screenshot from within, like, what it looks like in QuickBooks. And then what the… an invoice number from within WebEDI before she makes any changes. 15:30 Michael H.: Yes. So, before we make any changes, could you please provide two samples for… files for comparison to QuickBook exports, a… showing the invoice exactly as it appears in QuickBooks, and the corresponding Web EDI
version. 15:57 Sandy K.: Yes. Yeah, before any changes are made, right, just like that again. 15:59 Michael H.: Yes. You know. 16:02 Sandy K.: Mm-hmm. 16:05 Michael H.: Okay. 16:38 Michael H.: I sent you the response in, um… Uh, what was it, and Slack, Ms. Sandy, if… I&#39;m just making sure one more time before I click save. 16:52 Sandy K.: Oh, no, not… not a PDF, just… it&#39;s not a QuickBooks… there&#39;s no export. 16:56 Michael H.: Okay. Okay. 16:58 Sandy K.: It&#39;s just a screenshot from within QuickBooks showing… you know, what the QuickBooks file looks like So, right, there is no… there&#39;s no export, so what the QuickBooks Integrator does is It takes the file from inside QuickBooks, and it… takes it, and the integrator turns it into an EDI file. We don&#39;t have… see that, we don&#39;t touch that side of it. 17:06 Michael H.: Okay, screenshot. Oh, okay. 17:23 Sandy K.: So they can only give us screenshots of what they&#39;re looking at inside QuickBooks. 17:24 Michael H.: Yes. 17:28 Sandy K.: I just need a screenshot of the invoice in QuickBooks so I can see where the data is in QuickBooks. And then I need to be able to know the invoice that it corresponds to, that it came into YBDI, like you said, message ID would be great. 17:43 Michael H.: Yes. 17:43 Sandy K.: So that I can see the segments and elements that are being created based on the data that we see on the screenshot. 17:52 Michael H.: Yes. Okay. 17:52 Sandy K.: So, right, so just a screenshot… from the invoice inside QuickBooks, showing the fields. And then a Web EDI message ID or invoice number Um, that will… that we can, you know, that shows where the information, you know, the data in the WebEDI version Prior to you making any changes. 18:18 Michael H.: Okay. Yes. 18:21 Sandy K.: Yeah. Because once she makes the correction, either remove the
tracking number or delete the blank item. I no longer have anything to look at. So… I just want, on her next… integration of an invoice, and all I&#39;m going to have to do is take get those… capture that information, and then she can make the changes and send it. It&#39;s not like I&#39;m gonna ask her to sit on an invoice for days. I just need to be able to get that invoice, get the raw data. See where they&#39;re going, and then… talk to a developer. Because if I go talk to a developer now, they&#39;re going to say, well, can you get me a screenshot of QuickBooks Um, give me the segments and elements that are being affected, and I don&#39;t know. 19:06 Sandy K.: Because I can&#39;t find that an enhancement for pipette. In development. So, does that make sense? Does that help? 19:13 Michael H.: Yes, okay. Yes, yes, yes. 19:42 Michael H.: Yes. It just sent. That should be good. 20:51 Sandy K.: Can I call you back in a few
View original transcript at Tactiq.</p>
