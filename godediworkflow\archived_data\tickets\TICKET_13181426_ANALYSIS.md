# Ticket #13181426 Analysis - ISA/GS Configuration Error

## Summary
**Issue**: ASN (856) documents sent to John Deere Construction and Forestry failing due to incorrect ISA/GS IDs
**Customer**: Blachford/Milcut Inc
**Date**: July 7-8, 2025
**Severity**: SEV3
**Resolution**: ISA/GS ID configuration correction required

## Problem Details

### Customer Messages Affected
- Message: 44382907, Ref: 0274399-0000A ❌ FAILED
- Message: 44382905, Ref: 0274402-0000B
- Message: 44382904, Ref: 0274401-0000B

### Root Cause
<PERSON> system error message:
```
JD_X12DeEnvelopeUnified(556619081) - [DeenvelopeX12]ISA Envelope Lookup Failed
```

### Configuration Mismatch
- **Sent ISA ID**: 2627833300
- **Expected ISA ID**: DTS4511
- **Receiver ID**: ********* (<PERSON>)

### Raw EDI Data Captured
```
ISA*00*          *00*          *01*2627833300     *01**********     *250707*1434*U*00401*000000033*0*P*>~
GS*SH*2627833300***********20250707*1434*33*X*004010
```

## Solution Steps

1. **Update WebEDI Configuration**
   - Navigate to Trading Partner profile for John Deere Construction and Forestry
   - Change ISA Sender ID from "2627833300" to "DTS4511"
   - Ensure GS Sender ID matches ISA ID

2. **Verify Receiver IDs**
   - John Deere ISA Receiver ID: ********* ✓ (Correct)
   - Confirm qualifier is "01" for both sender and receiver

3. **Retransmit Documents**
   - After configuration update, retransmit all three ASNs
   - Monitor for 997 acknowledgments

## Prevention Guidelines

### Before Sending ASNs to John Deere:
1. Always verify ISA/GS IDs match trading partner requirements
2. Check if special IDs are required (like DTS4511 vs company DUNS)
3. Confirm 997 functional acknowledgments are received

### Common John Deere Requirements:
- ISA Qualifier: 01 (DUNS)
- Expected Sender ID: DTS4511 (not standard DUNS)
- Version: 004010
- Test Indicator: P (Production)

## Customer Communication
- Customer requested callback due to menu navigation issues
- Phone: ************ Ext. 1102
- Direct support contact needed to resolve configuration

## Related Knowledge
- ISA/GS configuration errors are #1 cause of EDI failures
- John Deere often requires specific sender IDs different from DUNS
- Always capture and review 997 acknowledgments

## Tags
#ISA_Configuration #John_Deere #856_ASN #WebEDI #Configuration_Error #DTS4511