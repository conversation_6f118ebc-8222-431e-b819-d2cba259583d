<p>Okay. So, for the first ticket, um, the first thing they need to do because they&#39;re looking, it sounds like they&#39;re looking to add, this is a new trading partner. I saw you asked them. Um, so what they need to do then is they need to fill out the form inside WebEDI to add a new trading partner. once. I&#39;m sorry. Go ahead. Right. And so, right inside of responses, there are these canned responses. And so, there is one in here for add trading partner. And if you you can just right there, you can just, you know, put in, you know, hi, whoever to add new trading partner. And it walks them step by step what they need to do. Once it&#39;s assigned to a project analyst, they will be able to answer any questions for for them. So that&#39;s all they have to do. They have to actually do this. We won&#39;t fill out the form, but once the analyst is assigned to it, they&#39;ll fill it out. But they have to in In order to initiate that, they need to complete this first. That one was super easy. Okay. Okay. So, when they did their first setup, they made a slight customization so that each I would not bring in the blank lines or FedEx tracking lines from QuickBooks into the invoice. Okay, it seems like that customization has been reversed and we are now having to delete blank lines and tracking info. Of course, it why wouldn&#39;t it be? This is Pipet, right? Pipet. This is the same one that is asked about adding Ulta. This Oh, both those tickets are the same customer. All right. So, they had a customization that would not bring in blank lines or the FedEx tracking line from QuickBooks invoice to EDI invoice. No have to look through the development tickets. So, oh, you want this recording? Sorry. No, it&#39;s not letting me record. It&#39;s not letting me record because it says I have to join and I am joined. Um, okay. So, Oh, okay. All right. All right. So, Pipet said they had some customizations. Customizations would always be done by our development team. Um, so I went to our Jira. I&#39;ll have to get you access to Jira, but um um to see here I just typed in pipet. So, the first one here, what is this customization for? Okay. Contains sever PWK segments. That&#39;s not here&#39;s an enhancement line item shipping discount and can it be added to their current integration? No, it&#39;s machine. Well, as soon as she said that she had a customization, so any customizations would not have anything to do with the portal. So, she had a customization done within the integrator to not bring over the FedEx tracking or any blank lines that were in the QuickBooks invoice to the EDI invoice. So, That means when they run the integrator, they had some customization that was not bringing over this from inside QuickBooks. But now, according to her, it appears that that is um happening that it&#39;s bringing over blank lines and it is bringing over and she didn&#39;t provide any examples, but um which is fine. So, I&#39;m just looking here to see any customizations or anything here for pipet. Um, getting shipping charge as a line item on the invoice. The invoice will not integrate. Nope, it&#39;s not it either. Okay. This was the enhancement that I looked at. There&#39;s another one. customer like to the contact name pulled from the peer segment and order number replaces the QB ship. No, it&#39;s not it either That&#39;s These are the only developments you guys see and I don&#39;t see anything that relates to pieces in your bones. Quickbooks reverse flow. Quickbooks partner configs. QuickBooks partner config. Just those This is all to do with the SEC segment. It&#39;s taking that long to load. I&#39;m waiting to see if the QuickBooks integrator flag is going to populate here so I can tell if Okay, there it is. I&#39;m I&#39;m going to assume that she&#39;s already fixed these with whatever information she didn&#39;t want on them. And all the notes on this would be in our old ticketing system, which we no longer have access to. Um, I&#39;m I&#39;m going to assume there was another line here like a line two. Can you ask her if she can um send over an example because I know the developer is going to ask for it because I can&#39;t find a ticket for it. So, can you just ask her if she could please send over an example of an invoice um that&#39;s pulling over that information inside WebEDI. So, before she corrects it and removes the information she doesn&#39;t want, could she please send over an example to us um from both QuickBooks, so what she sees in QuickBooks and what&#39;s coming into WebEI? I need to know the segments and the elements and she doesn&#39;t have to tell me that. But the example that comes over into WebDI will give me everything I need. So, I&#39;m going to need an example invoice that is bringing over this information again before she makes any changes. Because if once like if she says here, use this one, but she&#39;s already made corrections to it, that won&#39;t help me. I need it before she would make any changes. Right. Yeah. If she could provide a screenshot from within like what what it looks like in QuickBooks and then what the an invoice number from within WebEDI before she makes any changes. So, right, there is no there&#39;s no export. So, what the QuickBooks integrator does is it takes the file from inside QuickBooks and it takes it and the integrator turns it into an EDI file. We don&#39;t have see that. We don&#39;t touch that side of it. So, they can only give us screenshots of what they&#39;re looking at inside QuickBooks. I just need a screenshot of the invoice in QuickBooks so I can see where the data is in QuickBooks. And then I need to be able to know the invoice that it corresponds to that it came into web EDI. Like you said, message ID would be great. So then I can see the segments and elements that are being created based on the data that we see on the screenshot. So right so just a screenshot from the invoice inside QuickBooks showing the fields and then a web EDI message ID or invoice number. Um that will that we can, you know, that shows where the information, you know, the data in the web EDI version prior to you making any changes. No, because once she makes the correction, either remove the tracking number or delete the blank item, I no longer have anything to look at. So, I just want on her next integration of an invoice And all I&#39;m going to have to do is take get those capture that information and then she can make the changes and send it. It&#39;s not like I&#39;m going to ask her to sit on an invoice for days. I just need to be able to get that invoice, get the raw data, see where they&#39;re going, and then talk to a developer. Because if I go talk to a developer now, they&#39;re going to say, &#34;Well, can you get me a screenshot of QuickBooks and um give me the segments and elements that are being affected?&#34; And I don&#39;t know. because I can&#39;t find that an enhancement for pipet in development. So, does that make sense? Does that help? Can I call you back in a few minutes? Okay. Okay. I</p>
