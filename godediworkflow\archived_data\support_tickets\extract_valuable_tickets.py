#!/usr/bin/env python3
"""
Extract the most valuable EDI tickets based on Master E<PERSON> Troubleshooter criteria.
"""

import re
import json
from pathlib import Path
from typing import List, Dict, Tuple
from collections import defaultdict

class MasterEDITicketAnalyzer:
    """Analyze tickets based on Master EDI Troubleshooter criteria."""
    
    def __init__(self):
        self.valuable_tickets = []
        self.ticket_scores = {}
        
    def score_ticket(self, ticket: Dict) -> int:
        """Score a ticket based on troubleshooting value criteria."""
        score = 0
        
        # Clear problem description (10 points)
        if ticket.get('description') and len(ticket['description']) > 50:
            score += 10
            
        # Has troubleshooting steps (20 points)
        if ticket.get('troubleshooting_steps') and len(ticket['troubleshooting_steps']) > 0:
            score += 20
            
        # Root cause identified (30 points)
        if ticket.get('root_cause') and len(ticket['root_cause']) > 20:
            score += 30
            
        # Resolution provided (20 points)
        if ticket.get('resolution_details') and len(ticket['resolution_details']) > 20:
            score += 20
            
        # Multiple EDI types involved (complexity bonus)
        if len(ticket.get('edi_types', [])) > 2:
            score += 10
            
        # Communication/AS2/SFTP issues (high value)
        high_value_types = ['AS2', 'SFTP', 'Communication', 'X12', 'Mapping']
        if any(edi_type in high_value_types for edi_type in ticket.get('edi_types', [])):
            score += 15
            
        # Has multiple comments (collaboration bonus)
        if len(ticket.get('comments', [])) > 3:
            score += 10
            
        return score
    
    def categorize_ticket(self, ticket: Dict) -> List[str]:
        """Categorize ticket by issue type."""
        categories = []
        comments_text = []
        for comment in ticket.get('comments', []):
            if isinstance(comment, dict):
                comments_text.append(comment.get('text', ''))
            else:
                comments_text.append(str(comment))
        full_text = f"{ticket.get('title', '')} {ticket.get('description', '')} {' '.join(comments_text)}".lower()
        
        category_patterns = {
            'connectivity': ['connection', 'timeout', 'authentication', 'certificate', 'sftp', 'as2', 'ftp'],
            'data_format': ['format', 'validation', 'invalid', 'schema', 'x12', 'segment', 'element'],
            'mapping': ['mapping', 'translation', 'transform', 'convert', 'crossref'],
            'performance': ['stuck', 'slow', 'delay', 'queue', 'performance'],
            'configuration': ['config', 'setup', 'settings', 'parameter', 'profile'],
            'document_flow': ['850', '856', '810', '997', 'acknowledge', 'po', 'invoice', 'asn'],
            'error_handling': ['error', 'fail', 'reject', 'exception', 'retry']
        }
        
        for category, keywords in category_patterns.items():
            if any(keyword in full_text for keyword in keywords):
                categories.append(category)
                
        return categories

def extract_detailed_ticket_info(content: str) -> List[Dict]:
    """Extract detailed information from ticket HTML."""
    tickets = []
    
    # Split by ticket headers
    ticket_sections = re.split(r'<h4[^>]*id="[^"]*"[^>]*>', content)
    
    for section in ticket_sections[1:]:
        # Extract basic info
        header_match = re.match(r'\[([^\]]+)\]\s*(.+?)(?:\s*Created:|$)', section)
        if not header_match:
            continue
            
        ticket = {
            'id': header_match.group(1),
            'title': header_match.group(2).strip(),
            'raw_content': section[:5000]  # Keep first 5000 chars for reference
        }
        
        # Extract all metadata
        ticket['status'] = re.search(r'Status:\s*(\w+)', section)
        ticket['status'] = ticket['status'].group(1) if ticket['status'] else ''
        
        ticket['priority'] = re.search(r'Priority:\s*(\w+)', section)
        ticket['priority'] = ticket['priority'].group(1) if ticket['priority'] else ''
        
        ticket['assignee'] = re.search(r'Assignee:\s*([^\n]+)', section)
        ticket['assignee'] = ticket['assignee'].group(1).strip() if ticket['assignee'] else ''
        
        # Extract description
        desc_match = re.search(r'Description\s*\n(.+?)(?=Comments|$)', section, re.DOTALL)
        ticket['description'] = desc_match.group(1).strip() if desc_match else ''
        
        # Extract all comments with timestamps
        ticket['comments'] = []
        comments_section = re.search(r'Comments?\s*\n(.+?)(?=</ul>|$)', section, re.DOTALL)
        if comments_section:
            comment_blocks = re.findall(r'Comment by\s+([^[]+)\s*\[\s*([^\]]+)\s*\]\s*([^<]+(?:<[^>]+>[^<]+)*)', 
                                      comments_section.group(1))
            for author, date, comment in comment_blocks:
                ticket['comments'].append({
                    'author': author.strip(),
                    'date': date.strip(),
                    'text': re.sub(r'<[^>]+>', '', comment).strip()
                })
        
        # Analyze EDI content
        full_text = f"{ticket['title']} {ticket['description']} {' '.join([c['text'] for c in ticket['comments']])}".lower()
        
        edi_patterns = {
            '850': r'\b850\b|purchase\s*order\b',
            '856': r'\b856\b|ASN|advance\s*ship',
            '810': r'\b810\b|invoice',
            '997': r'\b997\b|functional\s*ack',
            'AS2': r'\bAS2\b',
            'SFTP': r'\bSFTP\b',
            'FTP': r'\bFTP\b',
            'X12': r'\bX12\b|ANSI',
            'EDI': r'\bEDI\b'
        }
        
        ticket['edi_types'] = []
        for edi_type, pattern in edi_patterns.items():
            if re.search(pattern, full_text, re.IGNORECASE):
                ticket['edi_types'].append(edi_type)
        
        # Extract troubleshooting steps
        ticket['troubleshooting_steps'] = []
        troubleshooting_patterns = [
            r'(?:i\s+)?(?:have\s+)?(?:checked?|verified?|tested?|found|updated?|fixed?|changed?)\s+[^.]+\.',
            r'the\s+(?:issue|problem|error)\s+(?:was|is)\s+[^.]+\.',
            r'(?:root\s+)?cause[:\s]+[^.]+\.',
            r'resolution[:\s]+[^.]+\.'
        ]
        
        for pattern in troubleshooting_patterns:
            matches = re.findall(pattern, full_text, re.IGNORECASE)
            ticket['troubleshooting_steps'].extend([m.strip() for m in matches])
        
        # Extract root cause if mentioned
        root_cause_match = re.search(r'(?:root\s+)?cause[:\s]+([^.]+\.)', full_text, re.IGNORECASE)
        ticket['root_cause'] = root_cause_match.group(1).strip() if root_cause_match else ''
        
        # Extract resolution
        resolution_match = re.search(r'(?:resolution|resolved?|fixed?)[:\s]+([^.]+\.)', full_text, re.IGNORECASE)
        ticket['resolution_details'] = resolution_match.group(1).strip() if resolution_match else ''
        
        if ticket['edi_types']:  # Only keep EDI-related tickets
            tickets.append(ticket)
            
    return tickets

def find_exemplary_tickets(tickets: List[Dict]) -> List[Dict]:
    """Find exemplary tickets that demonstrate best troubleshooting practices."""
    analyzer = MasterEDITicketAnalyzer()
    
    # Score all tickets
    for ticket in tickets:
        score = analyzer.score_ticket(ticket)
        ticket['score'] = score
        ticket['categories'] = analyzer.categorize_ticket(ticket)
        analyzer.ticket_scores[ticket['id']] = score
    
    # Sort by score and get top tickets
    sorted_tickets = sorted(tickets, key=lambda x: x['score'], reverse=True)
    
    # Group by category to ensure diversity
    category_tickets = defaultdict(list)
    for ticket in sorted_tickets:
        for category in ticket['categories']:
            category_tickets[category].append(ticket)
    
    # Select best tickets from each category
    exemplary_tickets = []
    seen_ids = set()
    
    # Get top 2 from each category
    for category, cat_tickets in category_tickets.items():
        for ticket in cat_tickets[:2]:
            if ticket['id'] not in seen_ids and ticket['score'] >= 50:
                exemplary_tickets.append(ticket)
                seen_ids.add(ticket['id'])
    
    # Add any remaining high-scoring tickets
    for ticket in sorted_tickets:
        if ticket['id'] not in seen_ids and ticket['score'] >= 70:
            exemplary_tickets.append(ticket)
            seen_ids.add(ticket['id'])
            if len(exemplary_tickets) >= 50:
                break
    
    return exemplary_tickets

def generate_knowledge_base_entries(tickets: List[Dict]) -> str:
    """Generate knowledge base entries from exemplary tickets."""
    kb_entries = []
    
    for ticket in tickets[:30]:  # Top 30 tickets
        entry = f"""
### Ticket {ticket['id']}: {ticket['title']}
**Score**: {ticket['score']}/115
**Categories**: {', '.join(ticket['categories'])}
**EDI Types**: {', '.join(ticket['edi_types'])}
**Priority**: {ticket['priority']}

#### Problem Description:
{ticket['description'][:500]}...

#### Root Cause:
{ticket['root_cause'] if ticket['root_cause'] else 'Not explicitly stated'}

#### Resolution:
{ticket['resolution_details'] if ticket['resolution_details'] else 'See troubleshooting steps'}

#### Key Troubleshooting Steps:
"""
        
        for i, step in enumerate(ticket['troubleshooting_steps'][:5], 1):
            entry += f"{i}. {step}\n"
        
        entry += "\n---\n"
        kb_entries.append(entry)
    
    return '\n'.join(kb_entries)

def main():
    """Main function to extract valuable tickets."""
    file_paths = [
        "/mnt/c/Users/<USER>/OneDrive - Cleo/Desktop/Sources/0001-1000.pdf(1).html",
        "/mnt/c/Users/<USER>/OneDrive - Cleo/Desktop/Sources/1001-2000.pdf(1).html",
        "/mnt/c/Users/<USER>/OneDrive - Cleo/Desktop/Sources/2001-3000.pdf(1).html",
        "/mnt/c/Users/<USER>/OneDrive - Cleo/Desktop/Sources/3001-3498.pdf.html"
    ]
    
    all_tickets = []
    
    print("Extracting valuable EDI tickets...")
    print("=" * 50)
    
    for file_path in file_paths:
        print(f"\nProcessing {Path(file_path).name}...")
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            tickets = extract_detailed_ticket_info(content)
            all_tickets.extend(tickets)
            print(f"  Extracted {len(tickets)} EDI tickets")
            
        except Exception as e:
            print(f"  Error: {e}")
    
    print(f"\nTotal EDI tickets extracted: {len(all_tickets)}")
    
    # Find exemplary tickets
    exemplary_tickets = find_exemplary_tickets(all_tickets)
    print(f"Identified {len(exemplary_tickets)} exemplary tickets")
    
    # Generate reports
    # 1. Summary report
    summary = {
        'total_tickets_analyzed': len(all_tickets),
        'exemplary_tickets': len(exemplary_tickets),
        'score_distribution': {
            '90+': len([t for t in exemplary_tickets if t['score'] >= 90]),
            '70-89': len([t for t in exemplary_tickets if 70 <= t['score'] < 90]),
            '50-69': len([t for t in exemplary_tickets if 50 <= t['score'] < 70])
        },
        'top_categories': defaultdict(int)
    }
    
    for ticket in exemplary_tickets:
        for category in ticket['categories']:
            summary['top_categories'][category] += 1
    
    # 2. Save exemplary tickets
    exemplary_output = []
    for ticket in exemplary_tickets[:50]:  # Top 50
        exemplary_output.append({
            'id': ticket['id'],
            'title': ticket['title'],
            'score': ticket['score'],
            'categories': ticket['categories'],
            'edi_types': ticket['edi_types'],
            'priority': ticket['priority'],
            'description': ticket['description'][:500],
            'root_cause': ticket['root_cause'],
            'resolution': ticket['resolution_details'],
            'troubleshooting_steps': ticket['troubleshooting_steps'][:5]
        })
    
    # Save outputs
    with open('/home/<USER>/edi_knowledge_base/support_tickets/exemplary_tickets.json', 'w') as f:
        json.dump(exemplary_output, f, indent=2)
    
    # Generate knowledge base markdown
    kb_content = generate_knowledge_base_entries(exemplary_tickets)
    with open('/home/<USER>/edi_knowledge_base/support_tickets/ticket_knowledge_base.md', 'w') as f:
        f.write("# EDI Support Ticket Knowledge Base\n\n")
        f.write("This knowledge base contains exemplary support tickets that demonstrate best practices in EDI troubleshooting.\n\n")
        f.write(kb_content)
    
    # Print summary
    print("\n" + "=" * 50)
    print("EXTRACTION SUMMARY")
    print("=" * 50)
    print(f"Total EDI tickets analyzed: {summary['total_tickets_analyzed']}")
    print(f"Exemplary tickets identified: {summary['exemplary_tickets']}")
    print(f"\nScore Distribution:")
    for range_name, count in summary['score_distribution'].items():
        print(f"  {range_name}: {count} tickets")
    print(f"\nTop Issue Categories:")
    for category, count in sorted(summary['top_categories'].items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {category}: {count} tickets")
    
    print("\nFiles created:")
    print("  - exemplary_tickets.json (detailed ticket data)")
    print("  - ticket_knowledge_base.md (formatted knowledge base)")

if __name__ == "__main__":
    main()