# CS-45087: EDI Setup Request: Fleet Optics Inc- Sephora

## Ticket Information
- **Key**: CS-45087
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON>
- **Created**: 2025-06-06T18:36:05.545-0600
- **Updated**: 2025-06-09T06:08:43.187-0600
- **Customer**: Optics Inc

## Description
{color:#172B4D}Hi Team,{color}{color:#172B4D}{color}  
  
{color:#172B4D} There is a client submitted ticket in CIC Support about resuming an implementation on DTS.{color}{color:#172B4D}{color}  
  
{color:#172B4D} Can you assist with the request below?{color}{color:#172B4D}{color}   
  
{color:#172B4D} = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = ={color}{color:#172B4D}{color}   
  
{color:#172B4D} FleetOptics Account #:7403{color} 
{color:#172B4D} Client Contact : <PERSON><PERSON>son <<EMAIL>>{color} 
{color:#172B4D} DTS Implementation Contact : <PERSON>leigh <PERSON>{color} 
{color:#172B4D} Hi, we started a EDI project with DataTrans Solutions requesting to establish an EDI connection between us FleetOptics Account #:7403, and our customer Sephora, unfortunately Sephora initiated the project before they were ready to set up the connection. At that time we were working with Kayleigh Benedict from DataTrans. Sephora are now ready to establish the EDI connection, but in the time it has taken them DataTrans was purchased by yourselves, I now need a new contact that I can give to the Sephora team to establish the EDI connection.{color}{color:#172B4D}{color}{color:#172B4D}{color}  
  
 [https://cleo.zendesk.com/agent/tickets/********|https://cleo.zendesk.com/agent/tickets/********]{color:#172B4D}{color}{color:#172B4D}{color}{color:#172B4D}{color}  
  
 !image.png|thumbnail!{color:#172B4D}{color}{color:#172B4D}{color} 
|
|
|
|{color:#000001}{color}
|{color:#000001}Mike{color}{color:#000001}{color}|{color:#000001}  \\{color}{color:#000001}{color}|{color:#000001}Olsen{color}{color:#000001}{color}|{color:#000001}{color}
|{color:#000001}{color}
|{color:#000001}Cleo{color}{color:#000001}{color}|{color:#000001} :  \\{color}{color:#000001}{color}|{color:#000001}Senior Support Engineer I{color}{color:#000001}{color}|{color:#000001}{color}
|
|{color:#000001}{color}
|{color:#000001}Tel: {color}{color:#000001}{color}|{color:#000001}815‑282‑7679{color}{color:#000001}{color}|{color:#000001}{color}
|
| | | {color:#000001}{color}
|{color:#000001}{color}{color:#000001}
{color}{color:#000001}{color}
|{color:#000001}Email: {color}{color:#000001}{color}|{color:#000001}{color}[{color:#000001}{color}{color:#000001}<EMAIL>{color}|mailto:<EMAIL>]{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}|{color:#000001}{color}
{color:#000001}{color}{color:#000001}{color}{color:#000001} | {color}{color:#000001}{color}{color:#000001}
Web: {color}{color:#000001}{color}{color:#000001}{color}[{color:#000001}{color}{color:#000001}www.cleo.com{color}|https://www.cleo.com/]{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}
| | | {color:#0083CA}{color}
|{color:#0083CA}{color}{color:#0083CA}{color}
|{color:#0083CA}{color}[{color:#0083CA}{color}{color:#0083CA}Join us at one of our upcoming events. Check out the list!{color}|https://www.cleo.com/events]{color:#0083CA}{color}{color:#0083CA} \\{color}{color:#0083CA}{color}|{color:#0083CA}{color}{adf}{"type":"expand","content":[{"type":"paragraph","content":[{"type":"text","text":"  "}]},{"type":"paragraph","content":[{"type":"text","text":"Michael Olsen "}]},{"type":"paragraph","content":[{"type":"text","text":"Cleo | Senior Technical Support Specialist"},{"type":"hardBreak"},{"type":"text","text":"Tel: ************  "}]},{"type":"paragraph","content":[{"type":"text","text":"Email: "},{"type":"text","text":"<EMAIL>","marks":[{"type":"link","attrs":{"href":"mailto:<EMAIL>"}}]},{"type":"text","text":" |Web: "},{"type":"text","text":"www.cleo.com","marks":[{"type":"link","attrs":{"href":"http://www.cleo.com"}}]}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

