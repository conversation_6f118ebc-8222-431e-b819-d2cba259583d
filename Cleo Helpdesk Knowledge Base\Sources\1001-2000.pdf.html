<p>15 LW_SA_LWJDBC Success None March 24, 2025 11:51:41 AM EDT
17 XSLTService Success None March 24, 2025 11:51:41 AM EDT
24 EDIEncoder Success None March 24, 2025 11:51:41 AM EDT
25 EDIEnvelope Success None March 24, 2025 11:51:41 AM EDT
28 X12EnvelopeUnified Success None March 24, 2025 11:51:41 AM EDT
30 GetDocumentInfoService Success None March 24, 2025 11:51:41 AM EDT
31 LW_Utility_Service Success None March 24, 2025 11:51:41 AM EDT
34 DecisionEngineService Success 2 March 24, 2025 11:51:41 AM EDT
37 LW_SA_LWJDBC_B2BI Success None March 24, 2025 11:51:41 AM EDT
47 LW_SA_DKR Success None March 24, 2025 11:51:41 AM EDT
50 GetDocumentInfoService Success None March 24, 2025 11:51:41 AM EDT
51 AssignService Success None March 24, 2025 11:51:41 AM EDT
59 LW_SA_FSA Success None March 24, 2025 11:51:41 AM EDT
69 LW_SA_LWJDBC Success None March 24, 2025 11:51:41 AM EDT
72 AssignService Success None March 24, 2025 11:51:41 AM EDT
74 AssignService Success None March 24, 2025 11:51:41 AM EDT
89 XSLTService Success None March 24, 2025 11:51:41 AM EDT
90 LW_SA_DKR Success None March 24, 2025 11:51:42 AM EDT
91 LW_SA_DKR Success None March 24, 2025 11:51:42 AM EDT
92 LW_SA_DKR Success None March 24, 2025 11:51:42 AM EDT
97 LW_SA_LWJDBC_B2BI Success None March 24, 2025 11:51:42 AM EDT
99 SFTPClientBeginSession Error  March 24, 2025 11:51:42 AM EDT
103 BPExceptionService Success Cannot authenticate.: March 24, 2025 11:51:42 AM EDT
104 InvokeInlineService Success Cannot authenticate.: March 24, 2025 11:51:42 AM EDT
105 InvokeInlineService Success Cannot authenticate.: March 24, 2025 11:51:42 AM EDT
106 InvokeSubProcessService Success Cannot authenticate.: March 24, 2025 11:51:42 AM EDT
112 DecisionEngineService Success 2 March 24, 2025 11:51:42 AM EDT
113 InvokeSubProcessService Success None March 24, 2025 11:51:42 AM EDT
115 AssignService Success None March 24, 2025 11:51:43 AM EDT
116 InvokeSubProcessService Success None March 24, 2025 11:51:43 AM EDT
117 InvokeNotifyCompleteService Success None March 24, 2025 11:51:43 AM EDT
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 24/Mar/25 ]
<em>PrimaryDocument.txt  (7 kB)</em>
<em>ProcessData.xml  (21 kB)</em>
Comment by Nicholas Sanchez [ 24/Mar/25 ]
Hello Jeffery,
We had an internal issue this morning which caused transmission delays. We have resolved this issue and can confirm the ASNs were sent to and received by S &amp; S Tires accordingly. I apologize for the inconvenience and if you
Nicolas Sanchez DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Nicholas Sanchez [ 24/Mar/25 ]
Nicolas Sanchez DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 24/Mar/25 ]
Hello Nicolas,
I saw your other email, and all asn’s have been sent, and I do appreciate your efforts.
Thanks,
Jeffrey D Reisinger Business Process Consultant
The Goodyear Tire &amp; Rubber Company
200 Innovation Way, Akron, OH 44316
Phone: ************ <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 25/Mar/25 ]
Hello Nicolas,
I have more failures with this Cannot Authenticate error, can you look at and let me know when I can resend these. thanks
Thanks,
Jeffrey D Reisinger Business Process Consultant
The Goodyear Tire &amp; Rubber Company
200 Innovation Way, Akron, OH 44316
Phone: ************ <a href="mailto:<EMAIL>"><EMAIL></a>
From: Jeffrey Reisinger
Comment by Nicholas Sanchez [ 25/Mar/25 ]
Hello Jeffrey,
DataTrans continues to experience an intermittent issue that is impacting the ability to log into the WebEDI portal. Please use the following URL to login <a href="https://datatrans-inc.com/login/">https://datatrans-inc.com/login/</a> . This is also impacting the delivery of inbou
Nicolas Sanchez DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-41092-re-fw-error-alert-hino-trucks-sd-interchange-number-*********-*********-stuck-at-ecom-created-24-mar-25-updated-27-mar-25-resolved-27-mar-25">[CS-41092] Re: FW: [ERROR ALERT, Hino Trucks]-SD: Interchange Number *********, ********* stuck at ecom Created: 24/Mar/25  Updated: 27/Mar/25  Resolved: 27/Mar/25</h4>
<h2 id="status-resolved-project-customer-support-components-none-affects-versions-none-fix-versions-none-type-support-priority-medium-reporter-krista-johnson-assignee-nicholas-sanchez-resolution-done-votes-0-labels-none-remaining-estimate-not-specified-time-spent-not-specified-original-estimate-not-specified-attachments-outlook-fablr211-png-outlook-ein-bild-png-outlook-i42jp3yo-png-outlook-4mrqlser-png-outlook-qzpv2uec-png-request-type-emailed-request-request-language-english-request-participants-organizations-none-csat-comment-webhookresponse-body-value-comment-description-copying-in-datatrans-support-if-you-have-a-question-still-but-i-think-you-d-need-to-contact-hino-truck-directly-if-they-assign-one-edi-setup-per-item-then-it-would-be-a-restriction-on-their-end-thanks-krista-on-3-24-2025-11-45-am-rajen-gandhi-wrote-that-is-what-i-thought-i-wonder-if-they-are-not-able-to-do-send-an-850-for-another-product-due-to-the-custom-objects-options-that-they-send-as-well-from-krista-johnson-sent-monday-march-24-2025-12-02-pm-to-rajen-gandhi-subject-re-fw-error-alert-hino-trucks-sd-interchange-number-*********-*********-stuck-at-ecom-external-email-hi-datatrans-doesn-t-have-any-settings-by-item-we-can-receive-any-po-from-hino-thanks-krista-on-3-24-2025-10-59-am-rajen-gandhi-wrote-hi-krista-can-you-confirm-that-the-incoming-850-has-only-been-setup-for-a-specific-model-truck-or-can-we-receive-a-po-from-hino-for-a-different-part-number-as-well-sincerely-rajen-gandhi-senior-business-analyst-hexagon-purus-systems-t-1-647-886-7685-rajen-gandhi-hexagonpurus-com-hexagonpurus-com-hexagon-purus-systems-canada-ltd-2150-matrix-crescent-kelowna-bc-canada-v1v-0c1-https-urldefense-proofpoint-com-v2-url-u-https-3a__twitter-com_hexagonpurus-d-dwmdaq-c-eugzstcatdllvimen8b7jxrwqof-v5a_cdpgnvfiimm-r-h-dxs7up0k1uwpgfq54jsg9euedrqtf4wtrpn8yfmvo-m-hmtykl2v_qqrljukwgi1l7pjm1dqzsgmig179jchn_xepwxevgncb-infbeu1xoh-s-qdm4jbal8m00ah85cg5n83a3chcw0nbloz2-qt5rvrc-e-https-urldefense-proofpoint-com-v2-url-u-https-3a__de-linkedin-com_company_hexagon-2dpurus-d-dwmdaq-c-eugzstcatdllvimen8b7jxrwqof-v5a_cdpgnvfiimm-r-h-dxs7up0k1uwpgfq54jsg9euedrqtf4wtrpn8yfmvo-m-hmtykl2v_qqrljukwgi1l7pjm1dqzsgmig179jchn_xepwxevgncb-infbeu1xoh-s-nmxviujnsgs6ujgerpld3_4ucog3hzukov2me-ryvqq-e-from-krista-johnson-kjohnson-datatrans-inc-com-sent-tuesday-march-4-2025-10-37-am-to-czischke-brad-czischke-hino-com-rajen-gandhi-rajen-gandhi-hexagonpurus-com-cc-rothey-jerrod-rothey-hino-com-subject-re-fw-error-alert-hino-trucks-sd-interchange-number-*********-*********-stuck-at-ecom-external-email-hi-those-are-997s-they-create-and-send-back-exactly-as-received-so-the-870-that-you-sent-will-have-created-that-i-tried-to-rerun-your-870-again-twice-yesterday-and-it-would-have-created-the-two-997s-the-997-has-to-use-the-same-ids-as-the-document-sent-it-may-be-better-if-you-figure-out-how-to-not-use-hinotrucks1-at-all-krista-on-3-4-2025-7-10-am-czischke-brad-wrote-hi-krista-attached-are-two-files-that-failed-in-translation-on-our-end-were-these-the-two-870-files-that-were-sent-yesterday-after-you-made-the-gs-id-update-please-review-fix-and-resend-i-will-also-work-with-my-dev-team-to-send-over-an-870-file-today-so-you-can-test-the-inbound-870-on-your-side-regards-brad-czischke-senior-manager-it-hino-trucks-a-toyota-group-company-45501-twelve-mile-rd-novi-mi-48377-************-office-************-mobile-czischke-hino-com-from-production-monitoring-prodmon-ecomtoday-com-sent-tuesday-march-4-2025-3-56-am-to-rothey-jerrod-rothey-hino-com-czischke-brad-czischke-hino-com-cc-production-monitoring-prodmon-ecomtoday-com-subject-error-alert-hino-trucks-sd-interchange-number-*********-*********-stuck-at-ecom-caution-this-email-originated-from-outside-of-the-organization-do-not-click-links-or-open-attachments-unless-you-recognize-the-sender-and-know-the-content-is-safe-dear-customer-we-have-received-two-997-interchange-from-your-tp-hexagon-purus-systems-to-you-having-interchange-control-*********-*********-that-have-been-failed-in-translation-at-our-end-due-to-below-currently-we-have-set-the-gs_03-as-hinotrucks-while-in-the-received-interchange-the-value-is-hinotrucks1-for-gs_03">Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Krista Johnson Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified Original estimate: Not Specified
Attachments:  Outlook-fablr211.png      Outlook-Ein Bild, .png      Outlook-i42jp3yo.png      Outlook-4mrqlser.png      Outlook-qzpv2uec.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Copying in DataTrans Support if you have a question still. But I think you&#39;d need to contact Hino Truck directly.
If THEY assign one EDI setup per item then it would be a restriction on their end.
Thanks,
Krista
On 3/24/2025 11:45 AM, Rajen Gandhi wrote:
That is what I thought.
I wonder if they are not able to do send an 850 for another product due to the custom objects / options that they send as well?
From: Krista Johnson Sent: Monday, March 24, 2025 12:02 PM To: Rajen Gandhi Subject: Re: FW: [ERROR ALERT, Hino Trucks]-SD: Interchange Number *********, ********* stuck at ecom
External Email
Hi...
DataTrans doesn&#39;t have any settings by item. We can receive any PO from Hino.
Thanks,
Krista
On 3/24/2025 10:59 AM, Rajen Gandhi wrote:
Hi Krista, Can you confirm that the incoming 850 has only been setup for a specific model truck, or can we receive a PO from Hino for a different part number as well?
Sincerely,
Rajen Gandhi Senior Business Analyst, Hexagon Purus Systems
T 1-647-886-7685
<a href="mailto:<EMAIL>"><EMAIL></a> hexagonpurus.com
Hexagon Purus Systems Canada Ltd. 2150 Matrix Crescent
Kelowna
BC Canada
V1V 0C1
<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__twitter.com_HexagonPurus&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=qdM4jbAl8M00ah85CG5n83A3ChCW0nBLOz2-qT5rVrc&amp;e=">https://urldefense.proofpoint.com/v2/url?u=https-3A__twitter.com_HexagonPurus&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=qdM4jbAl8M00ah85CG5n83A3ChCW0nBLOz2-qT5rVrc&amp;e=</a> <a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__de.linkedin.com_company_hexagon-2Dpurus&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=NMxvIUJnSGs6UjgERPlD3_4UCog3HzuKOv2mE-rYvQQ&amp;e=">https://urldefense.proofpoint.com/v2/url?u=https-3A__de.linkedin.com_company_hexagon-2Dpurus&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=NMxvIUJnSGs6UjgERPlD3_4UCog3HzuKOv2mE-rYvQQ&amp;e=</a>
From: Krista Johnson <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, March 4, 2025 10:37 AM To: Czischke, Brad <a href="mailto:<EMAIL>"><EMAIL></a>; Rajen Gandhi <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Rothey, Jerrod <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: FW: [ERROR ALERT, Hino Trucks]-SD: Interchange Number *********, ********* stuck at ecom External Email
Hi...
Those are 997s. They create and send back exactly as received. So the 870 that you sent will have created that.
I tried to rerun your 870 again twice yesterday and it would have created the two 997s.
The 997 has to use the same IDs as the document sent.
It may be better if you figure out how to not use HINOTRUCKS1 at all.
Krista On 3/4/2025 7:10 AM, Czischke, Brad wrote:
Hi Krista,
Attached are two files that failed in translation on our end. Were these the two 870 files that were sent yesterday after you made the GS ID update? Please review, fix, and resend.
I will also work with my DEV team to send over an 870 file today so you can test the inbound 870 on your side.
Regards , Brad Czischke | Senior Manager, IT
Hino Trucks - A Toyota Group Company
45501 Twelve Mile Rd., Novi, MI 48377
************ Office
************ Mobile
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Production Monitoring <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, March 4, 2025 3:56 AM To: Rothey, Jerrod <a href="mailto:<EMAIL>"><EMAIL></a>; Czischke, Brad <a href="mailto:<EMAIL>"><EMAIL></a> Cc: &#39;Production Monitoring&#39; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: [ERROR ALERT, Hino Trucks]-SD: Interchange Number *********, ********* stuck at ecom
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Dear Customer,
We have received Two 997 Interchange from your TP “ Hexagon Purus Systems” to you having interchange control # *********, ********* that have been failed in translation at our end due to below.
Currently we have set the GS_03 as &#34; HINOTRUCKS &#34;, while in the received interchange the value is &#34; HINOTRUCKS1 &#34; for GS_03.</h2>
<h2 id="isa00-00-zzdts6919-zzhinotrucks-2503031022u00401*********0p-gsfadts6919hinotrucks1202503031022*********x004010-st997*********">ISA<em>00</em> 00 ZZ<em>DTS6919 <em>ZZ</em>HINOTRUCKS <em>250303</em>1022</em>U<em>00401</em>*********<em>0</em>P~
GS<em>FA</em>DTS6919<em>HINOTRUCKS1</em>20250303<em>1022</em>*********<em>X</em>004010
ST<em>997</em>*********</h2>
<p>Please ask your TP to correct and resend the interchange.
Thank You,
E-Com Systems Inc.
Disclaimer
Hino Trucks (“Company”) is an authorized distributor for the following unaffiliated brands: Hino and Tern. This e-mail and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Please note that any views or opinions presented in this e-mail are solely those of the author and do not necessarily represent those of the Company, Hino or Tern. If you have received this e-mail in error, please notify the sender immediately and delete this e-mail from your system. Unless otherwise expressly stated in this message, nothing in this message is intended to constitute an electronic signature. Thank you for your cooperation and understanding.
–
<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__www.datatrans-2Dinc.com_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=EcRvdja1jJsgv359CpaLvyOB2Brtm5D3s_zUmqDBGEs&amp;m=aHdtbla9YmOfCj9aNnKaH0h3xtCGWaXxjYnq6StF-d33EEOL1SnaFaJnCGVb4tmI&amp;s=80vSFKW_lLlYDznpUiSoLPnnB_IkCj-fk_Hwr2S1M3Y&amp;e=">https://urldefense.proofpoint.com/v2/url?u=https-3A__www.datatrans-2Dinc.com_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=EcRvdja1jJsgv359CpaLvyOB2Brtm5D3s_zUmqDBGEs&amp;m=aHdtbla9YmOfCj9aNnKaH0h3xtCGWaXxjYnq6StF-d33EEOL1SnaFaJnCGVb4tmI&amp;s=80vSFKW_lLlYDznpUiSoLPnnB_IkCj-fk_Hwr2S1M3Y&amp;e=</a>
Krista Johnson
Datatrans Solutions : EDI Analyst
p: ************ x 206 w: datatrans-inc.com e: [<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]
<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__www.facebook.com_DataTransSolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=EcRvdja1jJsgv359CpaLvyOB2Brtm5D3s_zUmqDBGEs&amp;m=aHdtbla9YmOfCj9aNnKaH0h3xtCGWaXxjYnq6StF-d33EEOL1SnaFaJnCGVb4tmI&amp;s=hqaOBdBupKNssIqHHegXwfZCMh7IBu4My6aCv39HGvo&amp;e=">https://urldefense.proofpoint.com/v2/url?u=https-3A__www.facebook.com_DataTransSolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=EcRvdja1jJsgv359CpaLvyOB2Brtm5D3s_zUmqDBGEs&amp;m=aHdtbla9YmOfCj9aNnKaH0h3xtCGWaXxjYnq6StF-d33EEOL1SnaFaJnCGVb4tmI&amp;s=hqaOBdBupKNssIqHHegXwfZCMh7IBu4My6aCv39HGvo&amp;e=</a>
<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__www.instagram.com_datatranssolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=EcRvdja1jJsgv359CpaLvyOB2Brtm5D3s_zUmqDBGEs&amp;m=aHdtbla9YmOfCj9aNnKaH0h3xtCGWaXxjYnq6StF-d33EEOL1SnaFaJnCGVb4tmI&amp;s=T8ztcIHRApYCuiRTJ_GgRFfsJ8zrvbyblRARSOAa-cM&amp;e=">https://urldefense.proofpoint.com/v2/url?u=https-3A__www.instagram.com_datatranssolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=EcRvdja1jJsgv359CpaLvyOB2Brtm5D3s_zUmqDBGEs&amp;m=aHdtbla9YmOfCj9aNnKaH0h3xtCGWaXxjYnq6StF-d33EEOL1SnaFaJnCGVb4tmI&amp;s=T8ztcIHRApYCuiRTJ_GgRFfsJ8zrvbyblRARSOAa-cM&amp;e=</a>
<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__twitter.com_datatrans-5Fedi&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=EcRvdja1jJsgv359CpaLvyOB2Brtm5D3s_zUmqDBGEs&amp;m=aHdtbla9YmOfCj9aNnKaH0h3xtCGWaXxjYnq6StF-d33EEOL1SnaFaJnCGVb4tmI&amp;s=vw-DTUDHFq3vwS6Ey5hBspt9WQtlAN5JvBA4hyNOR5I&amp;e=">https://urldefense.proofpoint.com/v2/url?u=https-3A__twitter.com_datatrans-5Fedi&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=EcRvdja1jJsgv359CpaLvyOB2Brtm5D3s_zUmqDBGEs&amp;m=aHdtbla9YmOfCj9aNnKaH0h3xtCGWaXxjYnq6StF-d33EEOL1SnaFaJnCGVb4tmI&amp;s=vw-DTUDHFq3vwS6Ey5hBspt9WQtlAN5JvBA4hyNOR5I&amp;e=</a>
<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__www.linkedin.com_company_datatrans-2Dsolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=EcRvdja1jJsgv359CpaLvyOB2Brtm5D3s_zUmqDBGEs&amp;m=aHdtbla9YmOfCj9aNnKaH0h3xtCGWaXxjYnq6StF-d33EEOL1SnaFaJnCGVb4tmI&amp;s=hL0H_uBYfmdjplRndiG6sAcXi59S36nBOVHsq2yqcp0&amp;e=">https://urldefense.proofpoint.com/v2/url?u=https-3A__www.linkedin.com_company_datatrans-2Dsolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=EcRvdja1jJsgv359CpaLvyOB2Brtm5D3s_zUmqDBGEs&amp;m=aHdtbla9YmOfCj9aNnKaH0h3xtCGWaXxjYnq6StF-d33EEOL1SnaFaJnCGVb4tmI&amp;s=hL0H_uBYfmdjplRndiG6sAcXi59S36nBOVHsq2yqcp0&amp;e=</a>
Hexagon Purus Privacy Policy and Email Disclaimer
Hexagon Purus Privacy Policy Hexagon Purus Email Disclaimer
–
<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__www.datatrans-2Dinc.com_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=HkDvZzb3Y738CIB1vB5ywwk5vVAwQfTHWEk4HlFGba8&amp;e=">https://urldefense.proofpoint.com/v2/url?u=https-3A__www.datatrans-2Dinc.com_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=HkDvZzb3Y738CIB1vB5ywwk5vVAwQfTHWEk4HlFGba8&amp;e=</a>
Krista Johnson
Datatrans Solutions : EDI Analyst
p: ************ x 206 w: datatrans-inc.com e: [<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]
&lt;<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__www.facebook.com_DataTransSolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=nPJve8ei9MSWJ-">https://urldefense.proofpoint.com/v2/url?u=https-3A__www.facebook.com_DataTransSolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=nPJve8ei9MSWJ-</a>
qJyRAbfvr3zXczFPGl6EFzSoaZnCM&amp;e=&gt;  &lt;<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__www.instagram.com_datatranssolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-">https://urldefense.proofpoint.com/v2/url?u=https-3A__www.instagram.com_datatranssolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-</a>
Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=MlU0vrnmog-RYWQ-Pd-o-io1aPg15Qi7zr7XcNyfu9Y&amp;e=&gt;
<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__twitter.com_datatrans-5Fedi&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=x0tTN8f4HbTX4rIXEF8uMLuRX4P-LknI9hmoahQM124&amp;e=">https://urldefense.proofpoint.com/v2/url?u=https-3A__twitter.com_datatrans-5Fedi&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=x0tTN8f4HbTX4rIXEF8uMLuRX4P-LknI9hmoahQM124&amp;e=</a>
<a href="https://urldefense.proofpoint.com/v2/url?u=https-3A__www.linkedin.com_company_datatrans-2Dsolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=p7QsLBrluLlMcMvr7pKm-6iDo8dvnfYM9SHcvBbluv0&amp;e=">https://urldefense.proofpoint.com/v2/url?u=https-3A__www.linkedin.com_company_datatrans-2Dsolutions_&amp;d=DwMDaQ&amp;c=euGZstcaTDllvimEN8b7jXrwqOf-v5A_CdpgnVfiiMM&amp;r=H-Dxs7up0k1UwpGfq54Jsg9EUEDrQTF4wtrPn8YFMvo&amp;m=HmtYKL2V_QqRLJUkwgI1L7pjM1DqZSGMiG179jchN_XepwxeVgNcb-INFbeu1XoH&amp;s=p7QsLBrluLlMcMvr7pKm-6iDo8dvnfYM9SHcvBbluv0&amp;e=</a>
Hexagon Purus Privacy Policy and Email Disclaimer
Hexagon Purus Privacy Policy Hexagon Purus Email Disclaimer
–
<a href="https://www.datatrans-inc.com/">https://www.datatrans-inc.com/</a>
Krista Johnson
Datatrans Solutions : EDI Analyst
p: ************ x 206 w: datatrans-inc.com e: [<a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>">mailto:<EMAIL></a>]
<a href="https://www.facebook.com/DataTransSolutions/">https://www.facebook.com/DataTransSolutions/</a>  <a href="https://www.instagram.com/datatranssolutions/">https://www.instagram.com/datatranssolutions/</a>  <a href="https://twitter.com/datatrans_edi">https://twitter.com/datatrans_edi</a>  <a href="https://www.linkedin.com/company/datatrans-solutions/">https://www.linkedin.com/company/datatrans-solutions/</a>
Comments
Comment by Krista Johnson [ 24/Mar/25 ]
Comment by Rajen Gandhi [ 24/Mar/25 ]
Is it possible for us to setup multiple 850&#39;s to allow for different custom object / options with the same partner?
Not ideal - but wanted to clarify if it is possible.
From: Krista Johnson
Comment by Krista Johnson [ 24/Mar/25 ]
You are ending the 850? You can send any/all items that are for Hino Trucks unless they tell you that they can only have one per order. This would be a conversation you&#39;d need to have with them directly.
Krista</p>
<h4 id="cs-41268-dts-notice-created-25-mar-25-updated-27-mar-25-resolved-27-mar-25">[CS-41268] DTS Notice Created: 25/Mar/25  Updated: 27/Mar/25  Resolved: 27/Mar/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Comments
Comment by Nicholas Sanchez [ 25/Mar/25 ]
DataTrans continues to experience an intermittent issue that is impacting the ability to log into the WebEDI portal. Please use the following URL to login <a href="https://datatrans-inc.com/login/">https://datatrans-inc.com/login/</a> . This is also impacting the delivery of inbound and outbound integrated files going through FTP/SFTP. Our team is working to restore service. No details available at this time.We currently do not have an ETA, but we will send out periodic updates. Thank you for your patience.
Nicolas Sanchez DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Nicholas Sanchez [ 26/Mar/25 ]
Hello Spenser,
Our SFTP service is back up and running. Please let me know if you are still not able to access our server.
Nicolas Sanchez DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-41374-re-inc17472096-acknowledgement-not-received-for-po-3508040-3508040-edi-850-007347602dco-created-26-mar-25-updated-27-mar-25-resolved-27-mar-25">[CS-41374] RE: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO) Created: 26/Mar/25  Updated: 27/Mar/25  Resolved: 27/Mar/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image003.png      image005.png      image006.png      image009.png      image010.png      image003 (2e6b1f2e-7601-4838-bebb-65c9960f7e90).png      image001 (fb9ee5d4-80eb-4051-aadb-24f8537aa83f).png      image001 (d442f7a2-27f4-4250-973c-bae54da8151a).png      image002.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi @<a href="mailto:<EMAIL>"><EMAIL></a>
Can you please assist with the PO ACK for the 850 orders 3508040 ?
Thanks,
Vishal Agarwal
From: Cody Worthy <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, March 26, 2025 9:34 AM To: Sharma, Snehwardhan - Cognizant <a href="mailto:<EMAIL>"><EMAIL></a>; SEDCSupport <a href="mailto:<EMAIL>"><EMAIL></a>; Chas Worthy <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Wyzard, Nanishka Colette <a href="mailto:<EMAIL>"><EMAIL></a>; Wyzard, Nanishka Colette <a href="mailto:<EMAIL>"><EMAIL></a>; DIST-wMSupport <a href="mailto:<EMAIL>"><EMAIL></a>; GM-HVODC <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO)
<em>External - Potential security risk - Exercise caution</em>
@<a href="mailto:<EMAIL>"><EMAIL></a> Can you please send the raw EDI data?
Thanks,
Cody Worthy Operations Engineer
1700 Central Plank Rd
Wetumpka, AL 36092
334.541.4070 | c: 334.557.6880
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.TheOhSoCo.com">www.TheOhSoCo.com</a>
<a href="http://www.BahamaBos.com">www.BahamaBos.com</a>
<a href="http://www.WorthyPromo.com">www.WorthyPromo.com</a>
From: Sharma, Snehwardhan - Cognizant <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, March 26, 2025 1:41 AM To: Cody Worthy <a href="mailto:<EMAIL>"><EMAIL></a>; SEDCSupport <a href="mailto:<EMAIL>"><EMAIL></a>; Chas Worthy <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Wyzard, Nanishka Colette <a href="mailto:<EMAIL>"><EMAIL></a>; Wyzard, Nanishka Colette <a href="mailto:<EMAIL>"><EMAIL></a>; DIST-wMSupport <a href="mailto:<EMAIL>"><EMAIL></a>; GM-HVODC <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO)
Hi @Cody Worthy
We have not received it yet, can you please share raw EDI data to check further.
Thanks
Snehwardhan
From: Cody Worthy <a href="mailto:<EMAIL>"><EMAIL></a> Sent: 25 March 2025 19:47 To: SEDCSupport <a href="mailto:<EMAIL>"><EMAIL></a>; Chas Worthy <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Wyzard, Nanishka Colette <a href="mailto:<EMAIL>"><EMAIL></a>; Wyzard, Nanishka Colette <a href="mailto:<EMAIL>"><EMAIL></a>; DIST-wMSupport <a href="mailto:<EMAIL>"><EMAIL></a>; GM-HVODC <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO)
<em>External - Potential security risk - Exercise caution</em>
Good Morning,
855 has been sent.
Thanks,
Cody Worthy Operations Engineer
1700 Central Plank Rd
Wetumpka, AL 36092
334.541.4070 | c: 334.557.6880
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.TheOhSoCo.com">www.TheOhSoCo.com</a>
<a href="http://www.BahamaBos.com">www.BahamaBos.com</a>
<a href="http://www.WorthyPromo.com">www.WorthyPromo.com</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, March 24, 2025 11:01 PM To: Chas Worthy <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; Cody Worthy <a href="mailto:<EMAIL>"><EMAIL></a> Cc: <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO)
Dear Trading Partner,
Can you please help with the EDI 855 PO acknowledgement against below orders? Please find the below table for reference.
SEDC EDI 855 Reconciliation Report for EDI_850-PurchaseOrder transactions
PO_NUMBER PARTNER_NAME TRANSACTION_DATE
3508040 WORTHY PROMOTIONAL PRODUCTS LLC 2025-03-20 20:15:10.0
3508040 WORTHY PROMOTIONAL PRODUCTS LLC 2025-03-20 20:15:09.0
Please find an envelope for reference:
PO#3508040 - ISA<em>00</em> 00 ZZ<em>007347602DCO <em>ZZ</em>DTS3371 <em>250320</em>2015</em>U<em>00501</em>149152020<em>0</em>P^<del>GS<em>PO</em>007347602DCO<em>DTS3371</em>20250320<em>2015</em>150152020<em>X</em>005010</del>ST<em>850</em>202015148<del>BEG<em>00</em>SA<em>3508040</em>20250320</del>CUR
PO#3508040 - ISA<em>00</em> 00 ZZ<em>007347602DCO <em>ZZ</em>DTS3371 <em>250320</em>2015</em>U<em>00501</em>149152020<em>0</em>P^<del>GS<em>PO</em>007347602DCO<em>DTS3371</em>20250320<em>2015</em>150152020<em>X</em>005010</del>ST<em>850</em>202015148<del>BEG<em>00</em>SA<em>3508040</em>20250320</del>CUR
Regards, 7-Eleven RDC wM Support Team
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 26/Mar/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Mar/25 ]
@Cody Worthy
We are not able to locate any 855 against the Po# 3508040. Can you please cross check at your earliest convenience?
Thanks,
Vishal Agarwal
Comment by Nicholas Sanchez [ 27/Mar/25 ]
Vishal,
Can you confirm if this is a valid ISA Qual/ID for 7-11? 14/007347602SLND
Nicolas Sanchez DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Mar/25 ]
Hi @<a href="mailto:<EMAIL>"><EMAIL></a>
You got to send the PO ACK 855 to the same ISA under which you have received the PO / 850. The EDI transmission is already live since a YEAR now. Why are you asking the EDI ID now ?
The ISA IS “007347602DCO”
Thanks,
Vishal Agarwal
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Mar/25 ]
Looping in @Wyzard, Nanishka Colette &amp; @Cody Worthy as well.
Thank you,
John Sink
Sr. Demand Chain Analyst
************ | <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Nicholas Sanchez [ 27/Mar/25 ]
Vishal,
I understand the ISA ID used for the 855 has to be the same as the PO and will make the corrections to the 855. I am looking into the issue that caused the 855 to not be sent and I would like for you to confirm if ISA 007347602SLND is valid or not.
Nicolas Sanchez DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Mar/25 ]
Here are the ISA details :
007347602DCO : used for the RDC / SEDC
007347602SLND : DSD 7-Eleven RIS Stores
Thanks,
Vishal Agarwal
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Mar/25 ]
@Cody Worthy,
Can you please send us an 855 for PO #3508040? Pick up date is showing 3/28/25.
Thank you!
Nanishka C. Wyzard | 7 - Eleven, Inc.
Sr. Replenishment Analyst | Value Chain
705 Bradburn PL | Stafford, VA | 22554
((Office): ************ | ( Mobile): ************
<em>: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Sink, John Thomas <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, March 27, 2025 12:50 PM To: Agarwal, Vishal - Cognizant <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; Wyzard, Nanishka Colette <a href="mailto:<EMAIL>"><EMAIL></a>; Cody Worthy <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE:  CS-41374 RESOLVED  RE: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO)
Looping in @Wyzard, Nanishka Colette &amp; @Cody Worthy as well.
Thank you,
John Sink
Sr. Demand Chain Analyst
************ | <a href="mailto:<EMAIL>"><EMAIL></a>
From: Agarwal, Vishal - Cognizant <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, March 27, 2025 11:45 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Sink, John Thomas <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE:  CS-41374 RESOLVED  RE: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO)
Hi @<a href="mailto:<EMAIL>"><EMAIL></a>
You got to send the PO ACK 855 to the same ISA under which you have received the PO / 850. The EDI transmission is already live since a YEAR now. Why are you asking the EDI ID now ?
The ISA IS “007347602DCO”
Thanks,
Vishal Agarwal
From: Nicholas Sanchez <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, March 27, 2025 11:00 AM To: Agarwal, Vishal - Cognizant <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-41374 RESOLVED  RE: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO)
<em>External - Potential security risk - Exercise caution</em>
———-—
Reply above this line.
Nicholas Sanchez commented:
Vishal,
Can you confirm if this is a valid ISA Qual/ID for 7-11? 14/007347602SLND
Nicolas Sanchez DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
View request · Turn off this request&#39;s notifications
Sent on March 27, 2025 9:59:42 AM MDT
Comment by Nicholas Sanchez [ 27/Mar/25 ]
Vishal,
We have corrected the ISA IDs used for the 855s. Please let me know if there is anything else I can assist with
ISA</em>00* 00 ZZ<em>DTS3371 <em>ZZ</em>007347602DCO <em>250327</em>1650</em>U<em>00501</em>000000256<em>0</em>P^
Nicolas Sanchez DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-41333-re-inc17472096-acknowledgement-not-received-for-po-3508040-3508040-edi-850-007347602dco-created-26-mar-25-updated-27-mar-25-resolved-27-mar-25">[CS-41333] RE: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO) Created: 26/Mar/25  Updated: 27/Mar/25  Resolved: 27/Mar/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Nicholas Sanchez Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image003.png      image005.png      image006.png      image006 (548fc48a-8382-4deb-afcb-12459477ce19).png      image001 (7d2111a6-7a20-4dc2-ba62-588214d3d63a).png      image005 (a87cf4f3-1cbf-4e29-beef-748e1d3eddfe).png      image003 (378903f9-7df8-4e3d-8c58-c98efe88809d).png      image-20250326-181004.png      image-20250326-180745.png      image-20250326-181109.png      image-20250326-184733.png      image-20250326-184012.png      image-20250326-183807.png      image-20250326-183020.png      image-20250326-184200.png      image-20250326-182834.png      image-20250326-183459.png      image-20250326-183047.png      image-20250326-184635.png      7-Eleven_EDI_X12_855_SEDC_ITEM_PURCHASE_ORDER_ACKNOWLEDGEMENT_SPEC_V1.0.doc     image-20250326-193008.png      image-20250326-194426.png      image-20250326-194447.png      image-20250326-194617.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi @Cody Worthy
We have not received it yet, can you please share raw EDI data to check further.
Thanks
Snehwardhan
From: Cody Worthy <a href="mailto:<EMAIL>"><EMAIL></a> Sent: 25 March 2025 19:47 To: SEDCSupport <a href="mailto:<EMAIL>"><EMAIL></a>; Chas Worthy <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Wyzard, Nanishka Colette <a href="mailto:<EMAIL>"><EMAIL></a>; Wyzard, Nanishka Colette <a href="mailto:<EMAIL>"><EMAIL></a>; DIST-wMSupport <a href="mailto:<EMAIL>"><EMAIL></a>; GM-HVODC <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO)
<em>External - Potential security risk - Exercise caution</em>
Good Morning,
855 has been sent.
Thanks,
Cody Worthy Operations Engineer
1700 Central Plank Rd
Wetumpka, AL 36092
334.541.4070 | c: 334.557.6880
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.TheOhSoCo.com">www.TheOhSoCo.com</a>
<a href="http://www.BahamaBos.com">www.BahamaBos.com</a>
<a href="http://www.WorthyPromo.com">www.WorthyPromo.com</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, March 24, 2025 11:01 PM To: Chas Worthy <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; Cody Worthy <a href="mailto:<EMAIL>"><EMAIL></a> Cc: <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: INC17472096 | Acknowledgement not received for PO#3508040,3508040- EDI 850 (007347602DCO)
Dear Trading Partner,
Can you please help with the EDI 855 PO acknowledgement against below orders? Please find the below table for reference.
SEDC EDI 855 Reconciliation Report for EDI_850-PurchaseOrder transactions
PO_NUMBER PARTNER_NAME TRANSACTION_DATE
3508040 WORTHY PROMOTIONAL PRODUCTS LLC 2025-03-20 20:15:10.0
3508040 WORTHY PROMOTIONAL PRODUCTS LLC 2025-03-20 20:15:09.0
Please find an envelope for reference:
PO#3508040 - ISA<em>00</em> 00 ZZ<em>007347602DCO <em>ZZ</em>DTS3371 <em>250320</em>2015</em>U<em>00501</em>149152020<em>0</em>P^<del>GS<em>PO</em>007347602DCO<em>DTS3371</em>20250320<em>2015</em>150152020<em>X</em>005010</del>ST<em>850</em>202015148<del>BEG<em>00</em>SA<em>3508040</em>20250320</del>CUR
PO#3508040 - ISA<em>00</em> 00 ZZ<em>007347602DCO <em>ZZ</em>DTS3371 <em>250320</em>2015</em>U<em>00501</em>149152020<em>0</em>P^<del>GS<em>PO</em>007347602DCO<em>DTS3371</em>20250320<em>2015</em>150152020<em>X</em>005010</del>ST<em>850</em>202015148<del>BEG<em>00</em>SA<em>3508040</em>20250320</del>CUR
Regards, 7-Eleven RDC wM Support Team
Comments  Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 26/Mar/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 26/Mar/25 ]
@<a href="mailto:<EMAIL>"><EMAIL></a> Can you please send the raw EDI data?
Thanks,
Cody Worthy Operations Engineer
1700 Central Plank Rd
Wetumpka, AL 36092</p>
