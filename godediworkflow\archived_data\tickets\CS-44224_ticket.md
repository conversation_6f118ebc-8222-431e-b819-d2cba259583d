# CS-44224: Re: MSC EDI - v# 29360 Petol Gearench *Errored Docs*

## Ticket Information
- **Key**: CS-44224
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-05-22T10:51:15.757-0600
- **Updated**: 2025-06-02T10:16:50.763-0600
- **Customer**: Supply Co

## Description
{color:#172B4D} *Hi Laramie,*{color} 
{color:#172B4D} I've looped in{color} [{color:#172B4D} @<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D} since they handle all production issues. They will loop us in if they need help with anything.{color}{color:#172B4D}{color}   
  
  {color:#172B4D}*Hi Support Team,*{color}  
{color:#172B4D} Here is the issue reported by the customer. Please refer to the email below for additional information.{color}{color:#172B4D}{color}   
  
  {color:#172B4D}_"We are having an issue with 856 transactions erroring. Normally when documents error, they go to the draft folder in WebEDI. However, I do not see all of these in that folder (See below). Is there anything that we can do so that these 856 transactions will be accepted?"_{color} 
|
|
|
|{color:#000001}{color}
|{color:#000001}Jennifer{color}{color:#000001}{color}|{color:#000001}  \\{color}{color:#000001}{color}|{color:#000001}Martin{color}{color:#000001}{color}|{color:#000001}{color}
|{color:#000001}{color}
|{color:#000001}Cleo{color}{color:#000001}{color}|{color:#000001} :  \\{color}{color:#000001}{color}|{color:#000001}Project Manager{color}{color:#000001}{color}|{color:#000001}{color}
|
|
|
| | | {color:#000001}{color}
|{color:#000001}{color}{color:#000001}
{color}{color:#000001}{color}
|{color:#000001}Email: {color}{color:#000001}{color}|{color:#000001}{color}[{color:#000001}{color}{color:#000001}<EMAIL>{color}|mailto:<EMAIL>]{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}|{color:#000001}{color}
{color:#000001}{color}{color:#000001}{color}{color:#000001} | {color}{color:#000001}{color}{color:#000001}
Web: {color}{color:#000001}{color}{color:#000001}{color}[{color:#000001}{color}{color:#000001}www.cleo.com{color}|https://www.cleo.com/]{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}
| | | {color:#0083CA}{color}
|{color:#0083CA}{color}{color:#0083CA}{color}
|{color:#0083CA}{color}[{color:#0083CA}{color}{color:#0083CA}Join us at one of our upcoming events. Check out the list!{color}|https://www.cleo.com/events]{color:#0083CA}{color}{color:#0083CA} \\{color}{color:#0083CA}{color}|{color:#0083CA}{color} 
----
 
 {color:#172B4D}*From:* Laramie Aars <<EMAIL>>
  *Sent:* Thursday, May 22, 2025 10:46 AM
  *To:* Martin, Jennifer <<EMAIL>>
  *Cc:* Roberts, Kenrick <<EMAIL>>
  *Subject:* FW: MSC EDI - v# 29360 Petol Gearench *Errored Docs*{color}     
 *{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}
 
 
 
  

Good Day,  

  

We are having an issue with 856 transactions erroring. Normally when documents error, they go to the draft folder in WebEDI. However, I do not see all of these in that folder (See below). Is there anything that we can do so that these 856 transactions will be accepted? 

  

Also, some of the documents that SPS is showing are errored are showing accepted in WebEDI. Below is one example.  

  

!image013.png|thumbnail! 

!image014.png|thumbnail! 

  

Please advise if there is anything I can change on our end to fix this.  

   

 *{color:#172B4D} *Laramie Aars*{color}* 

Inside Sales Associate 

[{color:#0563C1}<EMAIL>{color}|mailto:<EMAIL>] I [{color:#0563C1}www.petol.com{color}|www.petol.com] 

Office: ************ 

Fax: ************ 

  

!image006.png|thumbnail! 

  

   

  
  

 *From:* Jaycee Simolin <<EMAIL>> 
  *Sent:* Thursday, May 22, 2025 10:36 AM
  *To:* Laramie Aars <<EMAIL>>
  *Subject:* RE: MSC EDI - v# 29360 Petol Gearench *Errored Docs*   

  

[@Laramie Aars|mailto:<EMAIL>] Good morning! Are you able to advise about correcting/resending these errored docs? 

  

!image001.png|thumbnail! 

   

 *{color:#201F1E} *Sincerely,*{color}*{color:#201F1E}{color} 

 *{color:#201F1E} {color}*{color:#201F1E}{color} 

 *{color:#7030A0} *Jaycee Simolin*{color}*{color:#201F1E}{color} 

 *_{color:#201F1E} *_Associate Product Manager_*{color}_* 

 *_{color:#201F1E} *_EDI Fulfillment_*{color}_*{color:#201F1E}{color} 

 *{color:#201F1E} *MSC Industrial Supply Co.*{color}*{color:#201F1E}{color} 

{color:#201F1E}525 Harbour Place Drive{color}  

{color:#201F1E}Davidson, NC 28036{color}{color:#201F1E}{color} 

{color:#201F1E}(************* phone{color}{color:#201F1E}{color}  

 {color:#954F72} +{color}[{color:#954F72} {color}{color:#954F72} +www.mscdirect.com+{color}{color:#954F72} {color}|http://www.mscdirect.com/]{color:#954F72} {color}+{color:#201F1E}{color} 

 *{color:#201F1E} {color}*{color:#201F1E}{color}{color:#172B4D}{color} 

{color:#172B4D}{color}{color:#172B4D}{color}!image002.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[http://twitter.com/#!/MSC_Industrial]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image003.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image004.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[http://www.linkedin.com/company/24034]>{color}{color:#201F1E}{color}{color:#172B4D}{color} 

!image005.png|thumbnail!{color:#172B4D}{color}{color:#201F1E}{color}{color:#201F1E}{color}   

  
  

 *From:* Jaycee Simolin 
  *Sent:* Tuesday, May 13, 2025 10:49 AM
  *To:* Laramie Aars <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* RE: MSC EDI - v# 29360 Petol Gearench *Errored Docs*   

  

I unfortunately do not as it sounds like you need to find a systematic way to send back “each” for those items. I’m not sure if SPS Support could help? 

  

 *{color:#201F1E} *Sincerely,*{color}*{color:#201F1E}{color} 

 *{color:#201F1E} {color}*{color:#201F1E}{color} 

 *{color:#7030A0} *Jaycee Simolin*{color}*{color:#201F1E}{color} 

 *_{color:#201F1E} *_Associate Product Manager_*{color}_* 

 *_{color:#201F1E} *_EDI Fulfillment_*{color}_*{color:#201F1E}{color} 

 *{color:#201F1E} *MSC Industrial Supply Co.*{color}*{color:#201F1E}{color} 

{color:#201F1E}525 Harbour Place Drive{color}  

{color:#201F1E}Davidson, NC 28036{color}{color:#201F1E}{color} 

{color:#201F1E}(************* phone{color}{color:#201F1E}{color}  

 {color:#954F72} +{color}[{color:#954F72} {color}{color:#954F72} +www.mscdirect.com+{color}{color:#954F72} {color}|http://www.mscdirect.com/]{color:#954F72} {color}+{color:#201F1E}{color} 

 *{color:#201F1E} {color}*{color:#201F1E}{color}{color:#172B4D}{color} 

{color:#172B4D}{color}{color:#172B4D}{color}!image002.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[http://twitter.com/#!/MSC_Industrial]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image003.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[http://www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image004.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[http://www.linkedin.com/company/24034]>{color}{color:#201F1E}{color}{color:#172B4D}{color} 

!image005.png|thumbnail!{color:#172B4D}{color}{color:#201F1E}{color}{color:#201F1E}{color}  

   

 *From:* Laramie Aars <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Monday, May 12, 2025 4:00 PM
  *To:* Jaycee Simolin <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* EXTERNAL: RE: MSC EDI - v# 29360 Petol Gearench *Errored Docs*  

   

{color:white}Thank you! Unfortunately, I have to manually complete those 855 and 856 transactions because the EDI transactions for those parts with an UOM of “set” keep erroring. Do you have any advise on how to fix this so the docs do not error? Laramie{color}   

{color:white}ZjQcmQRYFpfptBannerStart{color} 
| 
| 
|  *{color:#172B4D} *This Message Is From an External Sender*{color}* | 
| {color:#172B4D}This e-mail originated outside of MSC. \\ Exercise caution before clicking links or opening attachments{color} | 
  

{color:white}ZjQcmQRYFpfptBannerEnd{color}  

Thank you! 

  

Unfortunately, I have to manually complete those 855 and 856 transactions because the EDI transactions for those parts with an UOM of “set” keep erroring. Do you have any advise on how to fix this so the docs do not error? 

   

 *{color:#172B4D} *Laramie Aars*{color}* 

Inside Sales Associate 

[{color:#0563C1}<EMAIL>{color}|mailto:<EMAIL>] I [{color:#0563C1}www.petol.com{color}|https://urldefense.com/v3/__http:/www.petol.com__;!!G_S2aQPGww!2YBZEXnCaN753eb1sqzQx4n1inhWmzbKfC-HHT5JTyWka007WNKfA1A125DJem1kWBVgILqR85638Q$] 

Office: ************ 

Fax: ************ 

  

!image006.png|thumbnail! 

  

   

  
  

 *From:* Jaycee Simolin <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Monday, May 12, 2025 2:52 PM
  *To:* Laramie Aars <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* RE: MSC EDI - v# 29360 Petol Gearench *Errored Docs*   

  

Sending back “each” as the UOM is just fine as that is how we have it set up in our system.  

  

Got that corrected one you sent for PO# 6318660001 and all is good 😊 

  

 *{color:#201F1E} *Sincerely,*{color}* 

 *{color:#201F1E} {color}* 

 *{color:#7030A0} *Jaycee Simolin*{color}* 

 *_{color:#201F1E} *_Associate Product Manager_*{color}_* 

 *_{color:#201F1E} *_EDI Fulfillment_*{color}_* 

 *{color:#201F1E} *MSC Industrial Supply Co.*{color}* 

{color:#201F1E}525 Harbour Place Drive{color}  

{color:#201F1E}Davidson, NC 28036{color} 

{color:#201F1E}(************* phone{color}  

 {color:#954F72} +{color}[{color:#954F72} {color}{color:#954F72} +www.mscdirect.com+{color}{color:#954F72} {color}|http://www.mscdirect.com/]{color:#954F72} {color}+ 

 *{color:#201F1E} {color}*{color:#172B4D}{color} 

{color:#172B4D}{color}{color:#172B4D}{color}!image002.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!2YBZEXnCaN753eb1sqzQx4n1inhWmzbKfC-HHT5JTyWka007WNKfA1A125DJem1kWBVgILrIXliohQ$]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image003.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts__;!!G_S2aQPGww!2YBZEXnCaN753eb1sqzQx4n1inhWmzbKfC-HHT5JTyWka007WNKfA1A125DJem1kWBVgILr7pv2B2g$]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image004.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!2YBZEXnCaN753eb1sqzQx4n1inhWmzbKfC-HHT5JTyWka007WNKfA1A125DJem1kWBVgILrlBsiyTw$]>{color}{color:#172B4D}{color} 

!image005.png|thumbnail!{color:#172B4D}{color}{color:#201F1E}{color}  

   

 *From:* Laramie Aars <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Monday, May 12, 2025 12:07 PM
  *To:* Jaycee Simolin <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* EXTERNAL: RE: MSC EDI - v# 29360 Petol Gearench *Errored Docs*  

   

{color:white}Jaycee, I it seems that most of the errors are occurring when a FTS28, FTS212, or FTS1426 is purchased. I think this is due to these items being a “set” and not sold per “each”. It doesn’t give me the option to{color}   

{color:white}ZjQcmQRYFpfptBannerStart{color} 
| 
| 
|  *{color:#172B4D} *This Message Is From an External Sender*{color}* | 
| {color:#172B4D}This e-mail originated outside of MSC. \\ Exercise caution before clicking links or opening attachments{color} | 
  

{color:white}ZjQcmQRYFpfptBannerEnd{color}  

Jaycee,  

  

I it seems that most of the errors are occurring when a FTS28, FTS212, or FTS1426 is purchased. I think this is due to these items being a “set” and not sold per “each”. It doesn’t give me the option to select “set” in DataTrans. When I asked the EDI analyst about it, they said that SPS does not allow for “set” to be selected. Do you have any advice on this? We cannot change our part number, so it seems this is going to keep happening. I just received another that errored this morning, for what looks like the same reason.  

  

I am going to select “each” when I resend 6318660001. 

   

 *{color:#172B4D} *Laramie Aars*{color}* 

Inside Sales Associate 

[{color:#0563C1}<EMAIL>{color}|mailto:<EMAIL>] I [{color:#0563C1}www.petol.com{color}|https://urldefense.com/v3/__http:/www.petol.com__;!!G_S2aQPGww!0oUGQHQj1tEkQbpXZR_9qlPK7T87mO_xhjJywN_NxJzlft7hI1GUO5L3ZbLkvZ1_1rliTspPcNZD_w$] 

Office: ************ 

Fax: ************ 

  

!image006.png|thumbnail! 

  

   

  
  

 *From:* Jaycee Simolin <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Monday, May 12, 2025 7:17 AM
  *To:* Laramie Aars <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* RE: MSC EDI - v# 29360 Petol Gearench *Errored Docs*   

  

[@Laramie Aars|mailto:<EMAIL>] Good morning! The 855 for PO# 6318660001 shows errored in SPS’ system – attached is the error notification you were sent on 5/1 – can you check it out please? 

  

  

  

!image007.png|thumbnail! 

  

 *{color:#201F1E} *Sincerely,*{color}* 

 *{color:#201F1E} {color}* 

 *{color:#7030A0} *Jaycee Simolin*{color}* 

 *_{color:#201F1E} *_Associate Product Manager_*{color}_* 

 *_{color:#201F1E} *_EDI Fulfillment_*{color}_* 

 *{color:#201F1E} *MSC Industrial Supply Co.*{color}* 

{color:#201F1E}525 Harbour Place Drive{color}  

{color:#201F1E}Davidson, NC 28036{color} 

{color:#201F1E}(************* phone{color}  

[{color:#954F72}www.mscdirect.com{color}|http://www.mscdirect.com/] 

 *{color:#201F1E} {color}*{color:#172B4D}{color} 

{color:#172B4D}{color}{color:#172B4D}{color}!image002.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!0oUGQHQj1tEkQbpXZR_9qlPK7T87mO_xhjJywN_NxJzlft7hI1GUO5L3ZbLkvZ1_1rliTsqEswX0NA$]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image003.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts__;!!G_S2aQPGww!0oUGQHQj1tEkQbpXZR_9qlPK7T87mO_xhjJywN_NxJzlft7hI1GUO5L3ZbLkvZ1_1rliTsoJEjNOSg$]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image004.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!0oUGQHQj1tEkQbpXZR_9qlPK7T87mO_xhjJywN_NxJzlft7hI1GUO5L3ZbLkvZ1_1rliTsrMheCjzQ$]>{color}{color:#172B4D}{color} 

!image005.png|thumbnail!{color:#172B4D}{color}{color:#201F1E}{color}  

   

 *From:* Laramie Aars <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Friday, May 9, 2025 5:16 PM
  *To:* Jaycee Simolin <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* EXTERNAL: RE: MSC EDI - v# 29360 Petol Gearench *Errored Docs*  

   

{color:white}Jaycee, All should now be submitted. The 855 for 6318660001 was already showing as accepted on my end. I did submit the 856 transactions for the three in yellow. Let me know if anything else is needed! Have a good weekend! Laramie Aars Inside{color}   

{color:white}ZjQcmQRYFpfptBannerStart{color} 
| 
| 
|  *{color:#172B4D} *This Message Is From an External Sender*{color}* | 
| {color:#172B4D}This e-mail originated outside of MSC. \\ Exercise caution before clicking links or opening attachments{color} | 
  

{color:white}ZjQcmQRYFpfptBannerEnd{color}  

Jaycee,  

  

All should now be submitted. The 855 for 6318660001 was already showing as accepted on my end. I did submit the 856 transactions for the three in yellow. Let me know if anything else is needed! Have a good weekend! 

  

!image008.png|thumbnail! 

  

  

!image009.png|thumbnail! 

   

 *{color:#172B4D} *Laramie Aars*{color}* 

Inside Sales Associate 

[{color:#0563C1}<EMAIL>{color}|mailto:<EMAIL>] I [{color:#0563C1}www.petol.com{color}|https://urldefense.com/v3/__http:/www.petol.com__;!!G_S2aQPGww!3w-xUdcPTD-tXjYfY5nP6Z6qd2UkGYwVScSk1NGQHFb_3MEMS0-n7S0ZCTEcYeUDpryfVHBQ5FGJ4w$] 

Office: ************ 

Fax: ************ 

  

!image006.png|thumbnail! 

  

   

  
  

 *From:* Jaycee Simolin <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Tuesday, May 6, 2025 6:26 AM
  *To:* Laramie Aars <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* RE: MSC EDI - v# 29360 Petol Gearench *Errored Docs*   

  

Hey Laramie! So this is odd. For the ones in GREEN, if I remove the leading zero and just type in the 6 digit number I do see a successful copy of the doc sent. So those are good.  

  

I’m still not seeing anything for those in YELLOW though if you could please advise? 

  

  

!image010.png|thumbnail! 

 *{color:#201F1E} *Sincerely,*{color}* 

 *{color:#201F1E} {color}* 

 *{color:#7030A0} *Jaycee Simolin*{color}* 

 *_{color:#201F1E} *_Associate Product Manager_*{color}_* 

 *_{color:#201F1E} *_EDI Fulfillment_*{color}_* 

 *{color:#201F1E} *MSC Industrial Supply Co.*{color}* 

{color:#201F1E}525 Harbour Place Drive{color}  

{color:#201F1E}Davidson, NC 28036{color} 

{color:#201F1E}(************* phone{color}  

[{color:#954F72}www.mscdirect.com{color}|http://www.mscdirect.com/] 

 *{color:#201F1E} {color}*{color:#172B4D}{color} 

{color:#172B4D}{color}{color:#172B4D}{color}!image002.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!3w-xUdcPTD-tXjYfY5nP6Z6qd2UkGYwVScSk1NGQHFb_3MEMS0-n7S0ZCTEcYeUDpryfVHBvbQajfg$]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image003.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts__;!!G_S2aQPGww!3w-xUdcPTD-tXjYfY5nP6Z6qd2UkGYwVScSk1NGQHFb_3MEMS0-n7S0ZCTEcYeUDpryfVHCSWbpiuQ$]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image004.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!3w-xUdcPTD-tXjYfY5nP6Z6qd2UkGYwVScSk1NGQHFb_3MEMS0-n7S0ZCTEcYeUDpryfVHAfKFyq2g$]>{color}{color:#172B4D}{color} 

!image005.png|thumbnail!{color:#172B4D}{color}{color:#201F1E}{color}  

   

 *From:* Laramie Aars <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Monday, May 5, 2025 6:16 PM
  *To:* Jaycee Simolin <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* EXTERNAL: RE: MSC EDI - v# 29360 Petol Gearench *Errored Docs*  

   

{color:white}Jaycee, Thank you for reaching out! I will go back through and submit these. However, when I look them up in our sent folder, they are all showing as accepted? I also couldn’t tell why it was rejected when I went to restage. Do you have{color}   

{color:white}ZjQcmQRYFpfptBannerStart{color} 
| 
| 
|  *{color:#172B4D} *This Message Is From an External Sender*{color}* | 
| {color:#172B4D}This e-mail originated outside of MSC. \\ Exercise caution before clicking links or opening attachments{color} | 
  

{color:white}ZjQcmQRYFpfptBannerEnd{color}  

Jaycee,  

  

Thank you for reaching out! I will go back through and submit these. However, when I look them up in our sent folder, they are all showing as accepted? I also couldn’t tell why it was rejected when I went to restage. Do you have any insight? 

  

!image011.png|thumbnail! 

  

Thank you,  

   

 *{color:#172B4D} *Laramie Aars*{color}* 

Inside Sales Associate 

[{color:#0563C1}<EMAIL>{color}|mailto:<EMAIL>] I [{color:#0563C1}www.petol.com{color}|https://urldefense.com/v3/__http:/www.petol.com__;!!G_S2aQPGww!yCGH53FfskPtKeLo-JaC88qk-Voe2P6HEaZ45laq-DoD3q8g3-zLWeAFXWqBk5UNtdYdIkbdqy4CQg$] 

Office: ************ 

Fax: ************ 

  

!image006.png|thumbnail! 

  

   

  
  

 *From:* Jaycee Simolin <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Monday, May 5, 2025 12:53 PM
  *To:* Laramie Aars <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* MSC EDI - v# 29360 Petol Gearench *Errored Docs*   

  

[@Laramie Aars|mailto:<EMAIL>] Good afternoon! The following documents errored through EDI recently and I don’t show they have been corrected/resent yet. Can you please advise? 

  

!image012.png|thumbnail! 

  

 *{color:#201F1E} *Sincerely,*{color}* 

 *{color:#201F1E} {color}* 

 *{color:#7030A0} *Jaycee Simolin*{color}* 

 *_{color:#201F1E} *_Associate Product Manager_*{color}_* 

 *_{color:#201F1E} *_EDI Fulfillment_*{color}_* 

 *{color:#201F1E} *MSC Industrial Supply Co.*{color}* 

{color:#201F1E}525 Harbour Place Drive{color}  

{color:#201F1E}Davidson, NC 28036{color} 

{color:#201F1E}(************* phone{color}  

[{color:#954F72}www.mscdirect.com{color}|http://www.mscdirect.com/] 

 *{color:#201F1E} {color}*{color:#172B4D}{color} 

{color:#172B4D}{color}{color:#172B4D}{color}!image002.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/twitter.com/*!/MSC_Industrial__;Iw!!G_S2aQPGww!yCGH53FfskPtKeLo-JaC88qk-Voe2P6HEaZ45laq-DoD3q8g3-zLWeAFXWqBk5UNtdYdIkbBmOajzg$]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image003.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/www.facebook.com/pages/MSC-Industrial-Supply/101572582521?ref=ts__;!!G_S2aQPGww!yCGH53FfskPtKeLo-JaC88qk-Voe2P6HEaZ45laq-DoD3q8g3-zLWeAFXWqBk5UNtdYdIkZbWUSNRg$]>{color}{color:#1F497D}{color}{color:#172B4D}{color} {color:#172B4D}{color}{color:#172B4D}{color}!image004.png|thumbnail!{color:#172B4D}{color}{color:#172B4D} <[https://urldefense.com/v3/__http:/www.linkedin.com/company/24034__;!!G_S2aQPGww!yCGH53FfskPtKeLo-JaC88qk-Voe2P6HEaZ45laq-DoD3q8g3-zLWeAFXWqBk5UNtdYdIkaXrWiHXg$]>{color}{color:#172B4D}{color} 

!image005.png|thumbnail!{color:#172B4D}{color}{color:#201F1E}{color}

## Components


## Labels

