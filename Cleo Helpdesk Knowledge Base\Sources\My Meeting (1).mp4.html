<p>ticket because I thought, &#34;Oh, this will be simple.&#34; You know, you can see how to restage file. So, but I guess maybe not. All right. So, they sent ASN to their trading partner Sachs on May 2nd. They said three of the cartons were not on the ASN. However, I do see them on my end and they&#39;re asking for the raw data. The ASN we received did not contain these carton labels. Well, what would have been helpful Oh, maybe this is it. Is this okay? Let&#39;s see. This is the reference. This is the partner name. That&#39;s the sender. That&#39;s the receiver. Direction document type. Primary key. All right. Let&#39;s see if this is the All right. And who is this? Wait, I&#39;ll look it up. Violent Brooks 7543. I guess that&#39;s not their user anymore. Yeah. So the way you&#39;re seeing me access it is not how you access accounts. My whole team does it the way you&#39;re doing it. The way I&#39;m accessing account is our old way. But everybody has been since been trained on the new way. I am just not following protocol. Um, so hold on. I made Okay. So because when they acquired us our company, so before COVID, the company was based in Houston. They actually had an office. Employees went to an office after co they realized everybody could work from home and got rid of the office but the bulk of the employees live in Houston or the Houston area. So as the company expanded though since co more and more people were all over the country actually we have uh one of the analysts is in Canada and so but they didn&#39;t know what to call us. So they refute to refuse they refer to us as Cleo Houston. I don&#39;t live in Houston. That&#39;s why I always said I&#39;m not from Houston. Like I&#39;m not but that was just the name they gave to us was Cleo Houston even though Nico and <PERSON> on my team are both in that Houston area. So Cleo Houston. Yes. I I Yeah. Yeah, that&#39;s so that&#39;s how they refer to us. Okay, I know. Okay, so they did send three ASN all with the same shipment ID. Not supposed to do that, but um because usually if it goes through with shipment ID and goes through with the same ones, they won&#39;t usually accept them. But I guess Sak Fifth Avenue did because they&#39;re not saying that they came through with they sent three SNS. They said three of the cartons were not on the ASN. However, I do see them on my end. The ASN we received did not contain these carton label IDs. So this This is that SSCC18 number. As you can see right in the middle, there&#39;s a number here that is going to be the number for Violet and Brooks. So, if I go back to Violet and Brooks and I were to go to settings and I go to company info, see that is their UCC vendor prefix, which is this number right here. Okay. So this is the and that number is always going to be inside that SSC18. So they&#39;re saying that these three SSCC 18 numbers did not contain those carton label IDs. So they they received which is not possible if they printed labels off the system and printed labels with these. There&#39;s no way they could have scanned the label in without it being on the ASM. But We&#39;ll check because they sent it three times and I don&#39;t really know which one Sachs could be referring to. So, let&#39;s go back here. Um, put that number back. Okay. So, I&#39;m going to export the most recent one because they didn&#39;t say give us like uh control number. It&#39;s just sender receiver. So, I&#39;m just going to export the most recent one. I mean, I will eventually export all three, but I&#39;m going to look at them individually. that those that that number that they said is missing. Those three numbers that they said are missing. So, this number is the number I&#39;m going to look for that they say is missing, which is going to be 25414, which is the very first one. Oh, no. That&#39;s 1141. Okay, that&#39;s 21 because they&#39;ve done this. So, every time they do the ASN, they&#39;re going to if they&#39;re creating it brand new, these numbers are always going to be different. Now, these end in one445 111452 and these numbers are 25 4 407. So these are much higher. No, these are lower, right? Let&#39;s pull this. This is 009734 301. Okay, so this is not the ASN they&#39;re talking about because these numbers three, that&#39;s where their number ends. So this is 0025414. This starts out three. Oh, no. This starts out 01. And these aren&#39;t going to be sequential numbers. So, I&#39;m not going to use that data set. I&#39;m going to use the first one because that is probably the one that Sax is going by. I bet they didn&#39;t even take those two. Um, because it has the same ID. Let&#39;s take more. Okay, I&#39;ll bring up the ticket. My raw data 00 7 is even higher. 07. This is 002. I don&#39;t even know why they would think that number would be on any of these. Well, I don&#39;t think that number&#39;s on any of them. Cuz this one already is 111. 111 Oh, I see. Because they&#39;re probably all the same shipment. There&#39;s probably all the same order. That&#39;s why it has the same front. Oh, and then these are just tests. Test ASM. Okay, that was 39. All right. Well, let&#39;s do this. Brooks are sending ASM. Not even going to put in SAS for Violet and Brooks. No, go to the other server. So, they&#39;ve only sent three ASNs to Sax Avenue. I didn&#39;t think I was going to find that number anywhere. I mean, we can give them the raw data, but these numbers, this I really don&#39;t know where They came from numbers don&#39;t exist on I mean we can provide them with the raw data that&#39;s not a problem but they&#39;re not going to find what they&#39;re looking for on any of these so this is the oldest one that&#39;s the first one they sent based on the time 9:32 a.m. Well this one was 32 Oh yeah so this one is 441 one. Oh, 4413 2986. Okay. Oh, you know what? Let&#39;s see if they&#39;ve deleted anything. Well, these are just drafts, but I&#39;m Well, clearly didn&#39;t send them. What if they did an ASN, got the labels, put the labels on the boxes, and then did a new ASN. Those numbers would be different. So, I am going to restore these. So, I&#39;m going to restore these to the draft folder. I&#39;ll delete them. But I&#39;m curious if these were March. I&#39;m going to take this one cuz this one has a May shipment 0 1 0 1 03 and I&#39;m looking for 0 0. Okay. Then I&#39;m going to take this one that is older. There&#39;s nothing on it. Like I&#39;m not even pulling an ASN that&#39;s old enough to have those numbers. Here&#39;s a bunch from the first. Okay, let&#39;s just try you. understand where I&#39;m going though, right, with this. Yep. Right. I&#39;m thinking that they created an ASN. This is 0000665. These are still too high, right? I need zero. Oh, I need 0025. Okay. I need 0025. And this is 006. Okay. So, this is still not old enough. And I took Did I take the oldest one? Did I look at this one? 44. Let&#39;s see my active messages. That&#39;s it. All right. Let&#39;s go see what else is in Oh, there&#39;s my number. There&#39;s that. That&#39;s the number that they said. So, if I look even older, I&#39;m going to guess that they created an ASN. There&#39;s that number. This one&#39;s 429. I am going to guess that they created an ASN and got their labels and then they sent then they created another ASN not realizing that When they do that, they get all brand new numbers. Yes. Yes. It&#39;s exactly what I think they did. I&#39;m What? 0025315. And this is 00254. So, I think I&#39;m on the right ASN now. That or it&#39;s going to be the one on 51. So, let&#39;s go all the way to the last pack. 0025483. And this is 0025414. It&#39;s in the 407 414. That was it. 7414 and 421 and 421. So, and this ASN did not get sent because this is message ID 44 97832. And if I go back to the server, that message ID was never sent. And my search criter criteria goes back to March 19th. So that&#39;s exactly what they did. They created this ASN and printed their labels and then they created a new ASN. Now, possibly they created new labels off that new ASN. It&#39;s very possible that they did that. The problem is is that there had to have been a couple boxes that still had the old numbers on it that they didn&#39;t redo new labels. That&#39;s where their problem is. You think you can explain that to them? Yeah. Here, let me let me This is 732. I want to get rid of everything else in draft except this one as I&#39;m going to delete all these cuz these are all the ones I restore. So I&#39;m going to delete these. All right. So they created this ASN on April 29th. So you could probably give them a history. history. So it looks like you created an AFN given that message ID on April 29th which contains those three and you can if you want to give them the raw data you can export this and share it with them so they can have it but they never sent this ASN but you can give them this raw data. So but what I&#39;m thinking they did is they created ASN. They created their labels. There was a mistake on this ASN. I don&#39;t know if that&#39;s the case. This is my theory. They created new ASN. They created new labels. They went and put the new labels on the boxes over the old ones. But three of the boxes didn&#39;t get new labels put on them and still went with the old labels. And they could not match up those labels to an sent. So, let them know that, you know, you can give them this raw data for this ASM, but this one was not sent. And you could tell them that we do have an ASM that they created on 429 which contains those three label IDs, right? But we can see this ASN had not been sent. Subsequently, new ASN&#39;s had been created. ated though for for Sachs. Let&#39;s just do this. Did you create this? Okay. So, subsequently they created three other ASNs with that same shipment ID and those those three were sent. Now you can like ask you you can give them all four cuz if you give them these three, Sax is going to say, &#34;Well, that number wasn&#39;t on there.&#34; They&#39;re going to get fined. No matter what, they&#39;re going to get fined because they made a mistake. But to try and explain to them, we can see the initial ASN that was created has those three labeled IDs on them, but that ASN was not sent. It does appear though that a new ASN was created after the fact. with new label IDs. Um, and those were the ones that were sent. Um, I guess ask, you know, it&#39;s you can ask them, you know, did you create a second, you know, new ASN with new labels? Um, I know that&#39;s what they did, but Oh, they&#39;re getting fined. Sax is going to find them because they made a mistake. I mean, they&#39;re they&#39;re going to have a hard time getting out of that fine. They made a mistake. They created an ASN with labels and put the labels on the boxes. Then they created a new ASN. And I&#39;m going to guess they put those labels on the boxes except they missed three of them. But that&#39;s exactly what they did. Now, maybe these are all different items because it I think they&#39;re probably all associated to the same order. Yeah. So the order is probably has that um SDQ where you have to send them to distribution centers. So yeah because it&#39;ll create it&#39;s going to create the ASN by see there&#39;s all these are stores right these are all individual store numbers but these stores might go to one DC these stores a different DC this is a different DC that&#39;s why I created three ASN it created one ASN for each DC. Um, so one of the ASNs they had obviously recreated and they just they just made a mistake. I mean this let&#39;s see so this ASN is for which DC? Where&#39;s the ship to? 595. So here&#39;s the ship to 595 is the DC number. Okay, go here. I can I&#39;m going to look and see which one of these is DC59. This is it. So, this is the ASN they sent. The problem is is that They didn&#39;t use all the labels correctly. They didn&#39;t take all the labels off of um unless are these all DC595. Yeah. Oh, they actually all included these all buying party 401. Oh. Oh, here&#39;s 401. Here&#39;s 403. Where&#39;s my one that was in draft 403. So it looks like the one that was from the buyer 403 Hawaii they did twice. Can I find that ASN? I want to get rid of all this other stuff. I want to keep this ASN or did I get rid of it? No, that&#39;s the ASN I want. This one 403 403 Okay, so I want this ASN, which is that one, and this one. Are these the same items? So, we&#39;ve got 403. We&#39;ve got one of 650, 1770, 650, 177, 807. This was the original ASN. This is the one they were actually recreated and sent. And these were the new shipping label IDs. But it looks like three of the boxes got did not get new labels. Okay. So, this is the new ASN. These are all the new numbers. So, here&#39;s a number. This is a number. This is a number. These are all the new numbers. So, if I go to print label, I&#39;m going to get all these labels. Probably not. It&#39;s probably going to tell me it&#39;s got going to take a while because it&#39;s a good size AS. It&#39;s going to give me all the labels. So, so here&#39;s all my labels. I got 18 labels, right? They printed these out. They walked over the boxes and they put all of these labels on the boxes. The problem is is that the label how many are here? This should be 18 also. Okay. So, these were their original labels. These were already on the boxes. When they printed out the new labels, they had to go and take the new label and place it over the top of this one. The second one over the top of this one. So on and so forth, right? Over and over and over the top of these. But there&#39;s going to be three of them that they did not do that to. And it&#39;s going to be these three right here. They didn&#39;t put the new label over the top. of this one. They sent this carton with this label and this when they scanned this in, it didn&#39;t match the ASN. So, that&#39;s what they did wrong. Now, unless they didn&#39;t put the label over the top of it and they put it next to it. So, they scanned in the new label and they scanned in the old label. And that&#39;s why it says that these did not match because these three labels were on those cartons when they got scanned in for some reason. They didn&#39;t take the old labels off of their cartons and they needed to cover them up, take them off because Sax is you scans this barcode and this number comes up and it didn&#39;t match the new ASN that they actually sent. So, we can give them the raw data. the raw data for the ASN that was sent is not going to show these codes. So you could give them the raw data for all three of them that they asked for, but you can tell the customer what appears, you know, what it does appear is that there was initially an ASN created and the labels were printed off the original ASN. A new ASN was created and the old label had been used and obviously these three hadn&#39;t been removed off of the packages, you know, and had the new labels put on. Mhm. Mhm. Yeah. Yeah. I I would Yeah. I got Yeah. Just give them as much detail as possible because they definitely I mean, they&#39;re going to know that they created an ASN. So on April 29th, they created the first ASN and clearly used those labels. And then they created a new ASN on 51 with new labels. But the old labels were still on the cartons when it got shipped. So So And like I said, you can give them the raw data, but let them know that the raw data is going to show is not going to show these label IDs because those label IDs exist on the original ASN that was not used. wrong. But I mean, we always do try to um uh you know, see what we can do to help them dispute a fine, but this is clearly in them. There isn&#39;t anything this there&#39;s nothing I can do to help them not get fined because I know what they said, right? I think they said Sax is finding them. Correct. Um, the one thing and the reason I usually say to take it out of the server and I&#39;ll show you why. I usually take take it out of the server because this was sent on we can see here this was actually delivered on 52, right? If I export this out of webi today and I&#39;m going to put the two side by side. So you could see one exported off the server and one exported off webi. So that one&#39;s exporting and I&#39;m going to export just one of them. I&#39;m going to just double click it to open it. Okay. So if I click on this and I&#39;m going to say text because text will show me everything. The very top line, this ISA record shows me my date and my time that it was sent, right? When I export it out of web EDI, it picks up today always by default. Oh, edit text or edit won&#39;t show you the ISA record, but text will. And you can see here 5, that&#39;s today and it&#39;s 2:03 p.m. I use So if you were to send this to if you were give this to them to send to Sachs, it would have today&#39;s date on it and they&#39;d be like, &#34;Well, that&#39;s wrong because we got this on, too.&#34; So pulling out of WebEDI just to look at it for yourself is no problem. If you need to send it for someone who needs it for verification, you want to use it off the server because this is exactly what was sent. Okay, that&#39;s usually why I say If you have to provide somebody raw data, provide them the raw data off of the server so that the information is accurate. Are you getting the hang of it? I mean, you went and found all that on your own, so you&#39;re getting comfortable with everything. Mhm. Perfect. Great. Okay. So, I asked Rafat, I said, &#34;What is the max number of labels that can be sent per email link?&#34; Because there is going to be a limit. So, it can only send I said currently they said they&#39;re getting 12 I&#39;m assuming 12 pages, two labels to a page, which means they&#39;re getting 24 labels, you know. So, they&#39;re getting um and oh, maybe that&#39;s why it says 50. Okay, so when you go into settings, that&#39;s If I go to the label. Yeah. See, you could put up to four labels on a page, which would then put me at 50 because I found a really, really old development ticket from 2021 when this email to me feature came out. And the max number of labels that could be received per, you know, email was 50, but I think it&#39;s 50 because you could put four on a page and that would be 48. So, two on a page page that would give you 12 pages. He&#39;s getting 12 pages of two. So it&#39;s like 12 pages appears to be the limit. You can let them know that you know we&#39;ve spoke you know we&#39;re talking to our development team to find out what you know if the max allowed per email attachment can be increased. But really based on the packing of his is ASN. Technically, he only needs six because I think he&#39;s packing wrong. So, this really isn&#39;t even going to be for them, right? So, yeah, I thought that that was very clear like you&#39;re doing it wrong. without saying, you know, without saying you created your ASN corre and I guess we can say about nicely, but say it appears you&#39;ve created the ASN incorrectly. Um, it it appears you really only need six labels and that, you know, they&#39;ve ordered 9,800 whatever, but you&#39;re putting 1,200 or 1662, whatever it was, per per container. If that&#39;s the case, you only need six packs. Yeah. So maybe phrase it that way. Say it, you know, it does appear that I mean say we we&#39;ll talk to our development team about increasing that, but it appears you&#39;ve packed the ASN incorrectly. Um you know it you can fit whatever that was. It was like 1622 or 1226 or something like that per carton. You could put 1,200 items in a carton because they&#39;re probably something really small. And and if that if that&#39;s the case, if you pack 1,200 at a time, you only need six containers then. So, it does seem like they&#39;re packing wrong. Send him something like formulated like that. You&#39;re pretty good with words. And um if he comes back, I&#39;m going to set up a phone call with Yeah. Yeah. Put something together like that. And if he still doesn&#39;t understanding, I think we&#39;ll just kind of have a phone call with them and I can explain to him. Yeah. Right. Uh we&#39;ll see. I&#39;ll see what if there&#39;s anything though. Let me see what&#39;s there. That&#39;s all right. I&#39;m going to cancel this one off cuz these notify ones, I just cancel those off. We don&#39;t do anything with those. I I even told them that when we&#39;re on Zenesk, I don&#39;t want to see those. They can just make those go away. Um because they&#39;re just they&#39;re just like noise tickets that every time somebody has a new sign up, a ticket comes into support. I I I don&#39;t need to know that they have a new sign up. and our old ticketing system. I had like workflows that could just take them away so I didn&#39;t have to look at them. Um, let&#39;s see here. No, not Banner. Maria will probably take Banner. Um, which one? Oh, this one here. Okay, let me look at it. Could you please see below request from goal to help adding document A10 to our system. Kaylee, could you please help us including A10 to our buckle EDI? Okay, so is this Aendale? Is that the customer? Oh, I have some exciting news. To better serve our trading partner career, we&#39;re enhancing your EDR documents. You&#39;ve successfully completed the first phase, which involves updating the EDI 85056 to version 6010. The next phase our enhancements focuses on 810. You&#39;ve been selected to to begin testing these documents. Okay. So, yeah, you&#39;re not going to be able to do this, but um let me look here and see. Buckle. Do we even already support the A10? We don&#39;t even support the A10. So that Okay. So, we currently don&#39;t support the A10. So, a whole new map has to be built and I believe they&#39;re going to have to pay for that. Um, all right. I got to find that out first. I got to find that out. Yeah. Yep. You know, I I have to ask and Robert came back from vacation today because I was talking to Jason in sales like our sales manager and because we have another customer where Volvo the trading you know Volvo the trading trading partner they they were using US version of EDI and they&#39;re now converting to European version of EDI. And our sales manager said, &#34;Oh, yeah. Well, they&#39;re going to have to pay for three maps then, $190 a map.&#34; Well, they called me today and said, &#34;Why do I have to pay for this?&#34; And I went back to Jason and I said, &#34;Really?&#34; I said, &#34;We&#39;re going to charge them when the trading partner is making the change. We&#39;re going to charge our customer to build those maps.&#34; And he said, &#34;I know I never really agreed with that logic, but the first customer who has to go through that new form incurs the cost to build the maps. And I said, &#34;Are you sure?&#34; And he said, &#34;That&#39;s how it was set up.&#34; And I&#39;m like, &#34;Okay, well, this is another one.&#34; And so I am going to, like I said, Robert came back from vacation today. I&#39;m going to ask Robert about this. Like, hey, Robert, is this this true like Volvo is converting to Edifac and Miles Rubber was you know but Volvo reached out to us on behalf of our customer Miles Rubber to convert them to Edifact. Do they have to pay for those maps? Likewise, Buckle is now introducing an A10 and there is to have the A10 map built. Are we going to charge them to build an A10 map? map. And if he says yes, then we just have to let Joyce know that they&#39;re going to have to pay for a new map. I know. I you know, so I think this was Aendale. Is that who the customer is? Okay. And Aendale, if Aendale, now if Aendale&#39;s an integrated customer, this is really easy. It&#39;s 100% a project. Um because they&#39;ve got to do a map. But if it&#39;s if it&#39;s a WebEDI customer, I just disagree with that charging them when the trading partner makes a change like that. I mean, I just Yeah, they&#39;re just WebDI. I just don&#39;t agree with it. But, um, since Robert&#39;s back from vacation, I am going to ask him like, &#34;Hey, do we really charge for this?&#34; But, um, yeah. Well, see, so because the trading partner isn&#39;t like Buckle&#39;s not our customer, Aenddale is. So, who incurs the cost? Is is it that we just don&#39;t charge anybody because the trading partner makes a fee because the trading partner isn&#39;t going to pay us? But that&#39;s kind of how it happens. Um, it&#39;s even, let&#39;s say there&#39;s a new trading partner who&#39;s not in our network. Like, let&#39;s say somebody wants to, okay, let&#39;s use Alta Canada for example. I never even knew there was Alta Canada, but okay. So, Alta Canada, let&#39;s say Alta Canada has completely different maps than Alta US. The first customer that signs up with Alta Canada, they have to incur the fee. And if Alta Canada wants a specific label, that customer incurs the fee for the label. Everybody else gets the benefit of I know kind of crappy, right? I would have thought it would just been called alto.</p>
<p>I would have realized that we had multiple um you know I didn&#39;t realize there were multiple that it was the same. It&#39;s the same company. My son&#39;s my son works for the firm that&#39;s their architect and uh you know I&#39;m like I&#39;m going to ask him hey Alta Mexico I said you guys work with their Alta Mexico do you do their stores too I&#39;m gonna ask him when he comes home from work tonight he&#39;ll be like what no we don&#39;t do Alta Mexico hey you might be going to Mexico soon Uhhuh. I know. Yes. It&#39;s a lot. You&#39;ll get it though. You&#39;ll get it. You&#39;ve been catching on really good. You&#39;ve been catching on. So, let&#39;s just see what this is. This is um Kelly, our bookkeeper. So, somebody actually reached out to Kelly. Oh, I actually talked to this customer, Kelly. The Kelly Bacon is our bookkeeper. Yeah, she&#39;s one or one of the bookkeepers. This So, I actually talked to this customer on the phone. I already took care of them. They&#39;re all They&#39;re all good. They&#39;re all done because they called in. Um What is this one? Uh, I need to invoice for this order and I&#39;m getting the message below. I did not include it in a batch. I don&#39;t even know how to do that. Message response for process failed. Unable to create task already in already existing as a batch. Okay. Let me see what&#39;s here. Man, all right. Well, let&#39;s look at this one. 6 and 48 because I I don&#39;t want to confuse anything that you&#39;re working on. So, I&#39;m just We&#39;ll just look at this one real fast because she said she created an invoice and got a message that is already part of a batch. So I&#39;m going to look here. Okay. So these are lots of documents created today. Where are you? This is the order number. Always helpful when they give you either the order number, message ID. When there&#39;s nothing in here, we always have to ask them for it because otherwise you can&#39;t really find it. Okay. So, here&#39;s the 850 and there&#39;s one invoice in draft and she&#39;s saying I&#39;m getting that I did not include it in a batch. I need to invoice this. Well, let&#39;s see. So, if she did this respond 810. Okay. No error about it being in a batch that way. This response A10. No error that way. Okay. So, if you want to take that one, you can. I&#39;m going to delete these two that I just did. And can you just ask Ask her can she please provide what exactly she was doing when she received that error because this what she has here is very vague. I need to order invoice for this order. What was she doing when she got that message below? Can she please provide her steps and at what point did she get that message? Um because it&#39;s too vague. So we both tried to create I just tried to create an invoice both ways. and both worked without getting an error. So, I just need to know what steps she&#39;s doing. Oh, yeah. Here, I&#39;ll give it to you. I&#39;ll sign it to you. Your name right. Okay. So, there you go. You have it now. All right. So, you could go ahead and do the one for Violet and then you can do this one and then we can talk again. All right. Okay. Uhhuh. Okay. And then Tommy also reply to Tommy and just let him just tell him that it the Pierce is always creating the incorrectly. Um but yeah, so yeah, go ahead and take those three and then we can then we&#39;ll talk again. All right. Oh, you&#39;re welcome.</p>
