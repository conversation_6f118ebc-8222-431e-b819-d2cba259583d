<h3 id="my-meeting">My Meeting</h3>
<p>Meeting started: May 19, 2025, 9:00:52 AM Meeting duration: 29 minutes Meeting participants: <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="transcript">Transcript</h4>
<p>00:00 <PERSON>.: Hello, Ms. <PERSON>. Good, how about you? Did you have a good weekend? 00:02 Sandy K.: Hi, <PERSON>, how are you today? 00:08 Sandy K.: Um, yeah, it was a good weekend. How about you? 00:10 <PERSON>.: Oh, good, good. My mom came visited. So, I really do. 00:18 Sandy K.: No, that&#39;s nice. Does she live local, or she out of town? 00:21 <PERSON>.: Um, no, so I have 4 older sisters, which all have kids, so she rotates around during the weekday to babysit. So, um, I live, um, at home with my father. So, um, she comes back during the weekend to just spend time. But then she&#39;s… yeah, she&#39;s… she&#39;s already leaving now to my other sister to babysit this week. 00:40 <PERSON>.: Oh, okay. So your family&#39;s kind of structured like mine. I am one of five. And I have one brother, and I have 3 sisters. 00:54 <PERSON>.: Oh, yes. Oh, wow. Okay. Yes. 00:57 <PERSON>.: Do you have any brothers, or just sisters? Oh, so you&#39;re… oh, you&#39;re an even bigger family, okay. You said you had 4 older sisters, and then you… oh my god, so there&#39;s 7 of you? 01:01 Michael H.: Oh, I have two brothers. One little brother… Yes. Yes, I&#39;m the second youngest. So, um, forwarder sister, one order brother, then a little brother. 01:13 Sandy K.: Oh my god. Okay, that&#39;s a lot, kid. I mean, I was one of five, and I thought that was a lot. And then when I had kids, I had one. 01:29 Michael H.: Oh, and you said he&#39;s, um, at home visiting?
01:30 Sandy K.: Uh huh. That&#39;s it, we&#39;re done. No more kids after that one. I think he&#39;s bad, but he&#39;s, uh, you know… Wasn&#39;t for… yeah, no. One and done. I won&#39;t talk anybody out of having kids. Everyone should do what is best for them, but I told my mom all the time, I don&#39;t know how you handle 5. I said, I can&#39;t handle 1, so… 01:46 Michael H.: Yes. 02:00 Sandy K.: Alright. Well, I&#39;m glad you had a good weekend. 02:03 Michael H.: Yes. 02:05 Sandy K.: Okay, so Maria is going to meet with you at, um… 8.30. Um… I know Lauren did all last week, and I know she&#39;s got to lead the training class today, which you&#39;re gonna sit in. 02:10 Michael H.: Oh, great. Yes. 02:18 Sandy K.: So, I was gonna… I asked Maria if she had any tickets that she could show you this morning. I know she&#39;s just coming back. From being off, but um… She said, yes, she will sit with you at 8.30 is when I have my meeting. So, I did make a change, though. Um, and you should have received the Zoom meeting, so I&#39;m doing Meet the Team at 10 o&#39;clock. 02:40 Michael H.: Okay, yes. 02:41 Sandy K.: So, from 10 to 10.30, right before the training class, I&#39;ve moved it up. Um. The only one left to meet will be Nico at that point, but… Um, I have you in the training class, then. Um, with Lauren. Depending on how many people are in that class, and how many questions, sometimes that class goes for over an hour. 03:05 Sandy K.: So I did, um, leave an hour and a half for that. 03:05 Michael H.: Yeah. 03:09 Sandy K.: Um, then you go to lunch, and then it, um… 1 o&#39;clock, I&#39;m gonna have you meet with Nico. I might have to adjust these afternoon ones, um… Based on everyone, everyone goes to lunch. 03:18 Michael H.: Okay. 03:22 Sandy K.: I just don&#39;t remember what everyone&#39;s lunch schedule was. And I&#39;m trying to work around Nico&#39;s got several meetings this week. Because he&#39;s doing their SFTP migration, so… Um, I figured you would just… Shadow everyone today. Again, like, watch what they do. And then, starting tomorrow. I will have
them, myself. Any tickets that are, like, really simple. You know, just ones that aren&#39;t integrations or mappings or anything like that. 03:52 Sandy K.: To assign them to you. Um, and there will be… mentors throughout the course of the day, so… Um, everyone would be available. 04:03 Sandy K.: And during those time frames, if you had questions, you could go directly to that person. And they&#39;ll assist you with any questions that you have. Um… Now, you had tickets from last week. 04:15 Michael H.: Yes. Um, no one responded besides Dylan Smith, and he&#39;s just pretty much followed up, saying that he&#39;s gonna… he needs some confirmation on his side to make sure everything&#39;s Running correctly before he could, um, decide to go back to being automated. 04:15 Sandy K.: Uh, did anybody reply on any of those? 04:33 Michael H.: But right now, he&#39;s just kind of doing manual. And we&#39;ll get back to… us. 04:33 Sandy K.: Okay. Let me just, um… Okay, so these are the tickets you have. So Dylan was the only one? 05:06 Michael H.: Yes. Yes, and, um, I was about to follow up to say, like, um, just… confirming with them that I got the message, and to just let him know. 05:07 Sandy K.: Who responded? 05:20 Michael H.: Or let me know when he&#39;s available. 05:25 Sandy K.: Okay. All right, so then he&#39;s just gonna… look to see. Okay, so things he said. Uh, let me get confirmation first. Okay, so he needs to get some confirmation on his end before he wants us to actually turn all of that off. 05:37 Michael H.: Mm-hmm. 05:45 Sandy K.: Um, what was the Symphony Beauty one? Okay, this is the one that I asked her if she&#39;s looking to add a new trading partner. She never responded. 06:05 Michael H.: I went through all the tickets this morning to see if anyone, but, um, no one else seems like they responded. 06:12 Sandy K.: Okay. And this was on Thursday. All right, so we&#39;ll see if she doesn&#39;t respond back today. We&#39;ll send her another one asking If, you know, what we can 06:17 Michael H.: Yes.
06:29 Sandy K.: All right, and… nothing from him, so again, we&#39;ll give him the day. So none of these… said anything. 06:39 Michael H.: Yes. 06:42 Sandy K.: Is that great? And global pool products. Oh, this one is resolved. All right, I don&#39;t need that. Okay, I gotta change this filter, but I don&#39;t… Okay. Okay, so we have those. All right, so we don&#39;t have anything that we can… do with any of those, so let&#39;s see what we have that&#39;s… No. 08:08 Sandy K.: This one, I have to look back on Tyler&#39;s tickets, I don&#39;t know. 08:12 Michael H.: Yes. 08:12 Sandy K.: Somebody&#39;s asking for, um… It&#39;s been a couple months since I requested data transfer to provide an automatic inventory sync from QuickBooks to your EDI catalog. You said you would submit that request with your programming team, which I&#39;m sure he did. 08:31 Sandy K.: Um… I&#39;m gonna have to look back, because that would have been… an enhancement, which I don&#39;t think there were any enhancements being performed at the time. So, I&#39;m gonna have to check and see. I have to go back through all of… Tyler&#39;s Tickets for Planetary Design, or from Bob. So, let me just do… Miltur… Actually, I&#39;m just gonna use one of my current filters. All tickets. And I&#39;m gonna say that my reporter… Alright, so… I&#39;m gonna assume it&#39;s this one. This is the only one assigned to Tyler. 09:46 Michael H.: Yes. 09:55 Sandy K.: It&#39;s just seem… I&#39;ll see the conversation. Tyler Hip. Looks like they did everything over a phone. 10:09 Michael H.: Yes. 10:43 Sandy K.: Okay, that was only for E46 automations. I am going to 11:12 Sandy K.: I want to read… I&#39;m gonna have to ask for a fight. If he talked to her at all about automating… inventory… I don&#39;t think that that can be done. So, inside QuickBooks… Sorry. There&#39;s… we have to do mapping. Per items. They have to map their items. 11:44 Michael H.: Yes. 11:48 Sandy K.: Um… one by one. So… 12:12 Sandy K.: So, QuickBooks… I&#39;m just gonna open it so it&#39;s on its own tab. Items. So… Customers, when they have QuickBooks, obviously they have all of
their Inside QuickBooks, it&#39;s called Inventory. And so they have all of their items inventoried inside QuickBooks. And then they… you&#39;re familiar with the catalog. In WebBDI. Is, again, where all of their items are in WebEDI. When… they have a new item inside QuickBooks. Sorry, hold on for just one moment. This is my husband calling. 12:58 Michael H.: Oh yes, I&#39;m going. 13:44 Sandy K.: Alright, sorry about that, I wasn&#39;t certain if it was… Something he needed, okay. Alright, so, again, QuickBooks, they have an inventory. And Wabita is a catalog. And with QuickBooks Desktop, as I explained, there&#39;s a web connector prop program that sits in between their QuickBooks program, and WebEDI, again, because that&#39;s a… a program that resides on their computer, and ours is web-based, so it needs that 13:54 Michael H.: Yes. 14:16 Sandy K.: Web connector to… so the two programs can communicate. With QuickBooks Online, you don&#39;t need that, because they are both on web-based programs so they can speak to each other. But there&#39;s, um, inside that QuickBooks desktop program. When they run the web connector. For item mapping. It will bring in all the QuickBook QuickBooks items into these drop-downs. So all of their QuickBooks items will be listed here, as you can see. They have a lot of them, right? So it brings them in, and it will show all of their WebEDI items on the left. But it will not automatically map them. So, they have to come in here After they run the mapping to pull the items in. 15:05 Sandy K.: They have to come in here and say, okay, this catalog item is associated to this QuickBooks item. He is looking for something to… automate that process So it&#39;s just done… automatically. I really don&#39;t know even how, and I could hear Rafat already saying to me, like. 15:29 Sandy K.: Well, how do we know that which item, unless it was… exactly the same name. Like, it… this… was in here exactly. Character for character. Like, if there was any… change, it wouldn&#39;t be able to pick it up. I can see that he doesn&#39;t map to a name, so I&#39;m not really certain. Um, but I can ask for Tyler to talk to her about it at all. Um, and… Because that&#39;s, I think, what he&#39;s asking for. He is asking for… an automatic inventory sync from QuickBooks to your EDI catalog. 16:11 Michael H.: Just a faster way to do… Um.
16:17 Sandy K.: You said that you would submit the request to your programming team. Um… I&#39;m gonna talk to Rafan about that, because I don&#39;t really know if he did speak to her about that. 16:29 Michael H.: Yes. 16:29 Sandy K.: Um, it would be unlike Tyler not to speak to Rafat about it, but I don&#39;t have a ticket number here. 16:35 Sandy K.: So generally, when we escalate something to development, I ask everyone to put the ticket number here. I know this is my… this is the new ticket, but I didn&#39;t see… a ticket number on the one that Tyler had either. This is it. See? I always say put in the ticket number here so that we have a way of following it, knowing that there was something assigned. This is the only ticket that was related to… Um, QuickBooks? 17:10 Sandy K.: Because this was… you know, this is for a trading partner. A request for this training partner, but… Yeah, it has something to do with QuickBooks. 17:22 Michael H.: Yeah. 17:23 Sandy K.: Um, so I&#39;m going to ask for thought about that one. Um, to see if she… If he did speak with her about it. So I&#39;m just gonna sign that one to myself. And… I guess I&#39;ll ask Marianne, of course. This one&#39;s pretty old. Um, so they asked us to confirm our AS2 ID and your endpoint URL. And this is their certificate. And Marianne… was on this, so… from the projects team, so… I&#39;m going to ask Marianne if she… did respond to them with that information, or if… someone from support does needs to respond to them with the information. Because when an analyst is assigned… is, you know, CC&#39;d on it, sometimes they do respond to the ticket? Not always, but sometimes they do. 18:36 Michael H.: Yes. 18:39 Sandy K.: And if she did, I just don&#39;t want to… Um, respond a second time. Um, so I&#39;m going to reach out to her and… slant. 19:27 Sandy K.: We just built the AS2 setup on your end and let us know any… oh, so they need an AS2 connection built. 20:02 Sandy K.: I sent the certificate. Because I think we already have… Oh, man. Now, we will be using the production connection only with… oh, alright, I will send this to… Well, I&#39;ll just send this to our use two tickets. Nervous to do that. I want to
check to make sure we don&#39;t already have an AS2 connection with Uber Freight. 20:51 Michael H.: Yes. 20:54 Sandy K.: Alright, great. See, we already have one with Uber Freight. That&#39;s why… 21:47 Sandy K.: Listen to get before I reply to them. 22:18 Sandy K.: And the certificate… That we have for Uber Freight. Is valid since 1122. To 11… Oh, it was up… oh, this was just completed May 14th. 22:34 Michael H.: Yes. 22:34 Sandy K.: Oh, so I guess this is done. 23:12 Sandy K.: Maybe we don&#39;t have to do anything at all of this. Oh, here, May 15th. Attempt to send a test file in U18 getting this error. 23:51 Sandy K.: So she&#39;s gonna have to let me know if we have to… I see she&#39;s been communicating with them. Because I see here, they called her Kanya, that&#39;s her last name, but… I see, she replied back. Please use the below. For both test and production, our dev team has advised we already received a file. And they sent the file. I guess maybe now we need to know if these IPs Okay, okay. She&#39;s said to close this ticket. Oh, no, no, yeah, no, the ticket you have. 43086. All right, this is her original ticket. So, that is her ticket. 25:11 Sandy K.: And she said to close. This one. So I&#39;m going to put a note on this one. So it&#39;s being worked on this ticket. 25:29 Sandy K.: And she said to just close this one, so I&#39;m gonna actually cancel this one. Because it was already… Work done. Okay, so that one is easy. Oh, well… I know what this one is. So… the FedEx integrator, so… We have a FedEx, you know, just like we have QuickBooks, we have one for FedEx as well. And FedEx did an update Um, to their interface. Not that they have to tell us. But they did an update to their interface. And now, our setup guide. Does not work. Like, they can&#39;t follow it because it doesn&#39;t align with the new FedEx interface. 26:29 Michael H.: Yes. 26:30 Sandy K.: Um… we could try to just walk them through it. But I had asked… the development team, if they could. You know, update… the, um, the setup guide. Um, I had a ticket for him. In development. So that it can work, because… we could have him share his screen and try our best to walk him through it. But, um, yeah, I know what this is. Um, and I already have a ticket in my Q4.
27:26 Sandy K.: Fedex Integrator. So, I said, we&#39;ve run into this before, FedEx makes changes to their interface without notification, I&#39;ll work with our internal team to get the documentation updated. And… That is this here. Because I didn&#39;t put it on here. And this is what I usually ask people to do. So that I have the… we have the ticket, so then if you had to go into… JIRA to look up development tickets. You would have the ticket number here, and then you can just go into a development side of JIRA and click on it, and be able to you know, look at the ticket then. Obviously, or you can just see it&#39;s in progress. 28:23 Michael H.: Yes. 28:26 Sandy K.: You can always click on it here, but it&#39;s… easy for me when I&#39;m doing, like. Ticket reviews, and I have to… you know, make sure tickets are being replied to. I can ignore the ones that have a development ticket Because, you know, there&#39;s only so much we can do with those. 28:44 Michael H.: Yes. 28:48 Sandy K.: And… I&#39;m just gonna… there isn&#39;t any reason to have two of the same. I&#39;m gonna put this ticket on here. Oh, I gotta go get in the meeting. Okay, I&#39;m gonna go get in a meeting, and um… Maria said that she will… meet with you now. So, um, you could just reach out to her in Slack. 29:04 Michael H.: Yes.
View original transcript at Tactiq.</p>
