#!/usr/bin/env python3
import PyPDF2
import sys
import os
from datetime import datetime
import re

def extract_pdf_text(pdf_path):
    """Extract text content from a PDF file."""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            num_pages = len(pdf_reader.pages)
            
            for page_num in range(num_pages):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
            
            return text, num_pages
    except Exception as e:
        return f"Error reading PDF: {str(e)}", 0

def analyze_pdf_content(text):
    """Analyze PDF content for relevance and currency."""
    analysis = {
        'has_dates': False,
        'recent_dates': [],
        'has_contacts': False,
        'has_credentials': False,
        'has_specifications': False,
        'keywords': []
    }
    
    # Check for dates (various formats)
    date_patterns = [
        r'\b\d{1,2}/\d{1,2}/\d{2,4}\b',
        r'\b\d{4}-\d{2}-\d{2}\b',
        r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b'
    ]
    
    for pattern in date_patterns:
        dates = re.findall(pattern, text, re.IGNORECASE)
        if dates:
            analysis['has_dates'] = True
            analysis['recent_dates'].extend(dates[:5])  # First 5 dates found
    
    # Check for contact information
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    phone_pattern = r'\b(?:\+?1[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}\b'
    
    if re.search(email_pattern, text) or re.search(phone_pattern, text):
        analysis['has_contacts'] = True
    
    # Check for potential credentials (be careful not to expose)
    credential_keywords = ['password', 'pwd', 'pass:', 'credential', 'secret', 'api key', 'token']
    for keyword in credential_keywords:
        if keyword.lower() in text.lower():
            analysis['has_credentials'] = True
            break
    
    # Check for EDI specifications
    edi_keywords = ['810', '850', '855', '856', 'X12', 'EDIFACT', 'segment', 'qualifier', 
                    'trading partner', 'ISA', 'GS', 'ST', 'SE', 'GE', 'IEA', 'EDI']
    
    found_keywords = []
    for keyword in edi_keywords:
        if keyword in text:
            found_keywords.append(keyword)
    
    if found_keywords:
        analysis['has_specifications'] = True
        analysis['keywords'] = found_keywords[:10]  # First 10 keywords
    
    return analysis

def get_file_info(pdf_path):
    """Get file modification time and size."""
    stat = os.stat(pdf_path)
    mod_time = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
    size_mb = stat.st_size / (1024 * 1024)
    return mod_time, size_mb

# PDF files to analyze
pdf_files = [
    '/home/<USER>/edi_knowledge_base/trading_partners/wisebatch.pdf',
    '/home/<USER>/edi_knowledge_base/trading_partners/wisebatch 2.pdf',
    '/home/<USER>/edi_knowledge_base/trading_partners/Symphony_Beauty_Box_Corp_CVS_Production_Checklist-Tier1.pdf',
    '/home/<USER>/edi_knowledge_base/trading_partners/trading partners.pdf',
    '/home/<USER>/edi_knowledge_base/trading_partners/customers.pdf',
    '/home/<USER>/edi_knowledge_base/trading_partners/mckesson.pdf'
]

print("TRADING PARTNER PDF ANALYSIS")
print("=" * 80)

for pdf_path in pdf_files:
    if os.path.exists(pdf_path):
        filename = os.path.basename(pdf_path)
        print(f"\n### {filename}")
        print("-" * 40)
        
        # Get file info
        mod_time, size_mb = get_file_info(pdf_path)
        print(f"Modified: {mod_time}")
        print(f"Size: {size_mb:.2f} MB")
        
        # Extract and analyze content
        text, num_pages = extract_pdf_text(pdf_path)
        
        if isinstance(text, str) and "Error" not in text:
            print(f"Pages: {num_pages}")
            print(f"Text length: {len(text)} characters")
            
            # Analyze content
            analysis = analyze_pdf_content(text)
            
            print("\nContent Analysis:")
            print(f"- Contains dates: {analysis['has_dates']}")
            if analysis['recent_dates']:
                print(f"  Sample dates: {', '.join(analysis['recent_dates'][:3])}")
            print(f"- Contains contacts: {analysis['has_contacts']}")
            print(f"- Contains credentials: {analysis['has_credentials']}")
            print(f"- Contains EDI specs: {analysis['has_specifications']}")
            if analysis['keywords']:
                print(f"  EDI keywords found: {', '.join(analysis['keywords'][:5])}")
            
            # Sample content (first 500 chars, cleaned)
            sample = text[:500].replace('\n', ' ').strip()
            if sample:
                print(f"\nSample content:")
                print(f"{sample}...")
        else:
            print(f"Error: {text}")
    else:
        print(f"\n### {os.path.basename(pdf_path)}")
        print("FILE NOT FOUND")

print("\n" + "=" * 80)