# CS-45069: Fwd: <PERSON><PERSON><PERSON>'s Invoices

## Ticket Information
- **Key**: CS-45069
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON>
- **Created**: 2025-06-06T10:51:06.352-0600
- **Updated**: 2025-06-12T10:35:05.353-0600
- **Customer**: <PERSON>

## Description
Please see below for Costco. They are stating the TDS tag is pulling incorrect information.
{color:#cfcbc4}Thank you!{color}



---------- Forwarded message ---------
From:  *EDI* <[<EMAIL>|mailto:<EMAIL>]>
Date: Fri, Jun 6, 2025 at 12:40 PM
Subject: Re: Saude<PERSON>'s Invoices
To: <PERSON> <[<EMAIL>|mailto:<EMAIL>]>





{color:#172B4D}Hello <PERSON>,{color}{color:#172B4D}{color}

{color:#172B4D}We received the invoice but it will fail again as the invoice total in TDS _01 does not reflect the SAC amount. Please correct and retransmit invoice. If you are retransmitting today use a different invoice number other wise our system will think it as a duplicate{color}{color:#172B4D}{color}

{color:#172B4D} ST*810*6738 
 BIG*20241230*AR-496493*20241224*011961224270 
 ITD******20250123 
 IT1**240*CA*10.08**IN*1025795 
 PID*F****AA Lg Whl Kirklnd CHCF 5dz OW 
 IT1**720*CA*4.05**IN*637598 
 PID*F****AA LG WHC Kirkland CFCH 2 dz 
 TDS**533520*  *should be 528185* ( 2419.20 + 2916 -53.35 =5281.85) 
 SAC*A*F800***5335 
 CTT*2 
 SE*11*6738{color} 
 












































{color:#172B4D}Thank you,{color}{color:#172B4D}{color}


{color:#172B4D}V{color}{color:#172B4D}andana Swaminathan{color}{color:#172B4D}{color}
{color:#172B4D}Costco EDI{color} 


 {color:#3d85c6}{color}[{color:#3d85c6} *<EMAIL>*{color}|mailto:<EMAIL>]{color:#3d85c6}{color} 
{color:#3d85c6}{color}

 *{color:#444444} *PLEASE INCLUDE YOUR COSTCO VENDOR NUMBER ON EMAILS*{color}







On Fri, Jun 6, 2025 at 9:24 AM Ashley Rivera <[<EMAIL>|mailto:<EMAIL>]> wrote:

{quote}

This has been retransmitted with the SAC segment. Let me know if you have any questions

Thank you!{color:#222222}{color}{color:#172B4D}{color}



{color:#172B4D}{color}{color:#1155cc}{color}!https://lh3.googleusercontent.com/yXEsLEfUBCDfE7zKAxNi3VdVWGdDdE72R-mwBTX7CcY6ilDQ7Are8pPp_AFNK1K2vQcPgb27SosuNubGy2s7jatjU0Zk-qq95gw2lEN5WIAt4oOZ68njL1bEwa7nGexdrcfm48um!{color:#1155cc} <[https://urldefense.com/v3/__http://www.saudereggs.com/__;!!OuofLoc!uSLJjpffjtf2vHi8zPf5hc4ZMNCP3Qx5PuXkzliw1jeIyMlv9eVM8qrlVURo1k4mKVCA64qt6RCrKA$]>{color}{color:#172B4D}{color}{color:#222222}{color}


{color:#222222}{color}{color:#783f04}Ashley Rivera{color}{color:#222222}{color}

{color:#222222}{color}{color:#ff9900}Manager | Information Technology{color}{color:#222222}{color}

{color:#222222}{color}{color:#444444}Corp. ************ Ext.7414{color}{color:#222222}{color}

{color:#222222}{color}{color:#444444}Direct ************{color}{color:#222222}{color}

{color:#bdb7af}Cell Phone ************{color}{color:#222222} |{color}{color:#bab4ab}{color} [{color:#bab4ab}<EMAIL>{color}|mailto:<EMAIL>]{color:#bab4ab}{color}{color:#222222}{color}

{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4xfHkPcuPwgvkJlOqtaOdXGrraAENnoktQMr-nBTYuo3WRZblrA3Jbi_fGG8AybPivefwbBXDlvH3vH!{color:#1155cc} <[https://urldefense.com/v3/__https://www.facebook.com/Sauder0Eggs-184170408279763/__;!!OuofLoc!uSLJjpffjtf2vHi8zPf5hc4ZMNCP3Qx5PuXkzliw1jeIyMlv9eVM8qrlVURo1k4mKVCA64pPUYBorg$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4yGlg0lrEKAI6Nd1npJ22VhMiavSOaJaschD1IjnFNav4ht8yuuaF30vKz1wilBcIYhUOv86MAECJw7!{color:#1155cc} <[https://urldefense.com/v3/__https://youtu.be/oli8kreNaAI__;!!OuofLoc!uSLJjpffjtf2vHi8zPf5hc4ZMNCP3Qx5PuXkzliw1jeIyMlv9eVM8qrlVURo1k4mKVCA64q9PwojPg$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4zuVRlI32fN6jCTS0Jg2qDIdzv824vJ1ZNRFwQp87OnaodjIGtn8xsSNPC9_qEsHDjfUAIFGWgyKCD0!{color:#1155cc} <[https://urldefense.com/v3/__https://twitter.com/SaudersEggs__;!!OuofLoc!uSLJjpffjtf2vHi8zPf5hc4ZMNCP3Qx5PuXkzliw1jeIyMlv9eVM8qrlVURo1k4mKVCA64oKRzyuRg$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4zfwkbSWbyiOouspP1JPJBF9tGqkF_L2ARJZvbupxhezw_pHNOmXq27Mh2Ol0mRjSXtYFiXmCxY-mN5!{color:#1155cc} <[https://urldefense.com/v3/__https://www.saudereggs.com/blog/__;!!OuofLoc!uSLJjpffjtf2vHi8zPf5hc4ZMNCP3Qx5PuXkzliw1jeIyMlv9eVM8qrlVURo1k4mKVCA64q6dW-f0A$]>{color}{color:#222222}{color}



On Fri, Jun 6, 2025 at 11:32 AM Ashley Rivera <[<EMAIL>|mailto:<EMAIL>]> wrote:



Of course. I will retransmit it now.{color:#222222}{color}{color:#172B4D}{color}




{color:#172B4D}{color}{color:#1155cc}{color}!https://lh3.googleusercontent.com/yXEsLEfUBCDfE7zKAxNi3VdVWGdDdE72R-mwBTX7CcY6ilDQ7Are8pPp_AFNK1K2vQcPgb27SosuNubGy2s7jatjU0Zk-qq95gw2lEN5WIAt4oOZ68njL1bEwa7nGexdrcfm48um!{color:#1155cc} <[https://urldefense.com/v3/__http://www.saudereggs.com/__;!!OuofLoc!uSLJjpffjtf2vHi8zPf5hc4ZMNCP3Qx5PuXkzliw1jeIyMlv9eVM8qrlVURo1k4mKVCA64qt6RCrKA$]>{color}{color:#172B4D}{color}{color:#222222}{color}


{color:#222222}{color}{color:#783f04}Ashley Rivera{color}{color:#222222}{color}

{color:#222222}{color}{color:#ff9900}Manager | Information Technology{color}{color:#222222}{color}

{color:#222222}{color}{color:#444444}Corp. ************ Ext.7414{color}{color:#222222}{color}

{color:#222222}{color}{color:#444444}Direct ************{color}{color:#222222}{color}

{color:#bdb7af}Cell Phone ************{color}{color:#222222} |{color}{color:#bab4ab}{color} [{color:#bab4ab}<EMAIL>{color}|mailto:<EMAIL>]{color:#bab4ab}{color}{color:#222222}{color}

{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4xfHkPcuPwgvkJlOqtaOdXGrraAENnoktQMr-nBTYuo3WRZblrA3Jbi_fGG8AybPivefwbBXDlvH3vH!{color:#1155cc} <[https://urldefense.com/v3/__https://www.facebook.com/Sauder0Eggs-184170408279763/__;!!OuofLoc!uSLJjpffjtf2vHi8zPf5hc4ZMNCP3Qx5PuXkzliw1jeIyMlv9eVM8qrlVURo1k4mKVCA64pPUYBorg$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4yGlg0lrEKAI6Nd1npJ22VhMiavSOaJaschD1IjnFNav4ht8yuuaF30vKz1wilBcIYhUOv86MAECJw7!{color:#1155cc} <[https://urldefense.com/v3/__https://youtu.be/oli8kreNaAI__;!!OuofLoc!uSLJjpffjtf2vHi8zPf5hc4ZMNCP3Qx5PuXkzliw1jeIyMlv9eVM8qrlVURo1k4mKVCA64q9PwojPg$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4zuVRlI32fN6jCTS0Jg2qDIdzv824vJ1ZNRFwQp87OnaodjIGtn8xsSNPC9_qEsHDjfUAIFGWgyKCD0!{color:#1155cc} <[https://urldefense.com/v3/__https://twitter.com/SaudersEggs__;!!OuofLoc!uSLJjpffjtf2vHi8zPf5hc4ZMNCP3Qx5PuXkzliw1jeIyMlv9eVM8qrlVURo1k4mKVCA64oKRzyuRg$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4zfwkbSWbyiOouspP1JPJBF9tGqkF_L2ARJZvbupxhezw_pHNOmXq27Mh2Ol0mRjSXtYFiXmCxY-mN5!{color:#1155cc} <[https://urldefense.com/v3/__https://www.saudereggs.com/blog/__;!!OuofLoc!uSLJjpffjtf2vHi8zPf5hc4ZMNCP3Qx5PuXkzliw1jeIyMlv9eVM8qrlVURo1k4mKVCA64q6dW-f0A$]>{color}{color:#222222}{color}



On Fri, Jun 6, 2025 at 11:26 AM EDI <[<EMAIL>|mailto:<EMAIL>]> wrote:




{color:#172B4D}We don't have access to the data, can you retransmit one invoice with SAC segment and let me know, I will track its progress.{color}












































{color:#172B4D}Thank you,{color}
{color:#172B4D}Costco EDI{color} 

 {color:#3d85c6}{color}[{color:#3d85c6} *<EMAIL>*{color}|mailto:<EMAIL>]{color:#3d85c6}{color} 
{color:#3d85c6}{color}

 *{color:#444444} *PLEASE INCLUDE YOUR COSTCO VENDOR NUMBER ON EMAILS*{color}







On Fri, Jun 6, 2025 at 8:16 AM Ashley Rivera <[<EMAIL>|mailto:<EMAIL>]> wrote:



!image (24f05697-61ed-4001-bb7b-81e33015b6c4).png|thumbnail!


We are sending it out correctly to Costco. The SAC allowance line is there. It is also on the invoice you received but with no description. Is there something Costco has that is not including that line?

Thank you!{color:#222222}{color}{color:#172B4D}{color}



{color:#172B4D}{color}{color:#1155cc}{color}!https://lh3.googleusercontent.com/yXEsLEfUBCDfE7zKAxNi3VdVWGdDdE72R-mwBTX7CcY6ilDQ7Are8pPp_AFNK1K2vQcPgb27SosuNubGy2s7jatjU0Zk-qq95gw2lEN5WIAt4oOZ68njL1bEwa7nGexdrcfm48um!{color:#1155cc} <[https://urldefense.com/v3/__http://www.saudereggs.com/__;!!OuofLoc!oDTU5fudv2ZNwnphpAJ_Em2uvOwPsIUBDwcxwwa1bvXLi8YSsK8hwburzn4pNb4gAiSJzeXttJZ29Q$]>{color}{color:#172B4D}{color}{color:#222222}{color}


{color:#222222}{color}{color:#783f04}Ashley Rivera{color}{color:#222222}{color}

{color:#222222}{color}{color:#ff9900}Manager | Information Technology{color}{color:#222222}{color}

{color:#222222}{color}{color:#444444}Corp. ************ Ext.7414{color}{color:#222222}{color}

{color:#222222}{color}{color:#444444}Direct ************{color}{color:#222222}{color}

{color:#bdb7af}Cell Phone ************{color}{color:#222222} |{color}{color:#bab4ab}{color} [{color:#bab4ab}<EMAIL>{color}|mailto:<EMAIL>]{color:#bab4ab}{color}{color:#222222}{color}

{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4xfHkPcuPwgvkJlOqtaOdXGrraAENnoktQMr-nBTYuo3WRZblrA3Jbi_fGG8AybPivefwbBXDlvH3vH!{color:#1155cc} <[https://urldefense.com/v3/__https://www.facebook.com/Sauder0Eggs-184170408279763/__;!!OuofLoc!oDTU5fudv2ZNwnphpAJ_Em2uvOwPsIUBDwcxwwa1bvXLi8YSsK8hwburzn4pNb4gAiSJzeVGeX4bQA$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4yGlg0lrEKAI6Nd1npJ22VhMiavSOaJaschD1IjnFNav4ht8yuuaF30vKz1wilBcIYhUOv86MAECJw7!{color:#1155cc} <[https://urldefense.com/v3/__https://youtu.be/oli8kreNaAI__;!!OuofLoc!oDTU5fudv2ZNwnphpAJ_Em2uvOwPsIUBDwcxwwa1bvXLi8YSsK8hwburzn4pNb4gAiSJzeVBPhc-rw$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4zuVRlI32fN6jCTS0Jg2qDIdzv824vJ1ZNRFwQp87OnaodjIGtn8xsSNPC9_qEsHDjfUAIFGWgyKCD0!{color:#1155cc} <[https://urldefense.com/v3/__https://twitter.com/SaudersEggs__;!!OuofLoc!oDTU5fudv2ZNwnphpAJ_Em2uvOwPsIUBDwcxwwa1bvXLi8YSsK8hwburzn4pNb4gAiSJzeXnLeSyAQ$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4zfwkbSWbyiOouspP1JPJBF9tGqkF_L2ARJZvbupxhezw_pHNOmXq27Mh2Ol0mRjSXtYFiXmCxY-mN5!{color:#1155cc} <[https://urldefense.com/v3/__https://www.saudereggs.com/blog/__;!!OuofLoc!oDTU5fudv2ZNwnphpAJ_Em2uvOwPsIUBDwcxwwa1bvXLi8YSsK8hwburzn4pNb4gAiSJzeUtBq2grA$]>{color}{color:#222222}{color}



On Fri, Jun 6, 2025 at 10:53 AM EDI <[<EMAIL>|mailto:<EMAIL>]> wrote:




{color:#172B4D}Good Morning Ashley,{color}{color:#172B4D}{color}

{color:#172B4D}The invoices were rejected due to item level total not matching the invoice total. See screen shot of one of the rejected invoice. If any charge or discount was send on the PO in SAC segment, it should be returned back on the invoice in SAC segment.{color}
{color:#172B4D}Please correct and retransmit invoices.{color}{color:#172B4D}{color}{color:#172B4D}{color}{color:#172B4D}{color}


!image.png|thumbnail!{color:#172B4D}{color}













































{color:#172B4D}Thank you,{color}{color:#172B4D}{color}


{color:#172B4D}V{color}{color:#172B4D}andana Swaminathan{color}{color:#172B4D}{color}
{color:#172B4D}Costco EDI{color} 


 {color:#3d85c6}{color}[{color:#3d85c6} *<EMAIL>*{color}|mailto:<EMAIL>]{color:#3d85c6}{color} 
{color:#3d85c6}{color}

 *{color:#444444} *PLEASE INCLUDE YOUR COSTCO VENDOR NUMBER ON EMAILS*{color}







On Fri, Jun 6, 2025 at 6:07 AM Ashley Rivera <[<EMAIL>|mailto:<EMAIL>]> wrote:



Good morning, 

We have a number of invoices from December that have no been paid. We have resubmitted them twice and they are still not getting paid. Could we take a look at why these aren't being paid? The Invoices are being accepted through our EDI provider.

Please see attached excel spreadsheet.

Thank you!{color:#222222}{color}{color:#172B4D}{color}



{color:#172B4D}{color}{color:#1155cc}{color}!https://lh3.googleusercontent.com/yXEsLEfUBCDfE7zKAxNi3VdVWGdDdE72R-mwBTX7CcY6ilDQ7Are8pPp_AFNK1K2vQcPgb27SosuNubGy2s7jatjU0Zk-qq95gw2lEN5WIAt4oOZ68njL1bEwa7nGexdrcfm48um!{color:#1155cc} <[https://urldefense.com/v3/__http://www.saudereggs.com/__;!!OuofLoc!tQVO3FqXS-x6WYoM-cs2adws5M3mjNE_DsAWMxeXEcJTpGBjhgbUX35WVpbE-rt6fKi1wC3UhgJvVg$]>{color}{color:#172B4D}{color}{color:#222222}{color}


{color:#222222}{color}{color:#783f04}Ashley Rivera{color}{color:#222222}{color}

{color:#222222}{color}{color:#ff9900}Manager | Information Technology{color}{color:#222222}{color}

{color:#222222}{color}{color:#444444}Corp. ************ Ext.7414{color}{color:#222222}{color}

{color:#222222}{color}{color:#444444}Direct ************{color}{color:#222222}{color}

{color:#bdb7af}Cell Phone ************{color}{color:#222222} |{color}{color:#bab4ab}{color} [{color:#bab4ab}<EMAIL>{color}|mailto:<EMAIL>]{color:#bab4ab}{color}{color:#222222}{color}

{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4xfHkPcuPwgvkJlOqtaOdXGrraAENnoktQMr-nBTYuo3WRZblrA3Jbi_fGG8AybPivefwbBXDlvH3vH!{color:#1155cc} <[https://urldefense.com/v3/__https://www.facebook.com/Sauder0Eggs-184170408279763/__;!!OuofLoc!tQVO3FqXS-x6WYoM-cs2adws5M3mjNE_DsAWMxeXEcJTpGBjhgbUX35WVpbE-rt6fKi1wC04KYjyhQ$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4yGlg0lrEKAI6Nd1npJ22VhMiavSOaJaschD1IjnFNav4ht8yuuaF30vKz1wilBcIYhUOv86MAECJw7!{color:#1155cc} <[https://urldefense.com/v3/__https://youtu.be/oli8kreNaAI__;!!OuofLoc!tQVO3FqXS-x6WYoM-cs2adws5M3mjNE_DsAWMxeXEcJTpGBjhgbUX35WVpbE-rt6fKi1wC0yIdM1Gw$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4zuVRlI32fN6jCTS0Jg2qDIdzv824vJ1ZNRFwQp87OnaodjIGtn8xsSNPC9_qEsHDjfUAIFGWgyKCD0!{color:#1155cc} <[https://urldefense.com/v3/__https://twitter.com/SaudersEggs__;!!OuofLoc!tQVO3FqXS-x6WYoM-cs2adws5M3mjNE_DsAWMxeXEcJTpGBjhgbUX35WVpbE-rt6fKi1wC0Yr8h7Tw$]>{color}{color:#222222}{color}{color:#222222}{color}{color:#1155cc}{color}!https://ci3.googleusercontent.com/mail-sig/AIorK4zfwkbSWbyiOouspP1JPJBF9tGqkF_L2ARJZvbupxhezw_pHNOmXq27Mh2Ol0mRjSXtYFiXmCxY-mN5!{color:#1155cc} <[https://urldefense.com/v3/__https://www.saudereggs.com/blog/__;!!OuofLoc!tQVO3FqXS-x6WYoM-cs2adws5M3mjNE_DsAWMxeXEcJTpGBjhgbUX35WVpbE-rt6fKi1wC0DO-us-w$]>{color}{color:#222222}{color} 
 
 
 
 
 {quote}{adf}{"type":"expand","content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://lh3.googleusercontent.com/yXEsLEfUBCDfE7zKAxNi3VdVWGdDdE72R-mwBTX7CcY6ilDQ7Are8pPp_AFNK1K2vQcPgb27SosuNubGy2s7jatjU0Zk-qq95gw2lEN5WIAt4oOZ68njL1bEwa7nGexdrcfm48um","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}}]},{"type":"text","text":"http://www.saudereggs.com/","marks":[{"type":"link","attrs":{"href":"http://www.saudereggs.com/"}},{"type":"textColor","attrs":{"color":"#1155cc"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}}]}]},{"type":"paragraph","content":[{"type":"text","text":"Ashley Rivera","marks":[{"type":"textColor","attrs":{"color":"#783f04"}}]},{"type":"text","text":" "}]},{"type":"paragraph","content":[{"type":"text","text":"Manager | Information Technology","marks":[{"type":"textColor","attrs":{"color":"#ff9900"}}]},{"type":"text","text":" "}]},{"type":"paragraph","content":[{"type":"text","text":"Corp. ************ Ext.7414","marks":[{"type":"textColor","attrs":{"color":"#444444"}}]},{"type":"text","text":" "}]},{"type":"paragraph","content":[{"type":"text","text":"Direct ************","marks":[{"type":"textColor","attrs":{"color":"#444444"}}]},{"type":"text","text":" "}]},{"type":"paragraph","content":[{"type":"text","text":"Cell Phone ************","marks":[{"type":"textColor","attrs":{"color":"#bdb7af"}}]},{"type":"text","text":"|","marks":[{"type":"textColor","attrs":{"color":"#222222"}}]},{"type":"text","text":" "},{"type":"text","text":"<EMAIL>","marks":[{"type":"textColor","attrs":{"color":"#bab4ab"}},{"type":"link","attrs":{"href":"mailto:<EMAIL>"}}]}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://ci3.googleusercontent.com/mail-sig/AIorK4xfHkPcuPwgvkJlOqtaOdXGrraAENnoktQMr-nBTYuo3WRZblrA3Jbi_fGG8AybPivefwbBXDlvH3vH","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}}]},{"type":"text","text":"https://www.facebook.com/Sauder0Eggs-184170408279763/","marks":[{"type":"link","attrs":{"href":"https://www.facebook.com/Sauder0Eggs-184170408279763/"}},{"type":"textColor","attrs":{"color":"#1155cc"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}}]}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://ci3.googleusercontent.com/mail-sig/AIorK4yGlg0lrEKAI6Nd1npJ22VhMiavSOaJaschD1IjnFNav4ht8yuuaF30vKz1wilBcIYhUOv86MAECJw7","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}}]},{"type":"text","text":"https://youtu.be/oli8kreNaAI","marks":[{"type":"link","attrs":{"href":"https://youtu.be/oli8kreNaAI"}},{"type":"textColor","attrs":{"color":"#1155cc"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}}]}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://ci3.googleusercontent.com/mail-sig/AIorK4zuVRlI32fN6jCTS0Jg2qDIdzv824vJ1ZNRFwQp87OnaodjIGtn8xsSNPC9_qEsHDjfUAIFGWgyKCD0","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}}]},{"type":"text","text":"https://twitter.com/SaudersEggs","marks":[{"type":"link","attrs":{"href":"https://twitter.com/SaudersEggs"}},{"type":"textColor","attrs":{"color":"#1155cc"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}}]}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://ci3.googleusercontent.com/mail-sig/AIorK4zfwkbSWbyiOouspP1JPJBF9tGqkF_L2ARJZvbupxhezw_pHNOmXq27Mh2Ol0mRjSXtYFiXmCxY-mN5","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"<","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}}]},{"type":"text","text":"https://www.saudereggs.com/blog/","marks":[{"type":"link","attrs":{"href":"https://www.saudereggs.com/blog/"}},{"type":"textColor","attrs":{"color":"#1155cc"}}]},{"type":"text","text":">","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}}]}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

