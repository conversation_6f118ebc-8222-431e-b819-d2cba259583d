"""
Query Intelligence Agent - Natural Language Understanding for EDI Issues
Transforms vague queries into precise root cause investigations
"""

import re
import json
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from config import settings
try:
    from .claude_direct import get_claude
    <PERSON>_DIRECT_AVAILABLE = True
except ImportError:
    CLAUDE_DIRECT_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class QueryAnalysis:
    """Structured analysis of a user query"""
    original_query: str
    intent: str  # search, troubleshoot, analyze, monitor
    entities: Dict[str, List[str]]  # partner, transaction, error_type, etc.
    time_context: Optional[str]  # recent, today, last_week, specific_date
    urgency: int  # 1-10 scale
    probable_issues: List[str]
    search_expansions: List[str]
    confidence: float


class QueryIntelligenceAgent:
    """
    AI-powered query understanding using Claude
    Transforms natural language into structured search parameters
    """
    
    # Common EDI patterns and terminology
    EDI_PATTERNS = {
        'transactions': {
            '850': 'Purchase Order',
            '855': 'Purchase Order Acknowledgment', 
            '856': 'Advance Ship Notice (ASN)',
            '810': 'Invoice',
            '997': 'Functional Acknowledgment',
            '214': 'Shipment Status',
            '204': 'Motor Carrier Load Tender'
        },
        'errors': {
            'isa': ['ISA segment', 'interchange control header', 'envelope error'],
            'missing': ['missing segment', 'required field', 'mandatory element'],
            'format': ['invalid format', 'date format', 'number format', 'validation error'],
            'connection': ['timeout', 'connection refused', 'certificate', 'AS2', 'FTP', 'SFTP'],
            'duplicate': ['duplicate control number', 'duplicate invoice', 'already processed']
        },
        'partners': {
            'walmart': ['wal-mart', 'wmrt', 'walmart dc'],
            'target': ['target corp', 'tgt'],
            'amazon': ['amzn', 'amazon vendor'],
            'mckesson': ['mck', 'mckesson pharma']
        }
    }
    
    def __init__(self, use_claude_direct: bool = True):
        """Initialize the Query Intelligence Agent"""
        self.use_claude_direct = use_claude_direct and CLAUDE_DIRECT_AVAILABLE
        self.claude = None
        
        if self.use_claude_direct:
            try:
                self.claude = get_claude()
                logger.info("Claude Direct Interface initialized - using Claude Opus 4 directly")
            except Exception as e:
                logger.error(f"Failed to initialize Claude Direct: {e}")
                self.use_claude_direct = False
        
        if not self.use_claude_direct:
            logger.info("Using pattern matching only")
    
    def analyze_query(self, query: str, context: Optional[Dict] = None) -> QueryAnalysis:
        """
        Analyze a user query to understand intent and extract information
        
        Args:
            query: The user's natural language query
            context: Optional context (recent errors, customer info, etc.)
            
        Returns:
            QueryAnalysis object with structured information
        """
        # Start with pattern-based analysis
        base_analysis = self._pattern_analysis(query)
        
        # Enhance with Claude Direct if available
        if self.use_claude_direct and self.claude:
            try:
                enhanced_analysis = self._claude_direct_analysis(query, base_analysis, context)
                return enhanced_analysis
            except Exception as e:
                logger.error(f"Claude Direct analysis failed, falling back to patterns: {e}")
        
        return base_analysis
    
    def _pattern_analysis(self, query: str) -> QueryAnalysis:
        """Basic pattern matching analysis"""
        query_lower = query.lower()
        
        # Extract entities
        entities = {
            'transactions': self._extract_transactions(query_lower),
            'partners': self._extract_partners(query_lower),
            'errors': self._extract_errors(query_lower),
            'time_references': self._extract_time_references(query_lower)
        }
        
        # Determine intent
        intent = self._determine_intent(query_lower)
        
        # Generate search expansions
        expansions = self._generate_expansions(entities)
        
        # Identify probable issues
        probable_issues = self._identify_probable_issues(entities, query_lower)
        
        # Determine time context
        time_context = self._determine_time_context(entities['time_references'])
        
        # Calculate urgency
        urgency = self._calculate_urgency(query_lower)
        
        return QueryAnalysis(
            original_query=query,
            intent=intent,
            entities=entities,
            time_context=time_context,
            urgency=urgency,
            probable_issues=probable_issues,
            search_expansions=expansions,
            confidence=0.7  # Pattern matching baseline confidence
        )
    
    def _claude_direct_analysis(self, query: str, base_analysis: QueryAnalysis, 
                              context: Optional[Dict] = None) -> QueryAnalysis:
        """Enhanced analysis using Claude"""
        
        # Build context for Claude
        system_prompt = """You are an expert EDI (Electronic Data Interchange) support analyst.
        Analyze the user query and provide a structured analysis to help diagnose EDI issues.
        Focus on identifying:
        1. The actual problem the user is trying to solve
        2. Potential root causes based on common EDI issues
        3. Specific things to check or investigate
        
        Common EDI issues include:
        - Trading partner configuration changes
        - Certificate expiration
        - Format validation errors
        - Connection/transmission failures
        - Mapping configuration problems
        """
        
        user_prompt = f"""Analyze this EDI support query:
        Query: "{query}"
        
        Initial analysis:
        - Detected entities: {json.dumps(base_analysis.entities, indent=2)}
        - Probable issues: {base_analysis.probable_issues}
        
        Additional context: {json.dumps(context, indent=2) if context else 'None'}
        
        Provide a JSON response with:
        {{
            "intent": "search|troubleshoot|analyze|monitor",
            "root_cause_hypotheses": ["hypothesis1", "hypothesis2", ...],
            "specific_checks": ["check1", "check2", ...],
            "search_queries": ["expanded query1", "expanded query2", ...],
            "urgency": 1-10,
            "confidence": 0.0-1.0
        }}
        """
        
        try:
            # Use Claude Direct for analysis
            claude_result = self.claude.analyze_query(query, context)
            
            # Merge with base analysis
            return QueryAnalysis(
                original_query=query,
                intent=claude_result.get('intent', base_analysis.intent),
                entities=claude_result.get('entities', base_analysis.entities),
                time_context=base_analysis.time_context,
                urgency=claude_result.get('urgency', base_analysis.urgency),
                probable_issues=claude_result.get('probable_issues', base_analysis.probable_issues),
                search_expansions=claude_result.get('search_expansions', base_analysis.search_expansions),
                confidence=claude_result.get('confidence', 0.95)
            )
            
        except Exception as e:
            logger.error(f"Claude Direct enhancement failed: {e}")
            return base_analysis
    
    def _extract_transactions(self, query: str) -> List[str]:
        """Extract EDI transaction codes from query"""
        transactions = []
        
        # Direct transaction code matches (e.g., "850", "856")
        for code, name in self.EDI_PATTERNS['transactions'].items():
            if code in query or name.lower() in query:
                transactions.append(code)
        
        # Common variations
        if 'po' in query or 'purchase order' in query:
            transactions.append('850')
        if 'asn' in query or 'ship notice' in query:
            transactions.append('856')
        if 'invoice' in query:
            transactions.append('810')
            
        return list(set(transactions))
    
    def _extract_partners(self, query: str) -> List[str]:
        """Extract trading partner names from query"""
        partners = []
        
        for partner, variations in self.EDI_PATTERNS['partners'].items():
            if partner in query:
                partners.append(partner)
            else:
                for variation in variations:
                    if variation in query:
                        partners.append(partner)
                        break
        
        return list(set(partners))
    
    def _extract_errors(self, query: str) -> List[str]:
        """Extract error types from query"""
        errors = []
        
        for error_type, patterns in self.EDI_PATTERNS['errors'].items():
            for pattern in patterns:
                if pattern in query:
                    errors.append(error_type)
                    break
        
        return list(set(errors))
    
    def _extract_time_references(self, query: str) -> List[str]:
        """Extract time references from query"""
        time_refs = []
        
        # Common time patterns
        patterns = {
            'today': r'\btoday\b',
            'yesterday': r'\byesterday\b',
            'recent': r'\brecent(ly)?\b',
            'last_hour': r'\blast\s+hour\b',
            'this_morning': r'\bthis\s+morning\b',
            'tonight': r'\btonight\b',
            'last_week': r'\blast\s+week\b'
        }
        
        for ref, pattern in patterns.items():
            if re.search(pattern, query):
                time_refs.append(ref)
        
        return time_refs
    
    def _determine_intent(self, query: str) -> str:
        """Determine the primary intent of the query"""
        if any(word in query for word in ['failing', 'error', 'issue', 'problem', 'broken']):
            return 'troubleshoot'
        elif any(word in query for word in ['analyze', 'pattern', 'trend', 'report']):
            return 'analyze'
        elif any(word in query for word in ['monitor', 'watch', 'alert', 'notify']):
            return 'monitor'
        else:
            return 'search'
    
    def _generate_expansions(self, entities: Dict) -> List[str]:
        """Generate expanded search queries based on entities"""
        expansions = []
        
        # Combine entities for search
        if entities['transactions'] and entities['partners']:
            for trans in entities['transactions']:
                for partner in entities['partners']:
                    expansions.append(f"{partner} {trans}")
                    expansions.append(f"{trans} {partner}")
        
        # Add error-specific searches
        if entities['errors']:
            for error in entities['errors']:
                if entities['partners']:
                    for partner in entities['partners']:
                        expansions.append(f"{partner} {error}")
                if entities['transactions']:
                    for trans in entities['transactions']:
                        expansions.append(f"{trans} {error}")
        
        return expansions[:10]  # Limit to top 10 expansions
    
    def _identify_probable_issues(self, entities: Dict, query: str) -> List[str]:
        """Identify probable issues based on query analysis"""
        issues = []
        
        # Transaction-specific issues
        if '850' in entities['transactions']:
            issues.append("Purchase Order format validation error")
            issues.append("Missing required PO segments")
        
        if '856' in entities['transactions']:
            issues.append("ASN date format mismatch")
            issues.append("Invalid ship-to location code")
        
        # Error-specific issues
        if 'connection' in entities['errors']:
            issues.append("AS2/FTP connection timeout")
            issues.append("Certificate expiration")
            issues.append("Firewall blocking transmission")
        
        if 'format' in entities['errors']:
            issues.append("Date/time format change by partner")
            issues.append("Character encoding mismatch")
        
        # Partner-specific common issues
        if 'walmart' in entities['partners']:
            issues.append("Walmart EDI specification update")
            issues.append("Walmart DC routing code change")
        
        return issues[:5]  # Top 5 most likely issues
    
    def _determine_time_context(self, time_refs: List[str]) -> str:
        """Determine the time context for the search"""
        if 'today' in time_refs or 'recent' in time_refs:
            return 'last_24_hours'
        elif 'yesterday' in time_refs:
            return 'yesterday'
        elif 'last_hour' in time_refs:
            return 'last_hour'
        elif 'last_week' in time_refs:
            return 'last_week'
        else:
            return 'recent'  # Default to recent
    
    def _calculate_urgency(self, query: str) -> int:
        """Calculate urgency score (1-10) based on query"""
        urgency = 5  # baseline
        
        # Increase for urgent keywords
        urgent_words = ['urgent', 'critical', 'asap', 'emergency', 'down', 'failing', 'blocked']
        for word in urgent_words:
            if word in query:
                urgency = min(urgency + 2, 10)
        
        # Increase for production mentions
        if 'production' in query or 'prod' in query:
            urgency = min(urgency + 1, 10)
        
        # Decrease for analysis/report requests
        if 'analyze' in query or 'report' in query:
            urgency = max(urgency - 2, 1)
        
        return urgency


def demonstrate_query_intelligence():
    """Demonstrate the Query Intelligence Agent"""
    agent = QueryIntelligenceAgent()
    
    # Test queries
    test_queries = [
        "walmart 856 failing since this morning",
        "missing 850 purchase orders from target",
        "analyze EDI errors for mckesson last week",
        "connection timeout issues with AS2",
        "urgent: production invoices not sending"
    ]
    
    print("Query Intelligence Agent Demonstration\n")
    print("=" * 60)
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        analysis = agent.analyze_query(query)
        
        print(f"Intent: {analysis.intent}")
        print(f"Urgency: {analysis.urgency}/10")
        print(f"Time Context: {analysis.time_context}")
        print(f"Entities: {analysis.entities}")
        print(f"Probable Issues: {analysis.probable_issues}")
        print(f"Search Expansions: {analysis.search_expansions[:3]}...")
        print(f"Confidence: {analysis.confidence:.2f}")
        print("-" * 40)


if __name__ == "__main__":
    demonstrate_query_intelligence()