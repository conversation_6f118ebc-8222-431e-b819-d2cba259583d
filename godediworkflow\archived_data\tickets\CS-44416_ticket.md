# CS-44416: (7665) <PERSON>ER<PERSON> BUSINESS TECHNOLOGY SA DE CV - question

## Ticket Information
- **Key**: CS-44416
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-05-27T16:43:06.617-0600
- **Updated**: 2025-05-29T12:47:51.604-0600
- **Customer**: <EMAIL>

## Description
*{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}
 
 
 
  

Hello support team, 

  

We are trying to work with <PERSON><PERSON> in our EDI integration, and they’re requesting the following: 
# {color:#172B4D}Please complete the{color} [{color:#172B4D} {color}{color:#96607D} *Penske EDI Profile*{color}{color:#172B4D} {color}|https://urldefense.com/v3/__https:/app.smartsheet.com/b/form/a7ac262584bc4fad8af15421b7983636__;!!DRKjGdBm7_l7!RmkF675U4KO9Rlc65YbDH8aoTmO0PmboMqFqEV2lp3henwP_bpkqgueISdwYVitidQ$]{color:#172B4D} form with your Prod Support contacts and  *_+ISA/GS IDs+_* you’ll use,  *we need it done in order to request our internal setups and generate/process your test files.*{color}{color:#172B4D}{color}{color:#172B4D}{color} 
# {color:#172B4D}Initially we will exchange test files by e-mail and later in the integration process we will provide you with a link to the sFTP setup form or let us know if you are able to use VAN to exchange documents with us.{color}{color:#172B4D}{color}{color:#172B4D}{color} 
# {color:#172B4D}The transactions that will be exchanged are:{color} 
** {color:#172B4D}EDI 204(Original  *00*, Cancel  *01*,  _+Replace+_  *05*){color}
** {color:#172B4D}EDI 214( *X3,AF,AG,X6,X1,D1*).{color}
** {color:#172B4D}EDI 824 (Application advise if your statuses are rejected){color}
**  {color:#172B4D}*Not in Scope:*  _997/210/990_{color}{color:#172B4D}{color}{color:#172B4D}{color} 
# {color:#172B4D}Penske's Sender/Receiver ID's are as follows:{color} 
** {color:#172B4D}TEST = ZZ:PENSKELEAR *T*{color}
** {color:#172B4D}PROD = ZZ:PENSKELEAR{color}{color:#172B4D}{color}{color:#172B4D}{color} 
# {color:#172B4D}We would like to use specific filenames for transactions:{color} 
** {color:#172B4D}LEAR_OUT_*204/824*_SPBY_YYYYMMDDhhmmSS *sss*.edi{color}
** {color:#172B4D}PENSKE_LEAR_SPBY_*214*_YYYYMMDDhhmmSS *sss*.edi{color} 
***  {color:#172B4D}_+Please add 2-3 milliseconds. If not possible, any+_*_+unique+_*_+sequence or ID your system can generate. E.g. ISA Ctrl#.+_{color} 
 

  

However, it seems that we require an EDI Enveloper. I’ve checked our account, and I cannot find the section to do this. Here’s the support page I found with information on this process: 

  

[https://support.cleo.com/hc/en-us/articles/************-Creating-and-defining-EDI-Envelopers|https://support.cleo.com/hc/en-us/articles/************-Creating-and-defining-EDI-Envelopers] 

  

But I’m unable to find that in our dashboard. Do we have to pay extra to have it, add a new vendor, or something similar? 

  

Thanks, 

-Ali Darwich 

SPBY 

  

Get [{color:blue}Outlook for Mac{color} |https://aka.ms/GetOutlookForMac]

## Components


## Labels

