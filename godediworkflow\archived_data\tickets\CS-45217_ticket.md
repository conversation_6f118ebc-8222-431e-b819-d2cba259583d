# CS-45217: Hot Leathers (ID: 7273) ShipStation Integration Failure: POs Not Pushing from WebEDI

## Ticket Information
- **Key**: CS-45217
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-06-10T13:05:25.415-0600
- **Updated**: 2025-06-16T10:46:43.514-0600
- **Customer**: <EMAIL>

## Description
The customer: <PERSON>, 

Hot Leathers (ID: 7273)

*Integration:* ShipStation

is reporting a critical integration failure between his WebEDI system and ShipStation. Although purchase orders are visible in his WebEDI portal, they are not automatically pushing into his ShipStation account for fulfillment. He has already performed initial troubleshooting steps and confirmed the following:

*Troubleshooting Performed by Customer:*

* Confirmed integration settings show "no errors."
* Verified orders are marked as "ready to integrate."
* Performed due diligence, checking for item number mismatches and other common data errors.

## Components


## Labels

