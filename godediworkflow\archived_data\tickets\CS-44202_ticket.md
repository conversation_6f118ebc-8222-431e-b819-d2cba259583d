# CS-44202: Request for invoice deduction/ Account #: 6874

## Ticket Information
- **Key**: CS-44202
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON><PERSON><PERSON>
- **Created**: 2025-05-22T06:46:04.164-0600
- **Updated**: 2025-05-29T08:08:05.982-0600
- **Customer**: <PERSON><PERSON><PERSON>

## Description
*{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}
 
 
 
 
 
Good morning,  
  
For one of our customers, UNFI, we have to put in an 8% discount from the total amount of the invoice for every invoice we are submitting through EDI. We need this to be an automatic occurrence for each of their invoices going forward.  
  
UNFI supplied us with codes, "The EDI codes for pick up allowance are: 880 Code = 54, 810 Code = F340." 
  
May you please assist us with this request as soon as possible? Thank you very much.  

  
Regards,{adf}{"type":"expand","content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://ci3.googleusercontent.com/mail-sig/AIorK4z32X33zhcUFaTT4_OOLk0ivPnjePsz1q9aiUw1X-Yw16L6kEwSNy-yeHx2qrTBrhDnZ0XdsCRYpLc5","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"Magda Rodrigues","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#222222"}}]}]},{"type":"paragraph","content":[{"type":"text","text":"A:","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#222222"}}]},{"type":"text","text":" 350 Hurst Street, Linden, NJ 07036  ","marks":[{"type":"textColor","attrs":{"color":"#222222"}}]},{"type":"hardBreak"},{"type":"text","text":"E:","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#222222"}}]},{"type":"text","text":" "},{"type":"text","text":"<EMAIL>","marks":[{"type":"textColor","attrs":{"color":"#222222"}},{"type":"link","attrs":{"href":"mailto:<EMAIL>"}}]},{"type":"text","text":"P:","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#222222"}}]},{"type":"text","text":" 908-474-0011 ext. 9","marks":[{"type":"textColor","attrs":{"color":"#222222"}}]}]},{"type":"paragraph","content":[{"type":"text","text":"W:","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#222222"}}]},{"type":"text","text":" "},{"type":"text","text":"melonebakery.com","marks":[{"type":"textColor","attrs":{"color":"#1155cc"}},{"type":"link","attrs":{"href":"http://melonebakery.com/"}}]}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

