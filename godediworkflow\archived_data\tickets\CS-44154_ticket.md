# CS-44154: Issue with 850 PO to 810 Invoice Auto-fill for Tractor Supply (Bernie's Best)

## Ticket Information
- **Key**: CS-44154
- **Status**: Canceled
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-05-21T12:05:04.146-0600
- **Updated**: 2025-06-18T12:35:06.918-0600
- **Customer**: <EMAIL>

## Description
*{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}
 
 
 
 
 
 
{color:#444444} Hi Cleo Support,
 
 We're running into a snag with our EDI setup for Tractor Supply (TP ID: 542372). When we try to create an 810 Invoice from an incoming 850 Purchase Order, the system isn't autofilling crucial item details.
 
 Specifically, the  *UPC, Buyer's Item Number and our Vendor's (<PERSON>ller's) Item Number* are consistently blank in the 810, forcing us to manually enter them. This is happening for PO 1040002915, received on 05/05/2025. You can see the full PO details on our end.
 
 Could you please check our mapping or configuration for the 850-to-810 turnaround for Tractor Supply? We're with <PERSON>'s Best, and it seems like something needs to be adjusted on your end to ensure these fields populate automatically.
 
 Let us know what you find or if you need any more info from us.{color}{adf}{"type":"expand","content":[{"type":"paragraph","content":[{"type":"text","text":"David Leake","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#404040"}}]},{"type":"text","text":"  "}]},{"type":"paragraph","content":[{"type":"text","text":"Dir. of Ops, Bernie’s Best","marks":[{"type":"textColor","attrs":{"color":"#404040"}}]},{"type":"text","text":" "}]},{"type":"paragraph","content":[{"type":"text","text":"<EMAIL>","marks":[{"type":"textColor","attrs":{"color":"#404040"}},{"type":"link","attrs":{"href":"mailto:<EMAIL>"}}]}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

