# CS-45271:  AS<PERSON> Errors - Custom Simple Solutions

## Ticket Information
- **Key**: CS-45271
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-06-11T10:47:59.520-0600
- **Updated**: 2025-06-16T10:41:57.862-0600
- **Customer**: <EMAIL>

## Description
Dear B2B Production Support Team,

I am reaching out to you on behalf of our customer, Custom Simple Solutions, regarding an issue with an Advance Ship Notice (ASN) that is being rejected by your system. The customer is experiencing an error with the ASN, and we are working to resolve the issue as quickly as possible.

The customer's ISA ID is ZZ*DTS8716. They are receiving 864s with an error message stating that the error does not match the PO location. However, when we compare the 850 to the ASN, the locations appear to match.

We would greatly appreciate it if you could provide us with additional details on where the error is occurring. Could you please let us know what is causing the rejection of the ASN and what steps we need to take to resolve the issue?

The customer is waiting to ship out product today, and this issue is urgent. We would like to resolve this as quickly as possible to avoid any delays or fines.

I have attached the raw data for the ASN and the 864 for your reference. Please let us know if you need any additional information from us.

!image-20250611-163715.png|width=658,height=1221,alt="image-20250611-163715.png"!

!image-20250611-163747.png|width=661,height=191,alt="image-20250611-163747.png"!

## Components


## Labels

