#!/usr/bin/env python3

import PyPDF2
import sys
import os

def extract_pdf_text(pdf_path):
    """Extract text content from a PDF file."""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
            return text
    except Exception as e:
        return f"Error reading PDF: {str(e)}"

def analyze_ticket(pdf_path):
    """Analyze a support ticket PDF."""
    print(f"\n{'='*80}")
    print(f"Analyzing: {os.path.basename(pdf_path)}")
    print('='*80)
    
    text = extract_pdf_text(pdf_path)
    
    if text.startswith("Error"):
        print(text)
        return
    
    # Print first 3000 characters to get a good overview
    print("Content Preview:")
    print("-" * 40)
    print(text[:3000])
    print("-" * 40)
    print(f"\nTotal length: {len(text)} characters")
    
    # Look for key indicators
    indicators = {
        "Problem indicators": ["error", "issue", "problem", "failed", "cannot", "unable"],
        "Solution indicators": ["resolved", "fixed", "solution", "workaround", "try"],
        "Technical terms": ["EDI", "850", "810", "856", "X12", "EDIFACT", "AS2", "FTP", "SFTP"],
        "Customer info": ["@", "company", "email", "phone"]
    }
    
    print("\nKey Indicators Found:")
    for category, terms in indicators.items():
        found = [term for term in terms if term.lower() in text.lower()]
        if found:
            print(f"  {category}: {', '.join(found)}")

if __name__ == "__main__":
    tickets = [
        "/home/<USER>/edi_knowledge_base/support_tickets/cleo.zendesk.com_tickets_13113892_print.pdf",
        "/home/<USER>/edi_knowledge_base/support_tickets/cleo.zendesk.com_tickets_13138304_print.pdf",
        "/home/<USER>/edi_knowledge_base/support_tickets/cleo.zendesk.com_tickets_13160257_print.pdf",
        "/home/<USER>/edi_knowledge_base/support_tickets/cleo.zendesk.com_tickets_13126051_print.pdf",
        "/home/<USER>/edi_knowledge_base/support_tickets/cleo.zendesk.com_tickets_13137226_print.pdf"
    ]
    
    for ticket in tickets:
        analyze_ticket(ticket)