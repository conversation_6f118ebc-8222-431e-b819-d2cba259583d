# CS-45433: [ecgrid] ECGRID-IN: Parcel Structure Validation ERROR

## Ticket Information
- **Key**: CS-45433
- **Status**: Canceled
- **Priority**: Medium
- **Assignee**: Unassigned
- **Reporter**: <EMAIL>
- **Created**: 2025-06-13T16:37:04.249-0600
- **Updated**: 2025-06-13T16:49:21.242-0600
- **Customer**: ecgrid

## Description
!https://ecgrid.com/images/header_ecgrid_800px.png!  
 
 ECGRID-IN: Parcel Structure Validation ERROR 
| _Parcel Information_ | 
||filename: |20250613173435_134168428.edi | 
||date: |13 Jun 2025 22:36:12 UTC | 
||status: |2103 | 
||status_message: |ECGRID-IN: Parcel Structure Validation ERROR | 
||status_date: |13 Jun 2025 22:36:12 UTC | 
||comm_control_id: | | 
||network_id: |480 | 
||network_name: |Datatrans Solutions, Inc. | 
||mailbox_id: |0 | 
||mailbox_name: | | 
||parcel_id: |2477680546 | 
||parcel_acknowledgment: | | 
| | 
| _X12.56 Info_ | 
||X12.56|N/A| 
| | 
| _Interchange Information_ | 
||interchange_header|ISA^00^ ^00^ ^ZZ^AHN ^01^002811917TPC ^250613^2222^U^00401^003167086^0^P^:| 
||interchange_status|1101| 
||interchange_status_message|Interchange Control Number does not match 003167086/3167086|
  
 

 Thank you for choosing  *ECGrid*
  _the ECGrid team_  

 !https://ecgrid.com/Images/message_footer_640px.png! 
powered by ECGridOS v4.1 (Build 478)

## Components


## Labels

