# CS-44473: KOPF LOG OTM - KOPF5010 - Invalid 997 ***Error deenveloping X12 documents, process ID:450267620

## Ticket Information
- **Key**: CS-44473
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON><PERSON><PERSON><PERSON><PERSON>@aafes.com
- **Created**: 2025-05-28T16:09:10.021-0600
- **Updated**: 2025-06-06T13:51:56.077-0600
- **Customer**: External

## Description
{color:#2F5496}Hello Data Trans team,{color}{color:#2F5496}{color} 

  

{color:#2F5496}If you would, please respond to <PERSON><PERSON>’s inquiry.{color}{color:#2F5496}{color} 

  

{color:#2F5496}We have received another identical interchange at the same time, 7pm CDT, from  *KOPF LOG OTM – KOPF5010* that has caused another error in our production system. They are identical interchanges, and we have received them at 7pm CDT for the past five days in a row now. We sent GS control number: 1047 for a 204 (SM) a long time ago… The latest one that we sent contains GS control #: 7805.{color}{color:#2F5496}{color} 

  

{color:#2F5496}If you would, please review and advise.{color}{color:#2F5496}{color} 

  

 *{color:#2F5496} *Data received*{color}*_{color:#2F5496} _(sample of one)_{color}_*{color:#2F5496} *:*{color}* 

{color:#262626}ISA*00* *00* *02**KOPF5010* *14*001695568GP *250220*0650*^*00501**000002008**0*P*>~{color} 

 *{color:#262626} *GS*{color}*{color:#262626}**FA***KOPF5010**001695568GP*20250220*0650**2008**X*005010~{color} 

{color:#262626}ST**997**02008~{color} 

{color:#262626}AK1**SM***1047*~{color} 

{color:#262626}AK2**204***1047*~{color} 

{color:#262626}AK5*A~{color} 

{color:#262626}AK9*A*1*1*1~{color} 

{color:#262626}SE*6*02008~{color} 

{color:#262626}GE*1*2008~{color} 

{color:#262626}IEA*1*000002008~{color}{color:#2F5496}{color} 

  

 *{color:#2F5496} *From our EDI system:*{color}* 

!image001.png|thumbnail!{color:#2F5496}{color} 

  

{color:#2F5496}Thanks,{color} 

{color:#2F5496}Thomas{color}{color:#2F5496}{color} 

  

 *{color:#2F5496} *Thomas McColgan II*{color}* 

{color:#2F5496}EDI Specialist{color} 

{color:#2F5496}IT-G Services Portfolio 3106441000{color} 

{color:#2F5496}EDI Integration Services{color} 

{color:#2F5496}Army & Air Force Exchange Service{color}{color:#2F5496}{color} 

[{color:#2F5496}{color}{color:#2F5496}<EMAIL>{color}{color:#2F5496}{color}|mailto:<EMAIL>]{color:#2F5496}{color} 

{color:#2F5496}Visit our online store!{color} [{color:#2F5496} {color}{color:#2F5496}www.shop *my* exchange.com{color}{color:#2F5496}{color}|http://www.shopmyexchange.com/]{color:#2F5496}{color}{color:#2F5496}{color} 

[{color:#2F5496}{color}{color:#2F5496}EDI Info.{color}{color:#2F5496}{color}|https://www.aafes.com/about-exchange/doing-business/edi-info/]{color:#2F5496}{color}{color:#2F5496}{color}{color:#2F5496}{color} 

  
  

 *From:* Salinas, Daniel <<EMAIL>> 
  *Sent:* Wednesday, May 28, 2025 1:30 PM
  *To:* Mccolgan II, Thomas <<EMAIL>>; <EMAIL>; <EMAIL>
  *Cc:* EDI Tech Support <<EMAIL>>; Alisha Graves-Sermons from Tai Software <<EMAIL>>
  *Subject:* [External]-Re: KOPF LOG OTM - KOPF5010 - Invalid 997 ***Error deenveloping X12 documents, process ID:450267620   

   

{color:#172B4D}Please submit a help ticket through our Support Team at{color} [{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}.{color}{color:#172B4D}{color}   

    

{color:#172B4D}Daniel{color} 
| 
| 
| 
| 
|  *{color:#000001} *Daniel*{color}* | {color:#000001}{color} |  *{color:#000001} *Salinas*{color}* | 
| 
|  *{color:#000001} *Cleo*{color}* |  *{color:#000001} *:*{color}* | {color:#000001}Senior Implementation Engineer{color} | 
| 
| 
| 
| | | 
| 
| {color:#000001}Email:{color} | {color:#000001}{color}[{color:#000001}{color}{color:#000001}<EMAIL>{color}{color:#000001}{color}|mailto:<EMAIL>]{color:#000001}{color} |  {color:#000001} |{color}  {color:#000001}Web:{color}{color:#000001}{color}  [{color:#000001}{color}{color:#000001}www.cleo.com{color}{color:#000001}{color}|https://www.cleo.com/]{color:#000001}{color} 
| | | 
| | {color:#0083CA} *{color}[{color:#0083CA} {color}{color:#0083CA} *Join us at one of our upcoming events. Check out the list!*{color}{color:#0083CA} {color}|https://www.cleo.com/events]{color:#0083CA} {color}*{color:#0083CA}{color} | 
----
   

 *{color:#172B4D} *From:*{color}*{color:#172B4D} Mccolgan II, Thomas <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Sent:* Wednesday, May 28, 2025 11:23 AM
  *To:* Salinas, Daniel <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>;{color} [{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D} <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>;{color} [{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D} <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Cc:* EDI Tech Support <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>; Alisha Graves-Sermons from Tai Software <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Subject:* KOPF LOG OTM - KOPF5010 - Invalid 997 ***Error deenveloping X12 documents, process ID:450267620{color}   

     

 *{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}*
 
  
  

{color:#2F5496}Hello Data Trans team,{color}{color:#2F5496}{color} 

  

{color:#2F5496}If you would, please respond to Alisha’s inquiry.{color}{color:#2F5496}{color} 

  

{color:#2F5496}We have received another identical interchange at the same time, 7pm CDT, from  *KOPF LOG OTM – KOPF5010* that has caused another error in our production system. They are identical interchanges, and we have received them at 7pm CDT for the past five days in a row now. We sent GS control number: 1047 for a 204 (SM) a long time ago… The latest one that we sent contains GS control #: 7805.{color}{color:#2F5496}{color} 

  

{color:#2F5496}If you would, please review and advise.{color}{color:#2F5496}{color} 

  

 *{color:#2F5496} *Data received*{color}*_{color:#2F5496} _(sample of one)_{color}_*{color:#2F5496} *:*{color}* 

{color:#262626}ISA*00* *00* *02**KOPF5010* *14*001695568GP *250220*0650*^*00501**000002008**0*P*>~{color} 

 *{color:#262626} *GS*{color}*{color:#262626}**FA***KOPF5010**001695568GP*20250220*0650**2008**X*005010~{color} 

{color:#262626}ST**997**02008~{color} 

{color:#262626}AK1**SM***1047*~{color} 

{color:#262626}AK2**204***1047*~{color} 

{color:#262626}AK5*A~{color} 

{color:#262626}AK9*A*1*1*1~{color} 

{color:#262626}SE*6*02008~{color} 

{color:#262626}GE*1*2008~{color} 

{color:#262626}IEA*1*000002008~{color}{color:#2F5496}{color} 

  

 *{color:#2F5496} *From our EDI system:*{color}* 

!image001.png|thumbnail!{color:#2F5496}{color} 

  

{color:#2F5496}Thanks,{color} 

{color:#2F5496}Thomas{color}{color:#2F5496}{color} 

  

 *{color:#2F5496} *Thomas McColgan II*{color}* 

{color:#2F5496}EDI Specialist{color} 

{color:#2F5496}IT-G Services Portfolio 3106441000{color} 

{color:#2F5496}EDI Integration Services{color} 

{color:#2F5496}Army & Air Force Exchange Service{color}{color:#2F5496}{color} 

[{color:#2F5496}{color}{color:#2F5496}<EMAIL>{color}{color:#2F5496}{color}|mailto:<EMAIL>]{color:#2F5496}{color} 

{color:#2F5496}Visit our online store!{color} [{color:#2F5496} {color}{color:#2F5496}www.shop *my* exchange.com{color}{color:#2F5496}{color}|http://www.shopmyexchange.com/]{color:#2F5496}{color}{color:#2F5496}{color} 

[{color:#2F5496}{color}{color:#2F5496}EDI Info.{color}{color:#2F5496}{color}|https://www.aafes.com/about-exchange/doing-business/edi-info/]{color:#2F5496}{color}{color:#2F5496}{color} 

  
  

 *From:* Alisha Graves-Sermons from Tai Software <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Tuesday, May 27, 2025 1:49 PM
  *To:* Mccolgan II, Thomas <[<EMAIL>|mailto:<EMAIL>]>
  *Cc:* [<EMAIL>|mailto:<EMAIL>]; [<EMAIL>|mailto:<EMAIL>]; EDI Tech Support <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* [External]-Re: KOPF LOG OTM - KOPF5010 - Invalid Receiver ID ***Error deenveloping X12 documents, process ID:450267620   

  
  

{color:#444444}Good afternoon DataTrans team.{color}{color:#444444}{color} 

  

{color:#444444}Would this be something from your end?{color}{color:#444444}{color} 

  

{color:#444444}Thanks!{color}  

   

Need more info? Please check out our [Knowledge Base|https://learn.tai-software.com/knowledge]. 
 
 For more visibility on your tickets, please check out our [Ticket Portal|https://learn.tai-software.com/tickets-view?offset=0]. 
| | 
| 
| 
| 
{color:#1B1D36}{color}!image002.jpg|thumbnail!{color:#1B1D36}{color} <[https://www.tai-software.com/]> | | 
|  *{color:#1B1D36} *Alisha Graves-Sermons*{color}*  *{color:#E65300} *Project Manager*{color}* | 
|  *{color:#1B1D36} {color}* | 
| 
| 
{color:#1B1D36}{color}!image002.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}{color:#1B1D36}************* Option: 5 - Voicemail Only <tel:************%20Option:%205%20%20-%20Voicemail%20Only>*{color}* |  

 *{color:#1B1D36} {color}* 
| 
{color:#1B1D36}{color}!image002.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}[{color:#1B1D36} *<EMAIL>*{color}|mailto:<EMAIL>]{color:#1B1D36} {color}* | 
 

 *{color:#1B1D36} {color}* 
| 
{color:#1B1D36}{color}!image002.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}[{color:#1B1D36} *www.tai-software.com*{color}|https://www.tai-software.com]{color:#1B1D36} {color}* | 
 

 *{color:#1B1D36} {color}* 
| 
{color:#1B1D36}{color}!image002.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}[{color:#1B1D36} *Status IO*{color}|https://tai-software.status.io]{color:#1B1D36} {color}* |   *{color:#E65300} *401 Main St, Suite 201, Huntington Beach, CA 92648*{color}* 
   

   

On Tue, May 27, 2025 at 11:15 AM, Mccolgan II, Thomas <[<EMAIL>|mailto:<EMAIL>]> wrote:  
{quote}  

{color:#2F5496}Hello Alisha,{color}{color:#2F5496}{color}  

  

{color:#2F5496}We have received interchanges from  *KOPF LOG OTM – KOPF5010* that have caused errors in our production system. They are identical interchanges, and we have received them at 7pm CDT for the past four days in a row. We sent GS control number: 1047 for a 204 (SM) a long time ago… The latest one that we sent contains GS control #: 7805.{color}{color:#2F5496}{color}  

  

{color:#2F5496}If you would, please review and advise.{color}{color:#2F5496}{color}  

  

 *{color:#2F5496} *Data received*{color}*_{color:#2F5496} _(sample of one)_{color}_*{color:#2F5496} *:*{color}*  

{color:#262626}ISA*00* *00* *02**KOPF5010* *14*001695568GP *250220*0650*^*00501**000002008**0*P*>~{color}  

 *{color:#262626} *GS*{color}*{color:#262626}**FA***KOPF5010**001695568GP*20250220*0650**2008**X*005010~{color}  

{color:#262626}ST**997**02008~{color}  

{color:#262626}AK1**SM***1047*~{color}  

{color:#262626}AK2**204***1047*~{color}  

{color:#262626}AK5*A~{color}  

{color:#262626}AK9*A*1*1*1~{color}  

{color:#262626}SE*6*02008~{color}  

{color:#262626}GE*1*2008~{color}  

{color:#262626}IEA*1*000002008~{color}{color:#2F5496}{color}  

  

 *{color:#2F5496} *From our EDI system:*{color}*  

!image003.png|thumbnail!{color:#2F5496}{color} 

  

{color:#2F5496}Thanks,{color}  

{color:#2F5496}Thomas{color}{color:#2F5496}{color}  

  

 *{color:#2F5496} *Thomas McColgan II*{color}*  

{color:#2F5496}EDI Specialist{color}  

{color:#2F5496}IT-G Services Portfolio 3106441000{color}  

{color:#2F5496}EDI Integration Services{color}  

{color:#2F5496}Army & Air Force Exchange Service{color}{color:#2F5496}{color}  

[{color:#2F5496}{color}{color:#2F5496}<EMAIL>{color}{color:#2F5496}{color}|mailto:<EMAIL>]{color:#2F5496}{color}  

{color:#2F5496}Visit our online store!{color} [{color:#2F5496} {color}{color:#2F5496}www.shop *my* exchange.com{color}{color:#2F5496}{color}|http://www.shopmyexchange.com/]{color:#2F5496}{color}{color:#2F5496}{color}  

[{color:#2F5496}{color}{color:#2F5496}EDI Info.{color}{color:#2F5496}{color}|https://www.aafes.com/about-exchange/doing-business/edi-info/]{color:#2F5496}{color}{color:#2F5496}{color}{color:#2F5496}{color}  

  
  

 *From:* Alisha Graves-Sermons from Tai Software <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Tuesday, February 11, 2025 1:04 PM
  *To:* [<EMAIL>|mailto:<EMAIL>]; Mccolgan II, Thomas <[<EMAIL>|mailto:<EMAIL>]>; [<EMAIL>|mailto:<EMAIL>]
  *Cc:* EDI Tech Support <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* [External]-Re: KOPF LOG OTM - KOPF5010 - Invalid Receiver ID ***Error deenveloping X12 documents, process ID:450267620    

  
  

{color:#444444}Thanks! I've got these pushed through again, just a few moments ago.{color}{color:#444444}{color}  

  

{color:#444444}Thanks!{color}   

   

Need more info? Please check out our [Knowledge Base|https://learn.tai-software.com/knowledge]. 
 
 For more visibility on your tickets, please check out our [Ticket Portal|https://learn.tai-software.com/tickets-view?offset=0]. 
| | 
| 
| 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} <[https://www.tai-software.com/]> | | 
|  *{color:#1B1D36} *Alisha Graves-Sermons*{color}*  *{color:#E65300} *Integrations Project Manager*{color}* | 
|  *{color:#1B1D36} {color}* | 
| 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}{color:#1B1D36}************* Option: 5 - Voicemail Only <tel:************>*{color}* |  

 *{color:#1B1D36} {color}* 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}[{color:#1B1D36} *<EMAIL>*{color}|mailto:<EMAIL>]{color:#1B1D36} {color}* | 
 

 *{color:#1B1D36} {color}* 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}[{color:#1B1D36} *www.tai-software.com*{color}|https://www.tai-software.com]{color:#1B1D36} {color}* |   *{color:#E65300} *401 Main St, Suite 201, Huntington Beach, CA 92648*{color}* 
   

   

On Tue, Feb 11, 2025 at 10:44 AM, Salinas, Daniel <[<EMAIL>|mailto:<EMAIL>]> wrote:  
  

{color:#172B4D}No you will need to push those again.{color}{color:#172B4D}{color} 
| 
| 
| 
| 
|  *{color:#000001} *Daniel*{color}**{color:white} {color}**{color:white} {color}**{color:white} {color}**{color:white} {color}* {color:#000001}{color} | {color:#000001}{color} |  *{color:#000001} *Salinas*{color}* | 
| 
|  *{color:#000001} *Cleo*{color}* |  *{color:#000001} *:*{color}* | {color:#000001}EDI Analyst II{color} | 
| 
| 
| 
| | | 
| 
| {color:#000001}Email:{color} | {color:#000001}{color}[{color:#000001}{color}{color:#000001}<EMAIL>{color}{color:#000001}{color}|mailto:<EMAIL>]{color:#000001}{color} |  {color:#000001} |{color}  {color:#000001}Web:{color}{color:#000001}{color}  [{color:#000001}{color}{color:#000001}www.cleo.com{color}{color:#000001}{color}|https://www.cleo.com/]{color:#000001}{color} 
| | | 
| | {color:#0083CA} *{color}[{color:#0083CA} {color}{color:#0083CA} *Join us at one of our upcoming events. Check out the list!*{color}{color:#0083CA} {color}|https://www.cleo.com/events]{color:#0083CA} {color}* {color:#0083CA}{color} | 
----
   

 *{color:#172B4D} *From:*{color}*{color:#172B4D} Alisha Graves-Sermons from Tai Software <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Sent:* Tuesday, February 11, 2025 12:20 PM
  *To:* Salinas, Daniel <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>;{color} [{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D} <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>;{color} [{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D} <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Cc:*{color} [{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D} <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Subject:* Re: KOPF LOG OTM - KOPF5010 - Invalid Receiver ID ***Error deenveloping X12 documents, process ID:450267620{color}   

     

 *{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}*  
  

Thank you! 
 
 Will the failed files automatically reprocess? Or should I push them through again? 
 
 Thanks!   

Need more info? Please check out our [Knowledge Base|https://learn.tai-software.com/knowledge]. 
 
 For more visibility on your tickets, please check out our [Ticket Portal|https://learn.tai-software.com/tickets-view?offset=0]. 
| | 
| 
| 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} <[https://www.tai-software.com/]> | | 
|  *{color:#1B1D36} *Alisha Graves-Sermons*{color}*  *{color:#E65300} *Integrations Project Manager*{color}* | 
|  *{color:#1B1D36} {color}* | 
| 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}{color:#1B1D36}************* Option: 5 - Voicemail Only <tel:************>*{color}* |  

 *{color:#1B1D36} {color}* 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}[{color:#1B1D36} *<EMAIL>*{color}|mailto:<EMAIL>]{color:#1B1D36} {color}* | 
 

 *{color:#1B1D36} {color}* 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}[{color:#1B1D36} *www.tai-software.com*{color}|https://www.tai-software.com]{color:#1B1D36} {color}* |   *{color:#E65300} *401 Main St, Suite 201, Huntington Beach, CA 92648*{color}* 
   

   

On Tue, Feb 11, 2025 at 10:14 AM, Salinas, Daniel <[<EMAIL>|mailto:<EMAIL>]> wrote:  
  

{color:#172B4D}Alisha,{color}{color:#172B4D}{color}    

    

{color:#172B4D}Yes we have made the correction.{color}{color:#172B4D}{color}     

    

{color:#172B4D}Daniel{color}{color:#172B4D}{color} 
| 
| 
| 
| 
|  *{color:#000001} *Daniel*{color}**{color:white} {color}**{color:white} {color}**{color:white} {color}**{color:white} {color}* {color:#000001}{color} | {color:#000001}{color} |  *{color:#000001} *Salinas*{color}* | 
| 
|  *{color:#000001} *Cleo*{color}* |  *{color:#000001} *:*{color}* | {color:#000001}EDI Analyst II{color} | 
| 
| 
| 
| | | 
| 
| {color:#000001}Email:{color} | {color:#000001}{color}[{color:#000001}{color}{color:#000001}<EMAIL>{color}{color:#000001}{color}|mailto:<EMAIL>]{color:#000001}{color} |  {color:#000001} |{color}  {color:#000001}Web:{color}{color:#000001}{color}  [{color:#000001}{color}{color:#000001}www.cleo.com{color}{color:#000001}{color}|https://www.cleo.com/]{color:#000001}{color} 
| | | 
| | {color:#0083CA} *{color}[{color:#0083CA} {color}{color:#0083CA} *Join us at one of our upcoming events. Check out the list!*{color}{color:#0083CA} {color}|https://www.cleo.com/events]{color:#0083CA} {color}* {color:#0083CA}{color} | 
----
   

 *{color:#172B4D} *From:*{color}*{color:#172B4D} Alisha Graves-Sermons from Tai Software <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Sent:* Tuesday, February 11, 2025 11:53 AM
  *To:*{color} [{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D} <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>;{color} [{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D} <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Cc:*{color} [{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D} <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Subject:* Re: KOPF LOG OTM - KOPF5010 - Invalid Receiver ID ***Error deenveloping X12 documents, process ID:450267620{color}   

     

 *{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}*  
 
  

{color:#444444}Good morning Daniel!{color}{color:#444444}{color}  

  

{color:#444444}Are you able to look into this? It looks like the ISA/GS is reflecting incorrectly, but I'm showing that we are using the correct values when the file is sent from our system.{color}{color:#444444}{color}  

  

{color:#444444}Thanks!{color}   

   

Need more info? Please check out our [Knowledge Base|https://learn.tai-software.com/knowledge]. 
 
 For more visibility on your tickets, please check out our [Ticket Portal|https://learn.tai-software.com/tickets-view?offset=0]. 
| | 
| 
| 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} <[https://www.tai-software.com/]> | | 
|  *{color:#1B1D36} *Alisha Graves-Sermons*{color}*  *{color:#E65300} *Integrations Project Manager*{color}* | 
|  *{color:#1B1D36} {color}* | 
| 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}{color:#1B1D36}************* Option: 5 - Voicemail Only <tel:************>*{color}* |  

 *{color:#1B1D36} {color}* 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}[{color:#1B1D36} *<EMAIL>*{color}|mailto:<EMAIL>]{color:#1B1D36} {color}* | 
 

 *{color:#1B1D36} {color}* 
| 
{color:#1B1D36}{color}!image004.jpg|thumbnail!{color:#1B1D36}{color} | {color:#1B1D36} *{color}[{color:#1B1D36} *www.tai-software.com*{color}|https://www.tai-software.com]{color:#1B1D36} {color}* |   *{color:#E65300} *401 Main St, Suite 201, Huntington Beach, CA 92648*{color}* 
   

   

On Tue, Feb 11, 2025 at 9:48 AM, Mccolgan II, Thomas <[<EMAIL>|mailto:<EMAIL>]> wrote:  
  

{color:#2F5496}Hello,{color}{color:#2F5496}{color}  

   

{color:#2F5496}We have received several interchanges from  *KOPF LOG OTM – KOPF5010* that have caused errors in our production system. Our production ISA/GS qualifier and ID is:{color}  *{color:#00B050} *14/*{color}*{color:#2F5496}{color}  *{color:#00B050} *001695568GP*{color}*{color:#2F5496}.{color}{color:#2F5496}{color}  

  

{color:#2F5496}If you would, please correct any that you have sent with this issue and then re-send them.{color}  

  

 *{color:#2F5496} *Envelope segments received*{color}*  _{color:#2F5496} _(sample of one)_{color}_*{color:#2F5496} *:*{color}*  

 *ISA**00* *00* *02**KOPF5010* **{color:red} *01*{color}***{color:red} *001695568P*{color}*{color:red}{color} *250211*1014*^*00501**000000034**0*P*>~  

 *GS**GF**KOPF5010***{color:red} *001695568P*{color}**20250211*1014**34**X*005010~  

ST**990**0034~{color:#2F5496}{color}  

  

{color:#2F5496}Thanks,{color}  

{color:#2F5496}Thomas{color}{color:#2F5496}{color}  

  

 *{color:#2F5496} *Thomas McColgan II*{color}*  

{color:#2F5496}EDI Specialist{color}  

{color:#2F5496}IT-G EDI{color}  

{color:#2F5496}Army & Air Force Exchange Service{color}{color:#2F5496}{color}  

[{color:#2F5496}{color}{color:#2F5496}<EMAIL>{color}{color:#2F5496}{color}|mailto:<EMAIL>]{color:#2F5496}{color}  

{color:#2F5496}Visit our online store!{color} [{color:#2F5496} {color}{color:#2F5496}www.shop *my* exchange.com{color}{color:#2F5496}{color}|http://www.shopmyexchange.com/]{color:#2F5496}{color}{color:#2F5496}{color}  

[{color:#2F5496}{color}{color:#2F5496}EDI Info.{color}{color:#2F5496}{color}|https://www.aafes.com/about-exchange/doing-business/edi-info/edi.htm]{color:#2F5496}{color}{color:#2F5496}{color}  

   

 *From:* [<EMAIL>|mailto:<EMAIL>] <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Tuesday, February 11, 2025 10:15 AM
  *To:* EDI Tech Support <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* ***Error deenveloping X12 documents, process ID:450267620    

  
{noformat}ERROR Occurred Deenveloping: {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat}No matching ISA envelope was found. Interchange not processed. Please configure the appropriate inbound EDI envelopes and restart this business process.{noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat}SenderID: KOPF5010{noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat}ReceiverID: 001695568P {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat}Type: ISA IEA{noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat}Function: DEENVELOPE{noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat}InterchangeTestIndicator: P{noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat} {noformat}
 
{noformat}InterchangeControlVersionNumber: 00501{noformat}
 
{noformat} {noformat}
 
{noformat} {noform

_*...This comment is truncated as it exceeds the character limit.*_

## Components


## Labels

