<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EDI Support Ticket Analyzer & Flow Map</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
        }

        .main-container {
            display: flex;
            gap: 20px;
            padding: 20px;
            max-width: 1800px;
            margin: 0 auto;
        }

        .ticket-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            padding: 30px;
            width: 400px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .flow-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            padding: 30px;
            flex: 1;
            max-height: 90vh;
            overflow-y: auto;
        }

        h1 {
            color: #2a5298;
            margin-bottom: 20px;
            font-size: 2em;
        }

        h2 {
            color: #2a5298;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .ticket-input {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 14px;
            resize: vertical;
            font-family: inherit;
        }

        .analyze-btn {
            width: 100%;
            padding: 15px;
            margin-top: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .analyze-btn:hover {
            transform: translateY(-2px);
        }

        .ticket-analysis {
            margin-top: 20px;
            padding: 20px;
            background: #f7fafc;
            border-radius: 10px;
            display: none;
        }

        .detected-docs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .doc-tag {
            background: #5a67d8;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
        }

        .doc-tag:hover {
            background: #4c51bf;
        }

        .doc-categories {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .doc-category {
            background: #f7fafc;
            border-radius: 12px;
            padding: 20px;
        }

        .category-title {
            font-weight: bold;
            color: #2a5298;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .document-grid {
            display: grid;
            gap: 10px;
        }

        .document-node {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .document-node:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border-color: #5a67d8;
        }

        .document-node.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }

        .document-node.highlighted {
            border-color: #48bb78;
            border-width: 3px;
            background: #f0fff4;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .doc-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .doc-number {
            font-size: 1.4em;
            font-weight: bold;
        }

        .doc-direction {
            font-size: 0.8em;
            padding: 2px 8px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
        }

        .doc-name {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .flow-details {
            background: #f7fafc;
            border-radius: 12px;
            padding: 30px;
            margin-top: 20px;
            display: none;
        }

        .flow-path {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
            padding: 20px;
            background: white;
            border-radius: 10px;
        }

        .flow-step {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            position: relative;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .flow-arrow {
            color: #5a67d8;
            font-size: 1.2em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .info-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }

        .info-section h4 {
            color: #2a5298;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-list {
            list-style: none;
        }

        .info-list li {
            margin: 10px 0;
            padding-left: 20px;
            position: relative;
            line-height: 1.5;
        }

        .info-list li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #5a67d8;
            font-size: 0.8em;
        }

        .alert-box {
            background: #fed7d7;
            border: 2px solid #fc8181;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }

        .success-box {
            background: #c6f6d5;
            border: 2px solid #68d391;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }

        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .legend {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
            justify-content: center;
            padding: 15px;
            background: #f7fafc;
            border-radius: 10px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .legend-box {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 2px solid #e2e8f0;
        }

        .trading-partner-info {
            background: #e6f2ff;
            border: 2px solid #3182ce;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }

        .issue-keywords {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .keyword {
            background: #fef3c7;
            color: #92400e;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 12px;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* File Upload Styles */
        .file-upload-section {
            margin-bottom: 20px;
        }

        .file-upload-label {
            display: block;
            border: 2px dashed #5a67d8;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f7fafc;
        }

        .file-upload-label:hover {
            background: #e9ecef;
            border-color: #4c51bf;
        }

        .upload-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .file-upload-label small {
            display: block;
            margin-top: 5px;
            color: #666;
        }

        .uploaded-files {
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
        }

        .file-item {
            background: #e6f2ff;
            border: 1px solid #3182ce;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: bold;
            color: #2a5298;
        }

        .file-type {
            font-size: 0.8em;
            color: #666;
        }

        .file-content-preview {
            font-size: 0.8em;
            color: #444;
            margin-top: 5px;
            padding: 5px;
            background: #f7fafc;
            border-radius: 4px;
            max-height: 100px;
            overflow-y: auto;
        }

        .remove-file {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .remove-file:hover {
            background: #c53030;
        }

        .processing-indicator {
            display: inline-block;
            margin-left: 10px;
            color: #5a67d8;
        }

        .video-thumbnail {
            width: 100px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            margin-top: 5px;
        }

        .image-thumbnail {
            max-width: 150px;
            max-height: 100px;
            border-radius: 4px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="ticket-panel">
            <h2>📋 Ticket Analyzer</h2>
            
            <!-- File Upload Section -->
            <div class="file-upload-section">
                <label for="fileInput" class="file-upload-label">
                    <div class="upload-icon">📎</div>
                    <span>Drop files here or click to upload</span>
                    <small>Supports: Text, PDF, Images, Videos, Excel, CSV, EDI files</small>
                </label>
                <input type="file" id="fileInput" multiple accept="*" onchange="handleFileUpload(event)" style="display: none;">
                
                <div id="uploadedFiles" class="uploaded-files"></div>
            </div>
            
            <textarea class="ticket-input" id="ticketInput" placeholder="Paste your support ticket here or upload files above...

Example:
Customer: Walmart
Issue: I am missing 850 purchase orders from yesterday
Additional Info: We are receiving 997s but no 850s are showing up in our system"></textarea>
            
            <button class="analyze-btn" onclick="analyzeTicket()">🔍 Analyze Ticket</button>
            
            <div class="ticket-analysis" id="ticketAnalysis">
                <h3>📊 Analysis Results</h3>
                <div id="analysisContent"></div>
            </div>
        </div>
        
        <div class="flow-panel">
            <h1>🔄 EDI Document Flow Map - WebEDI Support</h1>
            
            <input type="text" class="search-box" id="searchBox" placeholder="Search for document number or name..." onkeyup="searchDocuments(event)">
            
            <div class="doc-categories">
                <div class="doc-category">
                    <div class="category-title">📥 Order Processing</div>
                    <div class="document-grid">
                        <div class="document-node" data-doc="850">
                            <div class="doc-header">
                                <span class="doc-number">850</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">Purchase Order</div>
                        </div>
                        <div class="document-node" data-doc="855">
                            <div class="doc-header">
                                <span class="doc-number">855</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">PO Acknowledgment</div>
                        </div>
                        <div class="document-node" data-doc="860">
                            <div class="doc-header">
                                <span class="doc-number">860</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">PO Change Request</div>
                        </div>
                        <div class="document-node" data-doc="865">
                            <div class="doc-header">
                                <span class="doc-number">865</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">PO Change Ack</div>
                        </div>
                        <div class="document-node" data-doc="875">
                            <div class="doc-header">
                                <span class="doc-number">875</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">Grocery PO</div>
                        </div>
                    </div>
                </div>
                
                <div class="doc-category">
                    <div class="category-title">📦 Shipping & Receiving</div>
                    <div class="document-grid">
                        <div class="document-node" data-doc="856">
                            <div class="doc-header">
                                <span class="doc-number">856</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">Ship Notice/ASN</div>
                        </div>
                        <div class="document-node" data-doc="861">
                            <div class="doc-header">
                                <span class="doc-number">861</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">Receiving Advice</div>
                        </div>
                        <div class="document-node" data-doc="945">
                            <div class="doc-header">
                                <span class="doc-number">945</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">Warehouse Ship Advice</div>
                        </div>
                        <div class="document-node" data-doc="940">
                            <div class="doc-header">
                                <span class="doc-number">940</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">Warehouse Ship Order</div>
                        </div>
                        <div class="document-node" data-doc="943">
                            <div class="doc-header">
                                <span class="doc-number">943</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">Warehouse Stock Transfer</div>
                        </div>
                    </div>
                </div>
                
                <div class="doc-category">
                    <div class="category-title">💰 Financial Documents</div>
                    <div class="document-grid">
                        <div class="document-node" data-doc="810">
                            <div class="doc-header">
                                <span class="doc-number">810</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">Invoice</div>
                        </div>
                        <div class="document-node" data-doc="820">
                            <div class="doc-header">
                                <span class="doc-number">820</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">Payment/Remittance</div>
                        </div>
                        <div class="document-node" data-doc="812">
                            <div class="doc-header">
                                <span class="doc-number">812</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">Credit/Debit Adjustment</div>
                        </div>
                        <div class="document-node" data-doc="849">
                            <div class="doc-header">
                                <span class="doc-number">849</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">Response to Product Transfer</div>
                        </div>
                    </div>
                </div>
                
                <div class="doc-category">
                    <div class="category-title">🔄 Acknowledgments</div>
                    <div class="document-grid">
                        <div class="document-node" data-doc="997">
                            <div class="doc-header">
                                <span class="doc-number">997</span>
                                <span class="doc-direction">BOTH</span>
                            </div>
                            <div class="doc-name">Functional Ack</div>
                        </div>
                        <div class="document-node" data-doc="824">
                            <div class="doc-header">
                                <span class="doc-number">824</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">Application Advice</div>
                        </div>
                        <div class="document-node" data-doc="999">
                            <div class="doc-header">
                                <span class="doc-number">999</span>
                                <span class="doc-direction">BOTH</span>
                            </div>
                            <div class="doc-name">Implementation Ack</div>
                        </div>
                    </div>
                </div>
                
                <div class="doc-category">
                    <div class="category-title">📊 Inventory & Planning</div>
                    <div class="document-grid">
                        <div class="document-node" data-doc="846">
                            <div class="doc-header">
                                <span class="doc-number">846</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">Inventory Inquiry/Advice</div>
                        </div>
                        <div class="document-node" data-doc="852">
                            <div class="doc-header">
                                <span class="doc-number">852</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">Product Activity Data</div>
                        </div>
                        <div class="document-node" data-doc="830">
                            <div class="doc-header">
                                <span class="doc-number">830</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">Planning Schedule</div>
                        </div>
                        <div class="document-node" data-doc="862">
                            <div class="doc-header">
                                <span class="doc-number">862</span>
                                <span class="doc-direction">IN</span>
                            </div>
                            <div class="doc-name">Shipping Schedule</div>
                        </div>
                    </div>
                </div>
                
                <div class="doc-category">
                    <div class="category-title">💬 Communication</div>
                    <div class="document-grid">
                        <div class="document-node" data-doc="864">
                            <div class="doc-header">
                                <span class="doc-number">864</span>
                                <span class="doc-direction">BOTH</span>
                            </div>
                            <div class="doc-name">Text Message</div>
                        </div>
                        <div class="document-node" data-doc="888">
                            <div class="doc-header">
                                <span class="doc-number">888</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">Item Maintenance</div>
                        </div>
                        <div class="document-node" data-doc="832">
                            <div class="doc-header">
                                <span class="doc-number">832</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">Price/Sales Catalog</div>
                        </div>
                        <div class="document-node" data-doc="867">
                            <div class="doc-header">
                                <span class="doc-number">867</span>
                                <span class="doc-direction">OUT</span>
                            </div>
                            <div class="doc-name">Product Transfer</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flow-details" id="flowDetails"></div>
            
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-box" style="background: #667eea;"></div>
                    <span>Active Document</span>
                </div>
                <div class="legend-item">
                    <div class="legend-box" style="background: #48bb78;"></div>
                    <span>Related/Dependency</span>
                </div>
                <div class="legend-item">
                    <div class="legend-box" style="background: #e53e3e;"></div>
                    <span>Issue Detected</span>
                </div>
                <div class="legend-item">
                    <div class="legend-box" style="background: #fbbf24;"></div>
                    <span>Search Match</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Comprehensive EDI document database
        const documentData = {
            '850': {
                name: 'Purchase Order',
                type: 'inbound',
                category: 'order',
                flow: ['Trading Partner creates PO', 'PO sent via EDI', 'VAN/AS2 receives', 'System imports 850', 'Generate 997 ACK', 'Process order', 'Send 855 if required'],
                dependencies: [],
                generates: ['997', '855', '856', '810'],
                troubleshooting: [
                    'Missing 850: Check VAN/AS2 connection status and logs',
                    'Missing 850: Verify trading partner mailbox ID and qualifier',
                    'Missing 850: Check for 997 rejections (syntax errors)',
                    'Missing 850: Review ISA/GS envelope settings',
                    'Missing 850: Confirm trading partner is sending to correct receiver ID',
                    'Missing 850: Check EDI maps for import errors',
                    'Missing 850: Verify file permissions in receive directory'
                ],
                commonIssues: [
                    'ISA08 receiver ID mismatch',
                    'Invalid product codes in PO lines',
                    'Date format issues (CCYYMMDD vs YYMMDD)',
                    'Missing required segments (N1, PO1)',
                    'Duplicate ISA control numbers',
                    'Character set encoding problems',
                    'Decimal point issues in quantities/prices'
                ],
                segments: ['ISA', 'GS', 'ST', 'BEG', 'DTM', 'N1', 'PO1', 'CTT', 'SE', 'GE', 'IEA']
            },
            '855': {
                name: 'Purchase Order Acknowledgment',
                type: 'outbound',
                category: 'order',
                flow: ['Receive 850', 'Validate order', 'Check inventory', 'Generate 855', 'Send to partner', 'Receive 997'],
                dependencies: ['850'],
                generates: ['997 expected'],
                troubleshooting: [
                    'Cannot send 855: Verify 850 exists in system',
                    'Cannot send 855: Check acknowledgment code setup',
                    'Partner not receiving: Verify VAN/AS2 connection',
                    'Partner rejecting: Check BAK segment codes'
                ],
                commonIssues: [
                    'Wrong acknowledgment codes (AC, RJ, etc.)',
                    'Missing line level acknowledgments',
                    'Date mismatches with original PO',
                    'Incorrect quantity acknowledgments'
                ],
                segments: ['ST', 'BAK', 'DTM', 'N1', 'PO1', 'ACK', 'SE']
            },
            '856': {
                name: 'Advance Ship Notice (ASN)',
                type: 'outbound',
                category: 'shipping',
                flow: ['Pick/pack order', 'Generate shipping labels', 'Create 856 ASN', 'Send before shipment', 'Physical shipment departs', 'Partner receives 856', 'Send 997'],
                dependencies: ['850', 'Sometimes 855'],
                generates: ['997 expected', 'Enables 810'],
                troubleshooting: [
                    'Cannot create 856: Verify 850 exists',
                    'Hierarchy errors: Check HL segment parent/child relationships',
                    'Partner rejection: Validate SSCC-18 codes',
                    'Missing data: Ensure all carton/pallet details included',
                    'Timing issues: Must send before shipment arrives'
                ],
                commonIssues: [
                    'HL hierarchy loops incorrect',
                    'Missing or duplicate SSCC codes',
                    'Pack/carton/pallet structure wrong',
                    'MAN segment serial numbers invalid',
                    'Ship date/time in wrong timezone',
                    'Missing required references (PO number)'
                ],
                segments: ['ST', 'BSN', 'HL', 'TD1', 'TD5', 'REF', 'DTM', 'N1', 'LIN', 'SN1', 'MAN', 'SE']
            },
            '810': {
                name: 'Invoice',
                type: 'outbound',
                category: 'financial',
                flow: ['850 received', '856 sent (if required)', 'Shipment complete', 'Generate 810', 'Calculate taxes/totals', 'Send to partner', 'Receive 997', 'Await 820 payment'],
                dependencies: ['850', 'Often 856'],
                generates: ['997 expected', 'Triggers 820'],
                troubleshooting: [
                    'Cannot send 810: Verify 850 exists',
                    'Cannot send 810: Check if 856 is required first',
                    'Totals mismatch: Validate calculations match PO',
                    'Tax errors: Check jurisdiction codes',
                    'Partner rejection: Review 824 for details'
                ],
                commonIssues: [
                    'Invoice total doesn\'t match PO',
                    'Tax calculations incorrect',
                    'Missing payment terms (ITD segment)',
                    'Line items don\'t match shipped quantities',
                    'Credit terms code invalid',
                    'Ship-to address doesn\'t match 850'
                ],
                segments: ['ST', 'BIG', 'REF', 'N1', 'ITD', 'DTM', 'IT1', 'TDS', 'SAC', 'TXI', 'SE']
            },
            '820': {
                name: 'Payment Order/Remittance Advice',
                type: 'inbound',
                category: 'financial',
                flow: ['Partner processes 810', 'Payment approved', 'Generate 820', 'Include remittance details', 'Send via EDI', 'Supplier receives', 'Apply payment', 'Send 997'],
                dependencies: ['810'],
                generates: ['997'],
                troubleshooting: [
                    'Payment mismatch: Check RMR segments for adjustments',
                    'Missing payment: Verify BPR payment method',
                    'Cannot apply: Match invoice numbers in RMR',
                    'Deductions unclear: Review adjustment reason codes'
                ],
                commonIssues: [
                    'Payment amount doesn\'t match invoice',
                    'Adjustment codes not mapped',
                    'Check/EFT trace numbers missing',
                    'Multiple invoices in one payment',
                    'Partial payment handling'
                ],
                segments: ['ST', 'BPR', 'TRN', 'REF', 'DTM', 'N1', 'RMR', 'SE']
            },
            '997': {
                name: 'Functional Acknowledgment',
                type: 'both',
                category: 'acknowledgment',
                flow: ['Receive any EDI document', 'Parse syntax/structure', 'Validate segments', 'Generate 997', 'Report accept/reject', 'Send to originator'],
                dependencies: ['Any EDI document'],
                generates: [],
                troubleshooting: [
                    'Not generating 997: Check auto-acknowledge settings',
                    'Partner not receiving: Verify return path configuration',
                    'Always rejecting: Review AK segments for errors',
                    'Accepted but errors: Check if generating 999 instead'
                ],
                commonIssues: [
                    'AK501 = R but no error details',
                    'Not sending 997s automatically',
                    'Delayed 997 generation',
                    'Wrong functional group acknowledgment'
                ],
                segments: ['ST', 'AK1', 'AK2', 'AK3', 'AK4', 'AK5', 'AK9', 'SE']
            },
            '824': {
                name: 'Application Advice',
                type: 'inbound',
                category: 'acknowledgment',
                flow: ['Send business document', 'Partner validates content', 'Business rules checked', 'Generate 824', 'Detail accept/reject reasons', 'Send back', 'Process response'],
                dependencies: ['Previous document (810, 856, etc.)'],
                generates: ['997'],
                troubleshooting: [
                    'Document rejected: Check BGN02 for status',
                    'Unclear errors: Review OTI segments',
                    'Multiple errors: Each TED has reason',
                    'Need to resend: Fix errors and new control number'
                ],
                commonIssues: [
                    'Business validation failures',
                    'Data quality rejections',
                    'Missing required fields',
                    'Value not in partner\'s system'
                ],
                segments: ['ST', 'BGN', 'OTI', 'TED', 'SE']
            },
            '860': {
                name: 'Purchase Order Change Request',
                type: 'inbound',
                category: 'order',
                flow: ['Original 850 exists', 'Buyer needs changes', 'Generate 860', 'Send change request', 'Supplier receives', 'Evaluate changes', 'Send 865 response'],
                dependencies: ['850'],
                generates: ['997', '865'],
                troubleshooting: [
                    'Cannot process: Original 850 not found',
                    'Changes invalid: Check POC segment codes',
                    'Dates confused: Compare to original PO dates',
                    'Line changes: Verify line numbers match'
                ],
                commonIssues: [
                    'PO number doesn\'t match original',
                    'Invalid change codes',
                    'Changing shipped items',
                    'Quantity decrease after shipping'
                ],
                segments: ['ST', 'BCH', 'REF', 'DTM', 'N1', 'POC', 'SE']
            },
            '865': {
                name: 'Purchase Order Change Acknowledgment',
                type: 'outbound',
                category: 'order',
                flow: ['Receive 860 changes', 'Evaluate feasibility', 'Check inventory impact', 'Generate 865', 'Accept/reject changes', 'Send to buyer', 'Update systems'],
                dependencies: ['860', '850'],
                generates: ['997 expected'],
                troubleshooting: [
                    'Cannot send: 860 not processed',
                    'Partial acceptance: Use proper POC codes',
                    'Date issues: Verify against 860 request',
                    'System sync: Ensure changes applied'
                ],
                commonIssues: [
                    'Acknowledging wrong change request',
                    'Status codes incorrect',
                    'Missing line level responses',
                    'Not updating internal systems'
                ],
                segments: ['ST', 'BCA', 'REF', 'DTM', 'N1', 'POC', 'SE']
            },
            '846': {
                name: 'Inventory Inquiry/Advice',
                type: 'outbound',
                category: 'inventory',
                flow: ['Inventory levels change', 'Scheduled or triggered update', 'Query current stock', 'Generate 846', 'Include all locations', 'Send to partners', 'Receive 997'],
                dependencies: [],
                generates: ['997 expected'],
                troubleshooting: [
                    'Wrong quantities: Check UOM conversions',
                    'Missing items: Verify item master sync',
                    'Location issues: Include all warehouses',
                    'Timing: Check update frequency requirements'
                ],
                commonIssues: [
                    'Unit of measure mismatches',
                    'Not including all locations',
                    'Quantity on hand vs available',
                    'Update frequency too low'
                ],
                segments: ['ST', 'BIA', 'DTM', 'REF', 'N1', 'LIN', 'QTY', 'SE']
            },
            '940': {
                name: 'Warehouse Shipping Order',
                type: 'inbound',
                category: 'shipping',
                flow: ['3PL receives 940', 'Create pick ticket', 'Allocate inventory', 'Pick and pack', 'Generate 945 response', 'Ship goods', 'Send 856 if required'],
                dependencies: [],
                generates: ['997', '945', 'Sometimes 856'],
                troubleshooting: [
                    'Cannot process: Check W06 customer codes',
                    'Inventory issues: Verify stock levels',
                    'Address problems: Validate N1 segments',
                    'Pick errors: Check LX/W01 line details'
                ],
                commonIssues: [
                    'Customer codes not mapped',
                    'Ship-to address incomplete',
                    'Requested ship date passed',
                    'Inventory not available',
                    'Carrier codes invalid'
                ],
                segments: ['ST', 'W05', 'N1', 'W66', 'LX', 'W01', 'SE']
            },
            '945': {
                name: 'Warehouse Shipping Advice',
                type: 'outbound',
                category: 'shipping',
                flow: ['Complete 940 fulfillment', 'Pack shipment', 'Generate 945', 'Include tracking', 'Send to customer', 'Physical ship', 'Customer receives'],
                dependencies: ['940'],
                generates: ['997 expected'],
                troubleshooting: [
                    'Missing data: Include all W06 references',
                    'Carrier issues: Validate SCAC codes',
                    'Tracking: Ensure carrier tracking included',
                    'Quantities: Must match or explain variance'
                ],
                commonIssues: [
                    'Not referencing original 940',
                    'Missing tracking numbers',
                    'Quantity shipped mismatches',
                    'Wrong ship date/time'
                ],
                segments: ['ST', 'W06', 'N1', 'W27', 'LX', 'W12', 'SE']
            },
            '943': {
                name: 'Warehouse Stock Transfer Receipt',
                type: 'inbound',
                category: 'shipping',
                flow: ['Need stock transfer', 'Create 943 request', 'Send to warehouse', 'Warehouse processes', 'Transfer inventory', 'Update systems', 'Send 944 response'],
                dependencies: [],
                generates: ['997', '944'],
                troubleshooting: [
                    'Location codes: Verify warehouse mapping',
                    'Item master: Check SKU synchronization',
                    'Quantities: Validate available to transfer',
                    'Reason codes: Use standard transfer reasons'
                ],
                commonIssues: [
                    'From/to location confusion',
                    'SKU mismatches between systems',
                    'Transfer quantity exceeds available',
                    'Missing transfer reason codes'
                ],
                segments: ['ST', 'W06', 'N1', 'W07', 'W08', 'SE']
            },
            '852': {
                name: 'Product Activity Data',
                type: 'outbound',
                category: 'inventory',
                flow: ['Collect POS data', 'Aggregate by period', 'Calculate metrics', 'Generate 852', 'Include all locations', 'Send to vendor', 'Vendor analyzes'],
                dependencies: [],
                generates: ['997 expected'],
                troubleshooting: [
                    'Data gaps: Ensure all stores included',
                    'Date ranges: Match partner requirements',
                    'UPC issues: Verify product codes',
                    'Calculations: Check quantity conversions'
                ],
                commonIssues: [
                    'Missing store locations',
                    'Wrong reporting period',
                    'UPC/SKU mapping errors',
                    'Quantity on hand vs sold confusion'
                ],
                segments: ['ST', 'XQ', 'XPO', 'N1', 'LIN', 'ZA', 'SE']
            },
            '830': {
                name: 'Planning Schedule with Release Capability',
                type: 'inbound',
                category: 'inventory',
                flow: ['Buyer planning system', 'Forecast demand', 'Generate 830', 'Send to supplier', 'Supplier receives', 'Plan production', 'Adjust inventory'],
                dependencies: [],
                generates: ['997'],
                troubleshooting: [
                    'Forecast accuracy: Compare to history',
                    'Horizon issues: Check BFR date ranges',
                    'Release vs forecast: Understand differences',
                    'Cumulative quantities: Track properly'
                ],
                commonIssues: [
                    'Forecast vs firm quantities',
                    'Planning horizon too short/long',
                    'Cumulative quantity resets',
                    'Authorization to ship confusion'
                ],
                segments: ['ST', 'BFR', 'REF', 'DTM', 'N1', 'LIN', 'FST', 'SE']
            },
            '862': {
                name: 'Shipping Schedule',
                type: 'inbound',
                category: 'inventory',
                flow: ['Buyer determines needs', 'Create ship schedule', 'Send 862', 'Supplier receives', 'Plan shipments', 'Ship per schedule', 'Send 856 ASNs'],
                dependencies: [],
                generates: ['997', 'Drives 856s'],
                troubleshooting: [
                    'Schedule conflicts: Check BSS dates',
                    'Quantity issues: Verify FST segments',
                    'Delivery windows: Understand DTM qualifiers',
                    'Location routing: Check N1 ship-to'
                ],
                commonIssues: [
                    'Overlapping schedule dates',
                    'Unrealistic quantities',
                    'Multiple ship-to locations',
                    'JIT timing requirements'
                ],
                segments: ['ST', 'BSS', 'DTM', 'N1', 'LIN', 'FST', 'SE']
            },
            '864': {
                name: 'Text Message',
                type: 'both',
                category: 'communication',
                flow: ['Identify message need', 'Create 864', 'Set message type', 'Include references', 'Send to partner', 'Partner processes', 'May trigger action'],
                dependencies: ['Situational'],
                generates: ['997'],
                troubleshooting: [
                    'Message codes: Use agreed-upon BMG01',
                    'References: Include relevant MSG data',
                    'Character limits: Check MIT text length',
                    'Context: Provide enough information'
                ],
                commonIssues: [
                    'Message type codes not agreed',
                    'Text too vague',
                    'Missing reference numbers',
                    'Character encoding issues'
                ],
                segments: ['ST', 'BMG', 'DTM', 'N1', 'MSG', 'SE']
            },
            '812': {
                name: 'Credit/Debit Adjustment',
                type: 'outbound',
                category: 'financial',
                flow: ['Identify adjustment need', 'Calculate adjustment', 'Create 812', 'Reference original invoice', 'Send to customer', 'Customer processes', 'Updates AR/AP'],
                dependencies: ['Usually 810'],
                generates: ['997 expected'],
                troubleshooting: [
                    'References: Must link to original invoice',
                    'Calculations: Ensure math is clear',
                    'Reason codes: Use standard codes',
                    'Sign convention: Credit vs debit clarity'
                ],
                commonIssues: [
                    'Missing invoice references',
                    'Adjustment reason unclear',
                    'Mathematical sign errors',
                    'Not updating totals correctly'
                ],
                segments: ['ST', 'BCD', 'REF', 'DTM', 'N1', 'CDD', 'SE']
            },
            '832': {
                name: 'Price/Sales Catalog',
                type: 'outbound',
                category: 'communication',
                flow: ['Update pricing', 'Compile catalog data', 'Generate 832', 'Include all items', 'Send to partners', 'Partners update systems', 'Confirm receipt'],
                dependencies: [],
                generates: ['997 expected'],
                troubleshooting: [
                    'Item coverage: Include all active SKUs',
                    'Price tiers: Check CTP bracket breaks',
                    'Effective dates: Set DTM properly',
                    'UOM pricing: Clarify unit pricing'
                ],
                commonIssues: [
                    'Missing items from catalog',
                    'Price effective date errors',
                    'UOM pricing confusion',
                    'Bracket pricing setup wrong'
                ],
                segments: ['ST', 'BCT', 'DTM', 'REF', 'N1', 'LIN', 'PO4', 'CTP', 'SE']
            },
            '861': {
                name: 'Receiving Advice/Acceptance Certificate',
                type: 'inbound',
                category: 'shipping',
                flow: ['Receive shipment', 'Inspect goods', 'Note discrepancies', 'Generate 861', 'Report receipt details', 'Send to supplier', 'Reconcile differences'],
                dependencies: ['856 usually'],
                generates: ['997'],
                troubleshooting: [
                    'Quantity variance: Report accurately in RCD',
                    'Damage codes: Use standard disposition',
                    'Timing: Send promptly after receipt',
                    'References: Include ASN/PO numbers'
                ],
                commonIssues: [
                    'Not reporting discrepancies',
                    'Delayed receiving advice',
                    'Missing damage descriptions',
                    'Quantity reconciliation errors'
                ],
                segments: ['ST', 'BRA', 'DTM', 'REF', 'N1', 'RCD', 'SE']
            },
            '999': {
                name: 'Implementation Acknowledgment',
                type: 'both',
                category: 'acknowledgment',
                flow: ['Receive EDI document', 'Syntax validation passes', 'Check implementation rules', 'Generate 999', 'Detail compliance issues', 'Send to originator', 'Guide corrections'],
                dependencies: ['Any EDI document'],
                generates: [],
                troubleshooting: [
                    'Context details: IK3/CTX provide location',
                    'Error hierarchy: Understand IK levels',
                    'Multiple errors: Each has own context',
                    'Implementation vs syntax: Different from 997'
                ],
                commonIssues: [
                    'Complex error reporting structure',
                    'Missing implementation guide refs',
                    'Unclear error descriptions',
                    'Not actionable error messages'
                ],
                segments: ['ST', 'AK1', 'IK3', 'CTX', 'IK4', 'IK5', 'SE']
            }
        };

        // Common issue patterns for ticket analysis
        const issuePatterns = {
            missing: {
                keywords: ['missing', 'not receiving', 'no', 'don\'t see', 'not getting', 'haven\'t received', 'where is', 'can\'t find'],
                action: 'Check document flow and dependencies'
            },
            cannot_send: {
                keywords: ['cannot send', 'can\'t send', 'unable to send', 'won\'t send', 'not sending', 'send failed', 'transmission error'],
                action: 'Verify dependencies and connection'
            },
            rejected: {
                keywords: ['rejected', 'rejection', 'error', 'failed', 'not accepted', 'declined', 'invalid'],
                action: 'Check acknowledgments and validations'
            },
            mismatch: {
                keywords: ['mismatch', 'doesn\'t match', 'different', 'discrepancy', 'not matching', 'incorrect'],
                action: 'Verify data mapping and calculations'
            },
            connection: {
                keywords: ['connection', 'timeout', 'AS2', 'VAN', 'FTP', 'network', 'communication'],
                action: 'Check connectivity and protocols'
            }
        };

        // Trading partner configurations
        const tradingPartners = {
            walmart: {
                name: 'Walmart',
                requirements: ['856 required before 810', 'SSCC-18 mandatory', '24-hour 855 response'],
                commonIssues: ['Strict ASN timing', 'Department number required']
            },
            amazon: {
                name: 'Amazon',
                requirements: ['ARN references required', 'Specific label formats', 'Tight delivery windows'],
                commonIssues: ['Carton content accuracy', 'ASIN mapping']
            },
            target: {
                name: 'Target',
                requirements: ['DPCI product codes', 'DC-specific routing', 'Pack size requirements'],
                commonIssues: ['DC routing guides', 'Inner pack requirements']
            },
            kroger: {
                name: 'Kroger',
                requirements: ['875 Grocery PO format', 'Random weight items', 'Perishable date tracking'],
                commonIssues: ['Best-by date format', 'Case pack configurations']
            }
        };

        let activeDocument = null;
        let ticketDocuments = [];

        // Document selection handler
        document.querySelectorAll('.document-node').forEach(node => {
            node.addEventListener('click', function() {
                const docType = this.dataset.doc;
                showDocumentFlow(docType);
            });
        });

        function showDocumentFlow(docType) {
            // Reset all nodes
            document.querySelectorAll('.document-node').forEach(n => {
                n.classList.remove('active', 'highlighted');
            });
            
            // Set active document
            const activeNode = document.querySelector(`[data-doc="${docType}"]`);
            if (activeNode) {
                activeNode.classList.add('active');
                activeDocument = docType;
                
                // Highlight dependencies
                const data = documentData[docType];
                if (data && data.dependencies.length > 0) {
                    data.dependencies.forEach(dep => {
                        const depDoc = dep.match(/\d{3}/);
                        if (depDoc) {
                            const depNode = document.querySelector(`[data-doc="${depDoc[0]}"]`);
                            if (depNode) depNode.classList.add('highlighted');
                        }
                    });
                }
                
                // Show flow details
                showFlowDetails(docType);
            }
        }

        function showFlowDetails(docType) {
            const data = documentData[docType];
            if (!data) return;
            
            const detailsPanel = document.getElementById('flowDetails');
            detailsPanel.innerHTML = `
                <h2>📄 ${docType} - ${data.name}</h2>
                
                <div class="flow-path">
                    ${data.flow.map((step, index) => `
                        <div class="flow-step">${step}</div>
                        ${index < data.flow.length - 1 ? '<span class="flow-arrow">→</span>' : ''}
                    `).join('')}
                </div>
                
                <div class="info-grid">
                    ${data.dependencies.length > 0 ? `
                        <div class="info-section">
                            <h4>⚠️ Dependencies</h4>
                            <div class="alert-box">
                                <ul class="info-list">
                                    ${data.dependencies.map(dep => `<li>${dep}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                    ` : ''}
                    
                    ${data.generates.length > 0 ? `
                        <div class="info-section">
                            <h4>✅ Generates/Expects</h4>
                            <div class="success-box">
                                <ul class="info-list">
                                    ${data.generates.map(gen => `<li>${gen}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                    ` : ''}
                    
                    <div class="info-section">
                        <h4>🔧 Troubleshooting Guide</h4>
                        <ul class="info-list">
                            ${data.troubleshooting.map(tip => `<li>${tip}</li>`).join('')}
                        </ul>
                    </div>
                    
                    <div class="info-section">
                        <h4>⚡ Common Issues</h4>
                        <ul class="info-list">
                            ${data.commonIssues.map(issue => `<li>${issue}</li>`).join('')}
                        </ul>
                    </div>
                    
                    ${data.segments ? `
                        <div class="info-section">
                            <h4>📋 Key Segments</h4>
                            <p>${data.segments.join(', ')}</p>
                        </div>
                    ` : ''}
                </div>
            `;
            
            detailsPanel.style.display = 'block';
        }

        function analyzeTicket() {
            const ticketText = document.getElementById('ticketInput').value.toLowerCase();
            const analysisDiv = document.getElementById('ticketAnalysis');
            const analysisContent = document.getElementById('analysisContent');
            
            if (!ticketText.trim()) {
                alert('Please enter ticket content to analyze');
                return;
            }
            
            // Extract document numbers
            const docMatches = ticketText.match(/\b\d{3}\b/g) || [];
            ticketDocuments = [...new Set(docMatches)];
            
            // Identify issue type
            let issueType = 'general';
            let issueKeywords = [];
            
            for (const [type, pattern] of Object.entries(issuePatterns)) {
                for (const keyword of pattern.keywords) {
                    if (ticketText.includes(keyword)) {
                        issueType = type;
                        issueKeywords.push(keyword);
                        break;
                    }
                }
            }
            
            // Extract trading partner
            let partner = null;
            for (const [key, config] of Object.entries(tradingPartners)) {
                if (ticketText.includes(key)) {
                    partner = config;
                    break;
                }
            }
            
            // Build analysis
            analysisContent.innerHTML = `
                ${partner ? `
                    <div class="trading-partner-info">
                        <strong>Trading Partner: ${partner.name}</strong><br>
                        Requirements: ${partner.requirements.join(', ')}<br>
                        Common Issues: ${partner.commonIssues.join(', ')}
                    </div>
                ` : ''}
                
                <h4>Detected Documents:</h4>
                <div class="detected-docs">
                    ${ticketDocuments.length > 0 ? 
                        ticketDocuments.map(doc => `
                            <span class="doc-tag" onclick="showDocumentFlow('${doc}')">${doc}</span>
                        `).join('') : 
                        '<em>No specific documents mentioned</em>'
                    }
                </div>
                
                <h4>Issue Type: ${issueType.replace('_', ' ').toUpperCase()}</h4>
                <div class="issue-keywords">
                    ${issueKeywords.map(kw => `<span class="keyword">${kw}</span>`).join('')}
                </div>
                
                <h4>Recommended Actions:</h4>
                <ul class="info-list">
                    ${getRecommendations(issueType, ticketDocuments, ticketText).map(rec => 
                        `<li>${rec}</li>`
                    ).join('')}
                </ul>
                
                <h4>Related Documents to Check:</h4>
                <div class="detected-docs">
                    ${getRelatedDocuments(ticketDocuments).map(doc => 
                        `<span class="doc-tag" onclick="showDocumentFlow('${doc}')">${doc}</span>`
                    ).join('')}
                </div>
            `;
            
            analysisDiv.style.display = 'block';
            
            // Highlight mentioned documents
            document.querySelectorAll('.document-node').forEach(node => {
                node.classList.remove('highlighted');
                if (ticketDocuments.includes(node.dataset.doc)) {
                    node.classList.add('highlighted');
                }
            });
        }

        function getRecommendations(issueType, documents, ticketText) {
            const recommendations = [];
            
            switch(issueType) {
                case 'missing':
                    recommendations.push('Check VAN/AS2 connection status and recent activity');
                    recommendations.push('Verify trading partner is sending to correct mailbox/ID');
                    recommendations.push('Look for 997 functional acknowledgments with rejections');
                    recommendations.push('Review EDI translator logs for import errors');
                    if (documents.includes('850')) {
                        recommendations.push('Confirm ISA08 receiver ID matches your setup');
                        recommendations.push('Check if partner sent 860 (change) instead of new 850');
                    }
                    break;
                
                case 'cannot_send':
                    recommendations.push('Verify all required dependencies exist');
                    recommendations.push('Check outbound connection configuration');
                    recommendations.push('Validate document against partner specifications');
                    if (documents.includes('810')) {
                        recommendations.push('Ensure 850 exists for this invoice');
                        recommendations.push('Check if 856 ASN is required but missing');
                    }
                    break;
                
                case 'rejected':
                    recommendations.push('Review 997 or 824 for specific error codes');
                    recommendations.push('Check data mapping for incorrect values');
                    recommendations.push('Validate against trading partner guidelines');
                    recommendations.push('Look for 999 implementation acknowledgments');
                    break;
                
                case 'mismatch':
                    recommendations.push('Compare source document with generated output');
                    recommendations.push('Verify calculation logic and rounding');
                    recommendations.push('Check unit of measure conversions');
                    recommendations.push('Review field mapping configuration');
                    break;
                
                case 'connection':
                    recommendations.push('Test connection with trading partner');
                    recommendations.push('Verify certificates and credentials');
                    recommendations.push('Check firewall and network settings');
                    recommendations.push('Review AS2/VAN configuration');
                    break;
                
                default:
                    recommendations.push('Review end-to-end document flow');
                    recommendations.push('Check system logs for errors');
                    recommendations.push('Verify trading partner setup');
                    recommendations.push('Contact trading partner for their status');
            }
            
            return recommendations;
        }

        function getRelatedDocuments(documents) {
            const related = new Set();
            
            documents.forEach(doc => {
                const docData = documentData[doc];
                if (docData) {
                    // Add dependencies
                    docData.dependencies.forEach(dep => {
                        const depDoc = dep.match(/\d{3}/);
                        if (depDoc) related.add(depDoc[0]);
                    });
                    
                    // Add what it generates
                    docData.generates.forEach(gen => {
                        const genDoc = gen.match(/\d{3}/);
                        if (genDoc) related.add(genDoc[0]);
                    });
                }
                
                // Add common related documents
                if (doc === '850') related.add('855', '856', '810', '997');
                if (doc === '810') related.add('850', '856', '820', '824');
                if (doc === '856') related.add('850', '810', '861');
            });
            
            // Remove documents already mentioned
            documents.forEach(doc => related.delete(doc));
            
            return Array.from(related);
        }

        function searchDocuments(event) {
            const searchTerm = event.target.value.toLowerCase();
            
            document.querySelectorAll('.document-node').forEach(node => {
                const docNumber = node.dataset.doc;
                const docData = documentData[docNumber];
                const docName = docData ? docData.name.toLowerCase() : '';
                
                if (docNumber.includes(searchTerm) || docName.includes(searchTerm)) {
                    node.style.background = '#fef3c7';
                    node.style.borderColor = '#f59e0b';
                } else {
                    node.style.background = '';
                    node.style.borderColor = '';
                }
            });
        }

        // Reset view when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.document-node') && 
                !e.target.closest('.flow-details') && 
                !e.target.closest('.doc-tag')) {
                document.querySelectorAll('.document-node').forEach(n => {
                    n.classList.remove('active', 'highlighted');
                    n.style.background = '';
                    n.style.borderColor = '';
                });
                document.getElementById('flowDetails').style.display = 'none';
            }
        });

        // Example ticket for demo
        document.getElementById('ticketInput').placeholder = `Paste your support ticket here or upload files above...

Example:
Customer: Walmart
Issue: I am missing 850 purchase orders from yesterday
Additional Info: We are receiving 997s but no 850s are showing up in our system

Or try:
"Cannot send 810 invoices to Target. Getting 824 rejections saying 856 is required."`;

        // File handling functionality
        let uploadedFileContents = [];

        async function handleFileUpload(event) {
            const files = event.target.files;
            const uploadedFilesDiv = document.getElementById('uploadedFiles');
            
            for (const file of files) {
                const fileId = `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                
                // Create file item display
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.id = fileId;
                fileItem.innerHTML = `
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-type">${file.type || 'Unknown type'} - ${formatFileSize(file.size)}</div>
                        <div class="processing-indicator">Processing...</div>
                    </div>
                    <button class="remove-file" onclick="removeFile('${fileId}')">Remove</button>
                `;
                uploadedFilesDiv.appendChild(fileItem);
                
                // Process file based on type
                try {
                    const content = await processFile(file);
                    uploadedFileContents.push({
                        id: fileId,
                        name: file.name,
                        type: file.type,
                        content: content
                    });
                    
                    // Update display with preview
                    const processingIndicator = fileItem.querySelector('.processing-indicator');
                    processingIndicator.innerHTML = `<div class="file-content-preview">${getContentPreview(content, file.type)}</div>`;
                    
                } catch (error) {
                    const processingIndicator = fileItem.querySelector('.processing-indicator');
                    processingIndicator.innerHTML = `<span style="color: red;">Error: ${error.message}</span>`;
                }
            }
        }

        async function processFile(file) {
            const fileType = file.type;
            const fileName = file.name.toLowerCase();
            
            // EDI files (.edi, .txt with EDI content, .x12)
            if (fileName.endsWith('.edi') || fileName.endsWith('.x12') || 
                (fileName.endsWith('.txt') && await isEDIFile(file))) {
                return await readTextFile(file);
            }
            
            // Text files
            else if (fileType.startsWith('text/') || fileName.endsWith('.txt')) {
                return await readTextFile(file);
            }
            
            // CSV files
            else if (fileType === 'text/csv' || fileName.endsWith('.csv')) {
                return await readCSVFile(file);
            }
            
            // Excel files
            else if (fileType.includes('spreadsheet') || fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
                return await readExcelFile(file);
            }
            
            // PDF files (basic text extraction)
            else if (fileType === 'application/pdf') {
                return await readPDFFile(file);
            }
            
            // Image files
            else if (fileType.startsWith('image/')) {
                return await processImage(file);
            }
            
            // Video files
            else if (fileType.startsWith('video/')) {
                return await processVideo(file);
            }
            
            // Email files (.eml, .msg)
            else if (fileName.endsWith('.eml') || fileName.endsWith('.msg')) {
                return await readEmailFile(file);
            }
            
            // JSON files
            else if (fileType === 'application/json' || fileName.endsWith('.json')) {
                return await readJSONFile(file);
            }
            
            // XML files
            else if (fileType === 'application/xml' || fileName.endsWith('.xml')) {
                return await readXMLFile(file);
            }
            
            // Default: try to read as text
            else {
                return await readTextFile(file);
            }
        }

        async function readTextFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = reject;
                reader.readAsText(file);
            });
        }

        async function isEDIFile(file) {
            const text = await readTextFile(file);
            // Check for common EDI markers
            return text.includes('ISA') && text.includes('GS') && text.includes('ST');
        }

        async function readCSVFile(file) {
            const text = await readTextFile(file);
            // Simple CSV preview - could use Papa Parse for better parsing
            const lines = text.split('\n').slice(0, 10);
            return `CSV Data Preview:\n${lines.join('\n')}\n...(showing first 10 lines)`;
        }

        async function readExcelFile(file) {
            // For demo purposes, just show file info
            // In production, you'd use a library like SheetJS
            return `Excel file detected: ${file.name}\nSize: ${formatFileSize(file.size)}\n\nNote: For full Excel parsing, integrate with SheetJS library`;
        }

        async function readPDFFile(file) {
            // For demo purposes, just show file info
            // In production, you'd use PDF.js or similar
            return `PDF file detected: ${file.name}\nSize: ${formatFileSize(file.size)}\n\nNote: For full PDF text extraction, integrate with PDF.js library`;
        }

        async function processImage(file) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.src = e.target.result;
                    resolve(`Image: ${file.name}\nType: ${file.type}\nSize: ${formatFileSize(file.size)}\n\n[Image would be displayed here]`);
                };
                reader.readAsDataURL(file);
            });
        }

        async function processVideo(file) {
            return `Video: ${file.name}\nType: ${file.type}\nSize: ${formatFileSize(file.size)}\n\nNote: Video content would be analyzed for any visible EDI documents or error messages`;
        }

        async function readEmailFile(file) {
            const text = await readTextFile(file);
            // Extract key email parts
            const subject = text.match(/Subject: (.+)/)?.[1] || 'No subject';
            const from = text.match(/From: (.+)/)?.[1] || 'Unknown sender';
            return `Email File:\nFrom: ${from}\nSubject: ${subject}\n\nContent preview:\n${text.substring(0, 500)}...`;
        }

        async function readJSONFile(file) {
            const text = await readTextFile(file);
            try {
                const json = JSON.parse(text);
                return `JSON Data:\n${JSON.stringify(json, null, 2).substring(0, 1000)}...`;
            } catch (e) {
                return `Invalid JSON file: ${e.message}`;
            }
        }

        async function readXMLFile(file) {
            const text = await readTextFile(file);
            return `XML Data:\n${text.substring(0, 1000)}...`;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function getContentPreview(content, fileType) {
            if (!content) return 'No content extracted';
            
            // Extract EDI document references
            const ediDocs = content.match(/\b\d{3}\b/g) || [];
            const uniqueDocs = [...new Set(ediDocs)];
            
            let preview = content.substring(0, 200);
            if (content.length > 200) preview += '...';
            
            if (uniqueDocs.length > 0) {
                preview = `EDI Docs found: ${uniqueDocs.join(', ')}\n\n${preview}`;
            }
            
            return preview;
        }

        function removeFile(fileId) {
            // Remove from display
            const fileItem = document.getElementById(fileId);
            if (fileItem) fileItem.remove();
            
            // Remove from stored contents
            uploadedFileContents = uploadedFileContents.filter(f => f.id !== fileId);
        }

        // Enhanced analyze function to include file contents
        function analyzeTicket() {
            let ticketText = document.getElementById('ticketInput').value;
            
            // Add uploaded file contents to analysis
            if (uploadedFileContents.length > 0) {
                ticketText += '\n\n=== Uploaded File Contents ===\n';
                uploadedFileContents.forEach(file => {
                    ticketText += `\nFile: ${file.name}\n${file.content}\n`;
                });
            }
            
            ticketText = ticketText.toLowerCase();
            const analysisDiv = document.getElementById('ticketAnalysis');
            const analysisContent = document.getElementById('analysisContent');
            
            if (!ticketText.trim()) {
                alert('Please enter ticket content or upload files to analyze');
                return;
            }
            
            // Extract document numbers
            const docMatches = ticketText.match(/\b\d{3}\b/g) || [];
            ticketDocuments = [...new Set(docMatches)];
            
            // Identify issue type
            let issueType = 'general';
            let issueKeywords = [];
            
            for (const [type, pattern] of Object.entries(issuePatterns)) {
                for (const keyword of pattern.keywords) {
                    if (ticketText.includes(keyword)) {
                        issueType = type;
                        issueKeywords.push(keyword);
                        break;
                    }
                }
            }
            
            // Extract trading partner
            let partner = null;
            for (const [key, config] of Object.entries(tradingPartners)) {
                if (ticketText.includes(key)) {
                    partner = config;
                    break;
                }
            }
            
            // Extract EDI-specific information
            const ediPatterns = {
                isaSegment: /ISA\*[^\n\r]*/gi,
                gsSegment: /GS\*[^\n\r]*/gi,
                stSegment: /ST\*(\d{3})\*/gi,
                errorCodes: /\b(AK[0-9]{3}|TA1|[0-9]{3})\b/gi
            };
            
            let ediInfo = '';
            const isaMatches = ticketText.match(ediPatterns.isaSegment);
            const stMatches = ticketText.match(ediPatterns.stSegment);
            
            if (isaMatches || stMatches) {
                ediInfo = `
                    <h4>EDI Content Detected:</h4>
                    <div class="alert-box">
                        ${isaMatches ? `<strong>ISA Segments found:</strong> ${isaMatches.length}<br>` : ''}
                        ${stMatches ? `<strong>Transaction Sets:</strong> ${stMatches.join(', ')}<br>` : ''}
                    </div>
                `;
            }
            
            // Build analysis
            analysisContent.innerHTML = `
                ${partner ? `
                    <div class="trading-partner-info">
                        <strong>Trading Partner: ${partner.name}</strong><br>
                        Requirements: ${partner.requirements.join(', ')}<br>
                        Common Issues: ${partner.commonIssues.join(', ')}
                    </div>
                ` : ''}
                
                ${uploadedFileContents.length > 0 ? `
                    <h4>Files Analyzed: ${uploadedFileContents.length}</h4>
                ` : ''}
                
                <h4>Detected Documents:</h4>
                <div class="detected-docs">
                    ${ticketDocuments.length > 0 ? 
                        ticketDocuments.map(doc => `
                            <span class="doc-tag" onclick="showDocumentFlow('${doc}')">${doc}</span>
                        `).join('') : 
                        '<em>No specific documents mentioned</em>'
                    }
                </div>
                
                ${ediInfo}
                
                <h4>Issue Type: ${issueType.replace('_', ' ').toUpperCase()}</h4>
                <div class="issue-keywords">
                    ${issueKeywords.map(kw => `<span class="keyword">${kw}</span>`).join('')}
                </div>
                
                <h4>Recommended Actions:</h4>
                <ul class="info-list">
                    ${getRecommendations(issueType, ticketDocuments, ticketText).map(rec => 
                        `<li>${rec}</li>`
                    ).join('')}
                </ul>
                
                <h4>Related Documents to Check:</h4>
                <div class="detected-docs">
                    ${getRelatedDocuments(ticketDocuments).map(doc => 
                        `<span class="doc-tag" onclick="showDocumentFlow('${doc}')">${doc}</span>`
                    ).join('')}
                </div>
            `;
            
            analysisDiv.style.display = 'block';
            
            // Highlight mentioned documents
            document.querySelectorAll('.document-node').forEach(node => {
                node.classList.remove('highlighted');
                if (ticketDocuments.includes(node.dataset.doc)) {
                    node.classList.add('highlighted');
                }
            });
        }

        // Drag and drop support
        const fileUploadLabel = document.querySelector('.file-upload-label');
        
        fileUploadLabel.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadLabel.style.background = '#e9ecef';
            fileUploadLabel.style.borderColor = '#4c51bf';
        });
        
        fileUploadLabel.addEventListener('dragleave', (e) => {
            e.preventDefault();
            fileUploadLabel.style.background = '#f7fafc';
            fileUploadLabel.style.borderColor = '#5a67d8';
        });
        
        fileUploadLabel.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadLabel.style.background = '#f7fafc';
            fileUploadLabel.style.borderColor = '#5a67d8';
            
            const files = e.dataTransfer.files;
            document.getElementById('fileInput').files = files;
            handleFileUpload({ target: { files } });
        });
    </script>
</body>
</html>