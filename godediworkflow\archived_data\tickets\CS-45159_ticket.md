# CS-45159: [ecgrid] ECGRID-IN: Parcel Structure Validation ERROR

## Ticket Information
- **Key**: CS-45159
- **Status**: Canceled
- **Priority**: Medium
- **Assignee**: Unassigned
- **Reporter**: <EMAIL>
- **Created**: 2025-06-09T19:58:03.999-0600
- **Updated**: 2025-06-10T04:23:34.006-0600
- **Customer**: ecgrid

## Description
!https://ecgrid.com/images/header_ecgrid_800px.png!  
 
 ECGRID-IN: Parcel Structure Validation ERROR 
| _Parcel Information_ | 
||filename: |20250609205417_134105632.edi | 
||date: |10 Jun 2025 01:57:04 UTC | 
||status: |2103 | 
||status_message: |ECGRID-IN: Parcel Structure Validation ERROR | 
||status_date: |10 Jun 2025 01:57:04 UTC | 
||comm_control_id: | | 
||network_id: |480 | 
||network_name: |Datatrans Solutions, Inc. | 
||mailbox_id: |0 | 
||mailbox_name: | | 
||parcel_id: |2472110520 | 
||parcel_acknowledgment: | | 
| | 
| _X12.56 Info_ | 
||X12.56|N/A| 
| | 
| _Interchange Information_ | 
||interchange_header|ISA^00^ ^00^ ^ZZ^AHN ^01^002811917TPC ^250610^0142^U^00401^003157364^0^P^:| 
||interchange_status|1101| 
||interchange_status_message|Interchange Control Number does not match 003157364/3157364|
  
 

 Thank you for choosing  *ECGrid*
  _the ECGrid team_  

 !https://ecgrid.com/Images/message_footer_640px.png! 
powered by ECGridOS v4.1 (Build 478)

## Components


## Labels

