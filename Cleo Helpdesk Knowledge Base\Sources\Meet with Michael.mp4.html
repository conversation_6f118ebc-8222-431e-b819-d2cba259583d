<p>I got an 824 rejection and 810 total amount error. I cannot see to locate where or why this would rejected. What company is this? Oh, <PERSON> and <PERSON><PERSON>. All right. So, 6393. to be four. So, what did he do with it? So, this is <PERSON> and <PERSON><PERSON>. The only thing in my inbox is this. He What did he do with the A20? Four. All right. Well, we&#39;ll just do a smart. Oh, he&#39;s got one ready for each 24s. It just brings up I can easily then look for all 82 24. So he obviously archived it. Okay. So this is the one he&#39;s talking about. So this is the invoice number and he&#39;s saying that the TDS total amount is wrong. So the TDS total I exported this here. So the TDS is 4 72305 and this is saying here. This is just saying that we&#39;re missing something in that. Okay. But 472305. Oh, and 5 cents. 472305. 1975 times 37 Oh, well, yeah. Oh, and then there&#39;s Okay. I wonder if that&#39;s why. So, in this I TA segment an allowance or charge SC is not valid. They&#39;re saying so 11975* 37 is 32 3 119 75 * 37 is 443075. Okay. And we have a charge of 790. Well, that&#39;s the rate. We have a charge of 29230 bus 29230. 472305. I wonder if they&#39;re looking for a dec. decimal here cuz this is not 29230. Oh yeah, here it is. 29230. This has a decimal place in it. This does not have a decimal place in it. I wonder if that&#39;s why cuz this doesn&#39;t have a decimal. It also says SC is invalid, but I don&#39;t think that&#39;s it. I think it&#39;s the fact that the rod doesn&#39;t have a decimal in it. Um, it&#39;s odd because Maria was asking about this same, but I don&#39;t know if it was Wolf and Swickard. No, it wasn&#39;t. Okay. Wasn&#39;t Wolf and Swicker. And this is um cat 89. Oborn. So let&#39;s see it. This says it&#39;s a string. The format says none. This one says to imply decimal. I think we have to change that. I think that&#39;s what we have to make that say none. because these say none. And I think that&#39;s probably why it rejected is because in the raw data this is I wonder if that&#39;s why they&#39;re not looking. This never has a decimal in but I don&#39;t know if they want a decimal in that one. See, this is a whole number, so there&#39;s no decimals. That is the ITA7. If it is present with it 6 or eight. Then seven takes precedence. Okay. Use two pairs of caution. Total dollar amount for charge. Yeah. So, I&#39;m just making sure that however they want it, we have it set up. But here it says if it is present, then nine is required. And he used six and seven. Okay. Well, then I think he just shouldn&#39;t. If it seven is present with either six or eight, then seven takes precedence. Then at least six, seven or eight must be present. So I say that he doesn&#39;t fill in this dollar amount. He just fills in the rate and the quantity. Leave that blank. I&#39;m going to guess this is the first time he&#39;s used an allowance or a charge. It&#39;s perfect. Mhm. No, no. So this is $11975 is the unit price. Total 37. So here&#39;s the total invoice. This is the total 44375 plus there is a charge additional charge of 29230. So this plus this equals that. So this is right. But this it does not like this 29230. So what I think he should do I&#39;m surprised it didn&#39;t automatically fill that in. But if I take his invoice, I&#39;m going to say I have 37. It&#39;s each 11975. And then I&#39;m going to have a charge assert. charge. I don&#39;t know what it said. 7.2 or 37 each. Save. Now go back here. by customer doesn&#39;t change it. Now it did. Okay. So I won&#39;t use this in this. You want this amount. All right. Test. Okay. I wanted to create a new invoice as a test to see if I didn&#39;t have this value, would this still change? I thought it would take the rate times the quantity and calculate the 292 and add it here, but no, you have to actually enter it in. So, I&#39;m going to go to the DOM. Oh, not it 7. And I&#39;m going to say not While that&#39;s saving, I&#39;ll get rid of my test invoice. this. I&#39;m going to create a new invoice. I&#39;ll just use this one. I don&#39;t care. All right. 37 1975. Oh. each 1975. Now we have a charge. Oh, wait. Oh, never mind. I have to go back. I have to I have to do it because I just changed cat 89. All right. I need this invoice number. two times. Okay, I&#39;ll use this E30 respond each 90. Okay. Perfect. This was a charge. I only have one option. He selected service charge paid by customer 7090 37 each. because I think it looked as that as 29,230. So now I think that it&#39;ll be correct. Um so he probably has to create a new invoice. He can&#39;t use the one that&#39;s already there. So let&#39;s see. Does it say um yeah, item rejected. So he cast has to create a brand new invoice. Yeah. Well, no, for this invoice, this one one, this invoice, he has to create a brand new one. So, you can let him know that we fixed the, you know, we fixed the invoice so that the the values will calculate correctly and he just has to create a new invoice. He can&#39;t he can&#39;t restate page the one that he had and resend it because it the once you have a document created it won&#39;t pick up those changes. So he has to re he has to create a brand new one. Yeah, it wasn&#39;t it wasn&#39;t formatted correctly for the the lot the charge value. So 44307 The orders are not displaying the price per case and when invoicing the total amount appears to be less than the actual invoice. I&#39;m assuming any dot foods order this. And who is the customer? store here. Broster. I don&#39;t know if it&#39;s here. Okay. 7496. Oh, okay. I guess that&#39;s not their username. They must have changed it. price he&#39;s saying which we don&#39;t control but okay we&#39;ll look at it guys all right so let&#39;s go back to the ticket we&#39;re currently experiencing an issue with dot foods pricing The orders are not displaying the price per case and when invoicing the total amount appears less than the actual invoice typically varying from$1 to $4. No examples though, right? Nothing. Hasn&#39;t provided any nothing. It&#39;s always so helpful. Okay. So, 4607 * 952 is 4385864. What&#39;s not right? Well, he&#39;s saying no, he&#39;s saying that Oh, I more detail. He&#39;s saying it&#39;s typically varying from$1 to $4 different. So he&#39;s saying that the total amount appears to be less than the actual invoice typically varying from a1 to $4. But I need an example like can you give me a PO number that this has happened on like as an example because I can create I could take this and create an invoice. So these are the case prices. So this invoice right here is 61,18640. Okay, I&#39;ll create an invoice. 61,18640. It&#39;s exactly the same price. So what What&#39;s different? So, he is going to have to provide examples. Can you give us an example of an order where um when you create the invoice, you know, they&#39;re not the orders are not displaying the price per case. So, I just looked at dot foods orders and they have price per case. So case price case so we could you know you could tell them that you know you looked at the do foods orders and the orders do have the unit of measure of case and we&#39;ve created an invoice off of an order to use as an example and it matched. Could he please give us an example of an order that&#39;s not displaying the correct, you know, like not displaying a case price or where the invoice is creating incorrectly, but yeah, we need some details. Yeah. Yeah. Okay. No, that&#39;s okay. Any others? Okay. Okay. Oh, you&#39;re welcome. Oh, you&#39;re welcome. All right. Bye.</p>
