<h3 id="lauren-cohn-s-zoom-meeting"><PERSON>&#39;s Zoom Meeting</h3>
<p>Meeting started: May 16, 2025, 9:35:20 AM Meeting duration: 39 minutes Meeting participants: <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="summary-and-action-items">Summary and Action items</h4>
<h3 id="quick-summary">Quick summary</h3>
<p>During this meeting, <PERSON> and <PERSON> discussed the status of customer support tickets, focusing on a specific issue regarding the order of received EDIs. They reviewed the process for managing tickets and the importance of proper categorization and assignment. Additionally, they addressed potential development needs for unresolved issues.</p>
<h3 id="action-items-for-lauren-return-voicemails-from-the-queue-later-today-investigate-the-issue-with-the-current-day-filter-for-edis-and-determine-if-a-development-ticket-is-necessary-ensure-all-tickets-are-assigned-correctly-and-labeled-before-closing">Action items for <PERSON> ###  - [ ] Return voicemails from the queue later today.  - [ ] Investigate the issue with the &#34;current day&#34; filter for EDIs and determine if a development ticket is necessary.  - [ ] Ensure all tickets are assigned correctly and labeled before closing.</h3>
<h3 id="action-items-for-michael-assist-lauren-in-reviewing-the-ticket-management-process-as-needed-confirm-the-organization-type-for-the-ticket-related-to-pharmaceuticals">Action items for Michael ###  - [ ] Assist Lauren in reviewing the ticket management process as needed.  - [ ] Confirm the organization type for the ticket related to pharmaceuticals.</h3>
<h4 id="create-a-linear-ticket-new">Create a Linear ticket [NEW]</h4>
<h3 id="ticket-title">Ticket Title:</h3>
<p><strong>Fix EDI Date Sorting Issue for Customer</strong></p>
<h3 id="ticket-description-the-customer-reported-that-when-viewing-their-electronic-data-interchange-edi-listings-the-entries-are-not-sorted-by-the-date-received-as-expected-instead-the-current-day-s-entries-are-mixed-up-showing-dates-from-the-14th-to-the-16th-instead-of-just-the-current-day">Ticket Description: The customer reported that when viewing their Electronic Data Interchange (EDI) listings, the entries are not sorted by the date received as expected. Instead, the current day’s entries are mixed up, showing dates from the 14th to the 16th instead of just the current day.</h3>
<p><strong>Steps to Reproduce:</strong> 1. Log in as the customer. 2. Navigate to the EDI listings. 3. Select &#34;current day&#34; to view today&#39;s entries.
<strong>Expected Outcome:</strong> Entries should be sorted in chronological order based on the date received.
<strong>Current Outcome:</strong> Entries are displayed out of order, causing confusion for the customer.
<strong>Additional Context:</strong> - The issue may be related to a background process misinterpreting the &#34;current day&#34; filter. - The customer provided a screenshot illustrating the problem.
<strong>Action Required:</strong> Investigate the sorting logic for EDI listings and ensure that the entries are displayed correctly when filtered by date. If necessary, escalate to the development team for further analysis.
<strong>Customer Feedback:</strong> &#34;Previously, EDI entries were listed by date received, but now they appear mixed up.&#34;
<strong>Link to Customer Conversation:</strong> [Link to conversation] (insert actual link here)</p>
<h4 id="detailed-summary-with-citation">Detailed summary with citation</h4>
<h1 id="meeting-summary">Meeting Summary</h1>
<h2 id="1-voicemail-management-lauren-will-be-responsible-for-returning-voicemails-and-will-handle-them-later-if-customers-do-not-call-back-today-00-06">1. Voicemail Management - Lauren will be responsible for returning voicemails and will handle them later if customers do not call back today. [00:06]</h2>
<h2 id="2-ticket-management-discussion-on-managing-tickets-based-on-their-creation-date-with-a-focus-on-older-tickets-that-are-pending-responses-from-customers-partners-or-the-development-team-00-12-lauren-identifies-a-ticket-labeled-random-listing-as-potentially-the-easiest-to-resolve-00-20">2. Ticket Management - Discussion on managing tickets based on their creation date, with a focus on older tickets that are pending responses from customers, partners, or the development team. [00:12] - Lauren identifies a ticket labeled &#34;random listing&#34; as potentially the easiest to resolve. [00:20]</h2>
<h2 id="3-customer-issue-with-edi-listings-a-customer-mr-mike-reported-that-his-edi-listings-were-not-displayed-in-the-correct-order-by-date-received-lauren-reviews-the-customer-s-screenshot-for-context-00-25-the-issue-is-identified-as-being-caused-by-the-sorting-mechanism-where-clicking-on-the-date-column-can-change-the-order-of-the-listings-00-35-lauren-explains-how-to-rearrange-the-listings-by-clicking-the-date-column-twice-to-toggle-between-oldest-to-newest-and-newest-to-oldest-00-40">3. Customer Issue with EDI Listings - A customer, Mr. Mike, reported that his EDI listings were not displayed in the correct order by date received. Lauren reviews the customer&#39;s screenshot for context. [00:25] - The issue is identified as being caused by the sorting mechanism, where clicking on the date column can change the order of the listings. [00:35] - Lauren explains how to rearrange the listings by clicking the date column twice to toggle between oldest to newest and newest to oldest. [00:40]</h2>
<h2 id="4-potential-development-ticket-while-the-sorting-issue-was-resolved-lauren-suspects-that-there-may-be-a-deeper-issue-related-to-the-current-day-filter-showing-incorrect-dates-this-may-require-a-development-ticket-for-further-investigation-00-50">4. Potential Development Ticket - While the sorting issue was resolved, Lauren suspects that there may be a deeper issue related to the &#34;current day&#34; filter showing incorrect dates. This may require a development ticket for further investigation. [00:50]</h2>
<h2 id="5-ticket-assignment-and-progress-tracking-emphasis-on-ensuring-that-tickets-are-assigned-to-the-correct-person-and-marked-as-in-progress-to-maintain-accurate-sla-service-level-agreement-tracking-01-05-lauren-mentions-the-importance-of-tagging-the-organization-involved-in-the-ticket-for-better-tracking-and-accountability-01-15">5. Ticket Assignment and Progress Tracking - Emphasis on ensuring that tickets are assigned to the correct person and marked as &#34;in progress&#34; to maintain accurate SLA (Service Level Agreement) tracking. [01:05] - Lauren mentions the importance of tagging the organization involved in the ticket for better tracking and accountability. [01:15]</h2>
<h2 id="6-communication-with-customers-lauren-plans-to-reach-out-to-mr-mike-to-inform-him-that-the-order-issue-has-been-fixed-and-advises-him-to-refresh-his-screen-for-updates-01-30">6. Communication with Customers - Lauren plans to reach out to Mr. Mike to inform him that the order issue has been fixed and advises him to refresh his screen for updates. [01:30]</h2>
<h2 id="7-miscellaneous-notes-lauren-mentions-her-allergies-affecting-her-voice-during-the-meeting-01-40-discussion-on-the-importance-of-labeling-tickets-appropriately-before-closing-them-although-it-is-not-deemed-crucial-at-the-moment-01-50">7. Miscellaneous Notes - Lauren mentions her allergies affecting her voice during the meeting. [01:40] - Discussion on the importance of labeling tickets appropriately before closing them, although it is not deemed crucial at the moment. [01:50]</h2>
<p>This summary captures the key points discussed during the meeting, along with their respective timestamps for easy reference.</p>
<h4 id="detailed-summary">Detailed summary</h4>
<h1 id="meeting-summary-2">Meeting Summary</h1>
<h2 id="participants-michael-lauren">Participants - Michael - Lauren</h2>
<h2 id="overview-the-meeting-focused-on-reviewing-customer-support-tickets-discussing-the-status-of-various-issues-and-troubleshooting-a-specific-ticket-related-to-edi-electronic-data-interchange-listings">Overview The meeting focused on reviewing customer support tickets, discussing the status of various issues, and troubleshooting a specific ticket related to EDI (Electronic Data Interchange) listings.</h2>
<h2 id="key-points-discussed">Key Points Discussed</h2>
<h3 id="ticket-management-1-queue-review-lauren-reviewed-the-support-ticket-queue-and-noted-that-many-tickets-were-awaiting-responses-from-customers-partners-or-the-development-team-lauren-will-handle-returning-voicemails-if-customers-do-not-call-back">Ticket Management 1. <strong>Queue Review</strong>    - Lauren reviewed the support ticket queue and noted that many tickets were awaiting responses from customers, partners, or the development team.    - Lauren will handle returning voicemails if customers do not call back.</h3>
<ol start="2">
<li><strong>Ticket Prioritization</strong>    - The team decided to address tickets based on their creation date, with a focus on those requiring immediate attention.</li>
</ol>
<h3 id="specific-ticket-discussion-1-ticket-from-mr-mike-mr-mike-reported-that-edi-listings-were-not-displayed-in-the-expected-order-by-date-received-lauren-analyzed-the-issue-using-mr-mike-s-provided-screenshot">Specific Ticket Discussion 1. <strong>Ticket from Mr. Mike</strong>    - Mr. Mike reported that EDI listings were not displayed in the expected order by date received.    - Lauren analyzed the issue using Mr. Mike&#39;s provided screenshot.</h3>
<ol start="2">
<li><strong>Troubleshooting Steps</strong>    - Lauren logged in as Mr. Mike to investigate the issue.    - It was determined that the listings were out of order due to a sorting issue, which could be fixed by rearranging the column by date received.    - Lauren explained the sorting functionality:      - Click once for oldest to newest.      - Click twice for newest to oldest.</li>
<li><strong>Potential Development Ticket</strong>    - Despite resolving the sorting issue, Lauren suspected that the &#34;current day&#34; filter might be incorrectly displaying data from the last few days instead of just the current day.    - This may require a development ticket for further investigation.</li>
</ol>
<h3 id="communication-with-customers">Communication with Customers</h3>
<ol>
<li><strong>Customer Notification</strong>    - Lauren planned to email Mr. Mike to inform him that the sorting issue was resolved and to advise him to refresh his screen for updates.    - Emphasized the importance of ensuring that internal notes are correctly marked before sending any communication to customers.</li>
<li><strong>Ticket Management Best Practices</strong>    - Lauren highlighted the need to assign tickets to oneself and mark them as &#34;in progress&#34; to track SLA (Service Level Agreement) times effectively.    - Mentioned the importance of tagging customers in tickets for better visibility and accountability.</li>
</ol>
<h3 id="additional-notes-lauren-shared-personal-challenges-with-allergies-which-affected-her-voice-during-the-meeting-michael-acknowledged-the-challenges-of-transitioning-to-a-new-system-and-reassured-lauren-that-mistakes-are-common-during-this-process">Additional Notes - Lauren shared personal challenges with allergies, which affected her voice during the meeting. - Michael acknowledged the challenges of transitioning to a new system and reassured Lauren that mistakes are common during this process.</h3>
<h2 id="action-items-lauren-to-follow-up-with-mr-mike-regarding-the-edi-listing-issue-investigate-the-current-day-filter-issue-further-potentially-creating-a-development-ticket-ensure-all-tickets-are-properly-assigned-and-marked-as-in-progress">Action Items - Lauren to follow up with Mr. Mike regarding the EDI listing issue. - Investigate the &#34;current day&#34; filter issue further, potentially creating a development ticket. - Ensure all tickets are properly assigned and marked as &#34;in progress.&#34;</h2>
<h2 id="conclusion-the-meeting-provided-a-thorough-review-of-current-customer-support-tickets-with-a-focus-on-resolving-specific-issues-and-improving-communication-practices-the-team-emphasized-the-importance-of-proper-ticket-management-and-customer-interaction">Conclusion The meeting provided a thorough review of current customer support tickets, with a focus on resolving specific issues and improving communication practices. The team emphasized the importance of proper ticket management and customer interaction.</h2>
<h4 id="generated-content">Generated Content</h4>
<h3 id="mr-mike-s-situation">Mr. Mike&#39;s Situation</h3>
<ul>
<li><strong>Issue</strong>:   - Mr. Mike reported that when he received Electronic Data Interchanges (EDIs), they were not listed in the correct order by date received.   - Specifically, when he checked for the current day, the entries were mixed up and not displayed as expected.</li>
</ul>
<h3 id="resolution-steps">Resolution Steps</h3>
<ol>
<li><strong>Initial Assessment</strong>:    - Lauren reviewed the situation and noted that the entries were indeed out of order.    - She identified that the issue was likely due to the way the sorting function was being used.</li>
<li><strong>Fix Implemented</strong>:    - Lauren demonstrated how to rearrange the entries by clicking on the &#34;Received&#34; column header twice:      - <strong>First Click</strong>: Sorts entries from oldest to newest.      - <strong>Second Click</strong>: Sorts entries from newest to oldest.    - This action corrected the order of the entries, ensuring they displayed properly.</li>
<li><strong>Ongoing Issue</strong>:    - Although the sorting issue was resolved, there was still a problem with the &#34;current day&#34; filter showing dates beyond the expected range (14th to 16th).    - Lauren suspected that this might require further investigation by the development team.</li>
</ol>
<h3 id="summary-mr-mike-s-issue-with-the-edi-order-was-fixed-by-rearranging-the-entries-based-on-the-received-date-however-the-problem-with-the-current-day-filter">Summary - Mr. Mike&#39;s issue with the EDI order was fixed by rearranging the entries based on the &#34;Received&#34; date. However, the problem with the &#34;current day&#34; filter</h3>
<p>remains unresolved and may need a development ticket for further action.</p>
<h4 id="generated-content-2">Generated Content</h4>
<h3 id="summary-of-mr-mike-s-issues">Summary of Mr. Mike&#39;s Issues</h3>
<ol>
<li><strong>Issue with EDI Listings</strong>    - <strong>Description</strong>: Mr. Mike reported that when he receives EDIs, they are not listed by the date received as expected.    - <strong>Initial Assessment</strong>: Entries were out of order.    - <strong>Resolution Steps</strong>:      - Sorting function was used to rearrange entries.      - Clicked to sort by oldest to newest and then newest to oldest to ensure proper display.    - <strong>Current Status</strong>: The sorting issue has been resolved, and entries are now displayed correctly.</li>
<li><strong>Current Day Display Issue</strong>    - <strong>Description</strong>: The &#34;current day&#34; filter is showing entries from the 14th to the 16th, which is incorrect.    - <strong>Next Steps</strong>:      - Further investigation needed by the development team.      - Communication with Mr. Mike will include an update on this issue.</li>
</ol>
<h3 id="key-points-ensure-all-tickets-are-assigned-and-marked-as-in-progress-for-accurate-sla-tracking-maintain-clear-communication-with-customers-regarding-the-status-of-their-issues-use-sorting-functions-effectively-to-manage-ticket-displays">Key Points - Ensure all tickets are assigned and marked as &#34;in progress&#34; for accurate SLA tracking. - Maintain clear communication with customers regarding the status of their issues. - Use sorting functions effectively to manage ticket displays.</h3>
<h3 id="thought-provoking-questions">Thought-Provoking Questions</h3>
<ul>
<li>What are the potential impacts of unresolved issues on customer satisfaction? - How can we improve the ticketing system to prevent similar issues in the future?</li>
</ul>
<h3 id="real-world-applications-understanding-customer-support-processes-can-enhance-service-delivery-in-various-industries-effective-communication-strategies-can-lead-to-better-customer-relationships">Real-World Applications - Understanding customer support processes can enhance service delivery in various industries. - Effective communication strategies can lead to better customer relationships.</h3>
<h3 id="areas-for-further-research-investigate-best-practices-for-ticket-management-systems-explore-common-issues-faced-by-users-in-similar-systems">Areas for Further Research - Investigate best practices for ticket management systems. - Explore common issues faced by users in similar systems.</h3>
<h3 id="potential-exam-questions-1-describe-the-steps-taken-to-resolve-mr-mike-s-edi-listing-issue-2-what-communication-strategies-should-be-employed-when-dealing-with-unresolved-customer-issues">Potential Exam Questions 1. Describe the steps taken to resolve Mr. Mike&#39;s EDI listing issue. 2. What communication strategies should be employed when dealing with unresolved customer issues?</h3>
<h3 id="glossary-of-new-terms-edi-electronic-data-interchange-a-system-for-exchanging-business-documents-in-a-standardized-electronic-format-sla-service-level-agreement-a-commitment-between-a-service-provider-and-a-client-regarding-the-expected-level-of-service">Glossary of New Terms - <strong>EDI</strong>: Electronic Data Interchange, a system for exchanging business documents in a standardized electronic format. - <strong>SLA</strong>: Service Level Agreement, a commitment between a service provider and a client regarding the expected level of service.</h3>
<h3 id="main-takeaways-sorting-issues-can-often-be-resolved-through-simple-user-actions-ongoing-communication-with-customers-is-crucial-especially-when-issues-are-still-being-investigated">Main Takeaways - Sorting issues can often be resolved through simple user actions. - Ongoing communication with customers is crucial, especially when issues are still being investigated.</h3>
<h4 id="generated-content-3">Generated Content</h4>
<h3 id="summary-of-meeting-notes">Summary of Meeting Notes</h3>
<h2 id="participants-michael-lauren-date-insert-date"><strong>Participants:</strong> Michael, Lauren
<strong>Date:</strong> [Insert Date]</h2>
<h4 id="1-ticket-management-process-lauren-will-handle-returning-voicemails-and-managing-the-ticket-queue-tickets-are-prioritized-based-on-their-creation-date-but-some-may-require-customer-input-or-development-team-assistance">1. <strong>Ticket Management Process</strong>    - Lauren will handle returning voicemails and managing the ticket queue.    - Tickets are prioritized based on their creation date, but some may require customer input or development team assistance.</h4>
<h4 id="2-customer-issue-resolution-case-study-mr-mike-s-edi-issue-initial-problem-edi-entries-were-not-listed-by-date-received-resolution-steps-lauren-logged-in-to-investigate-the-issue-discovered-that-the-entries-were-out-of-order-due-to-user-interaction-clicking-the-column-header-fix-rearranged-the-entries-by-clicking-the-received-column-twice-to-sort-by-date-noted-that-the-current-day-filter-was-still-displaying-incorrect-data-showing-dates-beyond-the-current-day-suggested-that-this-may-require-a-development-ticket-for-further-investigation">2. <strong>Customer Issue Resolution</strong>    - <strong>Case Study: Mr. Mike&#39;s EDI Issue</strong>      - <strong>Initial Problem:</strong> EDI entries were not listed by date received.      - <strong>Resolution Steps:</strong>        - Lauren logged in to investigate the issue.        - Discovered that the entries were out of order due to user interaction (clicking the column header).        - <strong>Fix:</strong> Rearranged the entries by clicking the &#34;Received&#34; column twice to sort by date.        - Noted that the &#34;Current Day&#34; filter was still displaying incorrect data (showing dates beyond the current day).        - Suggested that this may require a development ticket for further investigation.</h4>
<h4 id="3-communication-with-customers-importance-of-keeping-customers-informed-about-the-status-of-their-issues-suggested-email-template-for-customer-communication-acknowledge-the-issue-and-confirm-that-it-has-been-addressed-inform-them-about-ongoing-issues-and-that-further-investigation-is-underway-emphasized-the-need-for-clear-and-concise-communication-especially-for">3. <strong>Communication with Customers</strong>    - Importance of keeping customers informed about the status of their issues.    - Suggested email template for customer communication:      - Acknowledge the issue and confirm that it has been addressed.      - Inform them about ongoing issues and that further investigation is underway.    - Emphasized the need for clear and concise communication, especially for</h4>
<p>customers who may not be fluent in English.</p>
<h4 id="4-development-team-interaction-development-team-operates-on-a-different-schedule-and-may-be-located-internationally-e-g-india-tickets-submitted-to-development-may-take-time-to-resolve-and-updates-are-communicated-through-a-system-jira-lauren-shared-an-example-of-a-previous-ticket-submitted-to-development-detailing-the-back-and-forth-communication-required-to-resolve-the-issue">4. <strong>Development Team Interaction</strong>    - Development team operates on a different schedule and may be located internationally (e.g., India).    - Tickets submitted to development may take time to resolve, and updates are communicated through a system (JIRA).    - Lauren shared an example of a previous ticket submitted to development, detailing the back-and-forth communication required to resolve the issue.</h4>
<h4 id="5-best-practices-for-ticket-management-ensure-tickets-are-assigned-and-marked-as-in-progress-to-track-sla-service-level-agreement-compliance-use-internal-notes-for-documentation-before-sending-customer-communications-maintain-a-list-of-ongoing-issues-for-reference-and-accountability">5. <strong>Best Practices for Ticket Management</strong>    - Ensure tickets are assigned and marked as &#34;in progress&#34; to track SLA (Service Level Agreement) compliance.    - Use internal notes for documentation before sending customer communications.    - Maintain a list of ongoing issues for reference and accountability.</h4>
<hr>
<h3 id="key-takeaways-customer-communication-always-keep-customers-updated-on-their-issues-to-maintain-trust-and-transparency-ticket-management-properly-sort-and-manage-tickets-to-ensure-efficient-resolution-development-coordination-understand-the-development-team-s-workflow-and-be-patient-with-the-resolution-process">Key Takeaways - <strong>Customer Communication:</strong> Always keep customers updated on their issues to maintain trust and transparency. - <strong>Ticket Management:</strong> Properly sort and manage tickets to ensure efficient resolution. - <strong>Development Coordination:</strong> Understand the development team&#39;s workflow and be patient with the resolution process.</h3>
<hr>
<h3 id="citation-meeting-notes-from-a-discussion-between-michael-and-lauren-regarding-ticket-management-and-customer-service-processes-insert-date">Citation Meeting Notes from a discussion between Michael and Lauren regarding ticket management and customer service processes. [Insert Date].</h3>
<h4 id="generated-content-4">Generated Content</h4>
<h3 id="ticket-overview">Ticket Overview</h3>
<ol>
<li><strong>Ticket: Random Listing Issue</strong>    - <strong>Customer</strong>: Mr. Mike    - <strong>Issue</strong>: EDI entries were not listed by date received; they appeared jumbled.    - <strong>Initial Assessment</strong>:      - Entries were out of order.      - Sorting function was used to rearrange by date received.    - <strong>Resolution</strong>:      - Confirmed that sorting by oldest to newest and newest to oldest fixed the display issue.      - Noted that the &#34;current day&#34; filter was still showing incorrect dates (14th to 16th).    - <strong>Next Steps</strong>:      - Development team needs to investigate the &#34;current day&#34; filter issue.      - Email response to Mr. Mike confirming the fix for the order and informing him of the ongoing investigation.</li>
<li><strong>Ticket: Packing System Error</strong>    - <strong>Customer</strong>: Unspecified    - <strong>Issue</strong>: Customer&#39;s packing system showed discrepancies between packed items and submitted totals.    - <strong>Initial Assessment</strong>:      - Customer packed four packs, but the system recorded only one.    - <strong>Resolution Process</strong>:      - Submitted to development for investigation.      - Ongoing communication with the development team regarding the issue.      - Updates received from the development team about the status of the resolution.</li>
</ol>
<ul>
<li><strong>Next Steps</strong>:      - Await further updates from the development team regarding the resolution.</li>
</ul>
<h3 id="summary-of-actions-taken-for-mr-mike-s-ticket-resolved-sorting-issue-communicated-with-the-customer-about-the-fix-and-ongoing-investigation-for-packing-system-error-submitted-ticket-to-development-maintained-communication-with-the-development-team-for-updates">Summary of Actions Taken - <strong>For Mr. Mike&#39;s Ticket</strong>:   - Resolved sorting issue.   - Communicated with the customer about the fix and ongoing investigation.   - <strong>For Packing System Error</strong>:   - Submitted ticket to development.   - Maintained communication with the development team for updates.</h3>
<h3 id="additional-notes-ensure-all-tickets-are-assigned-and-marked-as-in-progress-for-sla-tracking-communicate-effectively-with-customers-to-keep-them-informed-of-progress-utilize-jira-for-tracking-development-tickets-and-updates">Additional Notes - Ensure all tickets are assigned and marked as &#34;in progress&#34; for SLA tracking. - Communicate effectively with customers to keep them informed of progress. - Utilize JIRA for tracking development tickets and updates.</h3>
<h4 id="generated-content-5">Generated Content</h4>
<h3 id="meeting-summary-3">Meeting Summary</h3>
<h4 id="participants-michael-lauren-2">Participants - <strong>Michael</strong> - <strong>Lauren</strong></h4>
<h4 id="agenda-review-of-customer-support-tickets-discussion-of-ticket-handling-procedures-interaction-with-the-development-team">Agenda - Review of customer support tickets - Discussion of ticket handling procedures - Interaction with the development team</h4>
<hr>
<h3 id="key-points">Key Points</h3>
<ol>
<li><strong>Ticket Management</strong>    - Lauren will handle returning voicemails and managing the ticket queue.    - Tickets are prioritized based on their creation date, but some may require customer follow-up or development team input.</li>
<li><strong>Customer Issue: EDI Listing</strong>    - <strong>Customer:</strong> Mr. Mike    - <strong>Issue:</strong> EDI listings were not displayed in the expected order.    - <strong>Resolution Steps:</strong>      - Initially thought to be a simple sorting issue.      - Identified that the current day filter was displaying incorrect dates (14th to 16th).      - Suggested that the customer refresh their screen to see updated information.      - Noted that the sorting issue was resolved, but the date filter issue may require a development ticket.</li>
<li><strong>Ticket Handling Procedures</strong>    - Ensure tickets are assigned and marked as &#34;in progress&#34; to track SLA (Service Level Agreement) timing.    - Importance of clear communication with customers about their issues and updates.    - Internal notes should be used for documentation, and care should be taken to avoid sending internal notes to customers.</li>
<li><strong>Development Team Interaction</strong>    - Development team operates on a different system (JIRA) and may be located in different time zones.    - Tickets submitted to development should include clear, concise information to avoid miscommunication.    - Updates from the development team are communicated back to the support
team for customer follow-up.</li>
<li><strong>Customer Communication</strong>    - Regular updates to customers are crucial, even if there is no immediate resolution.    - Use friendly and professional language in emails, especially with recurring customers.</li>
<li><strong>Additional Customer Case: Global Pool Products</strong>    - Lauren reviewed a ticket for Global Pool Products, where the customer needed to add a charge to an invoice.    - Identified necessary documentation and details required for processing the request.</li>
</ol>
<hr>
<h3 id="important-concepts">Important Concepts</h3>
<ul>
<li><strong>SLA (Service Level Agreement):</strong> The time frame in which a support ticket must be addressed. - <strong>EDI (Electronic Data Interchange):</strong> A system for exchanging business documents electronically. - <strong>JIRA:</strong> A project management tool used for tracking issues and development tasks.</li>
</ul>
<hr>
<h3 id="thought-provoking-questions-2">Thought-Provoking Questions</h3>
<ul>
<li>How can we improve the efficiency of our ticket resolution process? - What strategies can be implemented to enhance communication with the development team? - How can we better manage customer expectations regarding ticket resolution times?</li>
</ul>
<hr>
<h3 id="real-world-applications">Real-World Applications</h3>
<ul>
<li>Understanding ticket management can be applied in various customer service roles across industries. - Effective communication strategies are essential in any customer-facing position.</li>
</ul>
<hr>
<h3 id="areas-for-further-research">Areas for Further Research</h3>
<ul>
<li>Explore best practices for managing remote development teams. - Investigate tools that can streamline ticket management and customer communication.</li>
</ul>
<hr>
<h3 id="suggested-resources">Suggested Resources</h3>
<ul>
<li><strong>Books:</strong>   - &#34;The Lean Startup&#34; by Eric Ries (for understanding efficient processes)   - &#34;Crucial Conversations&#34; by Kerry Patterson et al. (for improving communication skills)</li>
<li><strong>Articles:</strong>   - Articles on customer service best practices   - Guides on using JIRA effectively</li>
<li><strong>Videos:</strong>   - Tutorials on ticket management systems</li>
<li>Webinars on customer service excellence</li>
</ul>
<hr>
<h3 id="glossary-of-terms">Glossary of Terms</h3>
<ul>
<li><strong>EDI:</strong> Electronic Data Interchange - <strong>SLA:</strong> Service Level Agreement - <strong>JIRA:</strong> Project management tool for issue tracking</li>
</ul>
<hr>
<h3 id="main-takeaways">Main Takeaways</h3>
<ul>
<li>Effective ticket management and communication are crucial for customer satisfaction. - Collaboration with the development team is necessary for resolving complex issues. - Continuous improvement in processes can lead to better customer service outcomes.</li>
</ul>
<h4 id="generated-content-6">Generated Content</h4>
<p>The transcript does not specify the exact number of tickets discussed. However, it mentions various tickets being reviewed, including one labeled &#34;random listing&#34; that was identified as the easiest ticket. Additionally, there are references to older tickets and the need for a development ticket for a specific issue. For precise numbers, further context or details from the meeting would be needed.</p>
<h4 id="generated-content-7">Generated Content</h4>
<h3 id="problem-with-pool-warehouse">Problem with Pool Warehouse</h3>
<ul>
<li><strong>Customer Issue</strong>:   - The customer from Pool Warehouse is experiencing a problem related to <strong>adding a charge on an invoice</strong>.</li>
<li><strong>Details</strong>:   - The customer provided a <strong>Purchase Order (PO)</strong> and mentioned they are trying to charge or change amounts on the invoice.   - It was noted that if any singular field is present in the invoice, then all three required fields must be filled out.</li>
<li><strong>Specifics</strong>:   - The customer only has the option for a <strong>discount</strong>, which may not be correct based on the context of their request.</li>
</ul>
<h3 id="next-steps-internal-notes-document-the-customer-issue-and-the-details-regarding-the-invoice-message-id-and-reference-further-investigation-check-the-specifics-of-the-invoice-and-ensure-all-required-fields-are-correctly-populated">Next Steps - <strong>Internal Notes</strong>:   - Document the customer issue and the details regarding the invoice message ID and reference.   - <strong>Further Investigation</strong>:   - Check the specifics of the invoice and ensure all required fields are correctly populated.</h3>
<h3 id="thought-provoking-questions-what-are-the-common-challenges-customers-face-when-dealing-with-invoice-adjustments-how-can-we-improve-the-communication-process-regarding-invoice-issues-to-prevent-misunderstandings">Thought-Provoking Questions - What are the common challenges customers face when dealing with invoice adjustments? - How can we improve the communication process regarding invoice issues to prevent misunderstandings?</h3>
<h3 id="real-world-application-understanding-invoice-adjustments-is-crucial-for-businesses-to-maintain-accurate-financial-records-and-customer-satisfaction">Real-World Application - Understanding invoice adjustments is crucial for businesses to maintain accurate financial records and customer satisfaction.</h3>
<h3 id="areas-for-further-research-investigate-best-practices-for-managing-invoice-discrepancies-and-customer-communication-strategies">Areas for Further Research - Investigate best practices for managing invoice discrepancies and customer communication strategies.</h3>
<h3 id="potential-exam-questions-1-what-steps-should-be-taken-when-a-customer-requests-changes-to-an-invoice-2-explain-the-significance-of-required-fields-in-invoice-processing">Potential Exam Questions 1. What steps should be taken when a customer requests changes to an invoice? 2. Explain the significance of required fields in invoice processing.</h3>
<h4 id="generated-content-8">Generated Content</h4>
<h3 id="ticket-notes-walmart-packing-situation">Ticket Notes: Walmart Packing Situation</h3>
<h4 id="1-ticket-overview-organization-walmart-issue-incorrect-packing-information-displayed-in-the-system">1. <strong>Ticket Overview</strong>    - <strong>Organization</strong>: Walmart    - <strong>Issue</strong>: Incorrect packing information displayed in the system.</h4>
<h4 id="2-details-of-the-issue-customer-reported-that-when-packing-the-system-showed-discrepancies-packed-items-customer-packed-4-packs-system-display-only-showing-1-pack-total-display-incorrect-total-of-12-packs">2. <strong>Details of the Issue</strong>    - Customer reported that when packing, the system showed discrepancies:      - <strong>Packed Items</strong>: Customer packed 4 packs.      - <strong>System Display</strong>: Only showing 1 pack.      - <strong>Total Display</strong>: Incorrect total of 12 packs.</h4>
<h4 id="3-investigation-process-initial-submission-customer-s-issue-was-submitted-to-the-development-team-development-team-response-provided-initial-feedback-on-the-issue-suggested-a-resolution-that-required-additional-specifications-from-walmart-back-and-forth-communication">3. <strong>Investigation Process</strong>    - <strong>Initial Submission</strong>: Customer&#39;s issue was submitted to the development team.    - <strong>Development Team Response</strong>:      - Provided initial feedback on the issue.      - Suggested a resolution that required additional specifications from Walmart.    - <strong>Back and Forth Communication</strong>:</h4>
<ul>
<li>Development team attempted fixes, but the issue failed QA multiple times.      - Continuous updates were provided to ensure clarity on the situation.</li>
</ul>
<h4 id="4-action-steps-taken-documentation-all-relevant-details-were-documented-in-the-ticket-screenshots-and-examples-were-included-to-illustrate-the-issue-communication-regular-updates-were-communicated-to-the-customer-to-keep-them-informed-of-progress-ensured-that-the-language-used-was-simple-for-clarity-especially-for-potential-translation-issues">4. <strong>Action Steps Taken</strong>    - <strong>Documentation</strong>:      - All relevant details were documented in the ticket.      - Screenshots and examples were included to illustrate the issue.    - <strong>Communication</strong>:      - Regular updates were communicated to the customer to keep them informed of progress.      - Ensured that the language used was simple for clarity, especially for potential translation issues.</h4>
<h4 id="5-next-steps-await-further-updates-from-the-development-team-regarding-the-resolution-continue-to-monitor-the-ticket-for-any-changes-or-additional-information">5. <strong>Next Steps</strong>    - Await further updates from the development team regarding the resolution.    - Continue to monitor the ticket for any changes or additional information.</h4>
<h4 id="6-key-takeaways-importance-of-clear-communication-with-both-customers-and-development-teams-keeping-detailed-records-of-issues-and-resolutions-aids-in-tracking-progress-and-accountability">6. <strong>Key Takeaways</strong>    - Importance of clear communication with both customers and development teams.    - Keeping detailed records of issues and resolutions aids in tracking progress and accountability.</h4>
<h3 id="thought-provoking-questions-how-can-we-improve-the-communication-process-between-customer-service-and-development-teams-to-reduce-resolution-times-what-strategies-can-be-implemented-to-ensure-that-customers-feel-informed-and-valued-during-the-troubleshooting-process">Thought-Provoking Questions - How can we improve the communication process between customer service and development teams to reduce resolution times? - What strategies can be implemented to ensure that customers feel informed and valued during the troubleshooting process?</h3>
<h3 id="potential-connections-this-situation-may-relate-to-previous-discussions-on-customer-service-best-practices-and-the-importance-of-effective-communication-in-technical-support-roles">Potential Connections - This situation may relate to previous discussions on customer service best practices and the importance of effective communication in technical support roles.</h3>
<h3 id="real-world-applications-understanding-the-impact-of-accurate-data-representation-in-inventory-management-systems-particularly-in-large-organizations-like-walmart">Real-World Applications - Understanding the impact of accurate data representation in inventory management systems, particularly in large organizations like Walmart.</h3>
<h3 id="areas-for-further-research-explore-best-practices-in-managing-discrepancies-in-inventory-systems-across-different-industries-investigate-how-other-companies-handle-customer-feedback-and-development-requests">Areas for Further Research - Explore best practices in managing discrepancies in inventory systems across different industries. - Investigate how other companies handle customer feedback and development requests.</h3>
<h3 id="suggested-resources-articles-on-effective-communication-in-technical-support-case-studies-on-inventory-management-systems-in-retail">Suggested Resources - Articles on effective communication in technical support. - Case studies on inventory management systems in retail.</h3>
<h3 id="glossary-of-new-terms-qa-quality-assurance-a-way-of-preventing-errors-or-defects-in-manufactured-products-and-avoiding-problems-when-delivering-solutions-or-services-to-customers-sac-service-allowance-charge-a-specific-charge-type-used-in-invoicing-and-billing-processes">Glossary of New Terms - <strong>QA (Quality Assurance)</strong>: A way of preventing errors or defects in manufactured products and avoiding problems when delivering solutions or services to customers. - <strong>SAC (Service Allowance Charge)</strong>: A specific charge type used in invoicing and billing processes.</h3>
<h3 id="summary-the-ticket-regarding-walmart-s-packing-situation-highlights-the-complexities-of-managing-discrepancies-in-inventory-systems-and-the-importance-of-effective-communication-between-customer-service-and-development-teams-continuous-updates-and-clear-documentation-are-crucial-for-resolving-such-issues-efficiently">Summary The ticket regarding Walmart&#39;s packing situation highlights the complexities of managing discrepancies in inventory systems and the importance of effective communication between customer service and development teams. Continuous updates and clear documentation are crucial for resolving such issues efficiently.</h3>
<h4 id="generated-content-9">Generated Content</h4>
<h3 id="pool-warehouse-ticket-overview-ticket-3-michelle-sadro">Pool Warehouse Ticket Overview (Ticket #3 - Michelle Sadro)</h3>
<h4 id="key-details-customer-michelle-sadro-trading-partner-pool-warehouse-issue-adding-a-charge-on-the-invoice">Key Details: - <strong>Customer:</strong> Michelle Sadro - <strong>Trading Partner:</strong> Pool Warehouse - <strong>Issue:</strong> Adding a charge on the invoice</h4>
<h4 id="current-status-the-ticket-is-in-progress-and-internal-notes-have-been-added-regarding-the-customer-s-request-to-add-a-charge-to-their-invoice">Current Status: - The ticket is in progress, and internal notes have been added regarding the customer&#39;s request to add a charge to their invoice.</h4>
<h4 id="steps-taken-1-customer-inquiry-michelle-sadro-has-requested-to-add-a-charge-on-the-invoice-2-investigation-reviewed-the-purchase-order-po-and-related-documents-850-855-856-and-810-noted-that-if-any-single-charge-field-is-present-all-three-fields-charge-amount-discount-etc-are-required">Steps Taken: 1. <strong>Customer Inquiry:</strong>    - Michelle Sadro has requested to add a charge on the invoice.   2. <strong>Investigation:</strong>    - Reviewed the purchase order (PO) and related documents (850, 855, 856, and 810).    - Noted that if any single charge field is present, all three fields (charge amount, discount, etc.) are required.</h4>
<ol start="3">
<li><strong>Internal Notes:</strong>    - Documented the customer issue and copied relevant details (Invoice Message ID and reference) for clarity.    - Noted that the only option available for charge modification is for discounts.</li>
<li><strong>Next Steps:</strong>    - Further investigation is required to determine the specifics of the charge and whether it can be processed.    - Need to check the specifications for Pool Warehouse to see if additional codes or information are available for processing the charge.</li>
</ol>
<h4 id="challenges-lack-of-detailed-specifications-for-the-sac-service-allowance-charge-in-the-system-which-may-hinder-the-ability-to-process-the-charge-effectively">Challenges: - Lack of detailed specifications for the SAC (Service Allowance Charge) in the system, which may hinder the ability to process the charge effectively.</h4>
<h4 id="suggested-actions-reach-out-to-the-trading-partner-pool-warehouse-for-clarification-on-the-charge-requirements-check-if-there-are-any-additional-resources-or-documentation-available-that-could-assist-in-resolving-the-issue">Suggested Actions: - Reach out to the trading partner (Pool Warehouse) for clarification on the charge requirements. - Check if there are any additional resources or documentation available that could assist in resolving the issue.</h4>
<h3 id="summary-the-ticket-for-michelle-sadro-at-pool-warehouse-involves-a-request-to-add-a-charge-to-an-invoice-initial-investigations-have-been-conducted-but-further-clarification-and-information-are-needed-to-proceed">Summary: The ticket for Michelle Sadro at Pool Warehouse involves a request to add a charge to an invoice. Initial investigations have been conducted, but further clarification and information are needed to proceed.</h3>
<h4 id="generated-content-10">Generated Content</h4>
<h3 id="current-ticket-overview">Current Ticket Overview</h3>
<ol>
<li><strong>Ticket Identifier</strong>: Random Listing Issue    - <strong>Customer</strong>: Mr. Mike    - <strong>Issue</strong>: EDI entries not listed by date received; current day showing mixed dates.</li>
<li><strong>Initial Assessment</strong>:    - Entries were out of order.    - Sorting function was applied to arrange entries by date received.</li>
<li><strong>Current Status</strong>:    - Sorting issue resolved; entries now display correctly.    - Ongoing issue with the &#34;current day&#34; filter showing dates beyond today (14th to 16th).</li>
<li><strong>Next Steps</strong>:    - Reach out to the development team (Rafat) for further investigation on the
current day filter issue.    - Communicate with Mr. Mike to inform him of the resolution and ongoing investigation.</li>
</ol>
<h3 id="key-actions-taken-sorting-function-applied-to-correct-the-order-of-entries-internal-notes-documented-changes-and-issues-for-tracking-customer-communication-drafted-an-email-to-mr-mike-updating-him-on-the-status">Key Actions Taken - <strong>Sorting Function</strong>: Applied to correct the order of entries. - <strong>Internal Notes</strong>: Documented changes and issues for tracking. - <strong>Customer Communication</strong>: Drafted an email to Mr. Mike updating him on the status.</h3>
<h3 id="summary-the-current-ticket-involves-resolving-an-edi-listing-issue-for-mr-mike-with-sorting-applied-successfully-but-further-investigation-needed-for-the-current-day-filter-communication-with-the-customer-and-development-team-is-ongoing">Summary The current ticket involves resolving an EDI listing issue for Mr. Mike, with sorting applied successfully but further investigation needed for the current day filter. Communication with the customer and development team is ongoing.</h3>
<h4 id="generated-content-11">Generated Content</h4>
<h3 id="ticket-details-for-pool-warehouse-with-michelle-sardo">Ticket Details for Pool Warehouse with Michelle Sardo</h3>
<h4 id="1-customer-information-customer-name-global-pool-products-contact-person-michelle-sardo-reference-number-provided-by-the-customer-specific-number-not-mentioned">1. <strong>Customer Information</strong>    - <strong>Customer Name:</strong> Global Pool Products    - <strong>Contact Person:</strong> Michelle Sardo    - <strong>Reference Number:</strong> Provided by the customer (specific number not mentioned)</h4>
<h4 id="2-issue-description-main-issue-michelle-sardo-requested-to-add-a-charge-on-an-invoice-specifics-the-customer-is-trying-to-charge-for-shipping-but-there-are-issues-with-the-required-fields-in-the-invoice">2. <strong>Issue Description</strong>    - <strong>Main Issue:</strong> Michelle Sardo requested to add a charge on an invoice.    - <strong>Specifics:</strong> The customer is trying to charge for shipping, but there are issues with the required fields in the invoice.</h4>
<h4 id="3-steps-taken-initial-review">3. <strong>Steps Taken</strong>    - <strong>Initial Review:</strong></h4>
<ul>
<li>Logged into the system using the new login method.      - Searched for the customer using the provided PO number.      - Found relevant documents: 850 (Purchase Order), 855 (Acknowledgment), 856 (Shipping Notice), and 810 (Invoice).</li>
<li><strong>Document Analysis:</strong>      - Noted that there were two 856s: one sent and one in draft.      - Reviewed the 810 invoice document for charge requirements.      - Identified that if any one of the three fields related to charges is present, all three are required.</li>
<li><strong>Internal Notes:</strong>      - Added an internal note detailing the customer&#39;s issue regarding adding a charge on the invoice.      - Included relevant document IDs and reference numbers for clarity.</li>
<li><strong>SAC Segment Review:</strong>      - Reviewed the SAC (Service Allowance Charge) segment for the invoice.      - Noted that the required fields for charges were not fully populated, which could be causing the issue.</li>
<li><strong>Next Steps:</strong>      - Considered reaching out to the trading partner for clarification on the missing codes.      - Planned to respond to Michelle Sardo, informing her that the issue is being investigated and that the invoice requirements are being reviewed.</li>
</ul>
<h4 id="4-current-status-the-ticket-is-in-progress-and-communication-with-the-customer-is-ongoing-awaiting-further-information-or-clarification-from-the-trading-partner-regarding-the-charge-codes">4. <strong>Current Status</strong>    - The ticket is in progress, and communication with the customer is ongoing.    - Awaiting further information or clarification from the trading partner regarding the charge codes.</h4>
<h3 id="summary">Summary</h3>
<p>The ticket involves a request from Michelle Sardo of Global Pool Products to add a shipping charge on an invoice. The issue was analyzed, and relevant documents were reviewed. Internal notes were made, and the next steps include reaching out for further clarification on the required fields. Communication with the customer will continue to keep them updated on the progress.</p>
<h4 id="generated-content-12">Generated Content</h4>
<h3 id="update-details-for-pool-warehouse-michelle-sadro">Update Details for Pool Warehouse - Michelle Sadro</h3>
<p><strong>Customer Issue:</strong> - Michelle Sadro is attempting to <strong>add a charge on an invoice</strong> for shipping but is facing issues due to the invoice format provided by the trading partner.
<strong>Current Status:</strong> - The invoice format does not include the necessary codes for adding a shipping charge. - The only available option is for a discount, which complicates the situation since it appears that the system is set up to only allow charges under specific conditions.
<strong>Next Steps:</strong> 1. <strong>Internal Review:</strong>    - Review the specifications for the 810 invoice type to determine if the required fields can be adjusted.    - Investigate whether the fields can be marked as not required or if they can be made conditional based on the presence of other fields.
2. <strong>Communication with Trading Partner:</strong>    - Consider reaching out to the trading partner to inquire about any additional codes that can be used for shipping charges.
3. <strong>Consultation with Sandy:</strong>    - Discuss the situation with Sandy to determine the best course of action, including whether to modify the invoice requirements or seek clarification from the trading partner.
<strong>Action Items:</strong> - Respond to Michelle to inform her that the issue is being investigated and that updates will be provided as soon as possible.
<strong>Key Points:</strong> - The situation is currently unresolved due to limitations in the invoice format. - Collaboration with both internal teams and the trading partner is essential to find a solution.
<strong>Notes:</strong> - Keep track of any updates or responses from both Sandy and the trading partner to ensure timely communication with Michelle.</p>
<h4 id="generated-content-13">Generated Content</h4>
<h3 id="ticket-summaries">Ticket Summaries</h3>
<h4 id="1-ticket-from-mr-mike-issue-edi-entries-were-not-listed-by-date-received-they-appeared-out-of-order-initial-assessment-entries-were-jumbled-due-to-sorting-issues-the-current-day-filter-was-showing-incorrect-dates-14th-to-16th-resolution-steps-rearranged-entries-by-date-received-confirmed-that-filtering-by-all-current-or-unread-now-displays-entries-in-proper-order">1. <strong>Ticket from Mr. Mike</strong>    - <strong>Issue</strong>: EDI entries were not listed by date received; they appeared out of order.    - <strong>Initial Assessment</strong>:      - Entries were jumbled due to sorting issues.      - The &#34;current day&#34; filter was showing incorrect dates (14th to 16th).    - <strong>Resolution Steps</strong>:      - Rearranged entries by date received.      - Confirmed that filtering by &#34;all,&#34; &#34;current,&#34; or &#34;unread&#34; now displays entries in proper order.</h4>
<ul>
<li><strong>Next Steps</strong>:      - Further investigation needed for the &#34;current day&#34; issue; may require a development ticket.      - Communicated with Mr. Mike to inform him of the resolution and ongoing investigation.    - <strong>Key Actions</strong>:      - Assigned ticket to self and marked as &#34;in progress.&#34;      - Took screenshots for internal notes.</li>
</ul>
<h4 id="2-ticket-from-walmart-issue-customer-walmart-reported-discrepancies-in-packing-submissions-system-showed-incorrect-quantities-initial-assessment-customer-packed-four-items-but-the-system-only-reflected-one-resolution-steps-submitted-a-ticket-to-the-development-team-for-investigation-maintained-communication-with-the-development-team-through-jira-key-actions-documented-the-issue-clearly-for-the-development-team-to-avoid-miscommunication-followed-up-on-the-ticket-status-to-ensure-timely-resolution">2. <strong>Ticket from Walmart</strong>    - <strong>Issue</strong>: Customer (Walmart) reported discrepancies in packing submissions; system showed incorrect quantities.    - <strong>Initial Assessment</strong>:      - Customer packed four items, but the system only reflected one.    - <strong>Resolution Steps</strong>:      - Submitted a ticket to the development team for investigation.      - Maintained communication with the development team through JIRA.    - <strong>Key Actions</strong>:      - Documented the issue clearly for the development team to avoid miscommunication.      - Followed up on the ticket status to ensure timely resolution.</h4>
<h4 id="3-ticket-from-michelle-pool-warehouse-issue-michelle-is-trying-to-add-a-charge-for-shipping-on-an-invoice-but-the-system-does-not-allow-it-due-to-missing-codes-initial-assessment-the-sac-service-allowance-charge-segment-only-allows-for-discount-codes-not-charges-resolution-steps-investigated-the-specifications-for-the-invoice-type-810-to-determine-if-additional-codes-are-available-considered-reaching-out-to-the-trading-partner-for-clarification-on-available-codes">3. <strong>Ticket from Michelle (Pool Warehouse)</strong>    - <strong>Issue</strong>: Michelle is trying to add a charge for shipping on an invoice, but the system does not allow it due to missing codes.    - <strong>Initial Assessment</strong>:      - The SAC (Service Allowance Charge) segment only allows for discount codes, not charges.    - <strong>Resolution Steps</strong>:      - Investigated the specifications for the invoice type (810) to determine if additional codes are available.      - Considered reaching out to the trading partner for clarification on available codes.</h4>
<ul>
<li><strong>Next Steps</strong>:      - Awaiting guidance from Sandy on how to proceed with the ticket.      - May need to reach out to the trading partner to request additional codes for shipping charges.    - <strong>Key Actions</strong>:      - Documented the issue and potential solutions in internal notes.      - Assigned the ticket to self and marked as &#34;in progress.&#34;</li>
</ul>
<h3 id="summary-of-key-actions-across-tickets-communication-regular-updates-to-customers-and-internal-teams-to-ensure-transparency-and-progress-tracking-documentation-detailed-notes-and-screenshots-taken-for-clarity-and-future-reference-collaboration-engaged-with-development-teams-and-trading-partners-for-issue-resolution">Summary of Key Actions Across Tickets - <strong>Communication</strong>: Regular updates to customers and internal teams to ensure transparency and progress tracking. - <strong>Documentation</strong>: Detailed notes and screenshots taken for clarity and future reference. - <strong>Collaboration</strong>: Engaged with development teams and trading partners for issue resolution.</h3>
<h3 id="thought-provoking-questions-1-how-can-we-improve-the-ticket-resolution-process-to-minimize-the-need-for-development-tickets-2-what-strategies-can-be-implemented-to-enhance-communication-with-customers-who-have-recurring-issues">Thought-Provoking Questions 1. How can we improve the ticket resolution process to minimize the need for development tickets? 2. What strategies can be implemented to enhance communication with customers who have recurring issues?</h3>
<h3 id="real-world-applications-understanding-edi-processes-is-crucial-for-businesses-that-rely-on-electronic-transactions-effective-ticket-management-can-significantly-improve-customer-satisfaction-and-operational-efficiency">Real-World Applications - Understanding EDI processes is crucial for businesses that rely on electronic transactions. - Effective ticket management can significantly improve customer satisfaction and operational efficiency.</h3>
<h3 id="areas-for-further-research-investigate-best-practices-for-edi-management-and-troubleshooting-explore-the-implications-of-outsourcing-development-teams-on-communication-and-efficiency">Areas for Further Research - Investigate best practices for EDI management and troubleshooting. - Explore the implications of outsourcing development teams on communication and efficiency.</h3>
<h3 id="potential-exam-questions-1-describe-the-steps-taken-to-resolve-an-edi-entry-sorting-issue-2-discuss-the-challenges-faced-when-dealing-with-invoice-discrepancies-in-a-trading-partner-relationship">Potential Exam Questions 1. Describe the steps taken to resolve an EDI entry sorting issue. 2. Discuss the challenges faced when dealing with invoice discrepancies in a trading partner relationship.</h3>
<h3 id="glossary-of-new-terms-edi-electronic-data-interchange-the-electronic-exchange-of-business-documents-in-a-standardized-format-sac-service-allowance-charge-a-segment-in-an-invoice-that-specifies-allowances-or-charges-jira-a-tool-used-for-issue-tracking-and-project-management-commonly-used-in-software-development">Glossary of New Terms - <strong>EDI (Electronic Data Interchange)</strong>: The electronic exchange of business documents in a standardized format. - <strong>SAC (Service Allowance Charge)</strong>: A segment in an invoice that specifies allowances or charges. - <strong>JIRA</strong>: A tool used for issue tracking and project management, commonly used in software development.</h3>
<h3 id="main-takeaways-effective-communication-and-documentation-are-essential-in-ticket-management-understanding-the-technical-specifications-of-trading-partners-can-help-resolve-issues-more-efficiently">Main Takeaways - Effective communication and documentation are essential in ticket management. - Understanding the technical specifications of trading partners can help resolve issues more efficiently.</h3>
<h4 id="generated-content-14">Generated Content</h4>
<p><strong>Ticket 4 Overview:</strong></p>
<ul>
<li><strong>Customer:</strong> Dave from Dolores Canning     *   <strong>Issue:</strong> Dave received an email notification for an 875 document but cannot find it in his account.     *   <strong>Details:</strong>         *   The email indicates the arrival of data, including document type and control numbers.</li>
<li>The screenshot provided by Dave is unclear, but he confirms that the document is not visible in his account.             *   It is noted that Dave is the admin user, which means he should have access to all documents.
<strong>Actions Taken:</strong></li>
<li>The support representative plans to:         *   Mark the ticket as unread to prompt Dave to refresh his account.             *   Suggest that Dave check for any sorting issues that might be hiding the document.
<strong>Next Steps:</strong></li>
<li>Await Dave&#39;s response after he refreshes his account to see if the document appears.</li>
</ul>
<h4 id="notes">Notes</h4>
<p>ticket 4 Dave from Dolores Canning</p>
<h4 id="transcript">Transcript</h4>
<p>00:00 Michael H.: Hello. Yes, I hear you perfectly. 00:01 Lauren C.: Hi, can you hear me now? Okay. Yeah. So I don&#39;t know. I don&#39;t
know what&#39;s going on with Slack, but… We&#39;re just going to bypass that. 00:12 Michael H.: Yes. 00:14 Lauren C.: Okay, so… I went through the queue earlier and just kind of scrolled through I&#39;ll probably be the one to return those voicemails, which is fine. If they don&#39;t already call back today. So I&#39;ll get those later. 00:25 Michael H.: Yes. 00:29 Lauren C.: Let&#39;s go based off of created. So normally you work whatever&#39;s first in your queue but like This one, the customer needs to reach out to me. This one, our development team is working on. This one, I actually don&#39;t know if I have a response on it yet. So all of my older tickets They&#39;re either waiting on the customer, the partner, or our development team so Sometimes. 00:55 Michael H.: Yes. 00:59 Lauren C.: Um… Yeah, literally everything down there.
01:08 Lauren C.: I&#39;m pretty sure this is waiting on her too. Yep, it is. Okay. Yes. Okay. So fun stuff. We can look at these guys. Actually. This top one that says random listing That is going to be our easiest ticket. So let&#39;s grab it. 01:30 Lauren C.: Forgot his actual name. Signature, Mike. So Mr. Mike here sent in an email and he said that when he received EDIs in the past, they were listed by date received.
01:41 Lauren C.: However, even when he shows current day, it mixes up and whatever. So in his screenshot that he provided, which customers are usually really good at if they have like an error or something&#39;s not right.
01:55 Lauren C.: They&#39;ll go ahead and provide it in here, which I do see where it says current day. I see the date was the 9th, so it shouldn&#39;t have been showing all of that. So let&#39;s go see. So 3038. Which I&#39;m just going to sign in as him. Hold on one second.
02:19 Lauren C.: So when you go to log in, you would still log in with your email if they set you up that way. I&#39;m just using this way because it&#39;s in a separate tab.
02:30 Michael H.: Oh, okay. 02:30 Lauren C.: So in Chrome, I have it set up like my training class so I can switch between the two numbers rather than my email because the passwords are different. 02:39 Michael H.: Yes.
02:41 Lauren C.: Alrighty. Interesting. Yeah. So when he looks for current day, one, they&#39;re not in order. Easy fix. You just rearrange by when it was received. So click that twice and it should start showing what it should be.
02:59 Lauren C.: So that is there. 02:59 Michael H.: So just double click on the receive. To just, okay.
03:06 Lauren C.: Yes. Usually the older customers will have that issue. Some of them log in on like a cell phone or a tablet. 03:14 Lauren C.: And if it runs slow, they&#39;ll start tapping on the screen and magically it&#39;s always this column that gets clicked. So his stuff was out of order because this column was clicked on. 03:26 Michael H.: Yes. 03:26 Lauren C.: So if you click it once, it&#39;ll go by oldest to newest. And if you click it a second time, it&#39;ll go by newest to oldest. But his day. Okay, I lied. This may end up being a death ticket. It may not be as easy as I thought.
03:41 Lauren C.: Darn it. I was hoping… Wasn&#39;t that? 03:48 Michael H.: So what do you think? Think that it could possibly possibly be 03:48 Lauren C.: Okay, so… Something on the background is seeing that button current day as maybe the last three to four days. So that&#39;s why we have the 16th through the 14th.
04:06 Lauren C.: Which is not right. Okay. I thought this was going to be an easy one. I thought we could just Refresh it and it would work. 04:17 Michael H.: Thank you. 04:18 Lauren C.: Darn. Okay, this one may need a development ticket. But as far as the actual issue where his stuff was jumbled up. 04:26 Lauren C.: That&#39;s already been resolved. It doesn&#39;t matter if he filters by all. Current, unread, any of that, those will all be in that proper order now. So scrolling through, you&#39;ll see it is actually Descending. 04:35 Michael H.: Yes. 04:39 Lauren C.: So that part&#39;s fixed. Not the other one. It&#39;s Friday. I can&#39;t expect anything to be easy. Okay, so I&#39;m going to take a screenshot. Oh my goodness, I forgot that when I am sharing my screen, it won&#39;t give me the pop-up for it. So let&#39;s try this again.
05:02 Lauren C.: I have to actually open the snip tool And then it&#39;ll populate over here. 05:07 Michael H.: Oh, I guess. 05:07 Lauren C.: So I click the little square Receive. And then in my note, I&#39;ll just put that I rearranged the column.
05:17 Michael H.: Okay. 05:21 Lauren C.: This one. Okay, so when you start a ticket, always make sure, one, it&#39;s assigned to you and two, the in progress is selected Like I said, Sandy is Very strict on that one just because your SLA count, the time from which it&#39;s received to which it&#39;s changed and replied on.
05:44 Lauren C.: That&#39;s what controls it. So make sure you go ahead and hit that guy there. 05:46 Michael H.: Yes.
05:50 Lauren C.: If you have the organization, perfect. You can go ahead and put it in here.
05:55 Lauren C.: Pretty sure it&#39;s pharmaceuticals. But I don&#39;t like guessing. Yes. No problem. So organization, and I apologize, I am raspy today i have crazy allergies. So I was coughing and sneezing all morning. 06:14 Michael H.: Oh, yes. 06:14 Lauren C.: So apologies. His issue, oh goodness. This is the air that I was like, eh, it&#39;s kind of vague. Um… I can skip that for now because it&#39;ll probably end up being a dev ticket, so I&#39;m not going to put a label just yet. 06:33 Lauren C.: The label isn&#39;t as crucial. Just make sure you do that before you close it. I can show you like Miss Sandy. Yeah, if I come in or she assigns something to me, she&#39;ll immediately put it from awaiting support to in progress or she&#39;ll come in and actually you&#39;ll see the updates.
06:53 Lauren C.: She&#39;ll tag. The actual organization. So if you send a ticket and you&#39;re like, hey, I need help the first thing she&#39;ll do is tag the customer on it. 07:03 Lauren C.: She&#39;s funny with that. So just a side note, she does look at that. You&#39;re not going to get in trouble. It&#39;s just something I&#39;ve noticed. So I&#39;ve tried being pretty much subconsciously aware of it. Okay, so rearranged. Received order. And then view by Current day is still populating. I think it&#39;s on the 14th or was it the 13th? That is… We&#39;re going to ignore that. We&#39;re not grammar Nazis over here. I&#39;m going to leave that there. Okay. Darn. So I can still reach out to him since we did fix the order. We can shoot back an email letting them know that he can refresh his screen. If he&#39;s already clicking around, it will update for him. 08:15 Lauren C.: But if he&#39;s sitting here staring at it and hasn&#39;t moved anything, then it may still be out of order on his side. So half of his issue, we can say, hey, this part is um So let&#39;s do save. 08:30 Lauren C.: So reply to customer. This will always send to the customer. And if there&#39;s anyone And the request participants, all of those people receive it. So this is outbound. So if you&#39;re ever taking a note. Make sure you are in the internal note. I wish it turned yellow while we were typing it, but it doesn&#39;t. It&#39;s only after. So just make sure if you&#39;re doing like screenshots of the server or anything. Trying to make sure you double check that before hitting save because once it&#39;s sent it&#39;s sent I&#39;ve made that mistake multiple times. So, oh my goodness when we moved
from the other system to this one. It&#39;s a completely different world. So I&#39;m very guilty of that.
09:13 Lauren C.: So you will not be the first, last, or only. If you do, it is okay. 09:13 Michael H.: Yes. Okay. 09:20 Lauren C.: Um… Let&#39;s see. I reviewed for account and Please refresh. Your page and let me know.
10:31 Lauren C.: I always sit here and reword it. So yes, I usually say hi. Sometimes if it&#39;s someone that&#39;s like a recurring customer, I&#39;ll even joke and be like, happy Friday. So. Yeah. So I&#39;m just like, hope you&#39;re doing well today. 10:42 Michael H.: Oh. 10:43 Lauren C.: Reviewed your account. I was able to get the order received back to how you&#39;re expecting. Please refresh your page and let me know if this is populating on your end. As for the current day showing beyond just today, I&#39;m going to reach out to our developers to see what we can, I&#39;m just going to stop at do. It just feels too wordy. Please let me know if you experience any further hiccups. So just communicating with them back and forth. So one, they see we&#39;re working on it and two. That just kind of gives us that updated SLA and shows our progress. 11:26 Michael H.: Yes. 11:26 Lauren C.: In this system, I don&#39;t believe we&#39;re counted on how many replies we have. And the old one, for some reason, we were only allowed to have like a handful. So that was crazy. But in this one, it doesn&#39;t matter. So just making sure you communicate with them is a big thing. 11:41 Michael H.: Yes. 11:45 Lauren C.: I will say we have a few of them that are very needy. If you don&#39;t respond. They will call you. Oh, Mike is not like that. He&#39;s actually really cool. Oh,
he said, good morning. I didn&#39;t say good morning back. Oh, well. I said, hope you&#39;re doing well. Hopefully he takes that. Okay. Let&#39;s look at… 12:08 Michael H.: I was just with that ticket, I&#39;m just going over my notes so What happened was the EDI was not listed, which Mr. Mike said. 12:20 Michael H.: And then… our first initial assessment was Of course, the entries were out of order. So we just did the sorting function to be used to just make everything organized. And then you just pretty much click from oldest to newest and second click was newest to oldest to just make sure everything display. But then we ran into a problem on the 14th to the 16th. And you said that Further. Investigations would be by the development team and then we just emailed him back and made sure we labeled everything and the customer details and everything and organizations and things change the ticket in progress. 13:10 Michael H.: And we just confirm everything with him. Okay. 13:14 Lauren C.: Yep. So if you don&#39;t have an answer on something, it is completely okay to not even address it on the email back. Some customers don&#39;t really care as long as they see you&#39;re working their issue. They&#39;re fine. 13:22 Michael H.: Yes. 13:27 Lauren C.: But for me, I usually go ahead and let them know like, oh, as for the other issue, I&#39;m still looking into this or I&#39;m going to have Dev look into it, which I will be actually reaching out to Rafat for it So I&#39;ll just show it to her. 13:34 Michael H.: Oh, yes. 13:41 Lauren C.: Sometimes it&#39;s super simple and she&#39;s like, oh, I can fix that. Or it&#39;s Hey, I need you to create a development ticket and the team will look at it so If she looks at it, great. It&#39;s usually done same day, but development is outsourced. 13:57 Lauren C.: I say outsource. I think our development team, some people are in India and I don&#39;t know where the other team is. But since they work on a I know Clio&#39;s is, but I don&#39;t know where ours. I think ours might just be in India. 14:07 Michael H.: Bingo. 14:13 Lauren C.: If I&#39;m not mistaken. I&#39;m not sure. But I do know that they work overnight. So when we interact with them, it&#39;s very disconnected. So don&#39;t get frustrated. It is something that we all we all fight with every day. Oh my goodness, hold on. Sorry, I cannot stop coughing today. It&#39;s so gross. But when you submit a ticket. Usually the first 24 hours, it may go through where, you know, Rafat takes
the ticket. I can show you actually. So our development team, they&#39;re also in JIRA, but it&#39;s like a different instance and version of it. 14:57 Lauren C.: So that&#39;s why I use different browsers. You can&#39;t have them open in the same one because for some reason it won&#39;t differentiate between the two. I don&#39;t know why. So this is the dev side. I have it in dark mode just because I prefer it. 15:06 Michael H.: Yes. 15:16 Lauren C.: But let&#39;s see. Ooh, is this in production? Okay, so here is a ticket that I had escalated. The customers, okay, I was like, did I forget screenshots? 15:30 Lauren C.: The customer would pack his stuff. And then when he finished packing his system was wrong. So like for me wrong I packed four packs. However, it was only showing as one. And so he was getting in trouble because when it was submitting, it was saying it was one And then down here, the total said 12. 15:42 Michael H.: Yes. 15:52 Lauren C.: So I submit this to development. They and I can scroll down. 15:57 Lauren C.: It looks the exact same. So all the notes and stuff, that&#39;s them. So my first update was from one of the guys on our development team letting us know that, hey. Here&#39;s the issue. Here&#39;s the resolution. It required this which I don&#39;t usually touch unless Rafat&#39;s like, hey, add this on the production side. 16:19 Lauren C.: Otherwise, we don&#39;t have to do anything. They&#39;ll let us know if we have to do anything. But yeah, he said, here, try this. And then Rafat was like, I don&#39;t know, that didn&#39;t look right. So she called me and she&#39;s like, hey, can you add in the specifications from Walmart? I was like, sure. So I sent it back to her and she responded and she&#39;s like, okay, this part passed, but this part failed. Yeah, so it&#39;s a back and forth with them. Um… So he tried again and he said, okay, we&#39;ll try QA here. So she did. It failed again. So we get to see their back and forth. 16:59 Lauren C.: He tried again. And that one shows correctly. So as of yesterday around lunchtime, so she&#39;ll probably reach out to me today and let me know it&#39;s fixed. But yes, this is our development side. If it has to go through that process, she&#39;ll mark it as assigned to whoever it needs to go to and update the statuses. So when we submit to development, we just submit what we know. So what I knew was this customer, this trading partner, this is the issue. And then give them
examples. So I try to keep it pretty plain and simple so that way there&#39;s no like issue with like translation if it&#39;s an English second language situation, which, you know, most countries are. 17:31 Michael H.: Yes. 17:45 Lauren C.: So I try to keep it as simple as possible. So if they are translating, there&#39;s no miscommunication or anything. So, but yeah, that&#39;s our only responsibility there unless Rafat is like Hi. And then she&#39;ll put at in your name and she&#39;ll ask a question or she&#39;ll just reach out directly. 17:52 Michael H.: Yes. 18:03 Lauren C.: But for the development side, we just submit what we know, they get back to us. 18:08 Michael H.: Yes. 18:08 Lauren C.: So if it turns into that, that&#39;s what I&#39;ll do here. I went ahead and tagged you. Do you have access to Jira already or no? 18:16 Michael H.: I&#39;m actually i read it. Yes, I&#39;m on JIRA right now. Yes. 18:18 Lauren C.: Perfect. So what I&#39;ll do, anything we touch today, I&#39;ll go ahead and tag you in it. And that way you can refer back to them. 18:27 Michael H.: Yes. No, no, no. It helps a lot. 18:27 Lauren C.: I don&#39;t know if that helps any, but… Alrighty, and then let me also for Sandy, in case she asks, I&#39;ll keep a list. So not that she&#39;s really… concerned about my productivity this week but 18:46 Michael H.: Yes. 18:49 Lauren C.: I don&#39;t know. We&#39;ll just keep it there just in case. Okay. So that one, I&#39;ll reach out to Rafat in a minute about. We&#39;ll leave that one there. This one, I have no idea who she is. So I need to, I&#39;m trying to send a letter. Okay, so global pool products.
19:14 Lauren C.: Let&#39;s grab. I don&#39;t know if it&#39;ll actually open. Or if it froze. Oh, it did. Okay. So we&#39;ll log in using our email. This is what they call the new way to log in. I try to give it a chance. When you do it that way, you can search through. And find the customer. So I didn&#39;t have to go all the way to admin. I can just search for the customer. So global pool products. 19:49 Michael H.: So this customer is global. Products. 19:49 Lauren C.: I find this? Yes. So I looked at, and they don&#39;t usually give us anything. So, so far it doesn&#39;t show that anyone&#39;s tagged him to an organization, so it didn&#39;t populate. 20:04 Michael H.: Yes. 20:04 Lauren C.: So when I look at his actual signature, it&#39;s global pool products. Yeah, this does not always work. Customers sometimes will have like completely random and different name that they do business with so It&#39;s a 50 50 shot. So for this one, thankfully he used that and that makes it a little easier He also did not give us Oh, he did. He gave us the PO. Alrighty, so let&#39;s go search the website see if it pulls up like that. Yes, it does. Alrighty, so when I search that reference number he gave me, it pulls up the 850. The 855 acknowledgement The 856.
20:57 Lauren C.: Interesting. There is also an 810 somewhere. Okay. So hitting the expand button down here on the PO, I can see everything generated. It looks like he had two 856s. One was sent, one was not.
21:11 Lauren C.: That&#39;s totally okay. He just has a draft somewhere. It&#39;s like an email. Unless you hit send, it&#39;s just there. It doesn&#39;t do anything. 21:11 Michael H.: Oh. 21:19 Lauren C.: So the last one, this 810. Go ahead and click there. Since he gave
us the PO and he said he&#39;s charging, the only thing you can charge or change amounts on is the actual invoice. So these three fields down here. The one thing about them, if any singular one is present, then all three are required. The only option he has is for a discount. Oh no, I don&#39;t think that&#39;s correct. Okay, so back to the ticket. We&#39;re going to go ahead and add an internal note. 21:56 Lauren C.: I&#39;ll put… Customer. Issue. Add a charge on invoice. And then I always grab details for that page. So I copy right here where it says invoice message ID and reference. I just copy all three because they&#39;re together. 22:21 Lauren C.: And then Control Shift B. And that way I know what document I was looking at exactly. 22:28 Michael H.: Yes. 22:29 Lauren C.: Alrighty, and then at the bottom, so I&#39;ll do the same thing. I&#39;ll do his SAC segment. Customer selected. Charge. But… Yeah. Only lists. Let&#39;s count. So with that, we will actually go play with Adam. How do I hide this?
23:06 Lauren C.: I don&#39;t like that there. Thank you. Okay. So then I&#39;ll also screenshot to show myself. With that. Okay, so that&#39;s our internal note. Oh, I guess I could have also helped us with who it&#39;s for. So Poll Warehouse is the trading partner that it&#39;ll be listed under within admin.
23:35 Lauren C.: So it doesn&#39;t matter what they call the partner. They could even name their buying person We just have to look up. Whatever it shows on the website because that&#39;s how it&#39;s going to be mapped on the back end for us. So let&#39;s grab We&#39;ll pretend like we just signed into admin. Okay, so signing into admin, we&#39;re going to go to legacy. It always loads to customers. If you click around, once it loads, it&#39;ll push you back anyway. So I just try to wait.
24:10 Lauren C.: So trading partners and trading partners Hasting Pool Warehouse. Oh, fun. Another SPS one. Alrighty, so in pool warehouse at the very bottom, they should have all their specifications. So I&#39;m looking for the one that has their 810, which is their invoice, which They nicely labeled. Normally they&#39;ll just put the document type. 810 is not the only invoice type there are tons of them. So it&#39;ll vary by what kind of situation it is. So like if it&#39;s I think regular retail or any of that is an 810. However, if it&#39;s like a grocery store, I believe it&#39;s 880. So they kind of vary just depending um i think any transportation system, they&#39;re also different because it&#39;s a different I don&#39;t know. It&#39;s completely different, the format and everything.
25:04 Lauren C.: So I always recommend go to the trading partners page. You can hit download. And this one is nicely a PDF. I don&#39;t want to do all that. Oh my goodness. Clouds. Turn that off. Okay. So let&#39;s grab This is the field we are looking for. So the SAC is service allowance charge.
25:34 Lauren C.: So we&#39;re going to scroll, which is almost to the very bottom. So we can bypass most of this. Here we are. Oh, no.
25:51 Lauren C.: That doesn&#39;t make any sense. Well, that&#39;s fun. Interesting. So normally there would be more listed here. If there were, we could go in to the actual DOM and enable it. But because there&#39;s not. I don&#39;t know what we should do with that one. Interesting. Okay. Told you we&#39;re just going to end up with rabbit hole tickets. 26:41 Michael H.: Yes. 26:41 Lauren C.: Fun. Oh my goodness. Okay. So on a good day with our luck, we would have had more codes there that we could grab from but today and this week.
26:56 Lauren C.: They&#39;re not on our side. Okay. 26:58 Michael H.: Yes. 27:00 Lauren C.: Hold. 810 specifications. Four. Pool warehouse. Reviewed. Sac. However… I wonder if… Okay, so… I wonder if I can unpair those two fields so It&#39;s like an if-then statement. Oh my goodness. Why did I grab that one and not this one? So at the bottom, it says if SAC01 is A or C, then at least one is required. Which is crazy that they list things that aren&#39;t even here. So SAC, oh my gosh SAC05 is here. But nothing beyond that. So I don&#39;t know what those two fields would even be. Anyways, it&#39;s the total amount. So this is a total. If this is present. With two other fields that don&#39;t exist, well, we&#39;re going to ignore that. Okay, this tells you what it would be. Alrighty, so from what I&#39;m seeing. 28:18 Lauren C.: Five and two are not required together. Interesting. Okay. So maybe we just need to unlink these guys. Let&#39;s see. Okay, so we&#39;ll put this. There. It gets tricky because on the requirement page you&#39;ll see that required says M. So it says mandatory and must use My goodness. But I don&#39;t believe… It&#39;s frustrating. I may even reach out to the trading partner for this one to ask them if there is another code that we can put there. 29:20 Lauren C.: Interesting. Let&#39;s grab… Who else do we have? I don&#39;t know why this is my go-to, but Home Depot. And they don&#39;t have the nicest specs, but that&#39;s okay. So eight sentence bucks. Pdf. Your meeting will end in time. Go away.
Okay. Oh my goodness. Sac segment. Okay, so here. Is what it normally would look like. This is that same field. This is that allowance or charge code. It gives you the code list and it gives you everything that you can enable. This is normally what that looks like. So I&#39;m not entirely sure why they only have a discount code. 30:24 Michael H.: Yes. 30:30 Lauren C.: That&#39;s fun. Did he say what he was trying to charge? Four. Oh, for shipping. Okay. Hmm. Yeah, because these guys have a handling, they have a freight charge Oh, goodness. Lovely. We shall see. 30:57 Michael H.: Yes. 31:00 Lauren C.: Okay, so we&#39;ll leave that there. I will respond back to Michelle, which Before I forget, organization, global. Cool. Perfect. And then since I already know it&#39;s an invoice, I&#39;ll put it in the label invoice questions. And that way I knock those two fields out. It&#39;s already in progress. All I need to do is respond to let her know that I&#39;m looking into it. The invoice and question. I am reviewing. Just to see what we can do. So this way she gets an update. And knows that we&#39;re looking at it. 32:06 Michael H.: Yes. 32:14 Lauren C.: And then full disclosure, anything I tag you on, I apologize. You will get every notification for.
32:20 Lauren C.: So when she responds or we add a ticket or a note Okay, so that one. 32:20 Michael H.: Honestly. 32:28 Lauren C.: Oh, Joy. That one is going to be fun. Hmm. 32:35 Michael H.: So pretty much the whole warehouse situation was like she was trying to charge for shipping but there was like a problem with her invoice. 32:44 Lauren C.: Yes. So she&#39;s trying to add a charge on the invoice so she can get paid for shipping the the item. However, the way that her partner has the um format setup, the instructions that they gave us There isn&#39;t a code for that. 32:52 Michael H.: Yes. 33:03 Lauren C.: It&#39;s almost like they only want her to use it If she&#39;s doing an allowance with a discount. If that&#39;s the case, why even include charge? That&#39;s my question. 33:15 Michael H.: Yes. 33:15 Lauren C.: So yeah, that&#39;s kind of where we&#39;re stuck. I can ask Sandy what she wants me to do with that one because there&#39;s a few things we can do. One, if the This field isn&#39;t actually required with The O5, then we can mark it not required Or not conditional. I don&#39;t know. We&#39;ll have to look into that. Or the other thing is she may actually have me go ahead and reach out to the trading partner to ask. If there are any additional codes we can use. Because technically.
33:48 Michael H.: Okay. 33:50 Lauren C.: This is the code list number. Oh, no. This is 1300. There&#39;s over a thousand codes that are available there. So it&#39;s kind of crazy that they&#39;re only allowing one But we can&#39;t make any changes until They relay information from their partner or we reach out to the partner directly. So as far as the actual DOM, those do not get changed unless the partner says, hey, yes, go ahead and update this, or hey, we&#39;re changing this. Otherwise, it doesn&#39;t matter what the customer wants. We can&#39;t do that. So that&#39;s where it gets fun. So that one we are waiting to see. Let me reach out to Sandy actually. And ask her what she&#39;d like me to do with that one. 34:35 Lauren C.: I hate that I keep putting a pen in every ticket, but my goodness, I have nothing that I can do start to finish. 34:41 Michael H.: Yes. Is it because like your hands are tied, you&#39;re not allowed to do these things, right? Or… 34:50 Lauren C.: So for this one, yeah, I don&#39;t know what they would want me to do for that. 34:54 Michael H.: Yeah. 34:56 Lauren C.: Let&#39;s see, the customer is trying to charge for shipping. However… the SAC only allows a discount code. And then I asked her, would I need to reach out to the TP. Okay, and then I will also show her. This.
35:51 Lauren C.: Interesting. Okay. So we will see. I&#39;ll come back to that, I guess. Most tickets are not a straight run through, unfortunately. They usually are like that. We can get to a certain point and then we have to ask whether it&#39;s internal, external Okay, so this one might… Hopefully, hopefully be an easy one. Okay, so this one&#39;s already in progress. So let&#39;s go ahead and tag him. Um… So that&#39;s, I&#39;ve already worked with him before, so I already know who he is, but it&#39;s in his um Email. But this is, oh, that email that Rafat was talking about yesterday. 36:21 Michael H.: The latest painting. 36:35 Lauren C.: This comes from the alerts. It says arrival of data and it&#39;ll tell you who it&#39;s from and what the document type is. And then below that, and it&#39;s usually blue. I don&#39;t know why it forwards this way. 36:43 Michael H.: Okay. 36:48 Lauren C.: But below that, it&#39;ll give you the date, when it came through, the document, the control numbers, and the reference number. So he&#39;s saying that, hi, I received this email for an 875. However, I don&#39;t see it. He&#39;s like, can you please check my account? Oh, his screenshot is not great. So we&#39;ll just pretend it was. 37:06 Michael H.: Yes. 37:10 Lauren C.: So sign out. Yes. 37:11 Michael H.: And this is ticket four from dave can um from dave from Dolores
Canning. 37:19 Lauren C.: Let&#39;s hear. 37:21 Michael H.: Yes. 37:23 Lauren C.: Go ahead and tag you. There we go. And that way it&#39;ll pop up for you. So you can actually see it too. So let&#39;s go over to WebEDI. Oh, nope. Dolores. 37:31 Michael H.: Yes. 37:35 Lauren C.: And continue. All right, so… He may not be the admin. Because I see it right here. In the admin page. Okay, so if you look in the top right, this is the admin&#39;s login. So the 2395, that&#39;s their actual username. So when we go to settings Oh, no, Pete is logged in. That is so strange. 38:11 Michael H.: So he is the Edmonton. 38:14 Lauren C.: Yep, two, three, nine, five. If there was another user here, then he would be able to change the permissions or whatnot. But Dave is the admin You can&#39;t change permissions because they will always have access to everything. That&#39;s one. Okay. So… 38:30 Michael H.: Okay. Yeah, that screenshot&#39;s really bad. 38:38 Lauren C.: It is terrible, but he&#39;s right. It&#39;s not there. Interesting. Let&#39;s try not to be funny with it, but I&#39;m going to mark it as unread. 38:42 Michael H.: Yes. 38:49 Lauren C.: And ask him to refresh. Or like the other guy, he could have accidentally clicked something else.
38:59 Lauren C.: So it&#39;s sorting by document type or anything like that. So let&#39;s play with the received And we&#39;re going to take this select action, mark as unread. Let me just make sure that was the reference number given. So ending in 3481. Yes. So we&#39;ll see. Since he&#39;s the only user. That was kind of easy. I make it unread so that way they&#39;ll see the one pop-up 39:35 Michael H.: Yes. 39:37 Lauren C.: Hi, Dave. Please. Oh, no. Refresh. Your screen. 39:43 Michael H.: Oh, you&#39;re going to just i think we&#39;re going to disconnect in five seconds
View original transcript at Tactiq.</p>
