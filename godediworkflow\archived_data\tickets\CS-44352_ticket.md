# CS-44352: Re: No.746665 and Message ID 44375096= Data Trans ID no 5354 - CARTON LABELS GENERATION ISSUE THRU EDI.

## Ticket Information
- **Key**: CS-44352
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: Sachin Thakur
- **Reporter**: BASKARAN DHARMALINGAM
- **Created**: 2025-05-24T01:39:09.990-0600
- **Updated**: 2025-05-27T04:56:26.940-0600
- **Customer**: Fashion Limited

## Description
Hello <PERSON>skar<PERSON> sir,


Thank you for your email. 

We have sent the carton labels to your registered email  {color:#211a14}{color}[{color:#211a14} *<EMAIL>*{color}|mailto:<EMAIL>]{color:#211a14}{color} Message ID 44375096.

Please check your email and let us know if you need any further assistance. 

!image.png|thumbnail!



On Sat, May 24, 2025 at 6:31 AM <PERSON><PERSON><PERSON> <[<EMAIL>|mailto:<EMAIL>]> wrote:

{quote}
 
  

{color:#074f6a}Dear Data Trans Team{color}  

{color:#074f6a}{color}  

{color:#074f6a}Pls revert for my below email service request and this need to resolve today .{color}  

{color:#074f6a}{color}   

{color:#074f6a}Baskaran Dharmalingam{color} {color:#074f6a}{color} 

{color:#074f6a}Divisional Merchandising Manager{color}{color:#074f6a}{color} {color:#074f6a}{color} 

{color:#074f6a}Zamira Fashion Limited{color} 

{color:#074f6a}{color}{color:#074f6a}{color}  

!image001.png|thumbnail!{color:#074f6a}{color}{color:#074f6a}{color}  

{color:#074f6a}{color}  
  

 *From:* Baskaran Dharmalingam 
  *Sent:* Friday, May 23, 2025 7:24 PM
  *To:* [<EMAIL>|mailto:<EMAIL>]
  *Cc:* Riaz Uddin Ahmmed <[<EMAIL>|mailto:<EMAIL>]>; Md. Atiqur Rahman <[<EMAIL>|mailto:<EMAIL>]>; Sperdha Verma <[<EMAIL>|mailto:<EMAIL>]>; [<EMAIL>|mailto:<EMAIL>]; Riffat Rahman <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* No.746665 and Message ID 44375096= Data Trans ID no 5354 - CARTON LABELS GENERATION ISSUE THRU EDI.    

  

Dear Team  

We have generated Carton labels for Pac Sun PO reference No.746665 and Message ID 44375096.  

Data trans ID no 5354.  

Total no of cartons labels are 1637 nos.  

Pls arrange to send carton labels thru email in PDF format. 
 Also in future, we want to have permanent solution to above issue since we are exporting garments  

And if we struck on weekend holidays then how to contact Data Trans team to get carton labels. 

We are in Bangladesh and Date trans Customer care phone nos in USA.  

  

Pls reply to us with a solution.  

  

  

  

  

  

!image002.png|thumbnail! 

  

Baskaran Dharmalingam  

Divisional Merchandising Manager  

Zamira Fashion Limited 

  

!image001.png|thumbnail! 

    {quote}


--{adf}{"type":"expand","content":[{"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"default"},"content":[{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"http://assets.datatrans-inc.com/images/logos/DTS-Cleo-Email-1.png","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":" <"},{"type":"text","text":"https://www.datatrans-inc.com/","marks":[{"type":"link","attrs":{"href":"https://www.datatrans-inc.com/"}}]},{"type":"text","text":"> "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Vikrant Chougale","marks":[{"type":"textColor","attrs":{"color":"#003366"}}]},{"type":"text","text":" "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"DataTrans Solutions","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#f2771a"}}]},{"type":"text","text":" : Developer","marks":[{"type":"textColor","attrs":{"color":"#f2771a"}}]},{"type":"text","text":" "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"p:","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#aaaaaa"}}]},{"type":"text","text":" ************ ","marks":[{"type":"textColor","attrs":{"color":"#aaaaaa"}}]},{"type":"hardBreak"},{"type":"text","text":"  ","marks":[{"type":"textColor","attrs":{"color":"#aaaaaa"}}]},{"type":"text","text":"w:","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#aaaaaa"}}]},{"type":"text","text":" ","marks":[{"type":"textColor","attrs":{"color":"#aaaaaa"}}]},{"type":"text","text":"datatrans-inc.com","marks":[{"type":"textColor","attrs":{"color":"#aaaaaa"}},{"type":"link","attrs":{"href":"https://www.datatrans-inc.com/"}}]},{"type":"text","text":"e:","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#aaaaaa"}}]},{"type":"text","text":" ","marks":[{"type":"textColor","attrs":{"color":"#aaaaaa"}}]},{"type":"text","text":"<EMAIL>","marks":[{"type":"textColor","attrs":{"color":"#aaaaaa"}},{"type":"link","attrs":{"href":"mailto:<EMAIL>"}}]},{"type":"hardBreak"},{"type":"text","text":" "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"DTS is now a part of Cleo. Read about the ","marks":[{"type":"textColor","attrs":{"color":"#aaaaaa"}}]},{"type":"text","text":"acquisition.","marks":[{"type":"textColor","attrs":{"color":"#328cd6"}},{"type":"link","attrs":{"href":"https://www.cleo.com/news-cleo-acquires-datatrans-solutions/"}}]},{"type":"text","text":" "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"http://assets.datatrans-inc.com/images/icons/facebook-icon.png","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"  <"},{"type":"text","text":"https://www.facebook.com/DataTransSolutions/","marks":[{"type":"link","attrs":{"href":"https://www.facebook.com/DataTransSolutions/"}}]},{"type":"text","text":"> "}]}]},{"type":"listItem","content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"http://assets.datatrans-inc.com/images/icons/instagram-icon.png","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"  <"},{"type":"text","text":"https://www.instagram.com/datatranssolutions/","marks":[{"type":"link","attrs":{"href":"https://www.instagram.com/datatranssolutions/"}}]},{"type":"text","text":"> "}]}]},{"type":"listItem","content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"http://assets.datatrans-inc.com/images/icons/twitter-icon.png","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"  <"},{"type":"text","text":"https://twitter.com/datatrans_edi","marks":[{"type":"link","attrs":{"href":"https://twitter.com/datatrans_edi"}}]},{"type":"text","text":"> "}]}]},{"type":"listItem","content":[{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"http://assets.datatrans-inc.com/images/icons/linkedin-icon.png","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"  <"},{"type":"text","text":"https://www.linkedin.com/company/datatrans-solutions/","marks":[{"type":"link","attrs":{"href":"https://www.linkedin.com/company/datatrans-solutions/"}}]},{"type":"text","text":"> "}]}]}]}]}]}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

