<p>Um, okay. All right. But you asked a couple questions on some tickets. Yeah. Um, I know, but I need to know you. Thank you. I have never used a batch for anything. When I click on the order and click invoice, it will not take me to the next screen. Um, so she clicks on the order and then I guess we&#39;re going to have to send her a screenshot then. because I&#39;m really not I I I still don&#39;t know exactly what she&#39;s doing. So that&#39;s why I need to know exactly that&#39;s why I want to know your what are your exact steps. Um you know like take me through your steps. So at this point now I&#39;m just you know here I&#39;ll just share my screen. I have like I know I know Okay. So, 69.8 and this peel. So, And I tried it both ways. So I tried I did rightclick invoice. It worked. I opened the invoice and did respond. So now this is going to be the screenshot to her. You know, open the order, click respond. Select 810. Just tell her so that you know, okay, I went to that order. I opened it. I clicked respond, clicked 810, and then, you know, show her the screenshot maybe. Yeah. So then just give her those two screenshots. Just say, &#34;Okay, I went in to your account. I opened the order. I clicked respond. I clicked A10.&#34; I mean, like this is what I was asking for step by step what you did. And then it opened an invoice. And so ask her, is this the process you&#39;re using? Yeah. Yeah. Show show that. So this is just so that you know this is what I did now. I went into the order. I opened it. I clicked respond. I selected A10 and it created an invoice. Is this the process you&#39;re following? Um so that we can get a better understanding of what exactly she&#39;s doing. So because I at this point I really have no idea what she&#39;s doing. I don&#39;t know how she&#39;s That&#39;s why I want to know what she&#39;s clicking on to get that error. So now hopefully she&#39;ll come back and either say yes, this is what I&#39;m doing or no, this is the process I&#39;m following. So then we&#39;ll get a better idea. Okay. All right. Um what was another question you had? Oh, for Voss. Okay. I put a bunch of notes on the Costco one. So, um, so yeah. So, on the on the one for Voss, I put the flow. So, Costco files come in on ECSO2 and then server shared ECSO3 for processing. I You can see both steps occurred and the files delivered to their FTP. So the files come in on O2. I know you can&#39;t see these screenshots, but I think I should still be hopefully I&#39;m still connected. Hopefully my screen my I&#39;m still logged into everything. Okay, so the orders come in on ECSO2 from Lauren data Costco and then here the target says server share. So what that server share means is that it&#39;s actually sending it AS2A. That&#39;s what ECSO3 used to be called. So our servers actually had alpha names and then they were changed. So it&#39;s server shares to AS2A which was ECSO3. So I would go to ECSO3. I got to find that screen because I also was doing something for Maria. Okay. So then they server share to ECSO3 and that&#39;s this here. So then I looked for Costco to boss and I could see they came in from Windows WinRO ECSO3 server sharing right that&#39;s the channel that was our input. So the target on O2 was to send it to 03. The input on 03 was to bring it in using server share. From here I could see that it did two things. It went into their inbox, which is great. But that&#39;s not where she&#39;s looking for it. She&#39;s looking for it in her FTP, which is this process right here. This is now taking this EDI file and converting it to an XML because they use X, they use XML on their side. So, if I go under logging, I could see that, you know, um the file came in, it ran through this map. That&#39;s the map. It the data for delivery to the VOSS A50XML and this was my file name. Voss XML is another output channel that sends it to another input channel that sends it to Boss out which would be here. I don&#39;t think I have that one open anymore. So that then sends it to Boss USA out that was on 522, right? So, here are all the files that our boss out and let&#39;s see we see the file name here and want to have them both on the screen. So, here&#39;s my file name. Here&#39;s my file name, right? That&#39;s my file. There&#39;s the file name. And it says it went to Boss USA out. So, Voss USA out goes to 1798. That&#39;s their number. So, it&#39;s going to go to their T drive. So, if I go to Voss 1798 in the T drive, I&#39;m going to go to backup because we already sent it out. I go to from DTS. There&#39;s my file name. So, it was sent to them, but she&#39;s saying they don&#39;t have it. That&#39;s fine. She doesn&#39;t have it. That&#39;s great. If she doesn&#39;t have it, we&#39;ll just resend it. So, all we have to do is rightclick, say restage. That&#39;s restage the delivery. So, at the bottom, we&#39;re just reststaging the delivery. This is the refresh button. You refresh it until it says successfully delivered. If I come back here to Voss and if I go to transfer from DTS Voss USA these are old but because I think that&#39;s where it&#39;s going right it goes to Dclient DTS boss boss USA it&#39;s going to appear here eventually it&#39;ll show up um cuz I don&#39;t think it goes here goes to bosses. This is where it&#39;ll come in once it runs through all of its processes because it&#39;s probably sitting in declient. If I go to decline, it&#39;s probably still there. It just we have a bunch of scripts that run that send it from one drive to another drive. So from DTS plus there it is. So eventually this will get picked up and sent to the T drive because this is the D drive is an internal drive on every server. So O2 has its own D drive. 03 has its own D drive. So everything gets sent to their directory on the D drive. And there&#39;s a script that takes it from from DTS in D drive and puts it to the T drive, which is where either the customer picks it up or we send it to them. Oh, and there it is. See, it just moved from there to there. So now it&#39;s it&#39;s there waiting for her to pick it up. So, Costco is done. Why didn&#39;t she get it on the 22nd? I don&#39;t know why she didn&#39;t get these on this 22nd. They&#39;re in their directory. Um I don&#39;t know if somebody picked them up and closed them, but same thing here with US Foods. Um you know, she&#39;s saying that they did not get uh this order from US Foods. Probably this is probably the one. So, we&#39;re going to have to go through and that whole same process. Find what server does US foods come in. I don&#39;t know. So, I can do a search here. I generally usually leave my sender blank, but keep it for whatever day they&#39;re saying they need it. Um, because sometimes it&#39;s, you know, if I do US Foods, we might have more than one US Foods. Here&#39;s US Foods. So, this is the one that she&#39;s looking for. Probably guarantee it&#39;s the one she&#39;s looking for. It&#39;s the same date. Yep. So, we server share again. So, came in server share to 03. I&#39;m going to have to go get in a meeting. But, um, here we&#39;re just going to do was again get this. And here&#39;s the US foods. That&#39;s our order. It went to that map. We gave it a file name. This ended in 50151. I still have my other Target boss USA. And that I think is it right. 650151 restage the delivery and then I refresh it. See it says reststaging. I can refresh it. Now it turns green again and it says successfully delivered. Okay. So and then again same thing will happen. It&#39;s going to go to declient. Oh there it is. And eventually it&#39;s going to pop over from declient to this one. and be there for her to pick up. So, you can let her know that both have been restaged. Okay. I&#39;ll give you a call as soon as I&#39;m done in my meeting and we can go over your other tickets. Okay. All right. I&#39;ll talk to you later. All right. Bye.</p>
