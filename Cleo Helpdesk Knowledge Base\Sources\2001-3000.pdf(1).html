<p>Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      image (29acc4e7-c75b-43d5-b41e-f3f0e8df08b0).png
Request Type: Support
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello. I am trying to cancel a <PERSON><PERSON>ds dropship order. I sent an 870 but they told me that&#39;s not the correct thing to do. This is for PO *********. <PERSON><PERSON><PERSON> responded with the following:
We have not received an EDI 870 Cancellation from you, still.
We have not sent back a 997 Functional Acknowledgment for an EDI 870 PO Cancellation from your side.
If you are using the EDI 870 as an acknowledment, <PERSON><PERSON><PERSON> does not use it like that, they are cancellations for Dropship vendors.
Comments
Comment by <PERSON> [ 12/Dec/24 ]
Hello I will look into this and advise for now whats your DTS account number the 4 digit account number thank you
<PERSON> DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <PERSON> [ 12/Dec/24 ]
Hello.
Our account number is 7177
Comment by <PERSON> [ 12/Dec/24 ]
Ok thank you
<PERSON> Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 12/Dec/24 ]
Hello Brad the only form we know to cancelled a PO received is an 855 and Dillard&#39;s does not have an 855 I guess they use the 870 but that&#39;s the only form why they said they have not received the 870? doesn&#39;t make sense after you said you guys sent it because the status report is prearrangement schedule or agreement . these document are design and build based on Dillards specifications and what documents they accept please reachout to your vendor for additional question for cancelations.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Brad Rusin [ 12/Dec/24 ]
Thank you Maria.
I have added Don Thronton from Dillard&#39;s to this email. Don, do the comments below make sense? How do we proceed?
Maria Keshwala commented:
Hello Brad the only form we know to cancelled a PO received is an 855 and Dillard&#39;s does not have an 855 I guess they use the 870 but that&#39;s the only form why they said they have not received the 870? doesn&#39;t make sense after you said you guys sent it because the status report is prearrangement schedule or agreement . these document are design and build based on Dillards specifications and what documents they accept please reachout to your vendor for additional question for cancelations.
Comment by Maria Keshwala [ 12/Dec/24 ]
Hi Brad
yes the only forms we have on file for Dillards are the ones in your account
“If you are using the EDI 870 as an acknowledgment, Dillard&#39;s does not use it like that, they are cancellations for Dropship vendors.” I mean if you read this it said that the 870 is use for cancellations dropship.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 12/Dec/24 ]
For dropship orders we use the 870 to cancel orders either fully cancel or parcel cancel. We do not use the 855.
The EDI 870 PO cancel is a required document for dropship.
Comment by Brad Rusin [ 12/Dec/24 ]
Hi Bobby.
Thank you. I previously sent the 870 and it appeared to be received according to Datatrans. However, I was told it was not received.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 12/Dec/24 ]
Please work with Datatrans to see why the 870 is not making it to our VAN. I&#39;m guessing because they do not use the 870 as a PO cancellation. Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 12/Dec/24 ]
Brad: do you need us to cancel the below order while you work this out with Datatrans?
Dec 3, 2024 3130471401
Comment by Maria Keshwala [ 12/Dec/24 ]
Hi Brad you said that Dillards mentioned the 870 is not the correct thing to do have you ever submitted the 870 it looks like they are waiting for it
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Brad Rusin [ 12/Dec/24 ]
Hello. Yes, the 870 has been sent before. Even though it was green, Dillard&#39;s said it wasn&#39;t received. This is a screenshot that I sent them a few days ago.
!image.png|thumbnail!
Comment by Brad Rusin [ 12/Dec/24 ]
Yes, please.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 12/Dec/24 ]
PO #3130471401
Merchant Support: please cancel the above order, vendor is unable to send the 870 at the moment
Dropcs: please credit the customer once cancelled
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 12/Dec/24 ]
PO is cancelled.
Thanks,</p>
<h4 id="cs-34159-issue-sending-an-855-due-to-invalid-date-error-dts-404-created-11-dec-24-updated-16-dec-24-resolved-16-dec-24">[CS-34159] Issue sending an 855 due to invalid date error -DTS-404 Created: 11/Dec/24  Updated: 16/Dec/24  Resolved: 16/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241211-132230.png      image-20241213-205803.png
Request Type: Phone call
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Date issue with 855 for GE Energy
Comments
Comment by Maria Keshwala [ 11/Dec/24 ]
Hello Rod
Hello this issue has been escalated to our development team we will keep you posted with any updates. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 12/Dec/24 ]
Hello Rod issue has been please , please recreate the 855 so update can reflect dont use the ones you already created let me know once you send the documents thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Sandy Karidas [ 13/Dec/24 ]
created an 855, and the date did not generate an error upon sending.
Sandy Karidas
DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Maria Keshwala [ 16/Dec/24 ]
good morning
an 855 was created on our end no error is no longer present.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-34154-from-dot-foods-missing-997-for-edi-850-po-created-11-dec-24-updated-18-dec-24-resolved-18-dec-24">[CS-34154] FROM DOT FOODS - Missing 997 for EDI 850 PO Created: 11/Dec/24  Updated: 18/Dec/24  Resolved: 18/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: Missing 997
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<h1 id="functional-acknowledgement-error-report">Functional Acknowledgement Error Report</h1>
<h4 id="2024-12-11-06-00-01">2024-12-11 06:00:01</h4>
<p>TrackID Partner Name Message Type Message ID Own ID Partner ID Filename Processing Start Time Status
000000950704.0009.0001 RIP_VAN_WAFELS_DATATRANS 850_PO 000000162:162 DOTFDSEE : DOTFDSEE DTS4533 : DTS4533 NOT_SET.000000950704.0009.0001 2024-12-09 10:55:50 Timeout
Comments
Comment by Maria Keshwala [ 18/Dec/24 ]
Hello we sent an 997 back
<em>ISA_00_ 00 ZZ_DTS4533 _ZZ_DOTFDSEE <em>241209_1058_U_00401_200000001_0_P~ GS_FA_DTS4533_DOTFDSEE_********_1058_200000001_X_004010 ST_997_200000001 AK1_PO_162 AK2_850_0001 AK5_A</em> AK2_850_0002 _AK5_A AK9_A_2_2_2 SE_8_200000001 GE_1_200000001 IEA_1_200000001</em>
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-34139-document-processing-error-allpro-corporation-2024-12-10-20-24-47-created-10-dec-24-updated-12-dec-24-resolved-12-dec-24">[CS-34139] Document Processing Error: ALLPRO Corporation 2024-12-10 20:24:47 Created: 10/Dec/24  Updated: 12/Dec/24  Resolved: 12/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Joseph Chiricosta Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
SPS Commerce was unable to successfully process the following document:
Serial Number: PCL55349344716
Document Originator: Coast To Coast Computer Products, Inc Document Recipient: ALLPRO Corporation Document Type: 810
InvoiceDate: 2024-12-10 InvoiceNumber: A2740705
ShipToLocation: Westerly Paints, Inc. InvoiceTotalAmount: 239.96 PurchaseOrderNumber: EMAIL
data:A2740705 Invoice Number: A2740705 The value &#39;C2330&#39; is not a valid AllPro member code. Please consult the ALLPRO member listing located in the AllPro vendor website to confirm the correct value. Please contact <a href="mailto:<EMAIL>"><EMAIL></a> with any questions regarding member codes.
An error has occurred while processing the document referenced above. If you would like assistance to resolve the error or need additional information, please visit our Support Center and choose one of our convenient contact channels to connect with a support representative.
For complimentary expert training visit <a href="https://trainingcenter.spscommerce.com/">https://trainingcenter.spscommerce.com/</a> and for complimentary support visit <a href="https://www.spscommerce.com/customer-support/support/">https://www.spscommerce.com/customer-support/support/</a> Kind regards, SPS Customer Operations Team
SPS Commerce Inc. 333 S 7th St #1000, Minneapolis, MN 55402 Having issues? Contact our support team Message Reference ID: SPS-3BN-9V-36D
Comments
Comment by Lauren Cohn [ 11/Dec/24 ]
Hi Joseph,
Please let us know if you need any assistance with the rejection received from SPS:
Document Originator: Coast To Coast Computer Products, Inc Document Recipient: ALLPRO Corporation Document Type: 810
InvoiceDate: 2024-12-10 InvoiceNumber: A2740705 ShipToLocation: Westerly Paints, Inc. InvoiceTotalAmount: 239.96 PurchaseOrderNumber: EMAIL
data:A2740705 Invoice Number: A2740705 The value &#39;C2330&#39; is not a valid AllPro member code. Please consult the ALLPRO member listing located in the AllPro vendor website to confirm the correct value. Please contact <a href="mailto:<EMAIL>"><EMAIL></a> with any questions regarding member codes.
Best,
Lauren Cohn | Technical Support Representative DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
If urgent, please call us with the ticket number and details of the issue.</p>
<h4 id="cs-34134-fw-edi-change-at-zf-plant-fenton-fawn-mexico-inc-lsid-236024-created-10-dec-24-updated-16-dec-24-resolved-16-dec-24">[CS-34134] FW: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024 Created: 10/Dec/24  Updated: 16/Dec/24  Resolved: 16/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  EDI_Change_at_ZF_Plant_Fenton____Fawn_Mexico,_Inc_____LSID__236024.eml      image-********-133148.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
The vendor code for trading partner TRW Automotive – Fenton will be changing to ******** in 2025.
Is this set to be changed?
Regards,
Stephanie Brinegar
IT Administrator/Accounting Analyst
Fawn Industries, Inc.
From: Alvarado Ricardo MNR FIXE3 <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, December 10, 2024 1:28 PM To: Stephanie Brinegar <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Brenda Mendoza <a href="mailto:<EMAIL>"><EMAIL></a>; Ricardo Marquez <a href="mailto:<EMAIL>"><EMAIL></a>; Edgar Ulises Molina Holguín <a href="mailto:<EMAIL>"><EMAIL></a>; Barbie Mathewson <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024
ZF Confidential
Greetings Fawn Team,
Following up on this topic, we haven&#39;t receive a response on regards of this migrations, could you please let us know is this is the right contac.
This is related to the existing EDI connections between Fenton site and Fawn Mexico with vendor code: 236024. Which will be migrated in 2025 into a new erp system changing the vendor code to ******** along with the EDI format changes to Global Edifact.
(Attached you&#39;ll find the original email with more detailed information)
We await your response. Thank you!
From: Alvarado Ricardo MNR FIXE3 <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, October 29, 2024 4:42 PM
To: Stephanie Brinegar &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Brenda Mendoza &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Ricardo Marquez &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Edgar Ulises Molina Holguín &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fawn Mexico Inc &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: Re: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024
Greetings,
We would like to inform you about postponement of the Go Live Date for Fenton 4606 and Fowlerville plant 4661, migration has been postponed to year 2025 and we will keep you updated as soon as a specific date is set.
The existing EDI connects for the old plant code/system will be active until there is a new Go Live date.
For open/ongoing topics regarding the respective connections and formats, we will stay in contact with you to finalized the onboarding process.
Any comments or questions, please let me know.
Atentamente / Kind regards / Mit freundlichen Grüßen Ricardo Alvarado
Business Process Integration &amp; EDI (FIXE3)
ZF Group Corporate Finance, IT, M&amp;A IT Data &amp; AI, Integration, Process Automation, Innovations &amp; EAM Eurofren Investment, S. de R.L. de C.V. Avenida Finsa 1211, Parque Industrial Finsa, 67132 Guadalupe, México <a href="mailto:<EMAIL>"><EMAIL></a>
From: Alvarado Ricardo MNR FIXE3 <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, October 18, 2024 11:24 AM To: Stephanie Brinegar &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Brenda Mendoza &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Ricardo Marquez &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Edgar Ulises Molina Holguín &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fawn Mexico Inc &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Lipp Melissa FNT CONES &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: Re: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024
Hello Fawn Team,
Were you able to review our previous email, to expedite the process we&#39;ve sent a test demand through the existing EDI connection for you to review. Attached a copy of the raw EDI message and EDI guidelines.
We await your response as soon as possible. Thank you!
Atentamente / Kind regards / Mit freundlichen Grüßen Ricardo Alvarado
Business Process Integration &amp; EDI (FIXE3)
ZF Group Corporate Finance, IT, M&amp;A IT Data &amp; AI, Integration, Process Automation, Innovations &amp; EAM Eurofren Investment, S. de R.L. de C.V. Avenida Finsa 1211, Parque Industrial Finsa, 67132 Guadalupe, México <a href="mailto:<EMAIL>"><EMAIL></a>
From: Alvarado Ricardo MNR FIXE3 <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, September 30, 2024 10:31 AM To: Stephanie Brinegar &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Brenda Mendoza &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Ricardo Marquez &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Edgar Ulises Molina Holguín &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fawn Mexico Inc &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Burke Kathleen EXT SupplyON &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: Re: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024
Greetings,
We would like to inform you about postponement of the Go Live Date for Fenton 4606 and Fowlerville plant 4661 from 1st Oct 2024 to 1st Nov 2024.
For open/ongoing topics regarding the respective connections and formats, we will stay in contact with you in the next weeks so the processes will run smoothly with the new plant codes and vendor codes.
In the meantine the existing/old vendor and plant codes will be active until 31st Oct 2024.
Any comments or questions, please let us know
Atentamente / Kind regards / Mit freundlichen Grüßen Ricardo Alvarado
Business Process Integration &amp; EDI (FIXE3)
ZF Group Corporate Finance, IT, M&amp;A IT Data &amp; AI, Integration, Process Automation, Innovations &amp; EAM Eurofren Investment, S. de R.L. de C.V. Avenida Finsa 1211, Parque Industrial Finsa, 67132 Guadalupe, México <a href="mailto:<EMAIL>"><EMAIL></a>
From: Alvarado Ricardo MNR FIXE3 <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, September 20, 2024 5:35 PM To: Stephanie Brinegar &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Brenda Mendoza &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Ricardo Marquez &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Edgar Ulises Molina Holguín &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fawn Mexico Inc &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Burke Kathleen EXT SupplyON &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: Re: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024
Hello Stephanie
As mentioned before, ZF is migrating to a new ERP system for several plants including Fenton US, on the 1st of October. All related information can be found on the attached letter.
Currently Fawn Mexico has an existing EDI communication with ZF Fenton US, using the vendor code: 236024, this code will change in the new system to: ********. We can use this existing connection if confirmed, but please be aware of the following:
Fawn Mexico is currently receiving DELFOR D97A from Fenton, this will be changed to Global Edifact format: DELFOR D04A (attached ZF Guideline) Fawn is currently sending DESADV D07A to Fenton, the ASN will need to be updated with the new UNB code for Fenton: 0013000046ZFCI1 as well as the vendor code.
Once we have received your confirmation we&#39;ll send a test delivery, through the existing connection, to validate correct reception at your side.
Please provide feedback as soon as possible, any questions or concerns let me know.
We await your response.
Atentamente / Kind regards / Mit freundlichen Grüßen Ricardo Alvarado
Business Process Integration &amp; EDI (FIXE3)
ZF Group Corporate Finance, IT, M&amp;A IT Data &amp; AI, Integration, Process Automation, Innovations &amp; EAM Eurofren Investment, S. de R.L. de C.V. Avenida Finsa 1211, Parque Industrial Finsa, 67132 Guadalupe, México + <a href="mailto:<EMAIL>"><EMAIL></a> +
From: Burke Kathleen EXT SupplyON <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, September 6, 2024 10:03 AM
To: Stephanie Brinegar &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Alvarado Ricardo MNR FIXE3 &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Brenda Mendoza &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Ricardo Marquez &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Edgar Ulises Molina Holguín &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Fawn Mexico Inc &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024
ZF Confidential
ZF Confidential
Hi Stephanie,
I believe the wrong LSID is referenced in the subject line. This should be an onboard for LSID ********. LSID 236024 is the old vendor code which will be obsolete come Oct 1 once we switch to the new system.
Upcoming Out of Office
Nov 25-29
Dec 16-Jan3
Kind regards,
Kathleen Burke
Supplier Digital Communication- External Contract
ZF Group
Materials Management
MM Digitalization and Services - Electronic Supplier Communication
ZF North America. Inc.
Greer, SC 29650/USA</p>
<ul>
<li><a href="mailto:<EMAIL>"><EMAIL></a> +
From: Stephanie Brinegar <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, September 6, 2024 9:53 AM To: Alvarado Ricardo MNR FIXE3 <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Brenda Mendoza <a href="mailto:<EMAIL>"><EMAIL></a>; Ricardo Marquez <a href="mailto:<EMAIL>"><EMAIL></a>; Edgar Ulises Molina Holguín <a href="mailto:<EMAIL>"><EMAIL></a>; Fawn Mexico Inc <a href="mailto:<EMAIL>"><EMAIL></a>; Burke Kathleen EXT SupplyON <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024
ZF Confidential
Reply from our EDI supplier:
We have updated this back in 05/2024 is already in place.
Regards,
Stephanie Brinegar
IT Administrator/Accounting Analyst
Fawn Industries, Inc.
From: Alvarado Ricardo MNR FIXE3 &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> +&gt; Sent: Thursday, August 29, 2024 4:10 PM To: Stephanie Brinegar &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Barbie Mathewson &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Cc: Brenda Mendoza &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Ricardo Marquez &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Edgar Ulises Molina Holguín &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Burke Kathleen EXT SupplyON &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Subject: Re: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024
ZF Confidential
Hello Stephanie and Fawn Team
Were you able to review my 1st email? Could you please provide feedback on this EDI migration or the right contact to address for this change?
We await your response, thank you.
Atentamente / Kind regards / Mit freundlichen Grüßen Ricardo Alvarado
Business Process Integration &amp; EDI (FIXE3)
ZF Group Corporate Finance, IT, M&amp;A IT Data &amp; AI, Integration, Process Automation, Innovations &amp; EAM Eurofren Investment, S. de R.L. de C.V. Avenida Finsa 1211, Parque Industrial Finsa, 67132 Guadalupe, México + <a href="mailto:<EMAIL>"><EMAIL></a> +
From: Alvarado Ricardo MNR FIXE3 &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Thursday, August 22, 2024 11:53 AM To: + <a href="mailto:<EMAIL>"><EMAIL></a> &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Fawn Mexico Inc &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Subject: Re: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024
Greetings Stephanie and Barbie,
You contact information was provided to me by my colleague Kathleen Burke.
Could you please review the email below and provide confirmation or direct me with the right contact to review this migration. Thank you in advance.
I await your response.
Atentamente / Kind regards / Mit freundlichen Grüßen Ricardo Alvarado
Business Process Integration &amp; EDI (FIXE3)
ZF Group Corporate Finance, IT, M&amp;A IT Data &amp; AI, Integration, Process Automation, Innovations &amp; EAM Eurofren Investment, S. de R.L. de C.V. Avenida Finsa 1211, Parque Industrial Finsa, 67132 Guadalupe, México + <a href="mailto:<EMAIL>"><EMAIL></a> +
From: Alvarado Ricardo MNR FIXE3 Sent: Wednesday, July 24, 2024 2:31 PM To: + <a href="mailto:<EMAIL>"><EMAIL></a> &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Cc: + <a href="mailto:<EMAIL>"><EMAIL></a> &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; + <a href="mailto:<EMAIL>"><EMAIL></a> &lt; + <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Subject: EDI Change at ZF Plant Fenton // Fawn Mexico, Inc. // LSID: 236024
Dear Supplier,
Referencing to the attached information package that you have received from our EDI team, I am contacting you from ZF Onboarding Service regarding the migration of the existing EDI connection related to our plant in Fenton(Plant Code 4606) for process(es):
Process Communication mode EDIFACT format alternative Web-EDI</li>
<li>Deliver Schedules + Direct EDI DELFOR D04A (VDA 4984) SupplyOn</li>
<li>Advance Shipping Notice (ASN) + Direct EDI DESADV D07A (VDA 4987) SupplyOn
Your new local vendor code ( LSID) will be:* *********.
On the new system we do require the ZF Global EDIFACT standards; SupplyOn WebEDI (SupplyChainCollaboration) is the alternate way.
The Go Live Date will be 1st October 2024. In the meantime, you will receive data from our ZF test system. The data is marked with a test flag (for direct EDI connection) and also any data which is sent out for the above mentioned combination of plant code/local vendor code via WebEDI connection is always test data if sent and received before Go-Live Date.
Please:
Confirm that you are the right contact person for EDI Onboarding topics and if not please provide the contact to me. Give feedback if you wish to have the connection set up as ZF Global EDIFACT or if you choose SupplyOn WebEDI (SCC). Confirm that you have read and understood that all data which you will be receiving before 1st October 2024 are test messages.
Thanks for your cooperation and feel free to reach out to me any time.
Atentamente / Kind regards / Mit freundlichen Grüßen Ricardo Alvarado
Business Process Integration &amp; EDI (FIXE3)
ZF Group Corporate Finance, IT, M&amp;A IT Data &amp; AI, Integration, Process Automation, Innovations &amp; EAM Eurofren Investment, S. de R.L. de C.V. Avenida Finsa 1211, Parque Industrial Finsa, 67132 Guadalupe, México + <a href="mailto:<EMAIL>"><EMAIL></a> +
[EXTERNAL EMAIL] Security Alert: This email originated from outside our organization. Do not click links or attachments unless you recognize the sender and know the content is safe.
[EXTERNAL EMAIL] Security Alert: This email originated from outside our organization. Do not click links or attachments unless you recognize the sender and know the content is safe.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 10/Dec/24 ]
<em>EDI_Change_at_ZF_Plant_Fenton___Fawn_Mexico,_Inc___LSID_236024.eml  (338 kB)</em>
Comment by Maria Keshwala [ 10/Dec/24 ]
Hello Stephanie I will investigate and advise . Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 16/Dec/24 ]
not sure if this is a project asking sandy
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 16/Dec/24 ]
Hello Stephanie this project has been completed for this new migration . Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 16/Dec/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ul>
<h4 id="cs-34074-fw-edi-created-09-dec-24-updated-09-dec-24-resolved-09-dec-24">[CS-34074] FW: EDI Created: 09/Dec/24  Updated: 09/Dec/24  Resolved: 09/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image001 (99724c51-80dd-4a57-9a2b-343f9452b873).png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
I had EDI this morning but had no CAT file for 12/9/24.
Please advise.
Marcy Lowrance
E H Baare Corp
618-546-1575 ext 107
From: Travis Crumrin <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, December 9, 2024 8:36 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: EDI
If you haven’t already, please alert DT.
Is this the 2nd week/Monday in a row?
Travis Crumrin
500 Heath Toffee Ave. Robinson, IL 62454
O 618-546-1575 x114 | C 618-554-6996
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, December 9, 2024 7:15 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject: EDI
We had no EDI for today – I had the small weekend files but none for 12/9/24
Marcy Lowrance
E H Baare Corp
618-546-1575 ext 107
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Dec/24 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Dec/24 ]
Here is the files I did have – but there is no CAT nor Deere for 12/9/24
Marcy Lowrance
E H Baare Corp
618-546-1575 ext 107 Comment by Maria Keshwala [ 09/Dec/24 ]
are those files for this weekend let me check
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Dec/24 ]
Yes - but we had no CAT file for 12/9 and that is very unusual.
Comment by Maria Keshwala [ 09/Dec/24 ]
Hi Marcy I see here you received a lot of CAT 830s this morning at 821am you wont get those until tomorrow then I see the last ones on 12/07/2024 from John Deere and the last CAT are dated 12/06/2024 then the new ones are in today, can you please confirmed if you have the files that we received on 12/06/2024?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Dec/24 ]
In the email I put a copy of the lines I received before I started processing them. here is what I did received.
i will attach it outside of this message.
it is just very unusual to not have a CAT file.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Dec/24 ]
Marcy Lowrance
E H Baare Corp
618-546-1575 ext 107 Comment by Maria Keshwala [ 09/Dec/24 ]
the 7 was Saturday and those are the files from the 6th the ones I mentioned I dont see files received Sunday yesterday let me keep checking
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 09/Dec/24 ]
Hi Marcy no CAT files received on the 7 or the 8 saturday or sunday but this morning we received a lot of files .
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 09/Dec/24 ]
not sure maybe on their end they did not release all the files until this morning
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Dec/24 ]
Ok – thanks
Marcy Lowrance
E H Baare Corp
618-546-1575 ext 107
Comment by Maria Keshwala [ 09/Dec/24 ]
you’re welcome
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-34068-re-issues-receiving-release-created-09-dec-24-updated-09-dec-24-resolved-09-dec-24">[CS-34068] RE: Issues receiving release Created: 09/Dec/24  Updated: 09/Dec/24  Resolved: 09/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: High
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-********-131522.png      AE856CLE.********11551987_********120010      AE856MCA.********10250748_********103011
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Missing documents FTP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
From: Janet Dorsey Sent: Monday, December 9, 2024 7:55 AM To: CTMS Service Team <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Sandy Karidas <a href="mailto:<EMAIL>"><EMAIL></a>; Ethan Paratto <a href="mailto:<EMAIL>"><EMAIL></a>; Val Sova <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Issues receiving release Importance: High
Good morning,
We haven’t received any releases from you since 12/6. Can you please look into this?
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
NOTE: This message contains confidential information and is intended only for the individual named. If you are not the named addressee, you should not disseminate, distribute or copy this email. Please notify the sender immediately by email if you have received this email by mistake and delete this email from your system. Email transmission cannot be guaranteed to be secure or error-free, as information could be intercepted, corrupted, lost, destroyed, arrive late or incomplete, or contain viruses. The sender, therefore, does not accept liability for any errors or omissions in the contents of this message which arise as a result of email transmission. If verification is required, please request a hard-copy version.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Dec/24 ]
Comment by Maria Keshwala [ 09/Dec/24 ]
Good morning Janet
looking into as we speak I will advise thank you
Let me look into this
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 09/Dec/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Dec/24 ]
Thank you
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
Comment by Maria Keshwala [ 09/Dec/24 ]
Hi Janet I see we are sending the files and there are no pending files on our end I see your other email that you sent this to the wrong support I will closed this it will re-open once you reply if you need our assistance. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Dec/24 ]
Hi,
We still seem to be having a problem. The 2 attached ASNs where pickup up by you, but never processed through to the WebEDI. Can you please look at this issue? Thank you
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
<em>AE856CLE.********11551987_********120010  (8 kB)</em>
<em>AE856MCA.********10250748_********103011  (5 kB)</em>
Comment by Sandy Karidas [ 09/Dec/24 ]
Hello Janet,
I do see the Whirlpool and JTEKT were both sent. The ASN for Fluid Routing failed validation and is in the draft folder. The equipment number is missing.
Please let me know if you need additional assistance.
Regards,
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Dec/24 ]
Thank you, we can see them now.
Janet Dorsey
IT Manager
************
Universal Metal Products, Inc.
29980 Lakeland Blvd.
Wickliffe, Ohio 44092
UMP is proud to be a certified WBENC, Women Owned Business
Comment by Maria Keshwala [ 09/Dec/24 ]
ok great thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-34032-cat010-caterpillar-inc-po-**********-810-to-draft-due-to-exceeding-maximum-field-length-created-06-dec-24-updated-10-dec-24-resolved-10-dec-24">[CS-34032] CAT010 CATERPILLAR INC PO ********** - 810 to draft due to exceeding maximum field length Created: 06/Dec/24  Updated: 10/Dec/24  Resolved: 10/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Glen Houghtaling Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image004.png      image005.png      D********T091843.AE810MCT.****************      image-20241210-181004.png      image-20241210-180940.png      image-************109.png      image-20241210-181957.png      image-************154.png      image-20241210-185842.png      image-20241210-185842 (25eddde3-bf60-491e-b99b-c949178fe342).png      GOOD810MILCUT.txt      image-20241210-181004 (cb4642bd-72b1-407a-abe5-77000df9bee5).png     810milcut.txt
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Global Shop, Invoice errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello,
This is the second time within the last two weeks that an invoice was rejected due to a maximum length requirement. I did not alter message ********/reference 299561 to help you investigate. The GSS data is attached. I need to know if this is a Datatrans or Milcut problem. I am hoping it can be fixed.
I can tell you there were no blank spaces around the data fields for the invoice I manually corrected. The only way I was able to send the 810 was to enter it again manually (see message ********/reference 299497).
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
Comments
Comment by Glen Houghtaling [ 06/Dec/24 ]
<em>D********T091843.AE810MCT.****************  (6 kB)</em>
Comment by Maria Keshwala [ 09/Dec/24 ]
Good morning Glen
I will check this one and advice, is not rejected because the TP has not received the file it failed validation on our end , it looks like the issue is on the Line Item level I will advise once I run some testing thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 10/Dec/24 ]
batch id ********* ECS-03
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 10/Dec/24 ]
Map = 802
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 10/Dec/24 ]
GOOD 810
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 10/Dec/24 ]
Hello Glen the value is coming from your document please see the good invoice that was sent and the bad one with this data value is in t he L record position 246 ln2
on the bad file you have more than 10 digits please review and resent or edit the one in draft and try to submit that one. Thank you
good message ID 42565182
BAD
810milcut.txt
GOOD810MILCUT.txt
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-34010-billed-twice-on-12-04-created-06-dec-24-updated-06-dec-24-resolved-06-dec-24">[CS-34010] Billed twice on 12/04 Created: 06/Dec/24  Updated: 06/Dec/24  Resolved: 06/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Support
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi, There are 2 charges on our credit card ending in 3436, each for $16.05 on 12/04, from DataTrans. It appears we were mistakenly billed twice. Would you please look into this and issue a credit for the second charge?
Comments
Comment by Maria Keshwala [ 06/Dec/24 ]
Hello please contact
Accounting Support
<a href="mailto:<EMAIL>"><EMAIL></a>
(281)292-8686
For English select 1, followed by 3 for Accounting
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33963-sent-failed-created-05-dec-24-updated-06-dec-24-resolved-06-dec-24">[CS-33963] SENT FAILED Created: 05/Dec/24  Updated: 06/Dec/24  Resolved: 06/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Candie Rogers Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: ASN errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good afternoon,
I just did 2 ASN’s that show sent failed. Please advise.
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comments
Comment by Candie Rogers [ 05/Dec/24 ]
Comment by Maria Keshwala [ 06/Dec/24 ]
Hi Candie I know what that was I will fix it and let you know once min
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 06/Dec/24 ]
Hi Candie it appears that it fix it self both of the asns have been accepted. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 06/Dec/24 ]
Maria,
Thank you! It looks like the all say Accepted now except for 2…they just show SENT. Is there a way to tell if those were sent?
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 06/Dec/24 ]
Hi Candie restage those two and send them again
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 06/Dec/24 ]
I have re-sent those 2. Thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 06/Dec/24 ]
ok great you’re welcome
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33957-invoice-submission-created-05-dec-24-updated-06-dec-24-resolved-06-dec-24">[CS-33957] Invoice Submission Created: 05/Dec/24  Updated: 06/Dec/24  Resolved: 06/Dec/24</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.jpg      image002.jpg      image-********-183359.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Delayed document delivery
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
[][To Whom It May Concern:|]
RE: Invoice 042061
Sent December 3, 2024 (Shipment 11/22/2024)
Amount $5,520.00
Status- Sent
Please help, this invoice hasn&#39;t been acknowledged, showing SENT on status. Thanks in advance.
Thank you,
Carmen Avalos
Accounting Clerk
Shamrock Precision
14850 Venture Drive
Farmers Branch, TX 75234
972-241-4226 Ext 522
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.shamrockprecision.com">www.shamrockprecision.com</a>
AS9100 &amp; ISO9001 Certified/ ITAR Registered
Notice: Shamrock Precision’s Terms and Conditions have been recently updated. You can find the current revision on our website at: shamrockprecision.com/purchase-order-terms-and-conditions
Comments  Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 05/Dec/24 ]
Comment by Maria Keshwala [ 06/Dec/24 ]
Hello I will look into this and advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 06/Dec/24 ]
Files was delivered t o halliburton sftp
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 06/Dec/24 ]
Hello
I see on my end the file was delivered successfully to Halliburton&#39;s sftp there are no files for them pending for pick up , please follow up with and let us know if you still have any issues. thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33953-td-synnex-effective-february-1-2025-monroe-nj-dnj-cm8-will-move-to-swedesboro-nj-dsw-cm503-created-05-dec-24-updated-09-dec-24-resolved-09-dec-24">[CS-33953] TD SYNNEX: Effective February 1, 2025 - Monroe, NJ, (DNJ-CM8) will move to Swedesboro, NJ (DSW-CM503) Created: 05/Dec/24  Updated: 09/Dec/24  Resolved: 09/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      image (bee849d9-a590-4cea-99c6-797fb602abaa).png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Dear TD SYNNEX EDI Partner,
Please be advised that TD SYNNEX has decided to permanently close operations from the warehouse in Monroe, NJ(DNJ_CM8) effective February 1, 2025.
As a result, the location change will be replaced with the warehouse in Swedesboro, NJ (DSW-CM503).
There won&#39;t be any changes with the mapping of EDI 832 / 846; we&#39;ll have the quantity zeroed out for this warehouse.
Please reply to this email to acknowledge that you have received it and let us know if you have any questions.
<em>Best Regards, Cathy Sipos EDI/B2B Support Lead, IT, Americas</em>
TD SYNNEX 16202 Bay Vista Drive Clearwater, Florida 33760 W: (************* Mobile: (************* <a href="mailto:<EMAIL>"><EMAIL></a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 05/Dec/24 ]
Comment by Maria Keshwala [ 09/Dec/24 ]
no edi mapping to be done
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33948-re-invoice-payments-issue-created-05-dec-24-updated-06-dec-24-resolved-06-dec-24">[CS-33948] RE: Invoice Payments issue Created: 05/Dec/24  Updated: 06/Dec/24  Resolved: 06/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Keli Bacon Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified Original estimate: Not Specified
Attachments:  image001.png      image002.png      ***********-0578.pdf      0203-5177855-0578.pdf      ***********-0578.pdf      0203-5177855-0554.pdf      0203-5177855-0588.pdf      0203-5177855-3801.pdf      ***********-0554.pdf      ***********-0588.pdf      ***********-0578.pdf      ***********-3801.pdf
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Label: Invoice questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Fabio,
This is the accounting department if you have questions about invoices we send to you for our services. I’ve copied our support team to help you with your questions below. However, if you are looking for payment from Target, that isn’t something that happens within our system or that we have control over, so you’ll need to speak with Target directly.
Thanks,
<a href="https://datatrans-inc.com/">https://datatrans-inc.com/</a>
Keli Bacon
Accounting Manager</p>
<ul>
<li><a href="mailto:<EMAIL>"><EMAIL></a> *</li>
<li><a href="http://www.datatrans-inc.com">www.datatrans-inc.com</a> *
(281) 292-8686, ext 115
<a href="https://www.linkedin.com/company/datatrans-solutions/">https://www.linkedin.com/company/datatrans-solutions/</a>  <a href="https://twitter.com/datatrans_edi">https://twitter.com/datatrans_edi</a>  <a href="https://www.facebook.com/DataTransSolutions/">https://www.facebook.com/DataTransSolutions/</a>  <a href="https://www.instagram.com/datatranssolutions/">https://www.instagram.com/datatranssolutions/</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> On Behalf Of Fabio Dalmo Sent: Thursday, December 5, 2024 5:29 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Cc: FTE RN <a href="mailto:<EMAIL>"><EMAIL></a>; Elisa Scarponi <a href="mailto:<EMAIL>"><EMAIL></a>; Greta Zuccheri <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Invoice Payments issue
Dear Accounting Team,
Fabio from the La Galvanina Italy, my pleasure
We are currently experiencing some issues with the payments related to the following POs.
Could you kindly assist us in resolving this matter?
Attached copy or the DataTrans receipt
Thank you in advance for your support
29/04/2024 00:00:00 5.779,20 279 29-Mar-24 ACK 04/16/2024 05:35:02 ***********-0578
29/04/2024 00:00:00 5.779,20 280 29-Mar-24 ACK 04/16/2024 05:04:31 0203-5177855-0578
29/04/2024 00:00:00 2.889,60 281 29-Mar-24 ACK 04/16/2024 05:35:02 ***********-0578
10/05/2024 00:00:00 2.889,60 325 10-Apr-24 ACK 05/30/2024 07:48:09 0203-5177855-0554
10/05/2024 00:00:00 2.889,60 326 10-Apr-24 ACK 04/16/2024 05:04:31 0203-5177855-0588
10/05/2024 00:00:00 4.334,40 327 10-Apr-24 ACK 04/16/2024 05:18:09 0203-5177855-3801
10/05/2024 00:00:00 2.889,60 328 10-Apr-24 ACK 06/19/2024 07:42:20 ***********-0554
10/05/2024 00:00:00 2.889,60 329 10-Apr-24 ACK 04/16/2024 05:28:55 ***********-0588
10/05/2024 00:00:00 5.779,20 330 10-Apr-24 ACK 06/20/2024 11:24:25 ***********-0578
10/05/2024 00:00:00 4.334,40 331 10-Apr-24 ACK 04/16/2024 05:35:02 ***********-3801
40.454,40
Fabio Dalmo Import/Export Customs Manager
. |. |Tel: +39 0541 743720 / 743318 <a href="mailto:<EMAIL>"><EMAIL></a> LA GALVANINA S.p.A. Via Popilia, 97 47922 Rimini (RN), Italy Info : <a href="mailto:<EMAIL>"><EMAIL></a> • Web : [www.galvanina.com |http://www.galvanina.com/] Linkedin: it.linkedin.com/company/galvanina |
.
<em>La presente email ha natura non personale e l’eventuale risposta alla stessa potrà essere conosciuta dall’organizzazione di appartenenza del mittente nell’ambito della policy dell’azienda. Le informazioni trasmesse sono destinate esclusivamente alla persona o alla società in indirizzo e sono da intendersi confidenziali e riservate. Ogni trasmissione, inoltro, diffusione o altro uso di queste informazioni a persone o società differenti dal destinatario è proibita. Se ricevete questa comunicazione per errore, contattate il mittente e cancellate le informazioni da ogni computer. Ringraziamo anticipatamente per la vostra preziosa collaborazione</em>
<em>This email has not personal nature, and any reply to it may be disclosed to the organization of the sender in accordance with the company policy. The information transmitted is intended only for the person or entity to which it is addressed and may contain confidential and/or privileged material. Any review, retransmission, dissemination or other use of, or taking of any action in reliance upon, this information by persons or entities other than the intended recipient is prohibited. If you received this in error, please contact the sender and delete the material from any computer. Thank you in advance for your contribution.</em>
P Prima di stampare questa mail, pensa all&#39;impatto sull&#39;ambiente - Please consider the environment before printing this e-mail.
Comments
Comment by Keli Bacon [ 05/Dec/24 ]
<em>0203-5177855-0554.pdf  (156 kB)</em>
<em>0203-5177855-0578.pdf  (152 kB)</em>
<em>0203-5177855-0588.pdf  (157 kB)</em>
<em>0203-5177855-3801.pdf  (157 kB)</em>
<em>***********-0554.pdf  (156 kB)</em>
<em>***********-0578.pdf  (156 kB)</em>
<em>***********-0588.pdf  (156 kB)</em>
<em>***********-3801.pdf  (156 kB)</em>
<em>***********-0578.pdf  (152 kB)</em>
<em>***********-0578.pdf  (153 kB)</em>
Comment by Fabio Dalmo [ 05/Dec/24 ]
Thanks for the quick reply, Keli.
Yes, we are indeed looking into Target payments
Thanks for your assistance for now!
Best Regards,
Fabio Dalmo Import/Export Customs Manager
Comment by Maria Keshwala [ 06/Dec/24 ]
Helo Favio
Your files have been acknowledge with target they have all the documents on file if you are waiting to get paid that is something you need to handle with Target directly we only make sure the files gets transferred to them not following up on accounts receivable. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ul>
<h4 id="cs-33927-adding-new-invoice-created-04-dec-24-updated-16-dec-24-resolved-12-dec-24">[CS-33927] Adding new invoice Created: 04/Dec/24  Updated: 16/Dec/24  Resolved: 12/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: Ray Kaiser Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified Original estimate: Not Specified
Attachments:  Total amount not including sales tax.jpg
Request Type: Support
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
I was creating December invoices for AHN and the invoicing totals are acting different this month. When I add sales tax it is not adding it to the total. See attachment as an example from previous month when it was working properly. The line item totals is 1350.00 and then sales tax is 94.50 so total is 1444.50. Now when I do it for December, the grand total is still 1350.00.
Comments
Comment by Jira Service Management Widget [ 04/Dec/24 ]
Comment by Lauren Cohn [ 06/Dec/24 ]
Hi Ray,
Thank you for bringing this to our attention. I will have our developers review this issue and see if we can get a resolution in place. Quick question: when you hit save and exit the document, does the total update within the draft folder or does it stay at 1350 on your end?
Best,
Lauren Cohn | Technical Support Representative DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
If urgent, please call us with the ticket number and details of the issue.
Comment by Ray Kaiser [ 06/Dec/24 ]
Hi Lauren,
Thanks for looking into this issue. So when I click save it doesn’t update the total but when I go to the drafts and bring it back up then the total is correct. I assume I can do this work around for now?
Also, it is just the Tax Information area where it is not updating while creating the invoice. If you add a charge to the right of the Tax Information box that works fine still.
Thanks for your help,
Ray
Comment by Lauren Cohn [ 10/Dec/24 ]
Hi Ray,
That is correct, the save and exit option is just a quicker way to confirm the current status/total. However, I am coming back in with another update. I received word from development that a fix has been located and tested successfully. The plan currently is to push the solution to production on Thursday. I will keep a close eye on your account and let you know the moment I see that the calculation issue has been rectified. Please let me know if you need anything else in the meantime. I hope you have a great day!
Warm Regards,
Lauren Cohn | Technical Support Representative DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
If urgent, please call us with the ticket number and details of the issue.
Comment by Maria Keshwala [ 12/Dec/24 ]
Hello Ray
Just following up on Lauren’s ticket she is out for a few months - if you continue experiencing any issues please advise this issue was resolved by our dev team I will closed this ticket if you continue to have issues please advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Ray Kaiser [ 12/Dec/24 ]
Thank you, I was able to work around the issue with her suggestion so everything worked out.
Comment by Maria Keshwala [ 16/Dec/24 ]
ok great thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33923-fw-edi-issue-created-04-dec-24-updated-06-dec-24-resolved-06-dec-24">[CS-33923] FW: EDI issue Created: 04/Dec/24  Updated: 06/Dec/24  Resolved: 06/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.png      image004.png
Request Type: Emailed request
Request language: English
Request participants: None Organizations: Label: Missing documents FTP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
We have another 0 order today in our inbound folder, please see below.
From: Edward Morgan <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, December 4, 2024 1:28 PM To: Nathan Harris <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: EDI issue
Got another one
From: Edward Morgan Sent: Wednesday, December 4, 2024 9:14 AM To: Nathan Harris <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: EDI issue
And another one!
From: Nathan Harris <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, December 4, 2024 7:47 AM To: Edward Morgan <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: EDI issue
You the man, Ed. Thanks.
From: Edward Morgan <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, December 4, 2024 7:01:09 AM To: Nathan Harris &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: EDI issue
Good morning Nate,
Here is another one from this morning.
Thanks,
Ed Morgan
Rangaire MFG Company
800-325-8351
817-556-6530
<a href="http://www.rangairemfg.com">www.rangairemfg.com</a>
(((Effective September 1, 2024
Customer Service Hours will change to:
Monday-Thursday 6:30am-5:00pm CST
(((Closed Friday/Saturday/Sunday
Visit us on Facebook
<a href="https://www.facebook.com/jensenmedicinecabinets/">https://www.facebook.com/jensenmedicinecabinets/</a>
!image004.png|thumbnail!
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 04/Dec/24 ]
Comment by Maria Keshwala [ 06/Dec/24 ]
Hello
I will look into this and advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 06/Dec/24 ]
We sent the file back to you with Bytes on the same day also I just restaged it
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33921-fw-cat47-tp-eh-baare-created-04-dec-24-updated-05-dec-24-resolved-05-dec-24">[CS-33921] FW: CAT47 TP- EH Baare Created: 04/Dec/24  Updated: 05/Dec/24  Resolved: 05/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-20241205-140146.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Maria – hey on this project. It has we were at 6 month cutoff would like to increase to 9 month cutoff. Please just let “all” data roll in for this one CAT trading partner.
Travis Crumrin
500 Heath Toffee Ave. Robinson, IL 62454
O 618-546-1575 x114 | C 618-554-6996
From: DocuSign NA3 System <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, December 4, 2024 1:07 PM To: Travis Crumrin <a href="mailto:<EMAIL>"><EMAIL></a> Subject: CAT47 TP- EH Baare
DataTrans Projects sent you a document to review and sign.</p>
<ul>
<li>REVIEW DOCUMENT *
DataTrans Projects <a href="mailto:<EMAIL>"><EMAIL></a>|
Please work auth for all CAT Data - thank you
Powered by | | Do Not Share This Email This email contains a secure link to Docusign. Please do not share this email, link, or access code with others.
Alternate Signing Method Visit Docusign.com, click &#39;Access Documents&#39;, and enter the security code: 7EA861CFF8DA4AEA80A6923E4BCE5EA33 About Docusign Sign documents electronically in just minutes. It&#39;s safe, secure, and legally binding. Whether you&#39;re in an office, at home, on-the-go – or even across the globe – Docusign provides a professional trusted solution for Digital Transaction Management™. Questions about the Document? If you need to modify the document or have questions about the details in the document, please reach out to the sender by emailing them directly.
Stop receiving this email Report this email or read more about Declining to sign and Managing notifications.
If you have trouble signing, visit &#34;[How to Sign a Document|https://support.docusign.com/s/articles/How-do-I-sign-a-DocuSign-document-Basic-Signing? language=en_US&amp;utm_campaign=GBL_XX_DBU_UPS_2211_SignNotificationEmailFooter&amp;utm_medium=product&amp;utm_source=postsend]&#34; on our Docusign Support Center, or browse our Docusign Community for more information.
Download the Docusign App <a href="https://www.docusign.com/features-and-benefits/mobile?utm_campaign=GBL_XX_DBU_UPS_2211_SignNotificationEmailFooter&amp;utm_medium=product&amp;utm_source=postsend">https://www.docusign.com/features-and-benefits/mobile?utm_campaign=GBL_XX_DBU_UPS_2211_SignNotificationEmailFooter&amp;utm_medium=product&amp;utm_source=postsend</a>
This message was sent to you by DataTrans Projects who is using the Docusign Electronic Signature Service. If you would rather not receive email from this sender you may contact the sender with your request.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 04/Dec/24 ]
Comment by Maria Keshwala [ 04/Dec/24 ]
Hello Travis I will look into this thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Hello Travis all CATs TP or just this TP just double checking I do see is for 9 months on the work authorization please advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Ok is only setup for this TP an analyst will work on this,. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
also requested all data roll instead of the 9 months cut off per your request an analyst will reach out to make sure everything is ok. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 05/Dec/24 ]
Only the CAT47 TP, yes work authorization had 9 months, please allow all data to roll in no cutoff at all.
Thanks,
Travis Crumrin
500 Heath Toffee Ave. Robinson, IL 62454
O 618-546-1575 x114 | C 618-554-6996
Comment by Maria Keshwala [ 05/Dec/24 ]
yep I have advise the analyst this morning thank you Travis
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ul>
<h4 id="cs-33914-nubridges-alert-j146e-error-processing-datatran3pbr5f0001-created-04-dec-24-updated-12-dec-24-resolved-12-dec-24">[CS-33914] nuBridges alert: J146E error processing - DATATRAN3PBR5F0001 Created: 04/Dec/24  Updated: 12/Dec/24  Resolved: 12/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
To our valued customer,
The following document(s) have been rejected by our network service due to the reason(s) listed below. Please correct the document and resend or contact our Customer Service team at <a href="mailto:<EMAIL>"><EMAIL></a> or 1-866-830-3600, option 2.
Thank you, Liaison Customer Service
CustID: DATATRAN3P, MsgID: BR5F0001 DlvrID: STERLING3P Date: 241204, Time: 093336 SenderID: ZZDTS6141, ReceiverID: ZZDLTR ControlNum: 00000000000001 ErrCode: J146E ErrText: ERROR=INVALID OR UNKNOWN SENDER ID
Comments
Comment by Maria Keshwala [ 12/Dec/24 ]
there are no reference number identifying which documents has an issue only the sender ID
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33865-fw-milcut-edi-enablement-b8874y0-created-03-dec-24-updated-09-dec-24-resolved-09-dec-24">[CS-33865] FW: Milcut - EDI Enablement B8874Y0 Created: 03/Dec/24  Updated: 09/Dec/24  Resolved: 09/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Glen Houghtaling Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image002.png      image004.png      image001.png      image003.jpg      image005.png      D20241114T090657.AE810MCT.2024111408525083     D20241030T085829.AE810MCT.2024103008520618      image-20241205-210334.png      image-20241205-210755.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Global Shop, Invoice errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello,
Caterpillar EDI is inquiring about issues with two invoices. The issue is the PO line item number is missing or incorrect. I need to know if this is a Datatrans or Global Shop issue. I have attached the 810 data from Global Shop.
Invoice 298634 - IT1<em>10</em>PC<em>25.85</em>PO<em>HESV45633</em>BP<em>313-8279 - The PO line item number is missing
Invoice 299070 - IT1</em>*************<em>PC</em>25.90<em>PO</em>HESV92599<em>BP</em>111-7090 - The PO line item number is incorrect
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
From: Caterpillar EDI Enablement <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, December 3, 2024 5:55 AM To: Glen Houghtaling <a href="mailto:<EMAIL>"><EMAIL></a>; Keith Hadjuk <a href="mailto:<EMAIL>"><EMAIL></a>; Elijah Anwey <a href="mailto:<EMAIL>"><EMAIL></a>; &#39;Brennan Desper&#39; <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Mike Ries <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Milcut - EDI Enablement B8874Y0
Hi @Glen Houghtaling,
Kindly please advise on the below email.
Thanks &amp; Regards,
Aishwarya S (AS)
Process Solutions Center
Strategic Procurement Division
Caterpillar Inc
Email ID : <a href="mailto:<EMAIL>"><EMAIL></a>
Desk Phone +91 44 7106 9019
EDI Enablement Americas &amp; EAME support - <a href="mailto:<EMAIL>"><EMAIL></a>
Click HERE to get our EDI training documentations &amp; X12/EDIFACT guidelines
Caterpillar: Confidential Green
From: Caterpillar EDI Enablement Sent: 23 November 2024 23:07 To: Glen Houghtaling <a href="mailto:<EMAIL>"><EMAIL></a>; Keith Hadjuk <a href="mailto:<EMAIL>"><EMAIL></a>; Elijah Anwey <a href="mailto:<EMAIL>"><EMAIL></a>; &#39;Brennan Desper&#39; <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Mike Ries <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Milcut - EDI Enablement B8874Y0
Hi @Glen Houghtaling @Keith Hadjuk,
I again validated your recent invoice and still the PO line item number is incorrect.
Please fix this issue so that invoice wont get rejected.
ISA<em>00</em> 00 ZZ<em>B8874Y0 <em>09</em>00507047919 <em>241114</em>0928</em>U<em>00200</em>000000749<em>0</em>P~ ST<em>830</em>17472
GS<em>IN</em>B8874Y0<em>19</em>241114<em>0928</em>749<em>X</em>003030 BFR<em>05</em>3241<em>SH</em>A<em>250826</em>250826<em>241119**HESV92599
ST</em>810<em>0657 N1</em>PN<em>MORTON</em>92<em>47
BIG</em>241113<em>299070**HESV92599 N1</em>SU<em>MILCUT, INC.<em>92</em>B8874Y0
CUR</em>SE<em>USD N1</em>ST<em>CATERPILLAR INC 47-52</em>92<em>DT4752
REF</em>PK<em>260668 N2</em>C/O SC2 AIRPORT
N1<em>SE</em>MILCUT<em>92</em>B8874Y0 N3<em>6409 WEST SMITHVILLE ROAD
N1</em>ST<em>CATERPILLAR INC 47-52</em>92<em>DT4752 N4</em>BARTONVILLE<em>IL</em>61607<em>US
N1</em>SF<em>MILCUT</em>92<em>B8874Y0 LIN</em>1<em>BP</em>111-7090<em>EC</em>05<em>PD</em>LINER<em>RN</em>83
DTM<em>011</em>241113 UIT<em>PC
IT1</em>*************<em>PC</em>25.90<em>PO</em>HESV92599<em>BP</em>111-7090 - The PO line item number is incorrect MEA<em>WT</em>U<em>1.58</em>LB
TDS<em>103600 PER</em>SC<em>DHANVANTRI YR</em>TE<em>91 80 3755 8303
CTT</em>1 SDP<em>Y</em>Z
SE<em>12</em>0657 FST<em>40</em>D<em>D</em>250826
GE<em>1</em>749 SHP<em>01</em>40<em>035</em>241115
IEA<em>1</em>000000749 SHP<em>02</em>142<em>004</em>230519<strong>241119
CTT<em>1</em>40
SE<em>18</em>17472
Thanks &amp; Regards,
Aishwarya S (AS)
Process Solutions Center
Strategic Procurement Division
Caterpillar Inc
Email ID : <a href="mailto:<EMAIL>"><EMAIL></a>
Desk Phone +91 44 7106 9019
EDI Enablement Americas &amp; EAME support - <a href="mailto:<EMAIL>"><EMAIL></a>
Click HERE to get our EDI training documentations &amp; X12/EDIFACT guidelines
From: Caterpillar EDI Enablement Sent: 05 November 2024 12:24 To: Glen Houghtaling <a href="mailto:<EMAIL>"><EMAIL></a>; Keith Hadjuk <a href="mailto:<EMAIL>"><EMAIL></a>; Elijah Anwey <a href="mailto:<EMAIL>"><EMAIL></a>; Brennan Desper <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Mike Ries <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Milcut - EDI Enablement B8874Y0
Hi @Glen Houghtaling,
I have validated your recent invoice for the PO# HESV45633.
I found error in your invoice. The PO line item number is missing in the IT1 segment of your invoice.
Please fix this in your mapping so that your future won’t get affected.
810 830
ISA<em>00</em> 00 ZZ<em>B8874Y0 <em>09</em>00507047919 <em>241030</em>0907</em>U<em>00200</em>000000738<em>0</em>P~
ISA<em>00</em> 00 09<em>00507047947 <em>ZZ</em>B8874Y0 <em>241029</em>0549</em>U<em>00200</em>000001692<em>0</em>P{color} | | GS<em>IN</em>B8874Y0<em>19</em>241030<em>0907</em>738<em>X</em>003030
GS<em>PS</em>47<em>B8874Y0</em>241029<em>0549</em>1582<em>X</em>002003
ST<em>810</em>0646 ST<em>830</em>17070
BIG<em>241029</em>298634</strong>HESV45633 BFR<em>05</em>3031<em>SH</em>A<em>241105</em>250513<em>241029**HESV45633
CUR</em>SE<em>USD N1</em>PN<em>MORTON</em>92<em>47
REF</em>PK<em>260141 N1</em>SU<em>MILCUT, INC.<em>92</em>B8874Y0
N1</em>SE<em>MILCUT</em>92<em>B8874Y0 N1</em>ST<em>CATERPILLAR INC 47-52</em>92<em>DT4752
N1</em>ST<em>CATERPILLAR INC 47-52</em>92<em>DT4752 N2</em>C/O SC2 AIRPORT
N1<em>SF</em>MILCUT<em>92</em>B8874Y0 N3<em>6409 WEST SMITHVILLE ROAD
DTM</em>011<em>241029 N4</em>BARTONVILLE<em>IL</em>61607<em>US
IT1</em>10<em>PC</em>25.85<em>PO</em>HESV45633<em>BP</em>313-8279 - The PO line item number is missing
LIN<em>1</em>BP<em>313-8279</em>EC<em>01</em>PD<em>INSULATION</em>DR<em>Q</em>RN<em>66
TDS</em>25850 UIT<em>PC
CTT</em>1 MEA<em>WT</em>U*.436<em>LB
SE</em>12<em>0646 PER</em>SC<em>DHANVANTRI YR</em>TE<em>91 80 3755 8303
GE</em>1<em>738 SDP</em>Y<em>Z
IEA</em>1<em>000000738 FST</em>10<em>C</em>D<em>241105
FST</em>10<em>D</em>D<em>250513
SHP</em>01<em>5</em>035<em>241009
SHP</em>02<em>15</em>004<em>230725**241029
NTE</em>DEL<em>SHIP NO MORE THAN 5 DAYS PRIOR TO SHIP DATE
CTT</em>1<em>20
SE</em>20*17070
Thanks &amp; Regards,
Aishwarya S (AS)
Process Solutions Center
Strategic Procurement Division
Caterpillar Inc
Email ID : <a href="mailto:<EMAIL>"><EMAIL></a>
Desk Phone +91 44 7106 9019
EDI Enablement Americas &amp; EAME support - <a href="mailto:<EMAIL>"><EMAIL></a>
Click HERE to get our EDI training documentations &amp; X12/EDIFACT guidelines
From: Caterpillar EDI Enablement Sent: 03 June 2024 12:17 To: Glen Houghtaling <a href="mailto:<EMAIL>"><EMAIL></a>; Keith Hadjuk <a href="mailto:<EMAIL>"><EMAIL></a>; Elijah Anwey <a href="mailto:<EMAIL>"><EMAIL></a>; Brennan Desper <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Mike Ries <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Milcut - EDI Enablement B8874Y0
Hi @Glen Houghtaling,
Thanks for your email.
@Elijah Anwey,
Since the EDI set up has been completed could you please trigger the schedules in EDI for Facility R8 so that we could validate their first live ASN and invoice and complete the request.
Thanks &amp; Regards,
Aishwarya S (AS)
Process Solutions Center
Strategic Procurement Division
Caterpillar Inc
Email ID : <a href="mailto:<EMAIL>"><EMAIL></a>
Desk Phone +91 44 7106 9019
EDI Enablement Americas &amp; EAME support - <a href="mailto:<EMAIL>"><EMAIL></a>
Click HERE to get our EDI training documentations &amp; X12/EDIFACT guidelines
From: Glen Houghtaling <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 29, 2024 11:35 PM To: Keith Hadjuk <a href="mailto:<EMAIL>"><EMAIL></a>; Elijah Anwey <a href="mailto:<EMAIL>"><EMAIL></a>; Brennan Desper <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Mike Ries <a href="mailto:<EMAIL>"><EMAIL></a>; Aishwarya Sankar <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Milcut - EDI Enablement
CAUTION: EXTERNAL EMAIL This is a message from <a href="mailto:<EMAIL>"><EMAIL></a>. Use caution when opening unexpected emails and do not click on links or attachments from unknown senders. For more resources, visit security.cat.com/phishing.
Hello,
I received the trading partner ID for Cat R8 today. Milcut has updated Global Shop. We are ready for EDI transactions.
Sincerely,
Glen Houghtaling
A/R Accounting Clerk
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
CUSTOMERS : Send Remittance Advice / Inquiries to: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Keith Hadjuk <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 29, 2024 12:04 PM To: Elijah Anwey <a href="mailto:<EMAIL>"><EMAIL></a>; Glen Houghtaling <a href="mailto:<EMAIL>"><EMAIL></a>; Brennan Desper <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Mike Ries <a href="mailto:<EMAIL>"><EMAIL></a>; Aishwarya Sankar <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Milcut - EDI Enablement
Glen
I think this would be you but let me know if you want assign to someone else? Thx.
Regards,
Keith Hadjuk
Regional Sales Manager
Blachford Acoustics Group
************ Mobile
<a href="mailto:<EMAIL>"><EMAIL></a>
100 Years of Success!
From: Elijah Anwey <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, May 29, 2024 10:53 AM To: Glen Houghtaling <a href="mailto:<EMAIL>"><EMAIL></a>; Keith Hadjuk <a href="mailto:<EMAIL>"><EMAIL></a>; Brennan Desper <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Mike Ries <a href="mailto:<EMAIL>"><EMAIL></a>; Aishwarya Sankar <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Milcut - EDI Enablement
You don&#39;t often get email from <a href="mailto:<EMAIL>"><EMAIL></a>. [ Learn why this is important
https://urldefense.com/v3/<em>https:/aka.<a class="g3mark-shortlink" href="https://ms.corp.google.com/LearnAboutSenderIdentification">ms/LearnAboutSenderIdentification</a></em>;!Unable to render embedded object: File (FtR4BK4x7WL3xYs) not found.-dybXeS1sndEDljkO7uVSlQ1mq0a63hBAbdssesK6YSsJZ0KY4J-t5LmdOhgeKQ4zyG8uDFb3adZeMdf0FKGLHaHzMD4$]
Good morning Keith and Glen,
Checking in on the below, can you provide an update on the status of your trading partner? We unfortunately will not be able to issue any POs or order parts until EDI is fully enabled.
Thanks and let me know if you have any questions or concerns,
Elijah Anwey
Facility Procurement Professional
Caterpillar Paving, Minneapolis -Earthmoving Division
<a href="mailto:<EMAIL>"><EMAIL></a> (*************
Caterpillar: Confidential Green
Comments  Comment by Glen Houghtaling [ 03/Dec/24 ]
<em>D20241030T085829.AE810MCT.2024103008520618  (3 kB)</em>
<em>D20241114T090657.AE810MCT.2024111408525083  (2 kB)</em>
Comment by Maria Keshwala [ 04/Dec/24 ]
Hello Glen I will look into this thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Hello Glen I do see the PO listed on this invoice thats the IT106, did you add the PO after ? I cant go back to the original file for future ones I should be able to. let m know thanks
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33860-fw-finance-request-fr1221561-has-been-resolved-created-03-dec-24-updated-05-dec-24-resolved-05-dec-24">[CS-33860] FW: Finance Request FR1221561 has been resolved Created: 03/Dec/24  Updated: 05/Dec/24  Resolved: 05/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None Fix versions: None
Type: Support Priority: Medium
Reporter: Michelle Rice Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-********-145004.png      image-********-145258.png      image-********-194950.png      image-********-204806.png      image-********-210848.png      image-********-211000.png      image-********-211547.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Project
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
We are still having issues with CAT England’s 810s. Is the map missing something?
Michelle Rice
Vee Engineering/Air Side Systems
<a href="mailto:<EMAIL>"><EMAIL></a>
765.778.7895 ext. 27
From: Caterpillar EDI Enablement <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, December 2, 2024 2:51 AM To: <a href="mailto:<EMAIL>"><EMAIL></a>; Michelle Rice <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Finance Request FR1221561 has been resolved
Hi @Michelle Rice,
When I validate the invoice I see the type of invoice is missing in the BIG 07 segment.
I assume this may be the reason why the invoice is not processed.
ISA<em>00</em> 00 ZZ<em>E9655D0 <em>09</em>00507047934 <em>241112</em>1322</em>U<em>00200</em>000000143<em>0</em>P~
GS<em>IN</em>E9655D0<em>34</em>241112<em>1322</em>143<em>X</em>003030
ST<em>810</em>0104
BIG<em>240326</em>253579A<strong>QSLL62345 – Type of invoice missing in the BIG 07.
CUR<em>SE</em>USD
REF<em>PK</em>299031
N1<em>SE</em>VEE ENGINEERING<em>92</em>E9655D0
N1<em>ST</em>CATERPILLAR UK LTD - BCP<em>92</em>DT3402
N1<em>SF</em>VEE ENGINEERING<em>92</em>E9655D0
DTM<em>011</em>240326
IT1<em>1</em>360<em>EA</em>49.83</strong>BP<em>5799240
TDS</em>1793880
CTT<em>1
SE</em>12<em>0104
GE</em>1<em>143
IEA</em>1<em>000000143
Thanks &amp; Regards,
Aishwarya S (AS)
Process Solutions Center
Strategic Procurement Division
Caterpillar Inc
Email ID : <a href="mailto:<EMAIL>"><EMAIL></a>
Desk Phone +91 44 7106 9019
EDI Enablement Americas &amp; EAME support - <a href="mailto:<EMAIL>"><EMAIL></a>
Click HERE to get our EDI training documentations &amp; X12/EDIFACT guidelines
Caterpillar: Confidential Green
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: 29 November 2024 20:25 To: Caterpillar EDI Enablement <a href="mailto:<EMAIL>"><EMAIL></a>; Michelle Rice <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Finance Request FR1221561 has been resolved
For Caterpillar employees - To verify the authenticity of this email, visit Cat@work and search for: 9d0%3Gb9w
Your ticket has now been resolved. If this request has not been resolved to your satisfaction, you have 5 days to re-open the ticket by following the link to Caterpillar&#39;s Finance@work portal below, or you can reply directly to this email. If you do not respond within 5 days, the request will be automatically closed.
If you are interested in sharing your feedback on your experience with this Finance Request, please click HERE
Resolution notes:
Additional Comments:
2024-11-14 11:22:33 CST - Michelle Rice Additional comments
Suspended request has been resumed by requester comments.
2024-11-14 11:22:33 CST - Michelle Rice Additional comments
I see the attachments. I still don&#39;t know what is missing. Can someone tell me what information needs to be added?
2024-11-14 10:47:48 CST - Sreerag NC Additional comments
Hi Michelle,
Please be informed that the invoice #253579A, which was submitted via EDI, has failed again. Please see the details of the previously processed invoice #255028 in the attached screenshot from the system. Additionally, the screenshot for the recently received failed invoice is missing key details, which is why the invoice is not being processed.
Regards, Global Delivery Centre Caterpillar Global Business Services
2024-11-12 13:23:36 CST - Michelle Rice Additional comments
The 810 has been resent. Thank you
2024-11-11 02:23:20 CST - Chaithra SB Additional comments
Hi Michelle,
Could you please resubmit your invoice with suffix &#39;A&#39; to avoid duplicate u=in EDI system.
Regards, Chaithra SB
Short Description: Inquiry on payment status
Description: &#34;Please describe how we can help you and provide us with additional information: Purchase Order Number: Invoice Submission Method: Ship Date: Delivery Note Number: Document number (if already posted)
Unsubscribe</em> | Notification Preferences*
<em>Changes you make will impact all Notifications you receive in the future
Ref:MSG350979081_gnRh5Y6PtceCLlCfZT
Comments
Comment by Michelle Rice [ 03/Dec/24 ]
Comment by Michelle Rice [ 03/Dec/24 ]
I just learned that Baare is not having issues with CAT England. Can we compare the maps?
Michelle Rice
Vee Engineering/Air Side Systems
<a href="mailto:<EMAIL>"><EMAIL></a>
765.778.7895 ext. 27
Comment by Sandy Karidas [ 03/Dec/24 ]
Hello Michelle,
Maria is out today. I will look into this and get back to you.
Regards,
Sandy Karidas
DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Michelle Rice [ 04/Dec/24 ]
Thank you
Michelle Rice
Vee Engineering/Air Side Systems
<a href="mailto:<EMAIL>"><EMAIL></a>
765.778.7895 ext. 27
Comment by Maria Keshwala [ 04/Dec/24 ]
CAT = Could you please resubmit your invoice with suffix &#39;A&#39; to avoid duplicate u=in EDI system. the invoice #253579A, which was submitted via EDI, has failed again. Please see the details of the previously processed invoice #255028 in the attached screenshot from the system. Additionally, the screenshot for the recently received failed invoice is missing key details, which is why the invoice is not being processed.
MISSING THE BIG07 SEGMENT
When I validate the invoice I see the type of invoice is missing in the BIG 07 segment.
I assume this may be the reason why the invoice is not processed.
ISA</em>00* 00 ZZ<em>E9655D0 <em>09</em>00507047934 <em>241112</em>1322</em>U<em>00200</em>000000143<em>0</em>P~
GS<em>IN</em>E9655D0<em>34</em>241112<em>1322</em>143<em>X</em>003030
ST<em>810</em>0104
BIG<em>240326</em>253579A<strong>QSLL62345 – Type of invoice missing in the BIG 07.
CUR<em>SE</em>USD
REF<em>PK</em>299031
N1<em>SE</em>VEE ENGINEERING<em>92</em>E9655D0
N1<em>ST</em>CATERPILLAR UK LTD - BCP<em>92</em>DT3402
N1<em>SF</em>VEE ENGINEERING<em>92</em>E9655D0
DTM<em>011</em>240326
IT1<em>1</em>360<em>EA</em>49.83</strong>BP<em>5799240
TDS</em>1793880
CTT<em>1
SE</em>12<em>0104
GE</em>1<em>143
IEA</em>1<em>000000143
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
BIG07 Is present on the document in WEBEDI MESSAGE ID 42131738
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
BIG07 present on the document outbound batch ID 139808676-- good file 255028 invoice number
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
TESTING THE GOOD FILE SENT ON 10/28/2024 - VS THE BAD FILE WITH NO BIG07 SEGMENT PRESENT
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
MAP = LINX 582
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
THE INVOICE DATE FROM 11/12/2024 IS 03/26/2024 Looks like it was done manually
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
originally sent 03/26/2024- had the BIG07 ----- This was done manually
<em>ISA_00_ 00 ZZ_E9655D0 _09_00507047934 _241204_1349_U_00200_000000102_0_P~ GS_IN_E9655D0_34_241204_1349_102_X_003030 ST_810_0001 BIG_240326_253579</em>QSLL62345</em>CA CUR_SE_USD REF_PK_299031 N1_SE_VEE ENGINEERING_92_E9655D0 _N3_3805 Reynolds Street N4_Ft. Wayne_IN_46803_US N1_ST_CATERPILLAR UK LTD 3400 (BCP)_92_DT3400 N1_BY_CATERPILLAR UK LTD 3400 (BCP)<em>92_DT3400 N3_CAPE FEAR BONDED WAREHOUSE</em> <em>N4_WILMINGTON_NC_28412 N1_SF_VEE ENGINEERING_92_E9655D0 DTM_011_240326 IT1_1_360_EA_49.83**PO_QSLL62345_BP_5799240</em> PID_F*_Shroud* TDS1793880 CADTZ <em>CTT_1 SE19_0001</em> GE_1_102 _IEA_1<em>000000102</em>
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
The global DOM for CAT34 UKLIM 810s does not have the BIG07.
the Destination DOM on the linx map has it —
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
hello,
I&#39;m back working on this issue. The integrated file map includes the BIG07 segment, which is typically used for integrated files. However, the file with the letter &#34;A&#34; at the end was manually entered in WebEDI, and it follows a different map—not the one you&#39;re integrated with. The original file, sent back in March this year (without the &#34;A&#34;), does include the BIG07 segment.
I’ve asked one of the analysts if it’s possible to add the BIG07 segment to our global map so you can manually process it. However, there are no issues with the integration itself, and this setup is functioning as expected. I will keep you updated.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
Asked support help to see if it is possible to add the BIG07 on the CAT’s 810 Global Map
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Michelle Rice [ 05/Dec/24 ]
Thank you for the update.
Michelle Rice
Vee Engineering/Air Side Systems
<a href="mailto:<EMAIL>"><EMAIL></a>
765.778.7895 ext. 27
Comment by Maria Keshwala [ 05/Dec/24 ]
You welcome a project has been created to add this segment to the global map so you can create the ASNs from WEBEDI. this ticket will be closed the analyst will advise when completed. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Michelle Rice [ 05/Dec/24 ]
Thank you
Michelle Rice
Vee Engineering/Air Side Systems
<a href="mailto:<EMAIL>"><EMAIL></a>
765.778.7895 ext. 27
Comment by Maria Keshwala [ 05/Dec/24 ]
you’re welcome
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Michelle Rice [ 05/Dec/24 ]
I just sent one Message ID : 42554912
Michelle Rice
Vee Engineering/Air Side Systems
<a href="mailto:<EMAIL>"><EMAIL></a>
765.778.7895 ext. 27
Comment by Maria Keshwala [ 05/Dec/24 ]
Just confirmed that went out to CAT Michelle and has BIG07 segment the ones you sent via integration they worked it was delivered Im closing this ticket as I created a project for this
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Michelle Rice [ 05/Dec/24 ]
Thank you
Michelle Rice
Vee Engineering/Air Side Systems
<a href="mailto:<EMAIL>"><EMAIL></a>
765.778.7895 ext. 27</p>
<h4 id="cs-33846-re-error-asn-rejected_errors-in-the-message_incorrect-nad-st-created-03-dec-24-updated-12-dec-24-resolved-11-dec-24">[CS-33846] Re: Error: ASN rejected_Errors in the message_Incorrect NAD+ST Created: 03/Dec/24  Updated: 12/Dec/24  Resolved: 11/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified Original estimate: Not Specified
Attachments:  image001.png      image002.jpg      image-20241211-164101.png      image-20241211-164142.png      image-20241211-164101 (092d2c85-2cfe-4ceb-bdeb-6f531fecfd53).png      image-20241211-164142 (70e23201-893e-4d9a-944e-8684b79460c6).png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi...
I&#39;ve copied in the DataTrans Support team to review this issue.
Can you advise which vendor this is for? And what ZF division this is for?
Thanks,
Krista
On 12/3/2024 1:25 AM, Bardos Eva EGR FIXE32 wrote:
Dear EDI Partner,
We received your message but it was failed due to errors in it and incorrect NAD+ST.
UNB+UNOA:3+4103089201:12+0013000046ZF-AG-TOR+241129:1345+11580++DESADV&#39;
UNH+1+DESADV :07A:UN:GAVF13&#39;
BGM+351+24TL065+9&#39;
DTM+137:20241129:102&#39;
RFF+CRN:24TL065&#39;
NAD+SE+0000236024::92+Fawn Mexico, Inc.&#39;
NAD+SF+0000236024::92+Fawn Mexico, Inc.&#39;
NAD+ST+236024::92+TRW Automotive - La Laguna&#39;
LOC+11+MAIN&#39;
TDT+12+30+FDEG::92:PROPIO&#39;
EQD+TE+24TL065&#39;
CPS+1++4&#39;
PAC+1+:35:AAD+CARTN::92+F:34126808:SA&#39;
QTY+52:675:PCE&#39;
PCI+17+++6J::5&#39;
LIN+1++34126808AGD:IN&#39;
QTY+12:675:PCE&#39;
ALI+DE&#39;
RFF+AAU:24TL065:00020&#39;
UNT+19+1&#39;
UNZ+1+11580&#39;
Could you please correct and resend it?
Thank you,
Kind regards,
Éva Bárdos
Data Exchange
EDI Support Services (FIXE32)
ZF Group
ZF Hungária Kft.
3300 Eger Ungarn/Hungary
<a href="mailto:<EMAIL>"><EMAIL></a>
Vorsitzender des Aufsichtsrats/Chairman of the Supervisory Board: Dr. Heinrich Hiesinger
Vorstand/Board of Management: Dr. Holger Klein (Vorsitzender/CEO), Dr. Lea Corzilius, Michael Frick, Dr. Peter Holdmann, Prof. Dr. Peter Laier
Sitz/Headquarters: Friedrichshafen Handelsregistereintrag Amtsgericht Ulm HRB 630206/Trade register of the municipal court of Ulm HRB 630206
Informationen zur Verarbeitung Ihrer Daten und zu Ihren Rechten erhalten Sie jederzeit in unseren Datenschutzhinweisen: <a href="https://www.zf.com/de/data-protection-notice">https://www.zf.com/de/data-protection-notice</a> You can find information about how we process your data and your rights in our data protection notice: <a href="https://www.zf.com/en/data-protection-notice">https://www.zf.com/en/data-protection-notice</a>
Comments
Comment by Krista Johnson [ 03/Dec/24 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 04/Dec/24 ]
Dear Krista,
The vendor is Fawn Mexico Inc. (LSID: 236024) . The ZF plant is RI/RB El Paso all (US) (Plant code: 411A)
Thank you for your support!
Kind regards,
Éva Bárdos
Data Exchange
EDI Support Services (FIXE32)
ZF Group
ZF Hungária Kft.
3300 Eger Ungarn/Hungary
<a href="mailto:<EMAIL>"><EMAIL></a>
Vorsitzender des Aufsichtsrats/Chairman of the Supervisory Board: Dr. Heinrich Hiesinger
Vorstand/Board of Management: Dr. Holger Klein (Vorsitzender/CEO), Dr. Lea Corzilius, Michael Frick, Dr. Peter Holdmann, Prof. Dr. Peter Laier
Sitz/Headquarters: Friedrichshafen Handelsregistereintrag Amtsgericht Ulm HRB 630206/Trade register of the municipal court of Ulm HRB 630206
Informationen zur Verarbeitung Ihrer Daten und zu Ihren Rechten erhalten Sie jederzeit in unseren Datenschutzhinweisen: <a href="https://www.zf.com/de/data-protection-notice">https://www.zf.com/de/data-protection-notice</a> You can find information about how we process your data and your rights in our data protection notice: <a href="https://www.zf.com/en/data-protection-notice">https://www.zf.com/en/data-protection-notice</a>
Comment by Sandy Karidas [ 05/Dec/24 ]
Hello Eva,
I am working on this and will get back to you.
Regards,
Sandy Karidas
DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Maria Keshwala [ 11/Dec/24 ]
The assigned by buyer is = Ship to location in the 830
on the 856
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 11/Dec/24 ]
Hi Stephanie
I see on the 830 the release issuer has a different assigned by buyer location 498C but please double check with your TP as to whats the correct Ship to location if is based on what it is on the 830 then you send an invalid ship to location
830
856
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 11/Dec/24 ]
This location according to their POs is 412A.
236024 is our ZF assigned vendor number.
Regards,
Stephanie Brinegar
IT Administrator/Accounting Analyst
Fawn Industries, Inc.
Comment by Maria Keshwala [ 11/Dec/24 ]
Please update and resend the document thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 11/Dec/24 ]
I wasn’t sure which ASN failed, so I resent both ASNs from 12/5 (msg id: ******** &amp; ********)
Regards,
Stephanie Brinegar
IT Administrator/Accounting Analyst
Fawn Industries, Inc.
Comment by Maria Keshwala [ 12/Dec/24 ]
Hi Stephanie I just search by the Reference number on the document they send on this email. but if you corrected and resend should be ok . Thanks
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33845-fw-all-newline-invoices-since-2023-received-12-2-2024-created-03-dec-24-updated-05-dec-24-resolved-05-dec-24">[CS-33845] FW: All newline invoices since 2023 received 12/2/2024 Created: 03/Dec/24  Updated: 05/Dec/24  Resolved: 05/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Chris McLaird Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  ~WRD0001.jpg      image001.png      image002.png      image003.png      image004.png      image005.png      image-20241205-150042.png      image-20241205-150058.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Greetings,
We received an 810 file with all invoices since 2023 for Newline on 12/2. Can you please help us understand what caused that? It causes us additional work to purge them out of our system.
Thank you,
Chris McLaird Director of Enterprise IT | Trafera Phone (*************
Website | LinkedIn | Twitter | Facebook | Instagram | YouTube
Confidentiality Note: This email is confidential and is intended only for the individual(s) to whom it is addressed. Unauthorized use, distribution or disclosure is prohibited. Thank you.
From: Thanh Nguyen <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, December 3, 2024 10:01 AM To: Chelsea Catalano <a href="mailto:<EMAIL>"><EMAIL></a>; Chris McLaird <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Emily Braun <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: All newline invoices since 2023 received 12/2/2024
CAUTION: This email originated outside of Trafera. If you do not recognize this sender, do not click on any links or open any attachments. If the sender or content below seems suspicious, please report this email using our built-in report button. |
Hello Chelsea/Chris,
We only sent out 2 Invoices on 12/02. There is no other transaction.
Chris,
Did you see duplicated EDI transaction coming in on your end recently or your system just get a load of Invoice data? Please check with your connection provider for further investigation.
SPS Commerce Infinite Retail Power
Houston Office-Covalent team
Thanh Nguyen
Senior Consultant
<a href="mailto:<EMAIL>"><EMAIL></a>
Office: ************
Phone: ************
From: Chelsea Catalano <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, December 3, 2024 9:29 AM To: Chris McLaird <a href="mailto:<EMAIL>"><EMAIL></a>; Thanh Nguyen <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Emily Braun <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: All newline invoices since 2023 received 12/2/2024
Very funky.
Thanh,
Do you have any insight on why 810’s were re-pushed yesterday?
Thank you,
<a href="http://www.newline-interactive.com/usa">http://www.newline-interactive.com/usa</a>
<a href="https://www.facebook.com/newlineinteractive">https://www.facebook.com/newlineinteractive</a>
<a href="https://www.linkedin.com/company/newline-interactive/">https://www.linkedin.com/company/newline-interactive/</a>
<a href="https://twitter.com/NewlineIDEAMAX">https://twitter.com/NewlineIDEAMAX</a>
<a href="https://www.youtube.com/c/NewlineInteractive">https://www.youtube.com/c/NewlineInteractive</a> | | Chelsea Catalano Accounting Manager |
Office: 972-468-9728
Direct: 972-468-9708
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
Address: 950 W Bethany Dr, Ste 330 Allen, TX 75013
Web: <a href="http://www.newline-interactive.com/usa">www.newline-interactive.com/usa</a>
From: Chris McLaird <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, December 3, 2024 8:44 AM To: Chelsea Catalano <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Emily Braun <a href="mailto:<EMAIL>"><EMAIL></a> Subject: All newline invoices since 2023 received 12/2/2024
Good Morning,
Yesterday we received 810s for what appears to be all orders since late 2023. I’m assuming this was in error, but please confirm. If possible, we would like to avoid this from happening again as we need to purge them.
Please let me know if I can provide any additional information. Thank you!
Chris McLaird Director of Enterprise IT
Toll-Free (*************
General (*************
[ Website|https://link.edgepilot.com/s/39e1b798/RIRVQV-Wf0_SmkHXb_iA3w?u=https://www.trafera.com/] | [ LinkedIn|https://link.edgepilot.com/s/9498ae6b/iH5d3YTD7EGyP9bw3XvjQQ? u=https://www.linkedin.com/company/traferaofficial/] | [ Twitter|https://link.edgepilot.com/s/455ba852/xOa69NJ1lEmhOuzMDkfEQg?u=https://twitter.com/TraferaOfficial/] | [ Facebook|https://link.edgepilot.com/s/62a579cf/LntIXa9PU0ytqko2RzcMaA?u=https://www.facebook.com/TraferaOfficial/] | [ Instagram|https://link.edgepilot.com/s/60f017b0/9KEfABGK4EmkmH_asaM3mw? u=https://www.instagram.com/traferaofficial/] | [ YouTube|https://link.edgepilot.com/s/c893c0ab/K4VU_7_p3kSqpo9D5zta5A?u=https://www.youtube.com/channel/UCEXEtAuZCcjUQrb6rptUEqQ]
<a href="https://link.edgepilot.com/s/073dec51/kkZsV10G50mWeGdzJnZ9tQ?u=https://www.trafera.com/signature-special/">https://link.edgepilot.com/s/073dec51/kkZsV10G50mWeGdzJnZ9tQ?u=https://www.trafera.com/signature-special/</a>
Confidentiality Note: This email and any files transmitted with it are confidential and are to be viewed solely by the individual(s) to whom they are addressed. Unauthorized use, distribution or disclosure of the contents of this email is strictly prohibited. If there is reason to believe that you are not the intended recipient, please notify the sender immediately and destroy all copies of this email and any files it contained. Thank you.
Comments
Comment by Chris McLaird [ 03/Dec/24 ]
Comment by Maria Keshwala [ 04/Dec/24 ]
Hello Chris I will check on my end and advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Chris McLaird [ 05/Dec/24 ]
good morning, this file was apparently sent during a test we were conducting with Dell 810s. I don’t know why/how it happened, but it does not appear as though Newline themselves sent this 810, the file was coming independently from Datatrans.
Comment by Maria Keshwala [ 05/Dec/24 ]
Hi Chris Thank you for the additional Information I will keep you posted as soon as I hear back from IT
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Hi Chris I only see two invoices on my end that were sent to you they are not from 2023 maybe it was a different issue that was sent by an error. but if it happens again please let us know. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33838-fwd-connection-info-needed-lidl-us-llc-rw-sauder-inc-created-03-dec-24-updated-04-dec-24-resolved-04-dec-24">[CS-33838] Fwd: Connection Info Needed (Lidl US, LLC): RW SAUDER INC Created: 03/Dec/24  Updated: 04/Dec/24  Resolved: 04/Dec/24</h4>
<h2 id="status-resolved-project-customer-support-components-none-affects-versions-none-fix-versions-none-type-support-priority-medium-reporter-timj-saudereggs-com-assignee-maria-keshwala-resolution-done-votes-0-labels-none-remaining-estimate-not-specified-time-spent-not-specified-original-estimate-not-specified-request-type-emailed-request-request-language-english-request-participants-organizations-none-csat-comment-webhookresponse-body-value-comment-description-can-you-please-make-sure-our-connection-w-loren-has-been-updated-per-this-email-forwarded-message-from-edi-testing-editesting-spscommerce-com-date-tue-dec-3-2024-at-3-53-am-subject-connection-info-needed-lidl-us-llc-rw-sauder-inc-to-timj-saudereggs-com-timj-saudereggs-com-hello-rw-sauder-inc-you-received-a-comm-last-week-regarding-an-upcoming-migration-to-sps-commerce-via-as2-or-ftp-within-5-business-days-please-reply-to-this-email-with-your-company-s-choice-of-either-as2-or-ftp-and-your-production-isa-id-gs-id-and-qualifier-if-existing-as2-or-ftp-setup-if-you-already-have-an-existing-as2-or-ftp-connection-established-with-sps-commerce-that-you-would-like-to-use-please-respond-directly-to-this-email-thread-referencing-your-company-s-as2-id-and-url-that-was-used-on-this-connection-or-the-name-of-the-ftp-username-if-you-would-like-to-use-as2-but-do-not-have-an-existing-one-already-established-please-reply-to-this-email-with-your-as2-id-your-certificate-and-your-production-as2-url-we-will-then-respond-with-our-information-once-your-connection-is-established-our-system-handles-only-base64-certificates-please-note-that-we-are-not-cutting-over-to-this-new-connection-yet-you-will-receive-another-communication-at-least-a-week-prior-to-notify-you-of-the-upcoming-cutover-thank-you-edi-testing-certification-teamretail-onboarding-editesting-spscommerce-comsps-commerce-proj-_a02nt000008v8za-proj-ref-_00d30bzv-_500nt0lg8hz-ref">Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: None CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Can you please make sure our connection w/Loren has been updated per this email?
---------- Forwarded message ---------From: EDI Testing <a href="mailto:<EMAIL>"><EMAIL></a> Date: Tue, Dec 3, 2024 at 3:53 AM Subject: Connection Info Needed (Lidl US, LLC): RW SAUDER INC To: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a>
Hello RW SAUDER INC,
You received a comm last week regarding an upcoming migration to SPS Commerce via AS2 or FTP.
Within 5 business days, please reply to this email with your company&#39;s choice of either AS2 or FTP and your Production ISA ID, GS ID, and qualifier.
If Existing AS2 or FTP Setup: If you already have an existing AS2 or FTP connection established with SPS Commerce that you would like to use please respond directly to this email thread (referencing your company&#39;s AS2 ID and URL that was used on this connection) or the name of the FTP Username.
If you would like to use AS2, but do not have an existing one already established, please reply to this email with your AS2 ID, your certificate, and your production AS2 URL. We will then respond with our information once your connection is established. Our system handles only Base64 certificates. Please note that we are not cutting over to this new connection yet. You will receive another communication at least a week prior to notify you of the upcoming cutover. Thank you,
EDI Testing &amp; Certification TeamRetail Onboarding <a href="mailto:<EMAIL>"><EMAIL></a> Commerce proj:_a02Nt000008V8Za:proj
ref:_00D30bzv._500Nt0Lg8hz:ref</h2>
<p>Comments
Comment by Maria Keshwala [ 04/Dec/24 ]
Helllo Tim it looks like this is a testing migration let me see if there is a current project if not I will create one. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
ok this project was completed 09/30/2024 added Daniel as he worked on this . thanks
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
Sent the info to Daniel he will confirmed thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
Hello Tim the connection has been setup through Loren Data and testing was done we received files from them through Loren Data/.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33837-invoice-issues-sales-tax-not-auto-calculating-created-03-dec-24-updated-16-dec-24-resolved-12-dec-24">[CS-33837] Invoice Issues: Sales Tax not Auto Calculating Created: 03/Dec/24  Updated: 16/Dec/24  Resolved: 12/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium Reporter: NATALIE KREIDINGER Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241203-144620.png
Request Type: Phone call
Request language: English
Request participants: None
Organizations: Label: Invoice questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
6998
Invoice issues:
sales tax not calculating instantly, customer has to hit save and exit document to see adjusted total
customer says status in inbox takes a bit to load on her side, however it loaded within 10-15 seconds during the call on my end. will still look into loading time
<a href="mailto:<EMAIL>"><EMAIL></a>
Comments
Comment by Lauren Cohn [ 06/Dec/24 ]
Hi Natalie,
I just wanted to follow up with you to let you know that our team is reviewing the tax calculation issue. I would just like to let you know that we have tested this issue and it is strictly visual. Once you hit save, exit the document and then reopen the document it should populate appropriately. Also, if you send the document with the tax included, it will also transfer and translate without error. Rest assured that we will work to get the automatic calculation back up and running as soon as humanly possible! I hope you have an amazing weekend and I will touch base with you next week with an update.
Sincerely,
Lauren Cohn | Technical Support Representative DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
If urgent, please call us with the ticket number and details of the issue.
Comment by NATALIE KREIDINGER [ 07/Dec/24 ]
Thank you for the update!
Natalie Kreidinger
Office Manager/Owner
Advanced Cleaning Systems, LLC
(************* Comment by Lauren Cohn [ 10/Dec/24 ]
Hi Natalie,
Just checking back in with another update. I received word from development that a fix has been located and tested successfully. The plan currently is to push the solution to production on Thursday. I will keep a close eye on your account and let you know the moment I see that the calculation issue has been rectified. Please let me know if you need anything else in the meantime. I hope you have a great day!
Warm Regards,
Lauren Cohn | Technical Support Representative DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
If urgent, please call us with the ticket number and details of the issue.
Comment by NATALIE KREIDINGER [ 11/Dec/24 ]
Thanks for the update Lauren. I will test it out at the end of December and report back.
Have a great day!
-Natalie
Natalie Kreidinger
Office Manager/Owner
Advanced Cleaning Systems, LLC
(*************
Comment by Maria Keshwala [ 12/Dec/24 ]
Hi Natalie this is Maria Im just following up on Laurens ticket I see this issue has been resolved if you continue to have problems please just reply back on this ticket and it will reopen thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by NATALIE KREIDINGER [ 13/Dec/24 ]
Thanks so much!
Natalie Kreidinger
Office Manager/Owner
Advanced Cleaning Systems, LLC
(*************</p>
<h4 id="cs-33794-re-cs-33367-fwd-si-error-message-prod-error-bp-id-*********-typingservice-processdata-failed-required-parameter-s-not-found-in-primary-document-count-1-created-02-dec-24-updated-04-dec-24-resolved-04-dec-24">[CS-33794] Re: CS-33367 Fwd: SI Error Message [PROD] Error BP ID : ********* : TypingService.processData() failed. Required parameter(s) not found in Primary Document|Count:1 Created: 02/Dec/24  Updated: 04/Dec/24  Resolved: 04/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-cid_image0.png      RE__SI_Error_Message__PROD__Error_BP_ID___*********__ _TypingService_processData___failed__Required_parameter_s__not_found_in <em>Primary_Document_Count_1.eml
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Description
Hello Team,
We&#39;ve received the corrected 997 resent by you and we&#39;ve confirmed that. Please find the attached email for your reference.
From: Maria Keshwala <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, December 2, 2024 10:26 AM To: EDI FD <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-33367 RESOLVED  Fwd: SI Error Message [PROD] Error BP ID : ********* : TypingService.processData() failed. Required parameter(s) not found in Primary Document|Count:1
CAUTION: This email originated from outside of the organization. Do not click on links or open attachments unless you were expecting them.
———-— Reply above this line.
Maria Keshwala commented:
we sent on 11/19/2024
*ISA_00</em> 00 ZZ_DTS6256 _ZZ_DLTRCAN <em>241119_2148_U_00401_*********_0_P~ GS_FA_DTS6256_DLTRCAN_20241119_2148_6_X_004010 ST_997_00006 AK1_PO_1 AK2_850_0001 AK5_A</em> AK2_850_0002 _AK5_A AK9_A_2_2_2 SE_8_00006 GE_1_6 IEA_1_**********
image-20241202-152627.png
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Maria Keshwala resolved this as Done.
View request · Turn off this request&#39;s notifications Sent on December 2, 2024 8:26:53 AM MST
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Dec/24 ]
<em>[^RE_SI_Error_MessagePRODError_BP_ID_*********_ TypingService_processData_failedRequired_parameter_s_not_found_in Primary_Document_Count_1.eml] _(164 kB)</em>
Comment by Maria Keshwala [ 04/Dec/24 ]
Great thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33784-re-john-deere-x401-lights-orders-tru-line-11-20-2024-created-02-dec-24-updated-02-dec-24-resolved-02-dec-24">[CS-33784] RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024 Created: 02/Dec/24  Updated: 02/Dec/24  Resolved: 02/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image006.png      image007.png      image008.gif      image009.png      image010.png      image011.png      image001.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice rejection
Description
@DataTrans Solutions, Inc.
Can you assist please?
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
From: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, November 29, 2024 8:29:59 AM To: John Warden <a href="mailto:<EMAIL>"><EMAIL></a>; Stacey Strohm <a href="mailto:<EMAIL>"><EMAIL></a>; Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a>; Kevin Turan <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Hi John
Next Monday please help me to check with your team the transmission of the EDI for this shipment
I was checking and the EDI 810 was received with an Error. Below screen shows off the transmission of the EDI and last 2-line comes from the invoice 9129.
Both have the same mistakes; they were transmitted to our system without the PO55 added.
Could you please help me to escalate this issue Monday morning, we still waiting for the importation.
Thank you!
Eliud Diaz Supply Management Planner Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 4:42 PM To: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a>; Stacey Strohm <a href="mailto:<EMAIL>"><EMAIL></a>; Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a>; Kevin Turan <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Absolutely. Just let me know if you need anything else on our end. Have a great week. John Warden Powder Coat Supervisor Truline MFG ************ ext. 108 From: Eliud Diaz &lt;DiazEliudM@ JohnDeere. com&gt; Sent: Wednesday, November 27, 2024 4: 40: 34
Absolutely. Just let me know if you need anything else on our end. Have a great week.
Company Use
From: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 4:40:34 PM To: John Warden &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Stacey Strohm &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Kevin Turan &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Thank you John
We already received a transmission, still on Status waiting for approval.
If everything is okey it will show off in our system quickly
Thank you!
Eliud Diaz Supply Management Planner Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 4:12 PM To: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a>; Stacey Strohm <a href="mailto:<EMAIL>"><EMAIL></a>; Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a>; Kevin Turan <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Invoice appears to have been accepted. Should be visible shortly. Please advise when you have received. John Warden Powder Coat Supervisor Truline MFG ************ ext. 108 From: John Warden &lt;<a class="g3mark-shortlink" href="https://teams.googleplex.com/jwarden">jwarden@</a> trulinemfg.  com&gt; Sent: Wednesday, November
Invoice appears to have been accepted. Should be visible shortly. Please advise when you have received.
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 4:08:14 PM To: Eliud Diaz &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Stacey Strohm &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Kevin Turan &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: Re: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
I have resubmitted both ASN and edi invoice. Let me know if you do not receive and I will contact datatrans for support.
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
From: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 4:02:45 PM To: John Warden &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Stacey Strohm &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Thank you John
Will be checking status
Eliud Diaz Supply Management Planner Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 3:53 PM To: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a>; Stacey Strohm <a href="mailto:<EMAIL>"><EMAIL></a>; Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Invoice seems to be stuck in sent not received yet. Going to try and tesubmit John Warden Powder Coat Supervisor Truline MFG ************ ext. 108 From: John Warden &lt;<a class="g3mark-shortlink" href="https://teams.googleplex.com/jwarden">jwarden@</a> trulinemfg. com&gt; Sent: Wednesday, November 27, 2024 2: 27: 35 PM
Invoice seems to be stuck in sent not received yet. Going to try and tesubmit
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 2:27:35 PM To: Eliud Diaz &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Stacey Strohm &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: Re: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
I will have access to the edi website in an hour and I will attempt to tesend
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
From: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 2:06:36 PM To: John Warden &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Stacey Strohm &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
John, the system didn’t receive any transmission successfully, currently we don’t present any try to send an electronic invoice from your side, i will need to check it we could receive tonight
Unfortunately, we were looking to import the material this week, but without the EDI we cannot do it.
Thank you for your support and I will reach you out if we still in the same situation. Have a nice holiday weekend.
Eliud Diaz Supply Management Planner
Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 10:32 AM To: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a>; Stacey Strohm <a href="mailto:<EMAIL>"><EMAIL></a>; Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Manually transmitted under ref#167186 John Warden Powder Coat Supervisor Truline MFG ************ ext. 108 From: John Warden &lt;<a class="g3mark-shortlink" href="https://teams.googleplex.com/jwarden">jwarden@</a> trulinemfg. com&gt; Sent: Wednesday, November 27, 2024 10: 23: 05 AM To: Eliud Diaz &lt;DiazEliudM@ JohnDeere. com&gt;;
Manually transmitted under ref#167186
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 10:23:05 AM To: Eliud Diaz &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Stacey Strohm &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: Re: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
We are closed for the holidays. Give me a few minutes I am attempting to access the website and manually transmit from my house.
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
From: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 10:05:57 AM To: John Warden &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Stacey Strohm &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Good day @John Warden @Stacey Strohm @Justin Stringer
Could you please help me with this emergency?
Thank you!
Eliud Diaz Supply Management Planner Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 26, 2024 11:26 AM To: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a>; Stacey Strohm <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
No problem. We will get these invoiced by end of day. Have a wonderful day!! John Warden Powder Coat Supervisor Truline MFG ************ ext. 108 From: Eliud Diaz &lt;DiazEliudM@ JohnDeere. com&gt; Sent: Tuesday, November 26, 2024 10: 43: 26 AM To:
No problem. We will get these invoiced by end of day.
Have a wonderful day!!
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
Company Use
From: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 26, 2024 10:43:26 AM To: John Warden &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Thank you John
Please help me with the transmission o EDI with this shipment
Eliud Diaz Supply Management Planner Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 26, 2024 9:42 AM To: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
They are loading it now John Warden Powder Coat Supervisor Truline MFG ************ ext. 108 From: Eliud Diaz &lt;DiazEliudM@ JohnDeere. com&gt; Sent: Tuesday, November 26, 2024 9: 40: 37 AM To: John Warden &lt;<a class="g3mark-shortlink" href="https://teams.googleplex.com/jwarden">jwarden@</a> trulinemfg. com&gt; Cc: Justin
They are loading it now
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
Company Use
From: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 26, 2024 9:40:37 AM To: John Warden &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Material is ready or needs to wait the truck?
Eliud Diaz Supply Management Planner Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 26, 2024 9:39 AM To: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
They are here now. John Warden Powder Coat Supervisor Truline MFG ************ ext. 108 From: Eliud Diaz &lt;DiazEliudM@ JohnDeere. com&gt; Sent: Tuesday, November 26, 2024 9: 26: 10 AM To: John Warden &lt;<a class="g3mark-shortlink" href="https://teams.googleplex.com/jwarden">jwarden@</a>  trulinemfg. com&gt; Cc: Justin Stringer
They are here now.
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
Company Use
From: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 26, 2024 9:26:10 AM To: John Warden &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Hello John
We already booked a direct unit, will be there in a few hours
Eliud Diaz Supply Management Planner Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, November 25, 2024 9:44 AM To: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
John Warden Powder Coat Supervisor Truline MFG ************ ext. 108 From: Eliud Diaz Sent: Monday, November 25, 2024 9: 27 AM To: John Warden Cc: Justin Stringer; Johnathan Cole Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024 Hello
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
Company Use
From: Eliud Diaz Sent: Monday, November 25, 2024 9:27 AM To: John Warden Cc: Justin Stringer; Johnathan Cole Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Hello John
Could you please help me to check these information? I will schedule a PFR for this shipment
Thank you!
Total packages, pallets, boxes, etc: Total of pcs: 5 rcd09
Total Weight: 75lbs
Dimensions of the package(s) : 24x12x10
Initial hour of pick up: 2 pm
Final hour for pick up: 4 pm
Preferred pick up location: Shipping
Ready date: 11/26
Complete pick up Address: 3510 central pkwy sw Decatur,AL 35603
Contact name: Justin stringer
Contac phone: ************ ext115
Contact email: <a href="mailto:<EMAIL>"><EMAIL></a>
Eliud Diaz Supply Management Planner Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 21, 2024 10:16 AM To: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a>; Johnathan Cole <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
We are working on them now and will know the amount by Monday at lunch and can set it up then for Tuesday. 50 pcs for sure but will try to have the whole order ready. John Warden Powder Coat Supervisor Truline MFG ************ ext. 108 From:
We are working on them now and will know the amount by Monday at lunch and can set it up then for Tuesday. 50 pcs for sure but will try to have the whole order ready.
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
Company Use
From: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 21, 2024 10:13:49 AM To: John Warden &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Johnathan Cole &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Hi
A partial would be more than great, I will create a label for the DHL, but I will need information to schedule it.
Please confirm if we could ship around 50pcs next 11-26 and the remaining balance until 12-3 to put it in the EDI
Eliud Diaz Supply Management Planner Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 3:42 PM To: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Justin Stringer <a href="mailto:<EMAIL>"><EMAIL></a>; Johnathan Cole <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
We will push to have at least a partial order by 11/26. You will have to set up a PFR or DHL shipment for them though. Because we are not shipping a regular truck next week to you guys. We are only working Monday and Tuesday next week. John
We will push to have at least a partial order by 11/26. You will have to set up a PFR or DHL shipment for them though. Because we are not shipping a regular truck next week to you guys. We are only working Monday and Tuesday next week.
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
Company Use
From: Eliud Diaz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 3:36:23 PM To: John Warden &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Justin Stringer &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt;; Johnathan Cole &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: JOHN DEERE X401 LIGHTS ORDERS TRU LINE 11/20/2024
Thank you John
Please help me to review if we can improve it for the next week? We would like to review if we pick up but I need your comments for this request
Eliud Diaz Supply Management Planner Blvd Santa Maria 1321, Parque Industrial Santa Maria Ramos Arizpe, Coahuila, C.P. 25903
Este mensaje, incluidos los accesorios, puede ser confidencial. Si cree que el mensaje se le envió por error, no lea el contenido y responda al remitente que ha recibido el mensaje por error. Si usted no es el destinatario, retención, difusión, distribución o copia previstos de esta comunicación, está estrictamente prohibido.
This message, including attachments, may be confidential. If you believe the message was sent to you in error, do not read the contents and please reply to the sender that you have received the message in error. If you are not the intended recipient, retention, dissemination, distribution, or copying of this communication is strictly prohibited.
Company Use
From: John Warden <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 3:12 PM *T
...This comment is truncated as it exceeds the character limit.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Dec/24 ]
Comment by Maria Keshwala [ 02/Dec/24 ]
Good morning I will look into this and advise thank you
Best
Maria
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Dec/24 ]
Hey I just resent. I left the p.o. blank.
John Warden
Powder Coat Supervisor
Truline MFG
************ ext.108
Comment by Maria Keshwala [ 02/Dec/24 ]
Oh ok thats good
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33631-re-cs72189-created-27-nov-24-updated-06-dec-24-resolved-06-dec-24">[CS-33631] RE: CS72189 Created: 27/Nov/24  Updated: 06/Dec/24  Resolved: 06/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Brian Wall Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image003.png      image004.jpg      image005.png      image-20241202-205509.png      image-20241202-205711.png      image-20241205-152059.png      image-20241205-164445.png      image-20241205-165345.png
Request Type: Emailed request
Request language: English
Request participants: Organizations:
Label: Document not delivered to TP
Description
Hi Saul,
Can you confirm the date you last received an ASN from us (EXACTO SPRING CORP)?
<em>Best Regards, Brian Wall – IT Manager</em>
EXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Ext: 202 Direct: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a> Website: <a href="http://www.exacto.com">http://www.exacto.com</a>
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 9:03 AM To: Brian Wall <a href="mailto:<EMAIL>"><EMAIL></a>; Jennifer Faas <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: CS72189
Sounds like Novares(Nogales) isn’t receiving our ASN’s. Could you please investigate?
Thanks.
Amy
From: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 8:56 AM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
We should see an inbound number on the system screen with the release lines, as of today I’m not seeing it.
<a href="https://linkprotect.cudasvc.com/url?a=http%3a%2f%2fwww.novaresteam.com&amp;c=E,1,576AVS4WzDr0CJLe22u-o_S12KQeqqosC99d0g8X6q8jeHXpY8cp6-G1kR6m2mvsmYxUbWU3VkrDE2wN-1KBaXmj_ZB0DgTLmCUhbPoN1kD6&amp;typo=1">https://linkprotect.cudasvc.com/url?a=http%3a%2f%2fwww.novaresteam.com&amp;c=E,1,576AVS4WzDr0CJLe22u-o_S12KQeqqosC99d0g8X6q8jeHXpY8cp6-G1kR6m2mvsmYxUbWU3VkrDE2wN-1KBaXmj_ZB0DgTLmCUhbPoN1kD6&amp;typo=1</a> | |
Saul RODRIGUEZ
Procurement Administrator
Business Unit - Americas Engine Components
Carretera Internacional Km 168 : Santa Ana, Sonora 84600 : Mexico
<a href="mailto:<EMAIL>"><EMAIL></a> : Phone +52 6624394100
<em>_ <a href="http://www.novaresteam.com">www.novaresteam.com</a> _</em>
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 7:52 AM To: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
We have been sending the ASN via EDI every week. Are you saying you are not receiving them?
Amy
From: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 8:50 AM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Not yet, Amy.
Maybe for the future lines to be firmed.
But I will keeping an eye on that. Thanks for letting us know.
<a href="https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,Po3YmT2_wNnDG-f6qdzdZOepO_iX-4xGIBnZpeBHnT96b8DyefN2JHg4HpcECa2ChHrJ8j6XKG-jqfSvbdrAfVOiQA1p_fMidcHO2LGGrSUwQ8glVVdNGug,&amp;typo=1__;JSUl!!Ba1thsyW!anDWSTafl6PLC1kFVkuPGUy-3BvX4XCJ8PRhw2GI41EqM47mtT0YzFC7Oq7l8yYGuWKkPfjTUb-EhuQumfxJ46fazZrA$">https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,Po3YmT2_wNnDG-f6qdzdZOepO_iX-4xGIBnZpeBHnT96b8DyefN2JHg4HpcECa2ChHrJ8j6XKG-jqfSvbdrAfVOiQA1p_fMidcHO2LGGrSUwQ8glVVdNGug,&amp;typo=1__;JSUl!!Ba1thsyW!anDWSTafl6PLC1kFVkuPGUy-3BvX4XCJ8PRhw2GI41EqM47mtT0YzFC7Oq7l8yYGuWKkPfjTUb-EhuQumfxJ46fazZrA$</a> | |
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 7:41 AM To: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Quick question – are you receiving our EDI ASN?
Our system shows that the ASN passed validation &amp; sent successfully on Monday.
Amy
From: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 8:33 AM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Thanks Amy.
Please note: For further shipments, and under managerial directions, we need to confirm the shipment/pick up before completion.
We appreciate your understanding on this new request. And I thank your support so far.
<a href="https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,Ry5ip39OYdqIovZfUPgd9unt7wqT_mqNML3OaBSD6OlbpHYKjk-xPT7Yjn677bU-u9hc62fpgHPD5n7sQOd9m9TcepiWRICwHxBNk1B9e3TeUNFYA6SJ&amp;typo=1__;JSUl!!Ba1thsyW!ffP0QKJdtxSNbZXDjuzIHVAuLsdzmd4Tle-txGfK_y3JEwybAJbazN-GLm-GVXNiRYQEVNj5xdlQzqgPy-jFoYl6_WT7$">https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,Ry5ip39OYdqIovZfUPgd9unt7wqT_mqNML3OaBSD6OlbpHYKjk-xPT7Yjn677bU-u9hc62fpgHPD5n7sQOd9m9TcepiWRICwHxBNk1B9e3TeUNFYA6SJ&amp;typo=1__;JSUl!!Ba1thsyW!ffP0QKJdtxSNbZXDjuzIHVAuLsdzmd4Tle-txGfK_y3JEwybAJbazN-GLm-GVXNiRYQEVNj5xdlQzqgPy-jFoYl6_WT7$</a> | |
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 27, 2024 6:00 AM To: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Hi Saul – we shipped 20k on Monday 11-25.
Please see attached.
Thank you!
Amy Grosklaus -Customer Service
Exacto Spring Corporation
1201 Hickory St. Grafton, WI 53024
(************* Ext. 207
Direct: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.exacto.com">http://www.exacto.com</a>
Exacto Spring will be closed Nov 28-29, Dec 24-25 and Jan 1.
From: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 26, 2024 6:18 PM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Hi team.
Checking up status for next shipment, need the pieces here by 12/16.
Please let me know status.
Please note that from now on and under managerial directions, we need to confirm every pick-up that is going to be made at our Suppliers&#39; docks. This is for all our Novares Suppliers.
I appreciate your support on this.
<a href="https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,18msDZuilL8r1uZES8Bfc_54iX3k9VAkvkMZT-NxCdnMW9Z6dE4QxkAs6cOrvyg6BfXWXFm2jkQUAcxcabVU3sW6vI00dRif3hZxln8az4-W54lhwMDsNVAt&amp;typo=1__;JSUl!!Ba1thsyW!bOY4mmFNY80LWsZI7Yafscx-hwnRl7jVWU3YZFWIcQq0_4Dxy3uUw2kXSPEB5ldTkU8e3TgaxyaeN-QkZx2t01eCWAmZ$">https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,18msDZuilL8r1uZES8Bfc_54iX3k9VAkvkMZT-NxCdnMW9Z6dE4QxkAs6cOrvyg6BfXWXFm2jkQUAcxcabVU3sW6vI00dRif3hZxln8az4-W54lhwMDsNVAt&amp;typo=1__;JSUl!!Ba1thsyW!bOY4mmFNY80LWsZI7Yafscx-hwnRl7jVWU3YZFWIcQq0_4Dxy3uUw2kXSPEB5ldTkU8e3TgaxyaeN-QkZx2t01eCWAmZ$</a> | |
From: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 21, 2024 9:04 AM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Thanks Amy,
Have a great one.
&lt;<a href="https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com*2f&amp;c=E,1,uSuFsOu7Zi3RZeUbezTOoXV7Nffvf5vHFTCwmSnCnoXxG0H3F7z7GE19ImWAJ">https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com*2f&amp;c=E,1,uSuFsOu7Zi3RZeUbezTOoXV7Nffvf5vHFTCwmSnCnoXxG0H3F7z7GE19ImWAJ</a> wo3HoRGAS2kO4Tolr1Ca537tutmt6dYyLC77z0OP1slf-<em>5URo,&amp;typo=1</em>_;JSUlJQ!!Ba1thsyW!bOY4mmFNY80LWsZI7Yafscx-hwnRl7jVWU3YZFWIcQq0_4Dxy3uUw2kXSPEB5ldTkU8e3TgaxyaeN-QkZx2t0-UMeRlg$&gt; | |
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 21, 2024 9:01 AM
To: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Please see attached.
Thanks again!
Amy
From: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 1:56 PM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Awesome Amy, thanks so much.
Can you share invoice of this shipment right here?
&lt;<a href="https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url">https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url</a>? a=http<em>3a</em>2f<em>2fwww.novaresteam.com&amp;c=E,1,K1cyQM_nu5v07sJqIEtOng3dTxfW4aiPXy7hMhiuo9owXu3CEpodMl6yBgxY6vGdsPbUEfIzQSk8zb2Cyb8_eQ30KmZC8Ji-n148bvyF-54q2yYJetI,&amp;typo=1__;JSUl!!Ba1thsyW!en1qB9GuafKznqBr7kVRfY_z6CnhoVzlAbNo0E5qgaRMYbzHfEBF7_CEkvZ6B-yCqDk1CL4eZY6oxGhU-CTUfHpD283u$&gt; | |
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 10:59 AM To: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
UPS 1Z5807430363297180
Thanks again!
Amy
From: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 10:33 AM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Awesome, thanks. Please share tracking info once available.
<a href="https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,c48fK3i_djbi1Zt3khLqz3nUgA_UFomhdX05v3XnL-2w5Tk3jsvWlMaK9nDOYdA6jICEh9VorHkMbWU1ZUnidelRcipzwkOIHoyNxbsMs-Khu58R&amp;typo=1__;JSUl!!Ba1thsyW!eX--UXORI_6pgJm2XcArduR7oAFk4kDYgWKdMR70nr9izP3C8ZTh7tvQ5aa416fuweBh9mhFYI6I0FtLj-Rdk65jVL9d$">https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,c48fK3i_djbi1Zt3khLqz3nUgA_UFomhdX05v3XnL-2w5Tk3jsvWlMaK9nDOYdA6jICEh9VorHkMbWU1ZUnidelRcipzwkOIHoyNxbsMs-Khu58R&amp;typo=1__;JSUl!!Ba1thsyW!eX--UXORI_6pgJm2XcArduR7oAFk4kDYgWKdMR70nr9izP3C8ZTh7tvQ5aa416fuweBh9mhFYI6I0FtLj-Rdk65jVL9d$</a> | |
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 9:32 AM To: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Will do – thanks again!
Amy
From: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 10:18 AM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Yes, please.
Can you ship it UPS Ground??
&lt;<a href="https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,UYn-Y4uhatbb8sXMw2XwtYof-jeHwj1NIVpcaG792ZCoaoteg0WOgjokj-8z8PzXttNt6EtT4oifXXKzga922xEuua--fb8Nwds0P_7i_bYYPljbASEF1GdeCcMp&amp;typo=1__;JSUl!!Ba1thsyW!aDWNG2lP2FleEE73Jn0u3Kx7uCmd1Uzjob8KQnQclkMPM30eRvQiS0a9EEG6ezJm2ii08CKtaVt4jxFT-x1uKsvK-JuH">https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,UYn-Y4uhatbb8sXMw2XwtYof-jeHwj1NIVpcaG792ZCoaoteg0WOgjokj-8z8PzXttNt6EtT4oifXXKzga922xEuua--fb8Nwds0P_7i_bYYPljbASEF1GdeCcMp&amp;typo=1__;JSUl!!Ba1thsyW!aDWNG2lP2FleEE73Jn0u3Kx7uCmd1Uzjob8KQnQclkMPM30eRvQiS0a9EEG6ezJm2ii08CKtaVt4jxFT-x1uKsvK-JuH</a>
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 9:09 AM To: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Hi Saul – we shipped 16,475pcs on 11-11. We have another 10k available, should I ship these today?
Thank you!
Amy Grosklaus -Customer Service
Exacto Spring Corporation
1201 Hickory St. Grafton, WI 53024
(************* Ext. 207
Direct: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.exacto.com">http://www.exacto.com</a>
Exacto Spring will be closed Nov 28-29, Dec 24-25 and Jan 1.
From: Debbie Miswald <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 10:05 AM To: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a>; MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a>; Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
@Amy Grosklaus
From: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 9:41 AM To: MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a>; Debbie Miswald <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: CS72189
Hello Debbie, do you have an update on this shipment??
&lt;<a href="https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,lNxc5w3GmVE4_CzST4Sa3d4WuQuSjV_omqexjo8Sj1p-wQ1HC00_NVhOO3DvMwMljT1l93Vr5nLlbFePfsmjaqK6LEt6UUGEfGMbOYH44Q,,&amp;typo=1__;JSUl!!Ba1thsyW!btOUBbIAEz5aiXZTeQ3xCBRhkSwy_XL7lC_vWGLFZZMOwtH7qy_xEPTot9s2_LhlrfP7_3AL4LzZDIvKCg6ocnAvg">https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url?a=http*3a*2f*2fwww.novaresteam.com&amp;c=E,1,lNxc5w3GmVE4_CzST4Sa3d4WuQuSjV_omqexjo8Sj1p-wQ1HC00_NVhOO3DvMwMljT1l93Vr5nLlbFePfsmjaqK6LEt6UUGEfGMbOYH44Q,,&amp;typo=1__;JSUl!!Ba1thsyW!btOUBbIAEz5aiXZTeQ3xCBRhkSwy_XL7lC_vWGLFZZMOwtH7qy_xEPTot9s2_LhlrfP7_3AL4LzZDIvKCg6ocnAvg</a> | |
From: MORA, Rodolfo <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 7, 2024 2:58 PM To: Debbie Miswald <a href="mailto:<EMAIL>"><EMAIL></a> Cc: RODRIGUEZ, Saul <a href="mailto:<EMAIL>"><EMAIL></a> Subject: CS72189 Importance: High
Hello Debbie,
Could you please prepare a shipment for the CS72189 20k pieces.
Also, I will be out of the office on paternity leave from 11-15 to 12-10, during which time my colleague Saul will be covering for me.
I hope we can continue the excellent communication and way of working we have had up until now.
I will also be available for anything I can assist with.
Regards.
&lt;<a href="https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url">https://urldefense.com/v3/__https:/linkprotect.cudasvc.com/url</a>? a=http</em>3a<em>2f</em>2fwww.novaresteam.com*2f&amp;c=E,1,hRD9zeQX3ByYSZDI7DMFDCfEPltJiJLtEEMttyaGbiXywN0DEorO6t4pQpPYxWc3EwkBmO3XjxaI0iC4haqSTKB77aup26Npe-bgbXT-6xy5MteTE6Nz1Q,,&amp;typo=1__;JSUlJQ!!Ba1thsyW!btOUBbIAEz5aiXZTeQ3xCBRhkSwy_XL7lC_vWGLFZZMOwtH7qy_xEPTot9s2_LhlrfP7_3AL4LzZDIvKCg6ocjzmGvEP$&gt; | |
Rodolfo MORA
Procurement Administrator
Business Unit - Americas Engine Components
Carretera Internacional Km 168 : Santa Ana, Sonora 84600 : Mexico
<a href="mailto:<EMAIL>"><EMAIL></a> : Phone +52 6624394106
<a href="http://www.novaresteam.com">www.novaresteam.com</a>
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necess represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necess represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necess represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necess represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necess represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necess represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necess represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necess represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
Comments
Comment by Brian Wall [ 27/Nov/24 ]
Comment by Brian Wall [ 27/Nov/24 ]
I was informed we have another trading partner that is not receiving our ASN - Novares (5105) The above email was inquiring as to when they received the last ASN from us so we can begin troubleshooting the issue. We just had this issue with another trading partner not receiving ASN from us, and I was told that had been resolved, can we confirm that all ASN 856 transactions are being sent for all of our Trading Partners?
Regards,
Brian Wall
Comment by Brian Wall [ 02/Dec/24 ]
Hi, I am just following up on this support request, can I get a status update?
-Brain
Comment by Maria Keshwala [ 02/Dec/24 ]
Hello I will work on this I just took this ticket today thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Brian Wall [ 02/Dec/24 ]
Thank you, I reached out to the trading partner to see when the last ASN was received by them, as soon as they respond, I will let you know and provide you full details (message id, date and time, etc.).
Thanks you, Brian
Comment by Maria Keshwala [ 02/Dec/24 ]
Thank you I appreciated I will wait.
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 02/Dec/24 ]
Im checking the ones that you guys sent today
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 02/Dec/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Maria Keshwala [ 04/Dec/24 ]
Good morning Brian I was out yesterday, did you received a reply from the TP as to when was the last time they received ASNs?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Brian Wall [ 04/Dec/24 ]
Hi Maria,
We have not received a response from them yet, as soon as I do I will update the ticket and let you know.
Best Regards,
Brian
Comment by Maria Keshwala [ 05/Dec/24 ]
Ok great thank you I will wait
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Brian Wall [ 05/Dec/24 ]
I don’t understand what you are waiting on, you should be able to verify the connection is working without that information. The last 856 for the trading partner did not go through troubleshoot that message.
Best Regards,
Brian
Comment by Maria Keshwala [ 05/Dec/24 ]
Im showing on my end files are going out and connection has been stablished plus I need the files that they did not received .
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
batch ID 140511299
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
We are checking deeper now and see with the last ASN to give you confirmation thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
the file name was incorrect Sandy update the event rule
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Hello Brian Issue has been fixed on our end and I see positive MDNs after I restaged the file and also restage from 12/02/ through now let me know if you want me to go back and restage other files but it has been fixed. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Brian Wall [ 05/Dec/24 ]
Ok Thank you, much appreciated! Should we be concerned that any of our other Trading Partners are experiencing the same issues as these last two with outbound ASN?
Best Regards,
Brian
Comment by Brian Wall [ 06/Dec/24 ]
I have confirmed the trading partner is now receiving the ASN. This service request can be closed. Thank you.
-Brian
Comment by Maria Keshwala [ 06/Dec/24 ]
Hello Brian thank you for confirming hopefully you should be ok moving forward but you have any more issues please let me know if this happens again. thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 06/Dec/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33617-cannot-submit-invoice-dts7372-created-27-nov-24-updated-09-dec-24-resolved-09-dec-24">[CS-33617] cannot submit invoice DTS7372 Created: 27/Nov/24  Updated: 09/Dec/24  Resolved: 09/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: Barbara Galbraith Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.gif      image-********-203548.png      image-********-203351.png
Request Type: Support
Request language: English
Request participants: None
Organizations: None
Label: Invoice errors, AHN issues
Description
I get an error message the Allowance or Charge Indicator are conditional. How can I fix to submit?
Comments
Comment by Maria Keshwala [ 02/Dec/24 ]
Hello
To further assist you can you please the data trans account number and message ID of the document you are working on . Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Barbara Galbraith [ 02/Dec/24 ]
Maria,
I’m not sure what our account number is. I have a supplier TN # AAA775638471 and a message ID # ********/Reference #*********
Comment by Maria Keshwala [ 02/Dec/24 ]
whats the company name
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Barbara Galbraith [ 02/Dec/24 ]
Combustion Service &amp; Equipment Company
Comment by Maria Keshwala [ 04/Dec/24 ]
Thank you looking into this now I was out of the office yesterday. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Login as ARbARB USER
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Hello Im reviewing this and the message ID you provided is the PO I also noticed this has been invoice multiple time did you check the other invoices already submitted as reference? I will need the actual message ID of the invoice you are trying to submit and it needs to be completed unfinished invoices will make it difficult for us to determine if there are any errors so may I please have the actual reference number and message ID for the invoice (not the PO) thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 09/Dec/24 ]
please let us know if you still need assistance regarding this particular issue I have requested additional information have not heard back. This issue will be closed and will be reopen once you reply back with the previous request thank you .
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Barbara Galbraith [ 09/Dec/24 ]
Maria,
The messaage ID ******** for this Purchase Order *********. How can I remove the duplicate invoices from your system? I never received any training for DataTrans and it is very confusing. I just want to submit the invoice with the Purchase order so we can get paid. I have been trying to rectify this issue for a few weeks now.
Hope you can help.
Thanks,
Barb Galbraith
Accounting Dept.
Ph-412-822-5132
Fax – 412-821-3687
Emal: <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Maria Keshwala [ 09/Dec/24 ]
These are all the documents attached to this PO have you ever created invoices I see you guys have created invoices for this PO in the past we do have a training class on Thursdays 10:30 CST <a href="https://meet.google.com/rsk-dphb-rhu">https://meet.google.com/rsk-dphb-rhu</a> this is the link its important for you to attend the trainings. you see all the documents have message IDs they are different. please let me know if you have any other questions. Thank you
To Delete the document go to your draft folder and follow the instructions below .
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33537-production-dataflow-error-action-required-coast-to-coast-allpro-corporation-created-26-nov-24-updated-27-nov-24-resolved-27-nov-24">[CS-33537] Production Dataflow Error  Action Required (Coast To Coast - ALLPRO Corporation) Created: 26/Nov/24  Updated: 27/Nov/24  Resolved: 27/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241127-132235.png      image-20241127-132457.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice rejection
Description
Greetings Coast To Coast Computer Products, Inc! We have received production dataflow for your trading partnership with ALLPRO Corporation and there is at least one error detailed below. Please review and send revised documents as soon as possible. Doc Type: 810 Document Sender: Coast To Coast Computer Products Inc Document Receiver: ALLPRO Corporation Document IDs: [&#39;A2735838&#39;, &#39;A2735838&#39;] Error Message: If InvoiceTypeCode is CR or DI, then BuyerPartNumber is required.
You can view your retailer specifications in the testing portal for your reference as needed HERE .
Please pass this email on to anyone that will be involved in in these corrections, including your EDI Provider.
Best Regards,EDI Testing &amp; Certification Team <a href="mailto:<EMAIL>"><EMAIL></a>
Project Ref ID: proj:_a02Nt000004jdvm:proj
Comments
Comment by Maria Keshwala [ 27/Nov/24 ]
Part number was added this document was restaged and done manually the customer needs to fixed the line Item number
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Maria Keshwala [ 27/Nov/24 ]
Hello coast to coast team did you guys fixed the issue if so because I see the document was restaged , and entered manually you added a line Item “2” when it should be Item line “1” make sure if you are only sending one Item then the line Items should should be 2 unless this part number was in line Item 2 on the PO.
A2735838
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33525-sales-tax-not-loading-created-25-nov-24-updated-09-dec-24-resolved-09-dec-24">[CS-33525] Sales Tax Not Loading Created: 25/Nov/24  Updated: 09/Dec/24  Resolved: 09/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice errors
Description
There is an 810 on WebEDI in Draft where the sales tax will not load on the total. Please fix so that the total of the 810 will match the total of the sales invoice in Sage.
The reference number of the 810 is 2134626.
Kevin Skold
Comments
Comment by Maria Keshwala [ 27/Nov/24 ]
Hello Kevin I will look into this matter and advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Good morning Kevin I see this project was completed a while ago back in 2020, before I escalate this issue to our analyst can you please let us know when this issue started with the sales tax not calculating?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Maria Keshwala [ 09/Dec/24 ]
customer has not reply emailed 4 days ago when I investigated all files have been accepted. if they have any more issues they will advise
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33515-adams-awg-issue-created-25-nov-24-updated-05-dec-24-resolved-05-dec-24">[CS-33515] Adams: AWG Issue Created: 25/Nov/24  Updated: 05/Dec/24  Resolved: 05/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Scott Shippee Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      AE_AWG_PO_20241125.145139.dat      image-20241205-144351.png
Request Type: Emailed request
Request language: English
Request participants: Organizations:
Description
Customer: Adams Extracts
Customer ID: 6061
Trading Partner: AWG
Document Type: 875 Grocery Purchase Order
Duplicate entries in the file.
Header records exist, no lines (RED), then both show up (BLUE). This is causing our EDI pickup to FAIL.
Please correct this issue.
See attached (copy of original file received) and below (image of file)
Scott W. Shippee
Nestell &amp; Associates| 5000 Birch Street | West Tower, Suite 3000 | Newport Beach, CA | 92660, USA
<a href="http://www.NestellAssociates.com">www.NestellAssociates.com</a>
Mobile: (*************
Timezone: EST/EDT
**** PTO Notice – Out of office: Wednesday, November 27 – Friday, November 29, 2024 – Thanksgiving Holiday*
**** PTO Notice – Out of office: Friday, December 6 – Monday, December 16, 2024 – Vacation (unavailable)*
Comments
Comment by Scott Shippee [ 25/Nov/24 ]
<em>AE_AWG_PO_20241125.145139.dat  (10 kB)</em>
Comment by Maria Keshwala [ 27/Nov/24 ]
Good morning Scott
Ok I will look and investigate this issue and advise as soon as I can. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
ECS-04 - Cant open the file
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Hello Scott this issue happened right when we were doing a migration we moved from one database to another I can even open the file we sent , I dont see any new 875 after 11/25/2024 if you come across this issue again please let us know and we will look further. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33500-fw-edi-fawn-motus-created-25-nov-24-updated-16-dec-24-resolved-16-dec-24">[CS-33500] FW: EDI FAWN / MOTUS Created: 25/Nov/24  Updated: 16/Dec/24  Resolved: 16/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image002.jpg      image004.jpg      image006.png      image008.jpg      image003.png      image005.jpg      image009.jpg      24M065.pdf      image-20241202-154242.png      image001.png      3. Motus_Supplier_856_Specification_Rev_1.pdf      image-********-215231.png      image-********-221700.png      856raw.txt
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: ASN errors
Description  For every bar-coded serial number we entered on the ASN, it duplicated the quantity received on Motus’ side.
We must enter the individual serial numbers. How can we fix this?
Screenshot below from Motus’ system in reference to our message ID ********.
Regards,
Stephanie Brinegar
IT Administrator/Accounting Analyst
Fawn Industries, Inc.
From: Alejandra Gonzalez <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, November 25, 2024 1:11 PM To: Stephanie Brinegar <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Karla Tovar <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RV: EDI FAWN / MOTUS
Steph, the motus asn is not working fine, It is doubling the total amount of the part number for each serial number that is entered.
Please help us with this. Data trans, needs to arrange this. Tx
|
Alejandra González : Materials Manager
Ave. Impulso #3001. Parque Industrial Impulso. Chihuahua, Chih. 31183 México <a href="mailto:<EMAIL>"><EMAIL></a> P: +52 (614) 442-0737
De: Melissa Zendejo <a href="mailto:<EMAIL>"><EMAIL></a> Enviado el: lunes, 25 de noviembre de 2024 12:00 PM Para: Alejandra Gonzalez <a href="mailto:<EMAIL>"><EMAIL></a>; Alan Fabricio Gaytan Vidales <a href="mailto:<EMAIL>"><EMAIL></a>; Karla Tovar <a href="mailto:<EMAIL>"><EMAIL></a> CC: Oscar Olivas <a href="mailto:<EMAIL>"><EMAIL></a>; Francisco Charles <a href="mailto:<EMAIL>"><EMAIL></a>; Elmer Ulises Maldonado Silva <a href="mailto:<EMAIL>"><EMAIL></a>; Tania Pacheco <a href="mailto:<EMAIL>"><EMAIL></a> Asunto: RE: EDI FAWN / MOTUS
Buenas tardes, @Alejandra Gonzalez @Karla Tovar
Nuevamente se envió el ASN erróneo. Se envió más de un serial de todos los números de parte.
Por favor, nos apoyan revisando cómo se está transmitiendo la información al momento de generar el ASN.
Gracias de antemano,
Saludos.
From: Alejandra Gonzalez <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, November 18, 2024 10:57 AM To: Alan Fabricio Gaytan Vidales <a href="mailto:<EMAIL>"><EMAIL></a>; Karla Tovar <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Oscar Olivas <a href="mailto:<EMAIL>"><EMAIL></a>; Francisco Charles <a href="mailto:<EMAIL>"><EMAIL></a>; Elmer Ulises Maldonado Silva <a href="mailto:<EMAIL>"><EMAIL></a>; Melissa Zendejo <a href="mailto:<EMAIL>"><EMAIL></a>; Tania Pacheco <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: EDI FAWN / MOTUS
CAUTION | EXTERNAL
Buen día Alan,
Se están haciendo todos los cambios y en esta ocasión los asn se realizaron desde la plataforma de Datatrans.
Me puedes ayudar a revisar los dos ASN que anexo para que verifiques que la información con la que se realizo es la correspondiente a los packings. Y que puedas ver que no se duplicaron.
El problema no es la información sino lo que transmitió.
|
Alejandra González : Materials Manager
Ave. Impulso #3001. Parque Industrial Impulso. Chihuahua, Chih. 31183 México <a href="mailto:<EMAIL>"><EMAIL></a> P: +52 (614) 442-0737
De: Alan Fabricio Gaytan Vidales <a href="mailto:<EMAIL>"><EMAIL></a> Enviado el: lunes, 18 de noviembre de 2024 10:42 AM Para: Karla Tovar <a href="mailto:<EMAIL>"><EMAIL></a> CC: Oscar Olivas <a href="mailto:<EMAIL>"><EMAIL></a>; Francisco Charles <a href="mailto:<EMAIL>"><EMAIL></a>; Alejandra Gonzalez <a href="mailto:<EMAIL>"><EMAIL></a>; Elmer Ulises Maldonado Silva <a href="mailto:<EMAIL>"><EMAIL></a>; Melissa Zendejo <a href="mailto:<EMAIL>"><EMAIL></a>; Tania Pacheco <a href="mailto:<EMAIL>"><EMAIL></a> Asunto: RE: EDI FAWN / MOTUS Importancia: Alta
Buen día Karla,
El día de hoy detectamos que su ASN del embarque del 11/15 duplico cantidades a modo que reflejan un tránsito de +900K USD en nuestro sistema, el problema que detectamos es que la cantidad total por PN la duplicaron en cada 1 de los seriales (ver ejemplo). Necesitamos que validen por que generaron los seriales de esta manera y que corrijan de inmediato. El embarque del siguiente viernes debe venir con la información correcta. Por lo pronto borraremos los registros duplicados de cada PN y dejaremos solo 1.
From: Alan Fabricio Gaytan Vidales Sent: Monday, November 11, 2024 3:08 PM To: Karla Tovar <a href="mailto:<EMAIL>"><EMAIL></a>; Elmer Ulises Maldonado Silva <a href="mailto:<EMAIL>"><EMAIL></a>; Melissa Zendejo <a href="mailto:<EMAIL>"><EMAIL></a>; Tania Pacheco <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Oscar Olivas <a href="mailto:<EMAIL>"><EMAIL></a>; Francisco Charles <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: EDI FAWN / MOTUS
Buen día Equipo,
De lo comentado el día de hoy:</p>
<ol>
<li>Se solicita a Fawn generar una sola etiqueta máster por pallet para facilitar el recibo en Motus. 2. El embarque de esta semana (W46) se realizará con EDI y no por el portal, para probar el sistema (1 serial por pallet). 3. Motus corregirá reléase para solicitar material en estándar pack (cantidad de piezas por tarima). 4. El objetivo al corto plazo será generar una etiqueta por caja + etiqueta máster por pallet + EDI ASN. Fawn validara esta opción con Data Trans (proveedor de servicio).
Quedamos al pendiente de monitorear los cambios y cerrar cualquier gap que detectemos,
----Original Appointment----From: Alan Fabricio Gaytan Vidales Sent: Friday, November 8, 2024 3:58 PM To: Alan Fabricio Gaytan Vidales; Karla Tovar; Elmer Ulises Maldonado Silva; Melissa Zendejo; Tania Pacheco Cc: Oscar Olivas Subject: EDI FAWN / MOTUS When: lunes, 11 de noviembre de 2024 10:30 a. m.-11:00 a. m. (UTC-06:00) Guadalajara, Ciudad de México, Monterrey. Where: <a href="https://motus.zoom.us/j/94711745853?pwd=1R6awT8OAoQY19QIoQ9A03veTJAMTE.1">https://motus.zoom.us/j/94711745853?pwd=1R6awT8OAoQY19QIoQ9A03veTJAMTE.1</a>
Buen día Equipo Fawn,
La intención de esta junta es revisar la generación de etiquetas master vs individual.
[]
<a href="https://zoom.us/">https://zoom.us/</a>
Hola:
Alan Fabricio Gaytan Vidales lo invita a una reunión de Zoom programada.
Entrar a la Reunión Zoom
Móvil con un toque: Estados Unidos: +13052241968 ,,94711745853# <a href="about:invalid#zCSafez">tel:+13052241968,,94711745853</a> o +13092053325 ,,94711745853# <a href="about:invalid#zCSafez">tel:+13092053325,,94711745853</a>
URL de la reunión: <a href="https://motus.zoom.us/j/94711745853?pwd=1R6awT8OAoQY19QIoQ9A03veTJAMTE.1">https://motus.zoom.us/j/94711745853?pwd=1R6awT8OAoQY19QIoQ9A03veTJAMTE.1</a>
ID de reunión: 947 1174 5853
Código de acceso: 808515
Unirse por teléfono
Para una mayor calidad, marque un número según su ubicación actual.
Marcar:
Estados Unidos : ****** 224 1968 o ****** 205 3325 o ****** 626 6799 o ****** 248 7799 o ****** 209 5623 o ****** 347 5053 o ****** 473 4847 o ****** 217 2000 o ****** 876 9923 o ****** 931 3860 o ****** 444 9171 o ****** 900 6833 o ****** 278 1000 o ****** 359 4580 o ****** 205 0468 o ****** 215 8782 o ****** 715 8592 Alemania : +49 69 5050 0952 o +49 ************ o +49 69 7104 9922 o +49 69 3807 9883 o +49 69 3807 9884 o +49 69 5050 0951 México : +52 ************ o +52 ************ o +52 ************ o +52 ************ o +52 ************ Francia : +33 1 7095 0103 o +33 1 7095 0350 o +33 1 8699 5831 o +33 1 7037 2246 o +33 1 7037 9729
ID de la reunión: 947 1174 5853
Números internacionales
[]
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 25/Nov/24 ]
<em>24M065.pdf  (87 kB)</em>
Comment by Maria Keshwala [ 27/Nov/24 ]
Good morning Stephanie
I will look into this matter and advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 02/Dec/24 ]
the amounts in webedi is different checking the file sent to the TP
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 02/Dec/24 ]
Hi Stephanie we still looking into this.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Dec/24 ]
A little more information…
What Motus told us is the Datatrans should check the segments from ASN where we put the total quantity and quantity send per serial number. And send the specifications from them, added.
Thanks,
Stephanie
<em>3. Motus_Supplier_856_Specification_Rev_1.pdf  (197 kB)</em>
Comment by Maria Keshwala [ 02/Dec/24 ]
Hi Stephanie Im addressing this issue with the analyst thank you for that additional information
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
Hello Stephanie did you create the ASN in WEBEDI or you sent us the file from your end? thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Dec/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 04/Dec/24 ]
ASNs are created in WebEDI.
Thanks, Stephanie
Comment by Maria Keshwala [ 04/Dec/24 ]
Ok the quantities in WEBEDI are they correct ? can you please advise and highlight what is wrong because this is created in webedi meaning if the TP is having another issue on their end with calculations thats on their end can you please tell us what the amounts should be?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 05/Dec/24 ]
The problem is, currently we are required to report each individual serial number per box. When we enter those numbers, it does not ask us for a quantity, but when the ASN is sent the total quantity is reported for each serial number.
We either need to be able to input a quantity for each box or have the total quantity divided by the number of serials.
Regards,
Stephanie Brinegar
IT Administrator/Accounting Analyst
Fawn Industries, Inc.
Comment by Maria Keshwala [ 05/Dec/24 ]
I will have to ask for this and see if this is possible this is how is done for everyone else. I will advise
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Dec/24 ]
Hello this has been escalated to out analyst to advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 12/Dec/24 ]
Hello Stephanie
Per the analyst there is anything else we can do as is usually done the way we send the document I will have to ask our dev team if they can do something again we are sending the files correctly that will be on your TPs ends doing other calculations. but I will investigate and advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 16/Dec/24 ]
Hello Stephanie
Per feedbacks we cannot do any other calculations as we are sending the file correctly and how it suppose to be you will have to talk to Motus because the calculation issues are on the end below is the raw data we are sending .Please reach out to them.
856raw.txt
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ol>
<h4 id="cs-33492-fwd-po-2351116-missing-created-25-nov-24-updated-04-dec-24-resolved-27-nov-24">[CS-33492] Fwd: PO#2351116 Missing Created: 25/Nov/24  Updated: 04/Dec/24  Resolved: 27/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      QSYSPRT858756000001.pdf
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Missing documents FTP
Description
We missed another Lipari PO. Can you see if it is somewhere on your server? It is attached. Sent 11/6.
---------- Forwarded message ---------From: Joyce Humrichouser <a href="mailto:<EMAIL>"><EMAIL></a> Date: Mon, Nov 25, 2024 at 11:38 AM Subject: Fwd: PO#2351116 Missing To: Timothy Jones <a href="mailto:<EMAIL>"><EMAIL></a>
Attached is a copy of PO 2351116 that the buyer at Lipari is stating he sent via EDI. I don&#39;t see that we received it. Can you check somehow to see if it was sent and why I can&#39;t find it?
---------- Forwarded message ---------From: Austin Hoyt <a href="mailto:<EMAIL>"><EMAIL></a> Date: Mon, Nov 25, 2024 at 11:22 AM Subject: RE: PO#2351116 Missing To: Joyce Humrichouser <a href="mailto:<EMAIL>"><EMAIL></a>
Hello,
This was sent via EDI I will have it marked as a total shortage unless you can get this do us this week?
Attached a hard copy.
Thank You,
Austin Hoyt | LIPARI FOODS
Category Analyst – Dairy
M-F 7 AM-4 PM EST.
( 586.447.3500 Ext. 9724
26661 Bunert Road | Warren | Michigan | 48089
<a href="http://www.liparifoods.com">www.liparifoods.com</a>
From: Joyce Humrichouser <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 3:17 PM To: Austin Hoyt <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: PO#2351116 Missing
**This message originated from an External sender
I don&#39;t have a PO with that number. We had 5 POs scheduled for delivery today
2350991
2353566
2353558
2351975
2353564
On Wed, Nov 20, 2024 at 3:04 PM Austin Hoyt <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Hello,
Sorry if I am missing something but I am showing PO# 2351116 was due today and has not arrived do we have an ETA?
Thank You,
Austin Hoyt | LIPARI FOODS
Category Analyst – Dairy
M-F 7 AM-4 PM EST.
( 586.447.3500 Ext. 9724
26661 Bunert Road | Warren | Michigan | 48089
<a href="http://www.liparifoods.com">www.liparifoods.com</a>
–
<a href="http://www.saudereggs.com/">http://www.saudereggs.com/</a>
Joyce Humrichouser
Customer Service
Corp: 800-860-5440 ext : 2001
Fax: 330-359-5774 | <a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.facebook.com/Sauder-Eggs-184170408279763/">https://www.facebook.com/Sauder-Eggs-184170408279763/</a>  <a href="https://youtu.be/oli8kreNaAI">https://youtu.be/oli8kreNaAI</a>  <a href="https://twitter.com/SaudersEggs">https://twitter.com/SaudersEggs</a>  <a href="https://www.saudereggs.com/blog/">https://www.saudereggs.com/blog/</a>
– --
<a href="http://www.saudereggs.com">http://www.saudereggs.com</a>
Timothy Jones
Manager | Information Technology
Corp. ************ Ext.7414
Direct ************ | <a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.facebook.com/Sauder0Eggs-184170408279763/">https://www.facebook.com/Sauder0Eggs-184170408279763/</a>  <a href="https://youtu.be/oli8kreNaAI">https://youtu.be/oli8kreNaAI</a>  <a href="https://twitter.com/SaudersEggs">https://twitter.com/SaudersEggs</a>  <a href="https://www.saudereggs.com/blog/">https://www.saudereggs.com/blog/</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 25/Nov/24 ]
<em>QSYSPRT858756000001.pdf  (6 kB)</em>
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 26/Nov/24 ]
Can you give me an update on the missing files?
Comment by Maria Keshwala [ 26/Nov/24 ]
working on it Tim
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 26/Nov/24 ]
Tim I search a few of this POs they are from earlier this month I see them in WEBEDI and with Reponses attached do you mean that the POs listed did not make it to your SFTP?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 26/Nov/24 ]
Correct. Only the one was missed.
On Nov 26, 2024, at 4:56 PM, Maria Keshwala <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
———-— Reply above this line.
Maria Keshwala commented:
Tim I search a few of this POs they are from earlier this month I see them in WEBEDI and with Reponses attached do you mean that the POs listed did not make it to your SFTP?
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
View request · Turn off this request&#39;s notifications Sent on November 26, 2024 2:56:09 PM MST
Comment by Maria Keshwala [ 27/Nov/24 ]
Ok thank you for confirming I will send that over to you now
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 27/Nov/24 ]
Hello Tim I just restaged and it looks like the file has been picked up thanks
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Nov/24 ]
I did not need it restaged! It was missed and not delivered. I need to know why or what is causing intermittent files from being missed. What can be done to ensure we are getting all of the files? This was a $26,000 lost sale for Sauders.
Comment by Maria Keshwala [ 02/Dec/24 ]
Hello I apologized for that , I will escalate this to my manager I cant give you answer as to why you received some and one got missed but is hard for us to determine as it was a while ago I showed on my end it was delivered and also you created a response document on WEBEDI meaning did you received the file on your end before ? and something happened ? is easier to identify an issue when it happens at that moment but after 10 days or more is hard to tell.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Dec/24 ]
We saw the PO in WebEDI but never got the file via SFTP. This seems to be an ongoing issue.
Comment by Maria Keshwala [ 02/Dec/24 ]
Hi Tim are you using our FTP client?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Dec/24 ]
No, we are using Microsoft&#39;s win sftp client.
Comment by Maria Keshwala [ 02/Dec/24 ]
Well thats the reason you need to be migrated to our new sftp client let me know I can send one our IT rep to schedule a migration this will prevent missing files issues.
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Dec/24 ]
Okay. They can reach out to Michael at <a href="mailto:<EMAIL>"><EMAIL></a> to schedule the session.
Comment by Maria Keshwala [ 02/Dec/24 ]
Ok Thank you I will forward the information
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 02/Dec/24 ]
Hi Tim I was told that you cant be migrated as you use your own sftp we were migrating customers from our old sftp to our new one. I did address that issue though
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 03/Dec/24 ]
So is there an advantage to using Datatrans&#39; new sftp client?
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 03/Dec/24 ]
Dedicated /fromdts and /todts folders and 15 min interval push and pulls. Reason you are inquiring?</p>
<h4 id="cs-33487-eh-baare-created-25-nov-24-updated-02-dec-24-resolved-02-dec-24">[CS-33487] EH Baare Created: 25/Nov/24  Updated: 02/Dec/24  Resolved: 02/Dec/24</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image-20241202-144228.png
Request Type: Emailed request
Request language: English
Request participants: None Organizations:
Description
Hi Maria – we are having some issue on a few “manual” created 810 Invoices via WedEDI. In our failed validation folder, you we see (2) of the (7) I believe that failed.
Please take a look at Message ID’s: 42441242 and 42441230 and determine why these will not send
Travis Crumrin
500 Heath Toffee Ave. Robinson, IL 62454
O 618-546-1575 x114 | C 618-554-6996
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 25/Nov/24 ]
Comment by Maria Keshwala [ 25/Nov/24 ]
Hello Travis Good morning
I will take a look at those and advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 26/Nov/24 ]
Hi Travis something with the TP Im getting an error , checking the settings or see if you have to create the document in a different way
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 02/Dec/24 ]
Hello Travis
If I create the 810 from the TP’s 830 I don&#39;t get the error . for that particular one you sent they only sent 824s and it was manually entered which will not work. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33442-invoice-errors-created-22-nov-24-updated-02-dec-24-resolved-02-dec-24">[CS-33442] Invoice Errors Created: 22/Nov/24  Updated: 02/Dec/24  Resolved: 02/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Description
Hey All,
We received the following invoices with ISS segments that contained commas and decimals causing an error.
22217655
22217662
22217696
22217699
********
********
All numeric values can be sent soley with numbers i.e. 12,3456.78 can be sent as ********. Can you please resend all of the invoices with an A at the end? Our system will reject them if sent with the same invoice number. Please let me know if there are any questions.
Thanks!
Malcolm O’Donnell
EDI Analyst
Dot Foods, Inc.
(815) 669-5815
[www.dotfoods.com|www.dotfoods.com]
Comments
Comment by Maria Keshwala [ 25/Nov/24 ]
Can you please advice who the customer is company name and ISA ID DTS account number to further assist on this?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 02/Dec/24 ]
no replies
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33434-ace-hardware-created-22-nov-24-updated-26-nov-24-resolved-25-nov-24">[CS-33434] Ace Hardware Created: 22/Nov/24  Updated: 26/Nov/24  Resolved: 25/Nov/24</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: kila rose Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.gif      image002.png      image003.png      image004.png      image005.png      image006.jpg      image007.jpg      image008.jpg      image009.jpg     image002 (ac5748b1-98fb-4a89-8d1f-8fd7194b1851).png      image001.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice rejection
Description
Please advise as we did correct a few invoices.
Do we need to correct again?
Are these accepted or rejected invoices?
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
Comments
Comment by kila rose [ 22/Nov/24 ]
Comment by Maria Keshwala [ 25/Nov/24 ]
Hello they are sending you 864 letting you know they rejected some of your invoices you need to open each one of the the 864s and read under text message information it looks like you already submitted the invoice reference number read and correct if necessary, you need to restage if it requires to be corrected , but thats what this is . They provided a phone number for additional information in case your think this was an error on their end.
“Please be advised that the following EDI 810 transmission has been rejected for the reasons noted below. Vendor Number: 0000088531 Invoice Number: 2131772 Invoice Date: 20240820 Invoice Amount: 535.32 Invoice number 2131772 already exists. This invoice will not be paid since the transmission was rejected. If necessary, please correct the information and re-transmit to Ace. For additional assistance please contact the Ace Hardware offices at (*************.”
Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by kila rose [ 25/Nov/24 ]
Hello
Thank you
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by kila rose [ 25/Nov/24 ]
We resubmitted/restaged these invoices and now we have an exception.
We do have a lot that say accepted but we received the 864
Here is an example invoice # 213348.
How do we correct this?
Thank you .
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Maria Keshwala [ 25/Nov/24 ]
Hello Keli the EDI format of the document its correct meaning none EDI errors but they are saying you already sent the invoice number before therefore you need to contact them and check read the 864s
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 25/Nov/24 ]
T correct the invoice number if you need to correct anything then you click restage on the invoice
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by kila rose [ 25/Nov/24 ]
Hello Maria,
We will .
Thank you .
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a></p>
<h4 id="cs-33422-re-fl2-plant-city-expansion-v-88531-nation-wide-products-created-22-nov-24-updated-27-nov-24-resolved-25-nov-24">[CS-33422] RE: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS Created: 22/Nov/24  Updated: 27/Nov/24  Resolved: 25/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: kila rose Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.jpg      image004.jpg      image005.jpg      image017.jpg      image018.png      image019.jpg      image020.jpg     image021.png      image022.png      image023.jpg      image024.png      image025.png      image026.png      image027.png      image028.png      image002
(a493d9e3-55a2-4b1c-ad54-1fc603d514a2).png      image001 (1a96acb2-dc9b-4b9a-bfca-fa039f7fd08b).png      image003 (2201d50a-3e01-4708-9a7c-37d7fb881c4e).jpg     image005.png      image004 (2103d6ac-4bd1-48b8-863d-5f1990b0b53b).jpg      image-20241127-145705.png      image-20241127-150614.png      image009.png
Issue links: Duplicate is duplicated by CS-31716 FW: FL2 Plant City Expansion - v#8853... Resolved
Request Type: Emailed request
Request language: English Request participants: Organizations:
Description
Hello ,
What pool code will this fall under?
Thank you .
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Selena Ochoa <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 19, 2024 9:30 AM To: &#39;Sara Doyle&#39; <a href="mailto:<EMAIL>"><EMAIL></a>; Kila Rose <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Jared Bennett <a href="mailto:<EMAIL>"><EMAIL></a>; Melinda Gann <a href="mailto:<EMAIL>"><EMAIL></a>; Helen Hopkins <a href="mailto:<EMAIL>"><EMAIL></a>; Nation Wide Products Accounts Receivable <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS
I can confirm that this information was received on my end. However, my role is in accounts receivables so I cannot confirm any information for the logistics department. I can ensure that this information is forwarded to higher-level management for visibility.
Thank you,
Selena Ochoa | Accounts Receivable
Nation Wide Products, LLC
1301 S Treadaway Blvd
Abilene, TX 79602
************ x 108 | Office
************ | Fax
From: Sara Doyle <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 19, 2024 9:26 AM To: Selena Ochoa <a href="mailto:<EMAIL>"><EMAIL></a>; Kila Rose <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Jared Bennett <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS
Good morning Selena and Kila,
Wanted to confirm you received the below?
Sara Doyle
|
| ************ (Mobile) |
| <a href="mailto:<EMAIL>"><EMAIL></a> |
| <a href="http://www.jmisales.com">www.jmisales.com</a> |
| 125 W. 55th Street, STE 102 Clarendon Hills, IL 60514 |
From: Sara Doyle <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 14, 2024 11:45 AM To: Selena Ochoa <a href="mailto:<EMAIL>"><EMAIL></a>; Kila Rose <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Jared Bennett <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Fw: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS
Hello,
Please see below from Ace regarding the order for their FL02 warehouse expansion. Please confirm received.
Thanks!
From: Brazel, Tyler <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 14, 2024 9:40:11 AM To: Sara Doyle &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Dear Ace Vendor Partner, The following updates pertain to ASRS (automated storage and retrieval system) installations to service break case area SKUs in the warehouse below.
· Each order will have specific requested ship dates which will allow us to stock this RSC in an efficient manner. These expansion orders are for SKUs not currently stocked in this location.
· **** It is critical that you follow the instructions outlined on the purchase order and ship when indicated.* There is a strong possibility that orders that are shipped early will be refused and then you will be responsible to ship again at the requested date.
v#88531 NATION WIDE PRODUCTS PO# VRWGU Requested Ship = 1/31/2025
<em>IncoTerms: PPD, Destination RSC = FL02 Payment Terms: 2% 60 Days</em>
Refer to the information below for PO information:
Location Break-Case Area PO Creation
Plant City (FL02) Beginning 11/13/24
As a reminder, below is the DUNS number and shipping address for this facility:
RSC Code Address DUNS
FL02 791 S County Line Road Plant City, FL 33566
0069283119905 Plant City Retail Support Center
Please let me know if you have any questions. Thank you
Comments
Comment by kila rose [ 22/Nov/24 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/Nov/24 ]
Hi Kila,
Apologies I am not sure what a pool code is.
Sara Doyle
|
| ************ (Mobile) |
| <a href="mailto:<EMAIL>"><EMAIL></a> |
| <a href="http://www.jmisales.com">www.jmisales.com</a> |
| 125 W. 55th Street, STE 102 Clarendon Hills, IL 60514 |
Comment by kila rose [ 22/Nov/24 ]
Hello ,
I am including the communication for our EDI provider .
Please let us know if I need to reach out for more information.
Thank you
Maria Keshwala commented:
Hi Kila for this new DC I need a Pool code and DC code to add the address thanks
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Maria Keshwala [ 25/Nov/24 ]
we need a dc code and pool code once the customer provided this information then we will update the location worked on another ticket
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by kila rose [ 25/Nov/24 ]
Hello,
Do we have any updates on this?
Thank you !
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Maria Keshwala [ 26/Nov/24 ]
Hello Kila you need to provide us the correct address maybe this is not pretending to a shipping address location with DC code and POOl codes otherwise we cant add this to the address table thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Nov/24 ]
Kila,
Can you help Maria with her request?
Kind Regards,
Helen
Helen Hopkins
Executive Assistant
Nation Wide Products, LLC
Phone ************, ext. 102
Fax: ************
“If you have knowledge let others light their candles in it.” – Margaret Fuller
Comment by Maria Keshwala [ 27/Nov/24 ]
See below address is coming over to the 856 thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Maria Keshwala [ 27/Nov/24 ]
Asked support help to add the DUNS on the global map for ACE Hardware -
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 27/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Maria Keshwala [ 27/Nov/24 ]
Hell you guys should be all set thank you -
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by kila rose [ 27/Nov/24 ]
Thank you for you help !
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Nov/24 ]
Thank you! Can you send the test ASN over to Ace for verification?
Thank you,
Selena Ochoa | Accounts Receivable
Nation Wide Products, LLC
1301 S Treadaway Blvd
Abilene, TX 79602
************ x 108 | Office
************ | Fax
Comment by Maria Keshwala [ 27/Nov/24 ]
Hello no we dont send test and I just confirmed with the analyst that via EDI never the SHiP TO is sent to ACE. so you guys are good and that is in ACEs specifications
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33367-fwd-si-error-message-prod-error-bp-id-*********-typingservice-processdata-failed-required-parameter-s-not-found-in-primary-document-count-1-created-21-nov-24-updated-02-dec-24-resolved-02-dec-24">[CS-33367] Fwd: SI Error Message [PROD] Error BP ID : ********* : TypingService.processData() failed. Required parameter(s) not found in Primary Document|Count:1 Created: 21/Nov/24  Updated: 02/Dec/24  Resolved: 02/Dec/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-20241202-152627.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Description
Hi Support,
Can you please look into this error Daniel is receiving. I do not know how to change element separators.
Best Regards, Kayleigh
---------- Forwarded message ---------From: Akshitha Narasegowda <a href="mailto:<EMAIL>"><EMAIL></a> Date: Thu, Nov 21, 2024 at 12:01 AM Subject: RE: SI Error Message [PROD] Error BP ID : ********* : TypingService.processData() failed. Required parameter(s) not found in Primary Document|Count:1 To: Daniel Stiefvater <a href="mailto:<EMAIL>"><EMAIL></a>, <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Cc: EDI FD <a href="mailto:<EMAIL>"><EMAIL></a>, EDI_IS <a href="mailto:<EMAIL>"><EMAIL></a>, Maia Palmer <a href="mailto:<EMAIL>"><EMAIL></a>, Rebeka Espitia <a href="mailto:<EMAIL>"><EMAIL></a>, EDI Testing <a href="mailto:<EMAIL>"><EMAIL></a>
Hello Daniel,
Please remove extra ~ in ISA segment and add ISA<em>16( Component Element Separator) field and resend the data.
ISA</em>00* 00 ZZ<em>DTS6256 <em>ZZ</em>DLTRCAN <em>241119</em>2148</em>U<em>00401</em>*********<em>0</em>P~~
Example:
ISA<em>00</em> 00 ZZ<em>SenderID <em>ZZ</em>ReciverID <em>241119</em>2148</em>U<em>00401</em>000000002<em>0</em>P&gt;~
Thanks &amp; Regards,
Akshitha
E-Commerce Team
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Daniel Stiefvater <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 20, 2024 6:13 PM To: Akshitha Narasegowda <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Cc: EDI FD <a href="mailto:<EMAIL>"><EMAIL></a>; EDI_IS <a href="mailto:<EMAIL>"><EMAIL></a>; Maia Palmer <a href="mailto:<EMAIL>"><EMAIL></a>; Rebeka Espitia <a href="mailto:<EMAIL>"><EMAIL></a>; EDI Testing <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: SI Error Message [PROD] Error BP ID : ********* : TypingService.processData() failed. Required parameter(s) not found in Primary Document|Count:1
CAUTION: This email originated from outside of the organization. Do not click on links or open attachments unless you were expecting them.
Hello Akshitha,
Thank you for your message.
Please advise what is required.
I’ve CC’d the team who did the initial EDI connection set up – our EDI platform is DataTrans.
I’ve also attached the PDFs of the 2 POs received through DataTrans.
Thanks all,
Daniel
Daniel Stiefvater
Owner/Operator
FOLEY DOG TREAT COMPANY
1945-B Bollinger Rd
Nanaimo, BC, V9S 5W9
Mobile: ************
<a href="http://www.foleydogtreat.com">www.foleydogtreat.com</a>
From: Akshitha Narasegowda <a href="mailto:<EMAIL>"><EMAIL></a> Sent: November 20, 2024 12:55 AM To: Daniel Stiefvater <a href="mailto:<EMAIL>"><EMAIL></a> Cc: EDI FD <a href="mailto:<EMAIL>"><EMAIL></a> Subject: SI Error Message [PROD] Error BP ID : ********* : TypingService.processData() failed. Required parameter(s) not found in Primary Document|Count:1
Hi Team,
The below file got failed due to extra delimiter(<del>) in the ISA segment and Mandatory Data Missing At ISA<em>16 . Please correct the data and resend.
Data:
ISA</em>00* 00 ZZ<em>DTS6256 <em>ZZ</em>DLTRCAN <em>241119</em>2148</em>U<em>00401</em>*********<em>0</em>P~~ --- &gt; Mandatory Data Missing At ISA*16 &amp; extra delimiter(</del>) in the ISA segment
GS<em>FA</em>DTS6256<em>DLTRCAN</em>20241119<em>2148</em>6<em>X</em>004010~
ST<em>997</em>00006~
AK1<em>PO</em>1~
AK2<em>850</em>0001~
AK5<em>A~
AK2</em>850<em>0002~
AK5</em>A~
AK9<em>A</em>2<em>2</em>2~
SE<em>8</em>00006~
GE<em>1</em>6~
IEA<em>1</em>*********~
Thanks &amp; Regards,
Akshitha
E-Commerce Team
<a href="mailto:<EMAIL>"><EMAIL></a>
----Original Message----From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 19, 2024 10:51 PM To: COGNIZANT_FDMOST <a href="mailto:<EMAIL>"><EMAIL></a>; EDI FD <a href="mailto:<EMAIL>"><EMAIL></a> Subject: SI Error Message [PROD] Error BP ID : ********* : TypingService.processData() failed. Required parameter(s) not found in Primary Document
This message was automatically generated on 2024-11-19 22:51 by SI [PROD]. If you think you have received this message in error, please contact the EDI team.
Business Process Name: FD_EDI_INBOUND_DEENVELOPE-X12
Business Process ID: *********
Notification Type: TypingService.processData() failed. Required parameter(s) not found in Primary Document
Notification Message:
**PRIVILEGE AND CONFIDENTIALITY NOTICE: The information in this e-mail is intended for the named recipient(s) only. This e-mail may contain confidential, proprietary or privileged information. If you are not an intended recipient, please advise by return e-mail and delete immediately without reading, printing, or forwarding to others.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/Nov/24 ]
Comment by Maria Keshwala [ 22/Nov/24 ]
Hi Kayleigh
do you have a reference number or message ID to check this ? I see the project ended in august but this is the first time they are sending documents was this tested or no. but provide me an message ID to look into this . Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/Nov/24 ]
Hi Maria,
It looks like the error is on the 997&#39;s they are sending out so there is no ref number.
Thank you, Kayleigh
Comment by Maria Keshwala [ 02/Dec/24 ]
we sent on 11/19/2024
<em>ISA_00_ 00 ZZ_DTS6256 _ZZ_DLTRCAN <em>241119_2148_U_00401_*********_0_P~ GS_FA_DTS6256_DLTRCAN_20241119_2148_6_X_004010 ST_997_00006 AK1_PO_1 AK2_850_0001 AK5_A</em> AK2_850_0002 _AK5_A AK9_A_2_2_2 SE_8_00006 GE_1_6 IEA_1_*********</em>
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33340-help-created-21-nov-24-updated-21-nov-24-resolved-21-nov-24">[CS-33340] HELP Created: 21/Nov/24  Updated: 21/Nov/24  Resolved: 21/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241121-181230.png      image001.png
Request Type: Emailed request
Request language: English Request participants: Organizations: Label: Missing documents FTP, Global Shop
Description
I came to work early so that I can attend a funeral and I have no Datra Trans files to process. ☹
Marcy Lowrance
E H Baare Corp
618-546-1575 ext 107
Comments
Comment by Maria Keshwala [ 21/Nov/24 ]
Hello Marcy
Let me look into that now thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 21/Nov/24 ]
Hi Marcy did you see the files ?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 21/Nov/24 ]
It looks like they been picked up
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 21/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/Nov/24 ]
We have the files now – Thank you Maria
Travis Crumrin
500 Heath Toffee Ave. Robinson, IL 62454
O 618-546-1575 x114 | C 618-554-6996
Comment by Maria Keshwala [ 21/Nov/24 ]
You’re welcome thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33301-advance-auto-error-urgent-created-20-nov-24-updated-22-nov-24-resolved-22-nov-24">[CS-33301] Advance Auto Error URGENT Created: 20/Nov/24  Updated: 22/Nov/24  Resolved: 22/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Candie Rogers Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      Advance PO 753131 DataTrans.pdf      Advance PO 753131 NetSuite.pdf      image-20241121-181735.png      image-20241121-185701.png     image-20241121-192053.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations:
Description
Good Morning,
Attached is the PO we received in EDI and the sales order in NetSuite after I integrated. Can you please advise on why the numbers have changed?
EXAMPLE:
EDI PO- HSSEDT16ZSP shows 84
NetSuite Sales Order-HSSEDT16ZSP shows 14.
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comments
Comment by Candie Rogers [ 20/Nov/24 ]
<em>Advance PO 753131 DataTrans.pdf  (173 kB)</em>
<em>Advance PO 753131 NetSuite.pdf  (10 kB)</em>
Comment by Candie Rogers [ 20/Nov/24 ]
HI, I just wanted to follow up on this. Thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 21/Nov/24 ]
Hello Candie looking into this thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 21/Nov/24 ]
Message ID 42393539---- Reference number 753131
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 21/Nov/24 ]
inbound document in ECS-04 BATCH id 41433767
MAP 850_AdvanceAuto_004010 to Netsuite PO_new.dtm
HSSEDT16ZSP
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 21/Nov/24 ]
ecs-02 batch id 131462378
original 131462376
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 21/Nov/24 ]
Hello Candie Ineed to have the analyst look into this I found the error and is showing a different quantity once its translated.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 21/Nov/24 ]
Is it ok for me to manually fix it in NetSuite so I can get this order processed? Thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 21/Nov/24 ]
yes Candie do that this is on the list for Kenrick to verify and fixed thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 21/Nov/24 ]
Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 21/Nov/24 ]
Message ID 42393539---- Reference number 753131
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
Hello Candie Kenrick has fixed the advance auto issue all set . Thank you the right quantities should display on translation
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-33298-critical-dell-pos-being-routed-to-unknown-partner-instead-of-dell-created-20-nov-24-updated-21-nov-24-resolved-21-nov-24">[CS-33298] CRITICAL: Dell POs being routed to unknown partner instead of Dell Created: 20/Nov/24  Updated: 21/Nov/24  Resolved: 21/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Chris McLaird Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Description
Good Morning,
Somehow our Dell Pos are getting routed to a partner we are unfamiliar with. Please help us get this fixed ASAP, and re-send all the Pos we have placed to Dell. This appears to have started 11/13. I can’t tell you a msg id because it tells me I don’t have permission for that trading partner.
Chris McLaird Director of Enterprise IT
Toll-Free (*************
General (*************</p>
<ul>
<li>Website * | [ LinkedIn |https://www.linkedin.com/company/traferaofficial/] | [ Twitter |https://twitter.com/TraferaOfficial/] | [ Facebook |https://www.facebook.com/TraferaOfficial/] | [ Instagram |https://www.instagram.com/traferaofficial/] | [ YouTube |https://www.youtube.com/channel/UCEXEtAuZCcjUQrb6rptUEqQ]
<a href="https://www.trafera.com/signature-special/">https://www.trafera.com/signature-special/</a>
Confidentiality Note: This email and any files transmitted with it are confidential and are to be viewed solely by the individual(s) to whom they are addressed. Unauthorized use, distribution or disclosure of the contents of this email is strictly prohibited. If there is reason to believe that you are not the intended recipient, please notify the sender immediately and destroy all copies of this email and any files it contained. Thank you.
Comments
Comment by Chris McLaird [ 20/Nov/24 ]
Comment by Chris McLaird [ 20/Nov/24 ]
please ensure duplicate POs do not get sent to Dell, we have re-sent at least one.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 20/Nov/24 ]
Hi Chris,
My apologies, I had a routing mixup in your 850 map from the Synnex go-live that impacted Dell, incorrectly assigning it to a different entity. This has been resolved, and I&#39;ve reviewed the others and confirmed Dell was the only one impacted by the update.
I went ahead and re-processed the 850&#39;s for you as well, fyi. You will see them flowing into your WebEDI account shortly.
Comment by Maria Keshwala [ 21/Nov/24 ]
Issue resolved by Robert.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ul>
<h4 id="cs-33297-adam-extract-step-one-ecs-03-failed-dates-11-18-2024-11-20-2024-created-20-nov-24-updated-22-nov-24-resolved-22-nov-24">[CS-33297] Adam Extract step one ECS-03 Failed dates 11/18/2024-11/20/2024 Created: 20/Nov/24  Updated: 22/Nov/24  Resolved: 22/Nov/24</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Technical Support Priority: Medium
Reporter: Maria Keshwala Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-151649.png
Comments
Comment by Maria Keshwala [ 20/Nov/24 ]
AdamsExtract856_FF to HEB856.dtm
Rule #72: Not enough data (expression: &#39;LIN03=AltItem&#39;, data: &#39;</p>
<h4 id="cs-33294-class-error-created-20-nov-24-updated-11-dec-24-resolved-22-nov-24">[CS-33294] Class error Created: 20/Nov/24  Updated: 11/Dec/24  Resolved: 22/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Candie Rogers Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image001 (35e99e7f-b54d-4160-aad5-12fa9e84e2a3).png      image-20241122-143218.png      image-20241122-142650.png     image-20241122-143007.png      image-20241122-143235.png      image-20241122-143702.png      image-20241122-143643.png      image-20241122-143736.png     image001 (ff3240a1-969b-424b-bd3a-2eac7c52c103).png      image001 (327df356-7643-4f30-b0a3-68408ce9b87e).png
Request Type: Emailed request Request language: English
Request participants: None
Organizations: Label: Netsuite
Description
Good Morning,
Please advise on this error….
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comments
Comment by Candie Rogers [ 20/Nov/24 ]
Comment by Candie Rogers [ 21/Nov/24 ]
I received 2 more class errors this morning. Please advise.
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 21/Nov/24 ]
Hello Candie
I will look into this matter thank you
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 22/Nov/24 ]
Good Morning,
I’m just following up on this since we do have a delivery timeline for these customers. I can enter manually if need be. Thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 22/Nov/24 ]
HiCandie go ahead and do it manually I was taking care of the other issues and now jumping on this one until I figured it out . I will let you know please do it manually for now thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 22/Nov/24 ]
Ok, thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 22/Nov/24 ]
po 1340550A43RR00
map 3 times
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
Candie it looks like a mapping issue can you please verify the mapping on that PO with your mapping and catalog ?
For example the EDTQWAG16OXSQ is map in the catalog for Autozone only please verify all of that first .and the other PO I saw is not mapped for O’Reilly Thank you .
PO
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 22/Nov/24 ]
It appears the mapping is correct. The error I’m receiving has to do with CLASS. One of the PO’s that is giving me an error doesn’t have EDTWAG on it, so I don’t believe the error has anything to do with EDTWAG.
Below is the mapping:
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Candie Rogers [ 22/Nov/24 ]
This is the error I’m receiving. Message ID 42400391 doesn’t have the EDTWAG on the PO.
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 22/Nov/24 ]
Hi Candie Both POs same issue with mapping check the other one in red they both have mapping issues
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 22/Nov/24 ]
I am aware that we have mapping issues with our different products b/c several of our vendors have different SKU’s for our products. However, this issue keeps saying the CLASS is the issue so I’m not understanding how the SKU mapping will fix the class error.
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 22/Nov/24 ]
Hi Candie to further assist the mapping needs to be corrected correct in this files for us to troubleshoot this this class s could mean different thing the first thing we need to address is t he mapping issue - once that is fixed we can troubleshoot again I cant give this to development if there are mapping errors that needs to be address first.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 22/Nov/24 ]
The mapping issues are currently being worked on by DataTrans. Is there anyone else I can talk to that can help me resolve this issue. It worked until this week. Thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 22/Nov/24 ]
Let me ask Jen because they are working on other things for you. but not sure about the mapping in Catalog let me let her know
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
They are working on Advance auto and Fred Invoice issue
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
Hi Candie we added this one to the list. escalated to Kenrick to work on this issue. We will keep you posted. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 22/Nov/24 ]
Thank you! I will enter manually in the mean time.
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 02/Dec/24 ]
Asked Jennifer for a status update
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 02/Dec/24 ]
HI Candie was this issue resolved they encourage about fixing the catalog on this one let me know thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 02/Dec/24 ]
I did have it happen one day last week, I still don’t believe it has anything to do with the catalog. It is a CLASS error.
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 11/Dec/24 ]
Hi Candie have you seen the class error again?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 11/Dec/24 ]
Not so far, Kendrick fixed it. Thanks for checking!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Please note: We are closed Christmas Eve 12/24 and Christmas Day 12/25. 🎄🎅🏻☃❆🎀🔔☃
New Year’s Eve 12/31 and New Year&#39;s Day 1/1. 🎉
Comment by Maria Keshwala [ 11/Dec/24 ]
ok awesome thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31728-inv-errors-created-19-nov-24-updated-20-nov-24-resolved-20-nov-24">[CS-31728] INV errors Created: 19/Nov/24  Updated: 20/Nov/24  Resolved: 20/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Candie Rogers Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image-********-145231.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
Description
Good afternoon,
Several of the invoices I sent this afternoon still say SENT and 3 show sent failed, can you please advise on what the issue is so I can fix it? Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comments
Comment by Candie Rogers [ 19/Nov/24 ]
Comment by Maria Keshwala [ 20/Nov/24 ]
Hi Candie let me look into this and get back to you as soon as I can thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 20/Nov/24 ]
Good Morning,
It looks like they have already been fixed. Thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 20/Nov/24 ]
When I looked they have been accepted . Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31726-cat005-edi-ship-to-code-for-caterpillar-location-ud-denison-tx-created-19-nov-24-updated-26-nov-24-resolved-26-nov-24">[CS-31726] CAT005 - EDI SHIP TO CODE FOR CATERPILLAR LOCATION UD - DENISON, TX. Created: 19/Nov/24  Updated: 26/Nov/24  Resolved: 26/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Glen Houghtaling Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image003.png      image004.png      image002.png      image008.png      image-20241122-155504.png      image004 (326a3023-f238-4d5d-9a74-47e4d9d4f207).png      image002 (d1d1eba9-0fc7-4f0f-a6a9-de1214f4bbfa).png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Global Shop, Invoice rejection
Description
Hello,
I am hoping you can confirm the proper ship to code for Caterpillar trading partner ID 2826. My data from caterpillar says the ship to code for location UD is 005070479UD. Our global shop EDI is set up using ship to code 0010. Milcut received EDI data for PO **********, but we cannot verify the ship to from that data. Our invoices (289685 and 297594) are not processing. I am trying to figure out why.
My notes:
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
Comments
Comment by Glen Houghtaling [ 19/Nov/24 ]
Comment by Maria Keshwala [ 20/Nov/24 ]
Good morning Glen
I will look into this Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
Hi Glen I see the 830 is showing as Ship to Code 0010 is that what you usually go by whats on the 830?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Glen Houghtaling [ 22/Nov/24 ]
The invoices submitted under 0010 are not being processed by Caterpillar. I was hoping you could tell me what ship to code is set up for Caterpillar trading partner ID 2826.
From the 830
Per Cat Interchange Order –
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
Comment by Glen Houghtaling [ 22/Nov/24 ]
My goal is to determine why invoices 289485 and 297594 are not processing. Caterpillar EDI has been no help. I am trying to see if there is a disconnect between the data we are sending to Cat, or is the problem on their end. I have tried UD, 0010, and 005070479UD as the EDI interchange number. Nothing has worked.
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
From: Glen Houghtaling
Comment by Maria Keshwala [ 22/Nov/24 ]
Hi Glen we need to email the contact at Caterpillar so they can confirmed as I checked the 830s and I see 0010 for Ship to I will have to email them the raw data and see if they can confirmed
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Glen Houghtaling [ 22/Nov/24 ]
I think my contact for Caterpillar EDI is no longer there. I have tried to get help, but it is challenging.
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
Comment by Maria Keshwala [ 22/Nov/24 ]
No worries will get somebody will be on s separate email though
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 26/Nov/24 ]
Hi Glen you need to contact the accounting department at CAT and investigate whats the assigned by buyer for you to send the invoices here is a contact <a href="mailto:<EMAIL>"><EMAIL></a>
We see what we received from them . Please let me know if there is anything else you need
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31716-fw-fl2-plant-city-expansion-v-88531-nation-wide-products-created-19-nov-24-updated-26-nov-24-resolved-26-nov-24">[CS-31716] FW: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS Created: 19/Nov/24  Updated: 26/Nov/24  Resolved: 26/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: kila rose Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image006.png      image007.png      image008.jpg      image009.jpg      image010.jpg      image011.jpg      image012.png      image013.png      image014.png      image015.png      image016.png
Issue links: Duplicate duplicates CS-33422 RE: FL2 Plant City Expansion - v#8853... Resolved
Request Type: Emailed request
Request language: English Request participants: None
Organizations:
Description
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Kila Rose Sent: Tuesday, November 19, 2024 9:41 AM To: &#39;Sara Doyle&#39; <a href="mailto:<EMAIL>"><EMAIL></a>; Selena Ochoa <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Jared Bennett <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS
Hello
Confirmed .
I will reach out to our EDI carrier for any updates needed.
Thank you
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Sara Doyle <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 19, 2024 9:26 AM To: Selena Ochoa <a href="mailto:<EMAIL>"><EMAIL></a>; Kila Rose <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Jared Bennett <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS
Good morning Selena and Kila,
Wanted to confirm you received the below?
Sara Doyle
|
| ************ (Mobile) |
| <a href="mailto:<EMAIL>"><EMAIL></a> |
| <a href="http://www.jmisales.com">www.jmisales.com</a> |
| 125 W. 55th Street, STE 102 Clarendon Hills, IL 60514 |
From: Sara Doyle <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 14, 2024 11:45 AM To: Selena Ochoa <a href="mailto:<EMAIL>"><EMAIL></a>; Kila Rose <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Jared Bennett <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Fw: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS
Hello,
Please see below from Ace regarding the order for their FL02 warehouse expansion. Please confirm received.
Thanks!
From: Brazel, Tyler <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 14, 2024 9:40:11 AM To: Sara Doyle &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS
CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Dear Ace Vendor Partner, The following updates pertain to ASRS (automated storage and retrieval system) installations to service break case area SKUs in the warehouse below.
· Each order will have specific requested ship dates which will allow us to stock this RSC in an efficient manner. These expansion orders are for SKUs not currently stocked in this location.
· **** It is critical that you follow the instructions outlined on the purchase order and ship when indicated.* There is a strong possibility that orders that are shipped early will be refused and then you will be responsible to ship again at the requested date.
<em>v#88531 NATION WIDE PRODUCTS PO# VRWGU Requested Ship = 1/31/2025 IncoTerms: PPD, Destination RSC = FL02 Payment Terms: 2% 60 Days</em>
Refer to the information below for PO information:
Location Break-Case Area PO Creation
Plant City (FL02) Beginning 11/13/24
As a reminder, below is the DUNS number and shipping address for this facility:
RSC Code Address DUNS
FL02 791 S County Line Road Plant City, FL 33566
0069283119905 Plant City Retail Support Center
Please let me know if you have any questions. Thank you
Comments
Comment by kila rose [ 19/Nov/24 ]
Comment by Maria Keshwala [ 21/Nov/24 ]
Hello Kila
Can you please advice what this issue is about how can I assist you thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
Hi Kila I asked yesterday what this issue is about as it created a support ticket please advise otherwise I will close this ticket and it will reopen once you reply back. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by kila rose [ 22/Nov/24 ]
There was no issue .
In the attachment there was new locations listed.
Im ensuring they are added as needed.
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Maria Keshwala [ 22/Nov/24 ]
Hi thank you for confirming which Trading partner is this update for ?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by kila rose [ 22/Nov/24 ]
Ace hardware
Dear Ace Vendor Partner, The following updates pertain to ASRS (automated storage and retrieval system) installations to service break case area SKUs in the warehouse below.
· Each order will have specific requested ship dates which will allow us to stock this RSC in an efficient manner. These expansion orders are for SKUs not currently stocked in this location.
· **** It is critical that you follow the instructions outlined on the purchase order and ship when indicated.* There is a strong possibility that orders that are shipped early will be refused and then you will be responsible to ship again at the requested date.
<em>v#88531 NATION WIDE PRODUCTS PO# VRWGU Requested Ship = 1/31/2025 IncoTerms: PPD, Destination RSC = FL02 Payment Terms: 2% 60 Days</em>
Refer to the information below for PO information:
Location Break-Case Area PO Creation Plant City (FL02) Beginning 11/13/24
As a reminder, below is the DUNS number and shipping address for this facility:
RSC Code Address DUNS FL02 791 S County Line Road
Plant City, FL 33566 0069283119905 Plant City Retail Support Center
Please let me know if you have any questions. Thank you
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Maria Keshwala <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, November 22, 2024 8:19 AM To: Kila Rose <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-31716 RESOLVED  FW: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS
———-—
Reply above this line.
Maria Keshwala commented:
Hi thank you for confirming which Trading partner is this update for ?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
View request · Turn off this request&#39;s notifications
Sent on November 22, 2024 7:18:51 AM MST
Comment by Maria Keshwala [ 22/Nov/24 ]
Hi Kila for this new DC I need a Pool code and DC code to add the address thanks
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
unless this is something else
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by kila rose [ 22/Nov/24 ]
Im reaching out to Ace hardware.
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Maria Keshwala [ 22/Nov/24 ]
Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31342-as2-tdts-file-directory-failed-banner-engeneering-created-15-nov-24-updated-15-nov-24-resolved-15-nov-24">[CS-31342] AS2 TDTS FILE DIRECTORY FAILED BANNER ENGENEERING Created: 15/Nov/24  Updated: 15/Nov/24  Resolved: 15/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Technical Support Priority: Medium
Reporter: Maria Keshwala Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241115-173227.png      image-20241115-183941.png      image-20241115-191046.png
Comments
Comment by Maria Keshwala [ 15/Nov/24 ]
850s from different TPs
Rule #19: Rule execution failed (expression: var_N104_bt_sites=DBLookupEx(var_bill_to_xref_sites, &#34;SELECT bill_to FROM banner_bill_to_sites_xrefs.csv WHERE TP_Name = &#39;&#34; &amp; var_N102_bt &amp; &#34;&#39; AND TP_Code = &#39;&#34; &amp; N104 &amp; &#34;&#39;&#34;) )
Map = Turck Germany_850 to Banner_POin_Flat_File.dtm
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 15/Nov/24 ]
Banner Canada 850 map reinstalled
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 15/Nov/24 ]
Newark Electronics - 850 to Banner
Rule #27: Rule execution failed (expression: var_N104_st_sites=DBLookupEx(var_ship_to_xref_sites, &#34;SELECT ship_to FROM banner_ship_to_sites_xrefs.csv WHERE TP_Name = &#39;&#34; &amp; var_N102_st &amp; &#34;&#39; AND TP_Code = &#39;&#34; &amp; N104 &amp; &#34;&#39;&#34;) )
(Map: &#39;Newark_850 to Banner_POin_Flat_File_NEW.dtm - invalid map not on file
the map that was running in ecs-04 is - Newark_850 to Banner_POin_Flat_File
fixing and restaging
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 15/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 15/Nov/24 ]
Now the error is a table lookup for value UPS GRD I/B COL
banner_shipVia_xrefs.csv
ShipVia&#34;, &#34;EDI_SCAC&#34;, TD503))
banner_crossreferance_shipvia.udl&#39;)
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 15/Nov/24 ]
Spoke with Kenrick Banners 850s moved back to 04.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31334-amg-resources-files-failed-in-ecs-03-delta-error-otis-sps-855-dts-6091-created-15-nov-24-updated-15-nov-24-resolved-15-nov-24">[CS-31334] AMG resources files failed in ECS-03 Delta Error oTIS SPS 855 DTS 6091 Created: 15/Nov/24  Updated: 15/Nov/24  Resolved: 15/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Technical Support Priority: Medium
Reporter: Maria Keshwala Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241115-163856.png      image-20241115-161148.png      image-20241115-160912.png      image-20241115-164153.png      image-20241115-164619.png
Comments
Comment by Maria Keshwala [ 15/Nov/24 ]
Restaged fixed the issue
missing delivery information in ECS-03
Delivery information in ECS-04
Customer was migrated from ECS-04 to ECS-03 files worked in ECS-04
AMG_855_FF to OTIS_SPS_855_004010.dtm
TPM MATCHES ALL THE INFORMATION
Rule #2: Error Creating Object (DOCUMENT(&#39;ANSI&#39;,&#39;4010&#39;,&#39;855&#39;))
Map: &#39;AMG_855_FF to OTIS_SPS_855_004010.dtm&#39;
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31328-rangaire-mfg-split-asn-failed-ecs-03-created-15-nov-24-updated-20-nov-24-resolved-20-nov-24">[CS-31328] Rangaire MFG Split ASN failed ecs-03 Created: 15/Nov/24  Updated: 20/Nov/24  Resolved: 20/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None Fix versions: None
Type: Technical Support Priority: Medium
Reporter: Maria Keshwala Assignee: Maria Keshwala
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241115-143212.png      image-20241115-142445.png      image-20241115-143424.png      image-20241115-144022.png      image-20241115-145307.png     image-20241115-150207.png      image-20241115-150432.png      image-20241115-151517.png      image-20241118-224058.png
Comments
Comment by Maria Keshwala [ 15/Nov/24 ]
Rule #42: Rule execution failed (expression: var_nodepointer=DBLookUpEx(&#34;mysql-webedi&#34;, &#34;SELECT CAST(nodepointer as CHAR) FROM webedi30.Messages m INNER JOIN webedi30.Elements e ON e.rootnode=m.rootnode INNER JOIN webedi30.Nodes n ON e.nodepointer =
ASN_FF_Rangaire to HDcom_856_004010.dtm
project completed 05/29/20223
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Maria Keshwala [ 15/Nov/24 ]
processed a good and bad ASN only the header the body is missing with the other segments
Reinstall the Map that fixed the issue restaged the files worked
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 15/Nov/24 ]
Found a good file from 09/25/2024 testing and see t
the good file failed as well asking an analyst
File from 11/14/2024
Error:
RANGAIRE - HD ASN MAPPING ERROR. PO# 5114866047, FAILED TO PROCESS:
ASN_FF_Rangaire to HDSupply_CHUB_856_004010.dtm
reinstalling the map did not worked the file has an error with this PO comparing a good file
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 15/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 15/Nov/24 ]
140146656 bad file 11/14/2024 good file 139203560
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 15/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Per Brantley
but it looks like they are missing the Line Item Number for the ones that are failing.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Maria Keshwala [ 20/Nov/24 ]
Customer resend the file
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31270-fw-fw-prod-error-in-workflow-841946508-bp-name-fm_spedeenvelope_i-created-14-nov-24-updated-26-nov-24-resolved-25-nov-24">[CS-31270] FW: FW: Prod | Error in workflow : 841946508 | BP Name : FM_SPEDeEnvelope_I Created: 14/Nov/24  Updated: 26/Nov/24  Resolved: 25/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Candie Rogers Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.png      image004.png      image.png      image001 (8aabe5a2-66ca-4602-980c-973f88a8946f).png      image-20241122-134656.png      image-20241122-140817.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Invoice errors
Description
Good afternoon,
When I am sending the Fred Meyer invoices and I put in the allowance, the allowance amount doesn’t come off of the total (see below). Can this please be fixed? Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
From: Jennifer Martin <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 14, 2024 2:39 PM To: Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: FW: Prod | Error in workflow : 841946508 | BP Name : FM_SPEDeEnvelope_I
It appears it&#39;s a template issue because the EDI goes out with the correct amount.
A ticket will need to be created to fix these issues.
On Thu, Nov 14, 2024 at 12:29 PM Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
When I put in a code it isn’t removing the amount from the total. The total should be $4135.96. They also show Salvage as the discount reason on the PO, but that isn’t an option in the drop down menu, so I just used quantity discount. I will try to re-send. Thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
From: Jennifer Martin <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 14, 2024 2:14 PM To: Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: FW: Prod | Error in workflow : 841946508 | BP Name : FM_SPEDeEnvelope_I
Yes, you need to select the type of allowance.
On Thu, Nov 14, 2024 at 11:36 AM Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Hi Jennifer,
Is this something you could possibly explain to me? It is for a rejected Fred Meyer INV187386. Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
From: FM edibiz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 14, 2024 1:32 PM To: Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Prod | Error in workflow : 841946508 | BP Name : FM_SPEDeEnvelope_I
Hi Team,
We could see that you are sending the EDI 810 document without SAC02 in SAC segment and with incorrect segment delimiter “|”.
TDS<em>413596|
SAC</em>A**4178|
CTT<em>3|
Please correct and resend the document with SAC02 and with segment delimiter “~’.
Thanks,
Janakiram Periyala
Kroger EDI | The Kroger Co.
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 14, 2024 2:39 AM
To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Prod | Error in workflow : 841946508 | BP Name : FM_SPEDeEnvelope_I
The following error has occurred on Business Process ID 841946508: &#34;&#34;.
BP Name Workflow ID Transaction ID ISA ControlNumber TP ISA ID Service Name(error) Error Desc State Status
FM_SPEDeEnvelope_I 841946508 810 000000005 DTS4984   ACTIVE ERROR
To take appropriate action, please go to the below Production B2Bi Dashboard links. <a href="http://u060b2bib101:40000/dashboard/portal/">http://u060b2bib101:40000/dashboard/portal/</a>
<a href="http://u060b2bib102:40000/dashboard/portal/">http://u060b2bib102:40000/dashboard/portal/</a>
<strong>This is a system generated email.Please do not reply to this</strong>
This e-mail message, including any attachments, is for the sole use of the intended recipient(s) and may contain information that is confidential and protected by law from unauthorized disclosure. Any unauthorized review, use, disclosure or distribution is prohibited. If you are not the intended recipient, please contact the sender by reply e-mail and destroy all copies of the original message.
This e-mail message, including any attachments, is for the sole use of the intended recipient(s) and may contain information that is confidential and protected by law from unauthorized disclosure. Any unauthorized review, use, disclosure or distribution is prohibited. If you are not the intended recipient, please contact the sender by reply e-mail and destroy all copies of the original message.
Comments
Comment by Candie Rogers [ 14/Nov/24 ]
Comment by Maria Keshwala [ 18/Nov/24 ]
Hi Candie I have to look into this thank you
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 20/Nov/24 ]
Hi Candie looking at this issue now , I dont see an Invoice number that I can see can you please provide me one or message ID and date which TP thank you to further assist you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 20/Nov/24 ]
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 20/Nov/24 ]
ok thanks
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 20/Nov/24 ]
42319367 message ID
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 20/Nov/24 ]
To send as test PO
0036558809
PO Total = 32.37</em>allowance Salvage = *4135.96
Basis Date
Delivery Date
Terms Discount Percent
1
Discount Days
30
Net Due Days
31
Description
1% 30 NET 31
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
Hello Candie now is escalated to an analyst as the discount is not working for me and see what the issue is. I will keep you posted
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 22/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 25/Nov/24 ]
Hello Candie this issue has been fixed thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Candie Rogers [ 25/Nov/24 ]
Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 26/Nov/24 ]
you’re welcome
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31221-fw-your-invoice-s-159763-for-order-s-0018457-0372-created-14-nov-24-updated-20-nov-24-resolved-20-nov-24">[CS-31221] FW: Your Invoice(s) 159763 for Order(s) 0018457-0372 Created: 14/Nov/24  Updated: 20/Nov/24  Resolved: 20/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Brian Wall Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.png      image004.png
Request Type: Emailed request
Request language: English
Request participants: Organizations:
Description
Some additional information for Ticket  CS-31218 RESOLVED  , the last ASN they received was
Message ID 40830852 on 7/8/2024.
<em>Best Regards, Brian Wall – IT Manager</em>
EXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Ext: 202 Direct: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a> Website: <a href="http://www.exacto.com">http://www.exacto.com</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 14, 2024 8:44 AM To: Brian Wall <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
HEllo Brian, the last ASN transmitted is the 155754 , after this date we haven’t received ASNs from Exacto
Gerardo Cervera
Materials Planner
BorgWarner PDS Irapuato, S. de R.L. de C.V.
Río Danubio 303 C.P. 36815
Parque Tecno–Industrial Castro del Río
Irapuato, Guanajuato, México
Mobile: +52 ************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Brian Wall <a href="mailto:<EMAIL>"><EMAIL></a> Sent: jueves, 14 de noviembre de 2024 08:06 a. m. To: Cervera, Gerardo (Irapuato) <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
⚠ **EXTERNAL SENDER
Hi Gerardo,
I am reaching out to our EDI VAN this morning to see what is happening here. Do you know when you received the last successful ASN?
<em>Best Regards, Brian Wall – IT Manager</em>
EXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a> Website: <a href="http://www.exacto.com">http://www.exacto.com</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 13, 2024 4:10 PM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>
Cc: Brian Wall <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
Your welcome Amy.
Gerardo Cervera
Materials Planner
BorgWarner PDS Irapuato, S. de R.L. de C.V.
Río Danubio 303 C.P. 36815
Parque Tecno–Industrial Castro del Río
Irapuato, Guanajuato, México
Mobile: +52 ************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: miércoles, 13 de noviembre de 2024 03:57 p. m. To: Cervera, Gerardo (Irapuato) <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Brian Wall <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
⚠ **EXTERNAL SENDER
I understand – thank you for your patience while we determine what caused this.
Amy
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 13, 2024 3:53 PM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
Yes, keep me posted
Because is for all the parts not just this one.
Gerardo Cervera
Materials Planner
BorgWarner PDS Irapuato, S. de R.L. de C.V.
Río Danubio 303 C.P. 36815
Parque Tecno–Industrial Castro del Río
Irapuato, Guanajuato, México
Mobile: +52 ************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: miércoles, 13 de noviembre de 2024 03:51 p. m. To: Cervera, Gerardo (Irapuato) <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
⚠ **EXTERNAL SENDER
Ok – I will have my IT manager dig deeper.
Amy
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 13, 2024 3:38 PM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
No and there is any ASN stopped detected by my system.
Gerardo Cervera
Materials Planner
BorgWarner PDS Irapuato, S. de R.L. de C.V.
Río Danubio 303 C.P. 36815
Parque Tecno–Industrial Castro del Río
Irapuato, Guanajuato, México
Mobile: +52 ************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: miércoles, 13 de noviembre de 2024 03:31 p. m. To: Cervera, Gerardo (Irapuato) <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
⚠ **EXTERNAL SENDER
Gerardo – did you receive our ASN?
Amy
From: Amy Grosklaus Sent: Tuesday, November 12, 2024 10:18 AM To: &#39;<a href="mailto:<EMAIL>"><EMAIL></a>&#39; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
Hi Gerardo – I’m not aware of any issues with the ASN, our system shows they passed validation &amp; sent without issue.
My IT manager will try resending the ASN, please confirm if you received later today.
Thank you,
Amy Grosklaus - Customer Service
Exacto Spring Corporation
1201 Hickory St. Grafton, WI 53024
(************* Ext. 207
Direct: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.exacto.com">http://www.exacto.com</a>
Exacto Spring will be closed Nov 28-29, Dec 24-25 and Jan 1.
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 12, 2024 10:05 AM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: Your Invoice(s) 159763 for Order(s) 0018457-0372
Amy Good day, we are not receiving the ASN, can you help me to check if something is broken the ASN?
From BW side I don’t have any issue to receipt but Exacto is not sending .
Gerardo Cervera
Materials Planner
BorgWarner PDS Irapuato, S. de R.L. de C.V.
Río Danubio 303 C.P. 36815
Parque Tecno–Industrial Castro del Río
Irapuato, Guanajuato, México
Mobile: +52 ************
<a href="mailto:<EMAIL>"><EMAIL></a>
----Original Message----From: JENNIFER FAAS <a href="mailto:<EMAIL>"><EMAIL></a> Sent: martes, 12 de noviembre de 2024 09:35 a. m. To: Cervera, Gerardo (Irapuato) <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Your Invoice(s) 159763 for Order(s) 0018457-0372
⚠ EXTERNAL SENDER
Your invoice(s) 159763 for order(s) 0018457-0372 are ready to view.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
Comments
Comment by Brian Wall [ 14/Nov/24 ]
Comment by Maria Keshwala [ 20/Nov/24 ]
Hello Brian Im working on the issue on another ticket I will close this one. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31218-fwd-fw-your-invoice-s-159763-for-order-s-0018457-0372-ds-10331-created-14-nov-24-updated-26-nov-24-resolved-26-nov-24">[CS-31218] Fwd: FW: Your Invoice(s) 159763 for Order(s) 0018457-0372 DS-10331 Created: 14/Nov/24  Updated: 26/Nov/24  Resolved: 26/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Brian Wall Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image004.png      image005.png      image006.png      DataTrans Integration report for 1136 - Exacto Spring Corp.eml      image-20241118-221011.png      image-20241118-222526.png      image-20241118-222705.png      image-20241118-222904.png      image-20241118-223954.png      image-20241119-134943.png      image-20241119-135111.png      CS_31221_FW__Your_Invoice_s__159763_for_Order_s__0018457_0372.eml     CS_31218_Fwd__FW__Your_Invoice_s__159763_for_Order_s__0018457_0372.eml      image-20241119-152124.png      image008.png      image-20241126-205057.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Document not delivered to TP
Description
From: Brian Wall <a href="mailto:<EMAIL>"><EMAIL></a> Date: Thu, Nov 14, 2024 at 8:41 AM Subject: FW: Your Invoice(s) 159763 for Order(s) 0018457-0372 To: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a>
Here is the first email I sent….
Best Regards,
Brian
From: Brian Wall Sent: Thursday, November 14, 2024 8:26 AM To: DataTrans Support <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
Good Morning DataTrans,
One of our Trading Partners, BorgWarner Irapuato (Code 3832) is reporting that they are not receiving our 856 (ASN) transactions. We sent the last one on October 11th and attempted to resend it on the 12th as well, Message ID 42289758. Can you please investigate this and see what the issue is? Please let me know if you need anything else from us, or need us to provide contact info for Borg’s EDI Team.
<em>Best Regards, Brian Wall – IT Manager</em>
EXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Ext: 202 Direct: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a> Website: <a href="http://www.exacto.com">http://www.exacto.com</a>
From: Brian Wall Sent: Thursday, November 14, 2024 8:06 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
Hi Gerardo,
I am reaching out to our EDI VAN this morning to see what is happening here. Do you know when you received the last successful ASN?
<em>Best Regards, Brian Wall – IT Manager</em>
EXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a> Website: <a href="http://www.exacto.com">http://www.exacto.com</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 13, 2024 4:10 PM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a>
Cc: Brian Wall <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
Your welcome Amy.
Gerardo Cervera
Materials Planner
BorgWarner PDS Irapuato, S. de R.L. de C.V.
Río Danubio 303 C.P. 36815
Parque Tecno–Industrial Castro del Río
Irapuato, Guanajuato, México
Mobile: +52 ************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: miércoles, 13 de noviembre de 2024 03:57 p. m. To: Cervera, Gerardo (Irapuato) <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Brian Wall <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
⚠ **EXTERNAL SENDER
I understand – thank you for your patience while we determine what caused this.
Amy
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 13, 2024 3:53 PM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
Yes, keep me posted
Because is for all the parts not just this one.
Gerardo Cervera
Materials Planner
BorgWarner PDS Irapuato, S. de R.L. de C.V.
Río Danubio 303 C.P. 36815
Parque Tecno–Industrial Castro del Río
Irapuato, Guanajuato, México
Mobile: +52 ************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: miércoles, 13 de noviembre de 2024 03:51 p. m. To: Cervera, Gerardo (Irapuato) <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
⚠ **EXTERNAL SENDER
Ok – I will have my IT manager dig deeper.
Amy
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 13, 2024 3:38 PM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
No and there is any ASN stopped detected by my system.
Gerardo Cervera
Materials Planner
BorgWarner PDS Irapuato, S. de R.L. de C.V.
Río Danubio 303 C.P. 36815
Parque Tecno–Industrial Castro del Río
Irapuato, Guanajuato, México
Mobile: +52 ************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Sent: miércoles, 13 de noviembre de 2024 03:31 p. m. To: Cervera, Gerardo (Irapuato) <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
⚠ **EXTERNAL SENDER
Gerardo – did you receive our ASN?
Amy
From: Amy Grosklaus Sent: Tuesday, November 12, 2024 10:18 AM To: &#39;<a href="mailto:<EMAIL>"><EMAIL></a>&#39; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Your Invoice(s) 159763 for Order(s) 0018457-0372
Hi Gerardo – I’m not aware of any issues with the ASN, our system shows they passed validation &amp; sent without issue.
My IT manager will try resending the ASN, please confirm if you received later today.
Thank you,
Amy Grosklaus - Customer Service
Exacto Spring Corporation
1201 Hickory St. Grafton, WI 53024
(************* Ext. 207
Direct: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.exacto.com">http://www.exacto.com</a>
Exacto Spring will be closed Nov 28-29, Dec 24-25 and Jan 1.
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 12, 2024 10:05 AM To: Amy Grosklaus <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: Your Invoice(s) 159763 for Order(s) 0018457-0372
Amy Good day, we are not receiving the ASN, can you help me to check if something is broken the ASN?
From BW side I don’t have any issue to receipt but Exacto is not sending .
Gerardo Cervera
Materials Planner
BorgWarner PDS Irapuato, S. de R.L. de C.V.
Río Danubio 303 C.P. 36815
Parque Tecno–Industrial Castro del Río
Irapuato, Guanajuato, México
Mobile: +52 ************
<a href="mailto:<EMAIL>"><EMAIL></a>
----Original Message----From: JENNIFER FAAS <a href="mailto:<EMAIL>"><EMAIL></a> Sent: martes, 12 de noviembre de 2024 09:35 a. m. To: Cervera, Gerardo (Irapuato) <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Your Invoice(s) 159763 for Order(s) 0018457-0372
⚠ EXTERNAL SENDER
Your invoice(s) 159763 for order(s) 0018457-0372 are ready to view.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of EXACTO SPRING CORP. Please contact the sender if you believe you have received this email in error.
Comments
Comment by Sandy Karidas [ 14/Nov/24 ]
<em>DataTrans Integration report for 1136 - Exacto Spring Corp.eml  (12 kB)</em>
Comment by Maria Keshwala [ 14/Nov/24 ]
message ID 40830852 last ASN received but TP ID 3832
the last one sent that they did not received was message ID 42289758 reference number 159763
21 ASNs
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Maria Keshwala [ 18/Nov/24 ]
Files in ECS-03
Last files went out in ECS-04 though server sharing to loren data date was July
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
there is no history on the event or any change ICCNET is the only one goes to Loren data not sure what caused to stop from picking them up and channel has not been disabled on 04 yet
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
They dont have an
url = /passthru-ICCNet-AS2B
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Asked support help
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Hello Brian an Analyst is looking into this as is complicated than I thought . I will keep you posted Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 19/Nov/24 ]
your data in question is in WebEDI, so eliminate everything before that point - as you mention, issue is after that point, getting back into ECS and delivering
what controls delivery from WebEDI to which ECS environment are called sendqueue&#39;s, and they are defined within TP Admin
per Robert
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Brian Wall [ 19/Nov/24 ]
Hi Sandy,
Have you made any progress on getting this resolved? I called last week Thursday on this and spoke to Maria, I never received a response or follow up. – Ticket  CS-31218 RESOLVED  . We need this connection fixed as soon as possible.
<em>Best Regards, Brian Wall – IT Manager</em>
EXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a> Website: <a href="http://www.exacto.com">http://www.exacto.com</a>
<em>CS_31221_FW_Your_Invoice_s159763_for_Order_s_0018457_0372.eml  (39 kB)</em>
<em>CS_31218_Fwd_FWYour_Invoice_s159763_for_Order_s_0018457_0372.eml  (35 kB)</em>
Comment by Maria Keshwala [ 19/Nov/24 ]
Hello Brian I emailed you yesterday check your spam this will be in development to fixed the issue thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Sandy Karidas [ 19/Nov/24 ]
Hello Brian,
I can see that Maria emailed a response to you through the ticket yesterday. Can you check you spam/junk folder to see if they are going there? An analyst was reviewing the connection and it has been escalated to our development team for further review at highest priority. I will try to get another update this afternoon.
Regards,
Comment by Brian Wall [ 19/Nov/24 ]
Hi Sandy,
Thank you for the quick response, I am not finding anything, would it have come from <a href="mailto:<EMAIL>"><EMAIL></a>? Maria and I we were having issues with the automated ticketing system and my email domain, when I initially called in/sent the support request email on the 14th.
<em>Best Regards, Brian Wall – IT Manager</em>
EXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Ext: 202 Direct: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a> Website: <a href="http://www.exacto.com">http://www.exacto.com</a>
Comment by Sandy Karidas [ 19/Nov/24 ]
Hi Brian,
Yes, it would have come from support. She just sent a follow up response to you from <a href="mailto:<EMAIL>"><EMAIL></a>. Our new ticketing system offers customers a portal option to view and track tickets. I would recommend setting that up. I resent the portal invite. You can also click on the view ticket button when you receive a reply from support.
Comment by Maria Keshwala [ 19/Nov/24 ]
Hello this issue has been escalated to our development team we will keep you posted with any updates. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Brian Wall [ 19/Nov/24 ]
Hi Sandy,
I set up the support portal and I am seeing Sandy’s responses there, but definitely am not receiving any email notifications from <a href="mailto:<EMAIL>"><EMAIL></a> regarding the updates to the ticket or when she responds on the ticket. I checked our mail logs as well (this was attached on the previous email) and it doesn’t show anything coming in.
<em>Best Regards, Brian Wall – IT Manager</em>
EXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a> Website: <a href="http://www.exacto.com">http://www.exacto.com</a>
Comment by Brian Wall [ 19/Nov/24 ]
Hi Maria,
Thank you for your responses and my apologies for the confusion but I am not receiving your emails, I have checked the inbound mail logs and nothing is hitting our mail server. Can you please have your team check our setup/verify they are being sent to <a href="mailto:<EMAIL>"><EMAIL></a>. I would see them in the mail log even if it was sent to the wrong alias if it was sent to an email address @exacto.com... Does the support responses come from a different email other than <a href="mailto:<EMAIL>"><EMAIL></a>, that I should be looking for? The above mail log screen shot shows we haven’t received anything from <a href="mailto:<EMAIL>"><EMAIL></a> since the 14th.
Best Regards, Brian
Comment by Sandy Karidas [ 19/Nov/24 ]
Hi Brian,
Wanted to provide an update to the Borg Warner issue. Our developers are still working on the issue and running some tests. I hope to have additional information in the morning.
Please contact me if you have any questions.
Regards, Comment by Brian Wall [ 21/Nov/24 ]
Good Morning,
Have your developers made any progress on this. Also, I still am not receiving any email alerts for this ticket, but did receive an email when Maria closed the redundant ticket,(  CS-31221 RESOLVED  ) so maybe it is just this ticket that has an issue? I think this ticket was created before I activated my account for the support portal if that has anything to do with it, maybe the ticket doesn’t have my email (<a href="mailto:<EMAIL>"><EMAIL></a>) linked to it somewhere…
Best Regards,
Brian
Comment by Maria Keshwala [ 21/Nov/24 ]
Good morning Brian the developers still working on it and the other ticket was related to this one as you wrote on another ticket that is related to this issue keeping everything in one ticket if thats ok with you . I will keep you posted once I heard a final update from the development team. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Sandy Karidas [ 21/Nov/24 ]
Hello Brian,
I met with Robert and our VP of Technology earlier this morning to review the Borg Warner issue. One of our lead developers is currently working on it. The documents are getting stuck in a validation stage and they are working to determine the cause for that. I will provide an update as soon as I hear back from them.
Regards,
Comment by Maria Keshwala [ 26/Nov/24 ]
Hello Brian
the issue has been fixed I have restaged the files below let me know if there is anything else I can assist you with files went to the trading partner . Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Brian Wall [ 26/Nov/24 ]
Thank you Maria and Sandy, I just emailed Borg Warner to confirm receipt of the ASN on their end.
-Brian
Comment by Maria Keshwala [ 26/Nov/24 ]
Ok great please let me know if any other issues. Thank you Brian
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31212-fw-fred-meyer-created-14-nov-24-updated-15-nov-24-resolved-14-nov-24">[CS-31212] FW: Fred Meyer Created: 14/Nov/24  Updated: 15/Nov/24  Resolved: 14/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Candie Rogers Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image003.png      image004.png      image005.png      image-20241114-152429.png      image-20241114-152429 (3d4c7b06-218a-46c4-8ccb-320b4b4cc4a1).png
Request Type: Emailed request
Request language: English
Request participants: None Organizations:
Description
Good Morning,
I sent INV187386 to Fred Meyer and it was rejected, they are asking for the EDI 810 raw data. Is this something you can provide them? Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
From: FM edibiz <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 13, 2024 4:51 PM To: Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Fred Meyer
HI Candie,
Could you please provide us the EDI 810 raw data.
Thanks,
Janakiram Periyala
Kroger EDI | The Kroger Co.
From: Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, November 14, 2024 3:00 AM To: FM edibiz <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: Fred Meyer
** [EXTERNAL EMAIL]: Do not click links or open attachments unless you recognize the sender and know the content is safe. **
Good afternoon,
INV187386 is showing rejected, can you please advise on why it is being rejected? Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
From: Brooke McCleese <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 13, 2024 4:02 PM To: Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Fred Meyer
Candie,
I was able to create my account online for Fred Meyer to see the portal transactions, payments and credit memos.
I noticed that INV187386 is not in there.
Thank you,
Brooke McCleese
Operations Manager
P: 614-915-3831 Direct
F: 614-600-5322
<a href="http://www.numbersup.com">www.numbersup.com</a>
<em>Your Outsourced Accounting Solutions Provider *
This e-mail message, including any attachments, is for the sole use of the intended recipient(s) and may contain information that is confidential and protected by law from unauthorized disclosure. Any unauthorized review, use, disclosure or distribution is prohibited. If you are not the intended recipient, please contact the sender by reply e-mail and destroy all copies of the original message.
This e-mail message, including any attachments, is for the sole use of the intended recipient(s) and may contain information that is confidential and protected by law from unauthorized disclosure. Any unauthorized review, use, disclosure or distribution is prohibited. If you are not the intended recipient, please contact the sender by reply e-mail and destroy all copies of the original message.
Comments
Comment by Candie Rogers [ 14/Nov/24 ]
Comment by Maria Keshwala [ 14/Nov/24 ]
Hello Candie I can send you the raw data
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 14/Nov/24 ]
Great, thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.Lu">www.Lu</a>
Comment by Maria Keshwala [ 14/Nov/24 ]
Hello Candie
<em>H.ST: ST_810_0001 H.BIG: BIG_20241017_INV187386_20241014_0036558809 H.PER: PER_SU_LI BAIN_TE_(503) 232-8844 H.N1.N1: N1_RI_Lubrication Specialties_9_1387796600000 H.N1.N3: N3_3975 Morrow Meadows Dr_ <em>H.N1.N4: N4_Mt Gilead_Oh_43338 H.N1.N1: N1_ST_FRED MEYER STORES_9_0079088092065 H.N1.N3: N3_11500 SE HWY 212</em> _H.N1.N4: N4_CLACKAMAS_OR_970159002 H.N1.N1: N1_BT_FRED MEYER, INC.<em>9_0079088090000 H.N1.N3: N3_P O BOX 305248</em> _H.N1.N4: N4_NASHVILLE_TN_372305103 H.ITD: ITD</em>2</em>12024121330202412143141781% 30 NET 31 H.DTM: DTM01120241017 B.IT1.IT1: IT1142EA<em>8.49</em>UP_729205270574_*
<em>B.IT1.PID.PID: PID_F_HOT SHOT EDT+ WINTER DEF FUEL BSTR 16OZ <em>B.IT1.PO4: PO4_1 B.IT1.IT1: IT1260EA_9.74**UP_837654914359 B.IT1.PID.PID: PID_FHOT SHOT STICTION ELIMINATOR 16 OZ</em> <em>B.IT1.PO4: PO4_1 B.IT1.IT1: IT13324_EA_9.99**UP_799493815900</em> B.IT1.PID.PID: PID_F_HOTSHOT EVERYDAY DIESEL TREATMENT 16OZ <em>B.IT1.PO4: PO4_1 T.TDS: TDS413596 T.SAC.SAC: SACA_4178</em> _T.CTT: CTT_3 T.SE: SE270001</em>
The error here that I noticed is the SAC segment at least the SAC02 and SAC03 is required I think this is why it rejected is restage for you to fix and resent thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Candie Rogers [ 14/Nov/24 ]
Thank you, I don’t know how to fix that. I will send this information to Fred Meyer EDI.
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.Lu">www.Lu</a>
Comment by Maria Keshwala [ 15/Nov/24 ]
Hi Candie ok great
but when you entered a data in one field then the next field is conditional related to the other data value then a value is expected
the SAC CODE is empty you have data in allowance - the amount is entered method of handling is expected
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31201-re-welcome-to-customer-support-created-14-nov-24-updated-14-nov-24-resolved-14-nov-24">[CS-31201] RE: Welcome to Customer Support Created: 14/Nov/24  Updated: 14/Nov/24  Resolved: 14/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Reyna Villafaña Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified Original estimate: Not Specified
Attachments:  image002.gif      image003.png      image004.png
Issue links: Duplicate is duplicated by CS-31199 RE: Welcome to Customer Support Resolved
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
Label: WebEDI login issue
Description
I don&#39;t know what the credentials are to enter the portal, I already tried with dbelectronics.ecgrid2.com, dbelectronics.ecgrid.com but it doesn&#39;t work
can you help me ?
Ing. Reyna Villafaña Trejo | Ofi: 55 8848 4706 | Cel: 55 2300 2680
<a href="mailto:<EMAIL>"><EMAIL></a> | <a href="https://www.movill.mx">https://www.movill.mx</a> | Twitter <a href="http://twitter.com/MovillSoft?s=20">http://twitter.com/MovillSoft?s=20</a>
De: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Enviado el: miércoles, 13 de noviembre de 2024 16:53 Para: reyna.villafana movill.mx <a href="mailto:<EMAIL>"><EMAIL></a> Asunto: RV: Welcome to Customer Support
Ing. Pedro Moreno
Gerente Técnico y Operaciones
T: +52 55 7694 0016 ext 119
C: +52 55 3900 7599
E: <a href="mailto:<EMAIL>"><EMAIL></a>
De: Customer Support <a href="mailto:<EMAIL>"><EMAIL></a> Enviado el: jueves, 7 de noviembre de 2024 12:03 p. m. Para: <a href="mailto:<EMAIL>"><EMAIL></a> Asunto: Welcome to Customer Support
Sign up to raise requests and receive notifications You have been invited to join Customer Support portal. By selecting the link below, you can start receiving email notifications for your requests. Complete the signup to raise requests and get help via portal. Sign up If you have received this email in error, please ignore it.
jsd-content-id-24d31706-39db-4cd9-99f3-7d5c4b6382a4 Powered by Jira Service Management
Comments
Comment by Reyna Villafaña [ 14/Nov/24 ]
Comment by Maria Keshwala [ 14/Nov/24 ]
working on another ticket
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31199-re-welcome-to-customer-support-created-14-nov-24-updated-18-nov-24-resolved-18-nov-24">[CS-31199] RE: Welcome to Customer Support Created: 14/Nov/24  Updated: 18/Nov/24  Resolved: 18/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Reyna Villafaña Assignee: Maria Keshwala
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image002.gif      image003.png      image004.png
Issue links: Duplicate duplicates CS-31201 RE: Welcome to Customer Support Resolved
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None Label: WebEDI login issue
Description
Good afternoon, I need a password to enter the portal, how do I get it?
Regards
Ing. Reyna Villafaña Trejo | Ofi: 55 8848 4706 | Cel: 55 2300 2680
<a href="mailto:<EMAIL>"><EMAIL></a> | <a href="https://www.movill.mx">https://www.movill.mx</a> | Twitter <a href="http://twitter.com/MovillSoft?s=20">http://twitter.com/MovillSoft?s=20</a>
De: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Enviado el: miércoles, 13 de noviembre de 2024 16:53 Para: reyna.villafana movill.mx <a href="mailto:<EMAIL>"><EMAIL></a> Asunto: RV: Welcome to Customer Support
Ing. Pedro Moreno
Gerente Técnico y Operaciones
T: +52 55 7694 0016 ext 119
C: +52 55 3900 7599
E: <a href="mailto:<EMAIL>"><EMAIL></a>
De: Customer Support <a href="mailto:<EMAIL>"><EMAIL></a> Enviado el: jueves, 7 de noviembre de 2024 12:03 p. m. Para: <a href="mailto:<EMAIL>"><EMAIL></a> Asunto: Welcome to Customer Support
Sign up to raise requests and receive notifications You have been invited to join Customer Support portal. By selecting the link below, you can start receiving email notifications for your requests. Complete the signup to raise requests and get help via portal. Sign up If you have received this email in error, please ignore it.
jsd-content-id-24d31706-39db-4cd9-99f3-7d5c4b6382a4 Powered by Jira Service Management
Comments
Comment by Reyna Villafaña [ 14/Nov/24 ]
Comment by Maria Keshwala [ 14/Nov/24 ]
Good morning whats your company name and account number and are you the admin ? please provide the information to further assist you thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Reyna Villafaña [ 15/Nov/24 ]
Good morning, yesterday I sent this information that you requested but you have not yet given me an answer.
Thank you
Regards
Ing. Reyna Villafaña Trejo | Ofi: 55 8848 4706 | Cel: 55 2300 2680
<a href="mailto:<EMAIL>"><EMAIL></a> | <a href="https://www.movill.mx">https://www.movill.mx</a> | Twitter <a href="http://twitter.com/MovillSoft?s=20">http://twitter.com/MovillSoft?s=20</a>
De: reyna.villafana movill.mx
Comment by Maria Keshwala [ 15/Nov/24 ]
Hello Reyna did you sent another ticket you did not reply on this email
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 15/Nov/24 ]
Please reply on this ticket with the information requested thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Reyna Villafaña [ 15/Nov/24 ]
Thank you Maria, the problem is that we still do not have the credentials to access the system, here are the data you asked me for
Company Name: DB Electronics
Account Number: I don&#39;t have that information or I don&#39;t know where to get it.
Pedro Moreno (pmoreno@dbelectronics) and I (Reyna Villafaña) , are administrators
Regards
Ing. Reyna Villafaña Trejo | Ofi: 55 8848 4706 | Cel: 55 2300 2680
<a href="mailto:<EMAIL>"><EMAIL></a> | <a href="https://www.movill.mx">https://www.movill.mx</a> | Twitter <a href="http://twitter.com/MovillSoft?s=20">http://twitter.com/MovillSoft?s=20</a>
Comment by Maria Keshwala [ 15/Nov/24 ]
Hello you are a Van customer you guys did not signup to have access to the portal where exactly is that you are saying you dont have access?
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Reyna Villafaña [ 15/Nov/24 ]
Thank you Maria, We received this email that I show you below, but we no longer received the email with the URL and credentials, we need it urgently, can you help me?
Comment by Reyna Villafaña [ 18/Nov/24 ]
Good morning, I&#39;ll get an answer soon, we need it urgently.
Thank you
Regards
Ing. Reyna Villafaña Trejo | Ofi: 55 8848 4706 | Cel: 55 2300 2680
<a href="mailto:<EMAIL>"><EMAIL></a> | <a href="https://www.movill.mx">https://www.movill.mx</a> | Twitter <a href="http://twitter.com/MovillSoft?s=20">http://twitter.com/MovillSoft?s=20</a>
De: reyna.villafana movill.mx
Comment by Maria Keshwala [ 18/Nov/24 ]
You need to contact sales if it is WEBEDI To have access to the portal if it is for Loren Data please ontact ECGrid loren data for login issues there thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Hello Reyna
Now you need to get access through Pedro he is the admin Sandy just sent a password reset for him then he needs to add you as a user. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Reyna Villafaña [ 18/Nov/24 ]
Thanks a lot Maria
Saludos
Ing. Reyna Villafaña Trejo | Ofi: 55 8848 4706 | Cel: 55 2300 2680
<a href="mailto:<EMAIL>"><EMAIL></a> | <a href="https://www.movill.mx">https://www.movill.mx</a> | Twitter <a href="http://twitter.com/MovillSoft?s=20">http://twitter.com/MovillSoft?s=20</a>
Comment by Maria Keshwala [ 18/Nov/24 ]
you &#39;re welcome
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31193-po-09262790-created-13-nov-24-updated-18-nov-24-resolved-18-nov-24">[CS-31193] po # 09262790 Created: 13/Nov/24  Updated: 18/Nov/24  Resolved: 18/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: kila rose Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.gif      image002.png      image003.png      image004.png      image005.jpg      image006.jpg      image007.jpg      image008.jpg    Request Type: Emailed request
Request language: English
Request participants: Organizations:
Description
Canadian tire has no record of this po # 09262790. Looks like it was completed and accepted .
Can you please advise?
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
Comments
Comment by kila rose [ 13/Nov/24 ]
Comment by Maria Keshwala [ 14/Nov/24 ]
Good morning by looking at the picture you sent yes they processed and sent us 997 I will send you some more info as soon as I get a chance. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
this is the 997 received for that control number 23 PO 09262790
<em>ISA_00_ 00 01_201613668 _ZZ_DTS7027 _240716_1813_U_00401_000000023_0_P@GS_FA_201613668_DTS7027_20240716_1813_23_X_004010<del>ST_997_0023</del>AK1_SH_30<del>AK2_856_0030</del>AK5_A<del>AK9_A_1_1_1</del>SE_6_0023<del>GE_1_23</del>IEA_1</em>000000023*
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31124-unable-to-log-in-created-13-nov-24-updated-14-nov-24-resolved-14-nov-24">[CS-31124] Unable to Log In Created: 13/Nov/24  Updated: 14/Nov/24  Resolved: 14/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Screenshot 2024-11-13 101356.png
Request Type: Emailed request
Request language: English
Request participants: None Organizations: Label: SFTP/FTP, FTP login issue
Description
Good Morning
I am currently unable to log in to the portal. Can I kindly be assisted urgently?
!Screenshot 2024-11-13 101356.png|thumbnail!
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 13/Nov/24 ]
Comment by Maria Keshwala [ 14/Nov/24 ]
Hello Neeketa
I will give this to IT to assist thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 14/Nov/24 ]
Credentials are correct we tested make sure you are using the correct password see below . Thank you
Host: transfer.datatrans-inc.com Port: 22 Username: dts5331 Password: H8b4gQ6C6CUb8eGEUDh8
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 14/Nov/24 ]
I have uploaded the file, can you please confirm that it was received without errors?
Comment by Maria Keshwala [ 14/Nov/24 ]
yes is good
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31110-auto-pick-and-pack-issue-ds-10433-created-13-nov-24-updated-19-dec-24-resolved-25-nov-24">[CS-31110] Auto pick and pack issue- DS-10433 Created: 13/Nov/24  Updated: 19/Dec/24  Resolved: 25/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: High
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241217-155625.png      image-20241217-161715.png      image-20241219-130554.png      image-20241219-130644.png      image-20241219-130443.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: ASN Packing Editor
Description
Hi team,
I hope you&#39;re well and having a good week so far.
We&#39;re having a lot of problems with our auto pick and pack option on Datatrans. Can we organise a google meet call for Thursday 7pm GMT to go through this and get a fix in place?
All the best, Alexandria
Comments
Comment by Tyler Daughrity (Inactive) [ 13/Nov/24 ]
Hi Alexandria,
Please use the below link to schedule our call.
Thank you,
Tyler Daughrity
DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a>
************
Comment by Tyler Daughrity (Inactive) [ 13/Nov/24 ]
Sorry here is the link
<a href="https://calendly.com/tdaughrity">https://calendly.com/tdaughrity</a>
Tyler Daughrity
DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a>
************
Comment by Tyler Daughrity (Inactive) [ 15/Nov/24 ]
Customer has scheduled a meeting for tomorrow
Join with Google Meet
meet.google.com/tnc-dpjg-xez
Tyler Daughrity
DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a>
************
Comment by Tyler Daughrity (Inactive) [ 15/Nov/24 ]
Had meeting, going to check with Riffat if requested task is something we can do
Tyler Daughrity
DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a>
************
Comment by Maria Keshwala [ 17/Dec/24 ]
Hello Madison
Per our phone conversation this is still an on going issue I will review this and see what we can do thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 17/Dec/24 ]
Per customer Khols sends the same PO number multiple times that&#39;s an issue on Khols end what the customer wants to resolved is the auto packing issue
PO number 15250628 customer is working
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Sandy Karidas [ 17/Dec/24 ]
<a href="https://dtsprojects.atlassian.net/browse/DS-10339">https://dtsprojects.atlassian.net/browse/DS-10339</a>
<a href="https://dtsprojects.atlassian.net/browse/DS-10366">https://dtsprojects.atlassian.net/browse/DS-10366</a>
Sandy Karidas
DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Maria Keshwala [ 17/Dec/24 ]
after one store 1216 after that it wont pick up any of the other stores PO number 15196526
Stores in the red square are being picked up but the rest of the stores in the document are not being picked up
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 17/Dec/24 ]
Created an ASN on PO number 15196526 - 42682011 MESSAGE ID
It is only doing the first line but not the rest of the stores
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 17/Dec/24 ]
<a href="https://dtsprojects.atlassian.net/browse/DS-10433">https://dtsprojects.atlassian.net/browse/DS-10433</a>
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 17/Dec/24 ]
Hello this issue has been escalated to our development team I have escalated as highest , created a subtask from the original development ticket , we will keep you posted with any updates. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Dec/24 ]
Ticket not yet assigned
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 19/Dec/24 ]
Good morning,
The POs with multiple DCs will generated multiple ASNs with their perspective store locations PO 15196526 dates 11/18/2024 is creating multiple ASNs because is going to multiple distribution centers and I asked this question while on the call, once you create the ASN please check your DRAFT as it creates multiple ASN that you need to work on. but this is working you just need to work on all the ASNs that the PO creates. Thank you
Note Please make sure you clean up your Draft Folder I have clean it a little bit please clean the rest check the files you are not using that way you dont have extra documents attached to a PO. Thank you
The PO message ID 42368031 creates 6 ASN’s and stores are divided into those 6 ASN’s based on DCcode
See all the message IDs created those are separate ASNs and this will always happened when there are multiple DCs
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31100-fw-856-asn-production-error-canadian-tire-nation-wide-products-created-13-nov-24-updated-18-nov-24-resolved-18-nov-24">[CS-31100] FW: 856 ASN - Production Error - Canadian Tire - NATION WIDE PRODUCTS Created: 13/Nov/24  Updated: 18/Nov/24  Resolved: 18/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: kila rose Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.png      image004.png      image006.png      image007.jpg      image008.jpg      image009.jpg      image010.jpg      image011.png      image-20241118-220220.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: ASN rejection
Description
SMTL is the carrier can you please advise?
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Jennifer Carter <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, November 13, 2024 7:54 AM To: Kaleena Gobert <a href="mailto:<EMAIL>"><EMAIL></a>; Kila Rose <a href="mailto:<EMAIL>"><EMAIL></a> Cc: SP.SupplierMeasures <a href="mailto:<EMAIL>"><EMAIL></a> Subject: 856 ASN - Production Error - Canadian Tire - NATION WIDE PRODUCTS
Be advised of the following translation errors for 856 ASN(s) received from your company. Check your 997(s) for details.
<em>Please correct and re-send the specified ASN(s) using new ISA Control Numbers.</em>
Please ensure that future ASNs meet Canadian Tire specifications.
DATE RECEIVED: 11/12/2024 VENDOR# 1790
ASN/BILL OF LADING# 00164715070 PO# 09549272
ERROR SEGMENT: TD5(014) – Line 5; Invalid data in field. “SMTL”
(blank line)
BSN\00\00164715070\20241112\1501
HL\1 S
TD1\PLT\1\\G\691\LB
TD5\2\SMTL\J\SOUTHWESTERN MOTOR TRANSPORT
REF\VR\1790
PER\DI\SCHOFIELD, CHAD AU\TE(416)480-9780 EXT. 2
DTM\011\20241112
N1\ST\9\2016136689005
HL\2\1\O
PRF\09549272\\20241029
HL\3\2\P
MAN\GM\00000702701000076224
HL\4\3\I
LIN\1\SK\043-8291-6\UP\************
SN1\420\EA
DTM\010\20241112
CTT\4
Should you require our 997 Interpretation Guide, please reply back to this email and we will email you a copy.
Regards
Canadian Tire
Electronic Commerce
| Jennifer Carter (she/her) EDI Support Analyst, UECOM
Canadian Tire Retail <a href="mailto:<EMAIL>"><EMAIL></a> |  |
This message, including any attachments, is privileged and may contain confidential information intended only for the person(s) named above. If you are not the intended recipient or have received this message in error, please notify the sender immediately by reply email and permanently delete the original transmission from the sender, including any attachments, without making a copy. Thank you.
Ce message, y compris toutes ses pièces jointes, est confidentiel et peut contenir des renseignements destinés uniquement aux personnes dont le nom est indiqué ci-dessus. Si vous n&#39;êtes pas le destinataire prévu ou si vous avez reçu ce message par erreur, veuillez en aviser l&#39;expéditeur immédiatement, en lui répondant par courriel. Veuillez aussi supprimer définitivement le message original de l&#39;expéditeur, y compris toute pièce jointe, sans faire de copie. Merci.
Comments
Comment by kila rose [ 13/Nov/24 ]
Comment by Maria Keshwala [ 14/Nov/24 ]
According to their email this segment stated do not use and also about the carrier data trans does not know what the TP carriers is you will have to contact them and find out what the correct carrier is but I will look and advise . Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Hello Just dont use this segment as is optional not used I requested to be removed from the map. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
I believe Sandy already has addressed this matter with you about not sending this segment because the specs stated that if they send a rush order you need to send this segment therefore we cant remove it thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-31023-fw-new-distribution-center-44-stanton-va-created-13-nov-24-updated-19-nov-24-resolved-19-nov-24">[CS-31023] Fw: New Distribution Center 44, Stanton VA Created: 13/Nov/24  Updated: 19/Nov/24  Resolved: 19/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Candie Rogers Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-Logo, comp.png    Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Address Table, Netsuite
Description
Please see below. Thank you!
From: Betsy Robinson <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, November 12, 2024 5:46 PM To: Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a> Subject: New Distribution Center 44, Stanton VA</p>
<h3 id="hot-shots-secret">Hot Shots Secret</h3>
<h3 id="hss-0-0">HSS 0 0</h3>
<h3 id="attn-candie-rogers">Attn: CANDIE ROGERS</h3>
<h3 id="re-new-distribution-center-dc-44-in-fredericksburg-va">RE: New Distribution Center, DC 44 in Fredericksburg, VA</h3>
<p>We will soon be opening a new distribution center (DC) in the Stafford, VA area.
All future references to the new Fredericksburg facility will be “Stafford DC,” “STA DC,” “DC 44” or “Ozark 44.” The “go live” date for this distribution center is set for August 4, 2025 Be prepared for initial stocking orders to begin transmitting as early as December 9, 2024.
The new DC will need to be set up in your internal EDI Systems. If you use a 3rd party VAN, please notify them to set up DC 44. This configuration needs to be completed as early as March 4th to avoid any delays in processing new DC 44 Purchase Orders. Please add the below DC interchange information to your internal EDI Systems and confirm you have acknowledged this email by responding back to <a href="mailto:<EMAIL>"><EMAIL></a> confirming you have scheduled this change in your system.
Warehouse location # Qual ISA and GS ID Time Zone
OZARK AUTOMOTIVE - STA 00044 12 5407797708 EST
200 Centreport Pkwy
Fredericksburg, VA 22406
540-779-7708
In additional to EDI setup, DC 44 needs to be set up in your ERP/order management system for example SAP. All INITIAL orders for DC 44 need to be set up as ship and cancel regardless of your standard fulfillment process for replenishment orders i.e. backorder supplier or ship and cancel supplier. If you are currently an EDI trading partner and you receive stock orders via VAN or PARTnerShip Network, these orders will be transmitted in that manner.
All ongoing REPLENISHMENT order fulfillment for DC 44 needs to be set up like existing DC locations as outlined in your supplier agreement. If your company backorders items not shipped, Atlanta should be set up as ship and backorder. If you cancel all items that do not ship, Atlanta orders will need to be handled as ship and cancel.
INITIAL ORDER DELIVERY: It is critical that your EDI and order management systems are set up to receive orders for DC 44 as initial stocking orders WILL BE TRANSMITTED VIA EDI and they will need to be shipped to ARRIVE ON A SPECIFIC DUE DATE to support our receiving schedule . Any product that arrives early will be refused. It will be the supplier’s responsibility to cover any costs incurred as a result.
If you are not the right contact for this topic, please forward this email to the appropriate contact and inform us of the correct contact for ongoing communication.
The ship-to address for this DC is:
200 Centrepoint Pkwy
Fredericksburg, VA 22406
Receiving Department Phone Number: ************
Should you have any questions, please contact any of the following:
EDI Support <a href="mailto:<EMAIL>"><EMAIL></a>
Betsy Robinson (************) <a href="mailto:<EMAIL>"><EMAIL></a>
Replenishment Support <a href="mailto:<EMAIL>"><EMAIL></a> The information transmitted is intended only for the person or entity to which it is addressed and may contain proprietary, business-confidential and/or privileged material. If you are not the intended recipient of this message you are hereby notified that any use, review, retransmission, dissemination, distribution, reproduction or any action taken in reliance upon this message is prohibited. If you received this in error, please contact the sender and delete the material from any computer.
Comments  Comment by Candie Rogers [ 13/Nov/24 ]
Comment by Maria Keshwala [ 13/Nov/24 ]
Hello Candie I will look into this and advise
Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Hello Candie this will need a work authorization to update the netsuite map to the new location which is $550.00 per location I will send that over thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Please confirmed before I do send the work auth this will take around 3 weeks lead time.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 19/Nov/24 ]
Yes, please send.
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 19/Nov/24 ]
Ok thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 19/Nov/24 ]
Work auth sent
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30975-adam-extract-first-step-unfi-810s-failed-ecs03-created-12-nov-24-updated-12-nov-24-resolved-12-nov-24">[CS-30975] Adam extract first step - UNFI 810S failed ECS03 Created: 12/Nov/24  Updated: 12/Nov/24  Resolved: 12/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None Fix versions: None
Type: Technical Support Priority: Medium
Reporter: Christopher Morrison Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241112-202049.png      image-20241112-205741.png      image-20241112-204839.png      image-20241112-205741 (fab6d175-05b6-46f7-8314-ab4d9b0516d0).png      adamextract810111124.txt      81011122024.txt      missing PI.txt      adamextract810111124 (325fbd3a-0e44-40a9-8982-c5cc4831e808).txt     missing PI (65c5f460-eb1a-4946-8017-212d3cb4eea7).txt      81011122024 (f154cb5a-d5a8-4c49-b063-9cc297dc85c5).txt
Comments
Comment by Maria Keshwala [ 12/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 12/Nov/24 ]
UNFI--
Map = AE810_FF to UNFI810 .dtm
error Rule #66: Data value not included in target code list (expression: IT106=OurItemQual, data: PI
IT106=OurItemQual, data: PI))
BATCH id 140073649 AND 140073649
Testing the File
failed missing PI data value
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 12/Nov/24 ]
81011122024.txt  adamextract810111124.txt  missing PI.txt
Hello , the 810s failed for he same issue address before
IT06 SEGMENT - please add the missing values and resend the documents thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 12/Nov/24 ]
81011122024 (f154cb5a-d5a8-4c49-b063-9cc297dc85c5).txt  adamextract810111124 (325fbd3a-0e44-40a9-8982-c5cc4831e808).txt  missing PI (65c5f460-eb1a-4946-8017-212d3cb4eea7).txt
Hello , the 810s failed for he same issue address before
IT06 SEGMENT - please add the missing values and resend the documents thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30950-universal-display-all-810-to-walmart-failed-created-12-nov-24-updated-12-nov-24-resolved-12-nov-24">[CS-30950] Universal Display - all 810 to Walmart failed Created: 12/Nov/24  Updated: 12/Nov/24  Resolved: 12/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Technical Support Priority: Medium
Reporter: Maria Keshwala Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241112-143359.png      image-20241112-143448.png
Comments
Comment by Maria Keshwala [ 12/Nov/24 ]
No destination added on the AS2 target
added to the target event rule
Restaged several files all passed delivery waiting for MDNs
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 12/Nov/24 ]
restaged 11/11 and 11/12
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 12/Nov/24 ]
Confirmed by Sandy all Walmart 810s got accepted.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30921-banner-810-to-awc-failed-in-ecs-03-created-11-nov-24-updated-12-nov-24-resolved-11-nov-24">[CS-30921] Banner 810 to AWC failed in ECS-03 Created: 11/Nov/24  Updated: 12/Nov/24  Resolved: 11/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Technical Support Priority: Medium
Reporter: Maria Keshwala Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20241111-171047.png      image-20241111-171107.png      image-20241111-222947.png
Comments
Comment by Maria Keshwala [ 11/Nov/24 ]
Banner recent the file
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30913-duplicate-po-s-created-11-nov-24-updated-18-nov-24-resolved-18-nov-24">[CS-30913] duplicate PO&#39;s Created: 11/Nov/24  Updated: 18/Nov/24  Resolved: 18/Nov/24</h4>
<h2 id="status-resolved-project-customer-support-components-none-affects-versions-none-fix-versions-none-type-support-priority-medium-reporter-timj-saudereggs-com-assignee-maria-keshwala-resolution-done-votes-0-labels-none-remaining-estimate-not-specified-time-spent-not-specified-original-estimate-not-specified-attachments-image-png-image-20241118-173416-png-image-20241118-174800-png-request-type-emailed-request-request-language-english-request-participants-organizations-label-duplicate-document-description-we-are-still-getting-duplicate-po-files-from-this-customer-why-is-this-happening">Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      image-20241118-173416.png      image-20241118-174800.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Duplicate Document
Description
We are still getting duplicate PO files from this customer. Why is this happening?</h2>
<p>Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 11/Nov/24 ]
Comment by Maria Keshwala [ 11/Nov/24 ]
Hello Tim I will look into this and advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
File sent 11/11/2024
Files in ECS-02
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 18/Nov/24 ]
Hello Tim this issue was fixed back in 11th
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30877-fwd-new-van-inc9399032-created-08-nov-24-updated-12-nov-24-resolved-12-nov-24">[CS-30877] Fwd: New Van / INC9399032 Created: 08/Nov/24  Updated: 12/Nov/24  Resolved: 12/Nov/24</h4>
<h2 id="status-resolved-project-customer-support-components-none-affects-versions-none-fix-versions-none-type-support-priority-medium-reporter-timj-saudereggs-com-assignee-maria-keshwala-resolution-done-votes-0-labels-none-remaining-estimate-not-specified-time-spent-not-specified-original-estimate-not-specified-attachments-wrd0000-jpg-image001-png-request-type-emailed-request-request-language-english-request-participants-none-organizations-none-description-forwarded-message-from-us_edi_admin-lidl-us-us_edi_admin-lidl-us-date-mon-sep-16-2024-at-1-24-pm-subject-re-new-van-inc9399032-to-timothy-jones-timj-saudereggs-com-us_edi_admin-lidl-us-us_edi_admin-lidl-us-hello-i-have-submitted-a-ticket-with-our-it-team-to-make-this-change-we-will-let-you-know-once-this-is-complete-thank-you-lidl-us-purchasing-edi-team-us_edi_admin-lidl-us-lidl-us-llc-3500-s-clark-street-arlington-virginia-22202-please-consider-the-environment-before-printing-this-e-mail-this-e-mail-including-any-attachments-may-include-proprietary-and-or-confidential-information-of-lidl-us-and-is-intended-only-for-the-person-or-entity-to-which-it-is-addressed-any-attachments-hereto-may-not-be-reviewed-reproduced-distributed-or-modified-without-the-prior-written-consent-of-lidl-us-if-you-are-not-the-intended-recipient-of-this-message-please-be-aware-that-any-use-review-retransmission-or-any-other-action-taken-in-reliance-of-this-message-is-prohibited-if-you-received-this-e-mail-in-error-please-notify-the-sender-immediately-and-delete-the-material-from-all-computers-from-timothy-jones-timj-saudereggs-com-sent-monday-september-16-2024-10-57-am-to-us_edi_admin-lidl-us-subject-new-van-hello-please-change-our-van-id-s-to-zz-dts5763-thank-you-http-www-saudereggs-com-timothy-jones-manager-information-technology-corp-************-ext-7414-direct-************-timj-saudereggs-com-https-www-facebook-com-sauder0eggs-184170408279763-https-youtu-be-oli8krenaai-https-twitter-com-sauderseggs-https-www-saudereggs-com-blog">Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  ~WRD0000.jpg      image001.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
Description
---------- Forwarded message ---------From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Date: Mon, Sep 16, 2024 at 1:24 PM Subject: RE: New Van / INC9399032 To: Timothy Jones <a href="mailto:<EMAIL>"><EMAIL></a>, <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a>
Hello,
I have submitted a ticket with our IT team to make this change. We will let you know once this is complete.
Thank you,
Lidl US Purchasing EDI Team
<a href="mailto:<EMAIL>"><EMAIL></a>
Lidl US, LLC
3500 S Clark Street, Arlington, Virginia 22202
Please consider the environment before printing this e-mail.
This e-mail, including any attachments, may include proprietary and/or confidential information of Lidl US and is intended only for the person or entity to which it is addressed. Any attachments hereto may not be reviewed, reproduced, distributed or modified without the prior written consent of Lidl US. If you are not the intended recipient of this message, please be aware that any use, review, retransmission or any other action taken in reliance of this message is prohibited. If you received this e-mail in error, please notify the sender immediately and delete the material from all computers.
From: Timothy Jones <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, September 16, 2024 10:57 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject: New Van
Hello
Please change our VAN ID&#39;s to ZZ: DTS5763
Thank you.
–
<a href="http://www.saudereggs.com/">http://www.saudereggs.com/</a>
Timothy Jones
Manager | Information Technology
Corp. ************ Ext.7414
Direct ************ | <a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.facebook.com/Sauder0Eggs-184170408279763/">https://www.facebook.com/Sauder0Eggs-184170408279763/</a>  <a href="https://youtu.be/oli8kreNaAI">https://youtu.be/oli8kreNaAI</a>  <a href="https://twitter.com/SaudersEggs">https://twitter.com/SaudersEggs</a> <a href="https://www.saudereggs.com/blog/">https://www.saudereggs.com/blog/</a></h2>
<p>Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 08/Nov/24 ]
Comment by Maria Keshwala [ 11/Nov/24 ]
Hello Tim I will look into this and advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 12/Nov/24 ]
Hello Tim is there is anything we need to do ono our end regarding this one? please advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 12/Nov/24 ]
no that was resolved by LIDL
Comment by Maria Keshwala [ 12/Nov/24 ]
ok thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30860-asn-missing-in-qualifier-created-08-nov-24-updated-13-nov-24-resolved-13-nov-24">[CS-30860] ASN Missing &#39;IN&#39; Qualifier Created: 08/Nov/24  Updated: 13/Nov/24  Resolved: 13/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.jpg
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: None
Description
Dear valued trading partner,
Thank you for your participation in EDI with Rite Aid Corporation via SPS Commerce.
In reviewing dataflow, it does not appear that your organization has sent &#39;IN&#39; Qualifier in LIN06 for buyer part number on the Advance Ship Notice document. We highly recommend and encourage you to use &#39;IN&#39; in this field. This will allow us to accurately ingest the data and properly plan for future orders.
We appreciate your partnership as always!
Thank you,
Rite Aid Corporation Team</p>
<hr>
<p>Greta Baith ** Manager, Vendor Performance
P ************
RITEAID.com
DISCLAIMER This e-mail, including attachments, may include confidential, proprietary privileged and/or private information, and may be used only by the person or entity to which it is addressed. If the reader of this e-mail is not the intended recipient or intended recipient’s authorized agent, the reader is hereby notified that any dissemination, distribution or copying of this e-mail is prohibited. If you have received this e-mail in error, please notify the sender by replying to this message and delete this e-mail and all attachments immediately..
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 08/Nov/24 ]
Comment by Maria Keshwala [ 11/Nov/24 ]
Can you please provide more information whats the customer’s ISA ID account number and company name to further assist. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 13/Nov/24 ]
There is no customer information there is no document information that was sent I have emailed them. waiting for them to reach out this ticket will reopen once they reply
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30821-rejected-created-08-nov-24-updated-11-nov-24-resolved-11-nov-24">[CS-30821] rejected Created: 08/Nov/24  Updated: 11/Nov/24  Resolved: 11/Nov/24</h4>
<h2 id="status-resolved-project-customer-support-components-none-affects-versions-none-fix-versions-none-type-support-priority-medium-reporter-timj-saudereggs-com-assignee-maria-keshwala-resolution-done-votes-0-labels-none-remaining-estimate-not-specified-time-spent-not-specified-original-estimate-not-specified-attachments-image-png-request-type-emailed-request-request-language-english-request-participants-organizations-none-description-can-someone-tell-me-why-this-was-rejected">Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Description
Can someone tell me why this was rejected ?</h2>
<p>Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 08/Nov/24 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 08/Nov/24 ]
BIG - missing date in BIG03
IT1 - missing line no (added the one you are including, but they may come back and ask for original)
TDS - I don&#39;t see anything wrong with this segment.
I restaged the invoice and resent..lets see what they come back with.
Daniel Comment by Maria Keshwala [ 11/Nov/24 ]
Daniel worked on this and fixed the issue.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30611-concept-packaging-in-856-server-failed-dts-4939-created-05-nov-24-updated-05-nov-24-resolved-05-nov-24">[CS-30611] Concept packaging In - 856 Server failed DTS 4939 Created: 05/Nov/24  Updated: 05/Nov/24  Resolved: 05/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Technical Support Priority: Medium
Reporter: Brent Noblitt Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-160802.png      image-********-161038.png      conceppackaging856.txt      image-********-161038 (bb91ce73-9bf5-4e9c-89cf-bfbf89b1d056).png
Comments
Comment by Maria Keshwala [ 05/Nov/24 ]
ECS-03 server
Rule #37: Not enough data (expression: &#39;N104=StringToken(StringToken(ST_NAME2_ID, &#39;/&#39;, 1), &#39;(&#39;, 2)&#39;, data: &#39;</p>
<h4 id="cs-30601-server-error-adam-extract-to-unfi-810-dts-6061-created-05-nov-24-updated-05-nov-24-resolved-05-nov-24">[CS-30601] Server error - Adam Extract - to UNFI 810 DTS 6061 Created: 05/Nov/24  Updated: 05/Nov/24  Resolved: 05/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Technical Support Priority: Medium
Reporter: Maria Keshwala Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-151801.png      image-********-145838.png      image-********-151608.png      image-********-150032.png      image-********-150127.png     image-********-153815.png      image-********-154214.png
Comments
Comment by Maria Keshwala [ 05/Nov/24 ]
Rule #68: Data value not included in target code list (expression: IT108=CustIemQual, data: )
Map = AE810_FF to UNFI810 .dtm
Batch IDs 139954115--139932962
Customer did not fixed or resent an updated file
Product service ID qualifier is expected no present on the document
start position 781
bad
good
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Nov/24 ]
bad file missing – IT105, Basis of unit price code IT108
added the missing data in IT07- which added the missing code value on the IT08 product service ID
and if the IT109 is added PI= Purchaser Item code then the IT109 is required product service ID
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Nov/24 ]
second batch from 11/04/2024
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30599-adams-invoice-12529948-not-appearing-in-web-edi-created-05-nov-24-updated-05-nov-24-resolved-05-nov-24">[CS-30599] Adams Invoice 12529948 not appearing in Web EDI Created: 05/Nov/24  Updated: 05/Nov/24  Resolved: 05/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Christopher Morrison Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      adamextra11042024bad810.txt      adamextractfilebad8101105.txt      image-********-151608.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice errors
Description
Customer: Adams Extracts
ID: 6061
Trading Partner: UNFI - Natural
Document: 810
Number: 12529948
Adams Invoice 12529948 has been transmitted to DTS three times, twice on 11/04 and once on 11/05. The invoice has not appeared in Web EDI. Please investigate.
Christopher Morrison | ERP Lead Developer Adams Flavors, Foods &amp; Ingredients
3217 Johnson Rd | PO Box 1726 Gonzales, TX 78629 O: 830-300-8030 | C: 610-513-6680 AdamsExtract.com
Book time with Morrison, Christopher
Comments
Comment by Christopher Morrison [ 05/Nov/24 ]
Comment by Maria Keshwala [ 05/Nov/24 ]
Hello Christ Im actually in the middle of it I will email you back shortly just finishing up gathering all my findings . Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Nov/24 ]
Hello Christ
you are missing data values in position 781 this is your IT108 error “adamextra11042024bad810.txt  adamextractfilebad8101105.txt  SEGMENT Data value not included in target code list (expression: IT108=CustIemQual, data: )
on both files once I added some values there the files passed please review both attached files and add the missing values missing on the IT107 “aLTItemno”
Once you add the missing data that should correct the issue as I have try myself and it worked. Please let me know if you have any other questions. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30598-server-error-dts-6534-11-04-2024-810-created-05-nov-24-updated-12-nov-24-resolved-05-nov-24">[CS-30598] Server error dts 6534 11/04/2024-- 810- Created: 05/Nov/24  Updated: 12/Nov/24  Resolved: 05/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Technical Support Priority: Medium
Reporter: Ann Marie Yates Assignee: Maria Keshwala Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-144213.png      image-********-144142.png      image-********-144816.png      siteone810.txt      image002.png      image005.png     image003.png      image004.png      image006.png      image001.png
Comments
Comment by Maria Keshwala [ 05/Nov/24 ]
Rule #21: Not enough data (expression: &#39;N104=Left(StringToken(Ship_To_Name, &#39;#&#39;, 2), 3)&#39;, data: &#39;3&#39;, minimum length: 2)
Map= MumGSS810 to SiteOne810.dtm
Testing File
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Nov/24 ]
Good morning the 810 file sent to Site One failed due to one invoice missing the full data in the N104 which is the Identification code , you sent “3” minimum length 2 invoice number to fix is 263996 (42571695) . Please update a
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Ann Marie Yates [ 05/Nov/24 ]
Good morning,
Can you please provide more information, we have no idea what N104 is. I don’t see anything in the raw EDI I’m looking at.
Thank you,
&lt;<a href="https://us.content.exclaimer.net/">https://us.content.exclaimer.net/</a>? url=http%3A%2F%2Fwww.mumindustries.com%2F&amp;tenantid=TUj4Muw3Ee6q8GBFvdVbFg&amp;templateid=4b8a77325cf0ee11aaf26045bdd55b16&amp;excomponentid=s4Dxn8FzU43xV5SkbyskObqoSQxv85FdLNteN_1Qh1A&amp;excom iGn6ihhNi21RBOmj95st5MPt3bbHpijqtyeCNTAakt0KhjlgKxyvioJtD_yMeCdhz6kT0U7xZgqKt4m5vrll3lmhkXCA-Nih0elpGhgVczoZFYyA_zzFkFW5AbJ-gCJBw5KPnWlfzkB-8etrMmj0Thxz2Te_wm1yw3RqwCWGW_aeZhq5ByLdE ggANTl35JGClmXqwKARRyO2Id5AtkcWpA&amp;v=1&amp;imprintMessageId=43122a7e-f3bc-4602-9162-5b5441e046f6&gt; |
Ann Marie Yates
IT/IS Manager
| ************ ext. 212 <a href="about:invalid#zCSafez">tel:************%20ext.%20212</a> | : |  | ************ <a href="about:invalid#zCSafez">tel:************</a> |
| <a href="mailto:<EMAIL>"><EMAIL></a> | : |  | <a href="http://www.mumindustries.com">www.mumindustries.com</a> |
|
8989 Tyler Blvd , Mentor, OH , 44060
Comment by Maria Keshwala [ 05/Nov/24 ]
Is the Identification code that Identifies the party you did sent 2 digit values on the other files except this one is the organization identification
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Ann Marie Yates [ 05/Nov/24 ]
I’ve updated the information, please let me know if any other issues.
You may have received the bad file again, I just uploaded a corrected version.
Please let me know if the corrected version goes through.
Thank you,
&lt;<a href="https://us.content.exclaimer.net/">https://us.content.exclaimer.net/</a>? url=http%3A%2F%2Fwww.mumindustries.com%2F&amp;tenantid=TUj4Muw3Ee6q8GBFvdVbFg&amp;templateid=4b8a77325cf0ee11aaf26045bdd55b16&amp;excomponentid=s4Dxn8FzU43xV5SkbyskObqoSQxv85FdLNteN_1Qh1A&amp;excom iGn6ihhNi21RBOmj95st5MPt3bbHpijqtyeCNTAakt0KhjlgKxyvioJtD_yMeCdhz6kT0U7xZgqKt4m5vrll3lmhkXCA-Nih0elpGhgVczoZFYyA_zzFkFW5AbJ-gCJBw5KPnWlfzkB-8etrMmj0Thxz2Te_wm1yw3RqwCWGW_aeZhq5ByLdE ggANTl35JGClmXqwKARRyO2Id5AtkcWpA&amp;v=1&amp;imprintMessageId=93e9da60-b7a8-4a4b-b123-161fb49b64d9&gt; |
Ann Marie Yates
IT/IS Manager
| ************ ext. 212 <a href="about:invalid#zCSafez">tel:************%20ext.%20212</a> | : |  | ************ <a href="about:invalid#zCSafez">tel:************</a> |
| <a href="mailto:<EMAIL>"><EMAIL></a> | : |  | <a href="http://www.mumindustries.com">www.mumindustries.com</a> |
|
8989 Tyler Blvd , Mentor, OH , 44060
Comment by Maria Keshwala [ 05/Nov/24 ]
yes it looks good now thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Ann Marie Yates [ 05/Nov/24 ]
Thank you for letting me know!
&lt;<a href="https://us.content.exclaimer.net/">https://us.content.exclaimer.net/</a>? url=http%3A%2F%2Fwww.mumindustries.com%2F&amp;tenantid=TUj4Muw3Ee6q8GBFvdVbFg&amp;templateid=4b8a77325cf0ee11aaf26045bdd55b16&amp;excomponentid=s4Dxn8FzU43xV5SkbyskObqoSQxv85FdLNteN_1Qh1A&amp;excom iGn6ihhNi21RBOmj95st5MPt3bbHpijqtyeCNTAakt0KhjlgKxyvioJtD_yMeCdhz6kT0U7xZgqKt4m5vrll3lmhkXCA-Nih0elpGhgVczoZFYyA_zzFkFW5AbJ-gCJBw5KPnWlfzkB-8etrMmj0Thxz2Te_wm1yw3RqwCWGW_aeZhq5ByLdE ggANTl35JGClmXqwKARRyO2Id5AtkcWpA&amp;v=1&amp;imprintMessageId=37cdafc1-99e2-4268-a507-67a67758adc5&gt; |
Ann Marie Yates IT/IS Manager
| ************ ext. 212 <a href="about:invalid#zCSafez">tel:************%20ext.%20212</a> | : |  | ************ <a href="about:invalid#zCSafez">tel:************</a> |
| <a href="mailto:<EMAIL>"><EMAIL></a> | : |  | <a href="http://www.mumindustries.com">www.mumindustries.com</a> |
|
8989 Tyler Blvd , Mentor, OH , 44060
Comment by Maria Keshwala [ 05/Nov/24 ]
you’re welcome 🙂
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Ann Marie Yates [ 06/Nov/24 ]
Hi Maria,
How long does it typically take for the invoices we process through EDI invoices ones they through DataTrans? SiteOne is indicating they did not get the ones we processed yesterday.
&lt;<a href="https://us.content.exclaimer.net/">https://us.content.exclaimer.net/</a>? url=http%3A%2F%2Fwww.mumindustries.com%2F&amp;tenantid=TUj4Muw3Ee6q8GBFvdVbFg&amp;templateid=4b8a77325cf0ee11aaf26045bdd55b16&amp;excomponentid=s4Dxn8FzU43xV5SkbyskObqoSQxv85FdLNteN_1Qh1A&amp;excom iGn6ihhNi21RBOmj95st5MPt3bbHpijqtyeCNTAakt0KhjlgKxyvioJtD_yMeCdhz6kT0U7xZgqKt4m5vrll3lmhkXCA-Nih0elpGhgVczoZFYyA_zzFkFW5AbJ-gCJBw5KPnWlfzkB-8etrMmj0Thxz2Te_wm1yw3RqwCWGW_aeZhq5ByLdE ggANTl35JGClmXqwKARRyO2Id5AtkcWpA&amp;v=1&amp;imprintMessageId=b8845733-f22d-4286-83d4-c955232d4f4b&gt; |
Ann Marie Yates
IT/IS Manager
| ************ ext. 212 <a href="about:invalid#zCSafez">tel:************%20ext.%20212</a> | : |  | ************ <a href="about:invalid#zCSafez">tel:************</a> |
| <a href="mailto:<EMAIL>"><EMAIL></a> | : |  | <a href="http://www.mumindustries.com">www.mumindustries.com</a> |
|
8989 Tyler Blvd , Mentor, OH , 44060
Comment by Maria Keshwala [ 11/Nov/24 ]
Hello Ann I was away on PTO just got back they still dont have them? please confirm thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30544-issue-with-invoice-error-created-04-nov-24-updated-04-nov-24-resolved-04-nov-24">[CS-30544] Issue with invoice - error Created: 04/Nov/24  Updated: 04/Nov/24  Resolved: 04/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Phone call Request language: English
Request participants: Organizations:
Description
Message ID = 41783087
AHN invoice
DTS 7196
Comments
Comment by Maria Keshwala [ 04/Nov/24 ]
Hello Sheriy
this is your ticket I will take a look and advice I will email you back with an update.
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Nov/24 ]
************
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Nov/24 ]
Hi Sheriy
see below contact information for AHN as the invoice does not have a valid visual reason for rejection. Thank you
<a href="mailto:<EMAIL>"><EMAIL></a>    Raymond Helwich
<EMAIL>-
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30524-help-creating-asn-created-04-nov-24-updated-04-nov-24-resolved-04-nov-24">[CS-30524] HELP CREATING ASN Created: 04/Nov/24  Updated: 04/Nov/24  Resolved: 04/Nov/24</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Maria Keshwala Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Phone call
Request language: English
Request participants: None Organizations: None
Description
Customer called needed assistant creating the ASN
autopack</p>
<h4 id="cs-30472-fw-external-04753094aa-to-05308-created-01-nov-24-updated-11-nov-24-resolved-11-nov-24">[CS-30472] Fw: [EXTERNAL]04753094AA to 05308 Created: 01/Nov/24  Updated: 11/Nov/24  Resolved: 11/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Ann Thomas Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image005.png      image012.png      image027.png      image028.png      image047.png      image048.png      image052.png      image053.png     image054.png      image055.png      image056.png      image057.png      image058.png      image059.jpg      image060.png      image061.png      image062.png      image063.png      image064.png      image065.png      image068.jpg      image071.png      image072.png      image073.png      image074.png     image075.png      image076.png      image077.png      image078.png      image079.jpg      image080.png      image081.png      image082.png      image083.jpg
image084.png      image085.jpg      image001.png      image002.jpg      image003.png      image004.png      image010.png      image011.png      image013.png      Outlook-2A NA.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations:
Description  Good Morning Maria, I have two issues, please view below:</p>
<ol>
<li>Did you send the Mockup EDI ? Is the ASN issue solved or not yet? This question is from Stellantis, what is a MOCK-UP EDI? I would we proceed with this process? Are we still having issues with their EDI process? If so please explain to me what&#39;s going on with their EDI.</li>
<li>DDC part number DDA1860233, we can&#39;t process it in our system, no packing-slip or ASN? Please assist, we ship 648 manual, and we need to invoice them NOW. Material already shipped!
From: Containers support <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, October 31, 2024 2:33 AM To: Mohammed Ishan Iqbal <a href="mailto:<EMAIL>"><EMAIL></a>; CHRISTINE TRINGALI <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Ann Thomas <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: [EXTERNAL]04753094AA to 05308 Hello,
Mohammed, the Shipments that were in transit and you receipted in the system, have you received all of these physically ? If yes do you have the PODs for all of them ?
Did you send the Mockup EDI ? Is the ASN issue solved or not yet ? The CMS inventory cannot be updated until this is resolved. Because it will be incorrect again on your next shipment.
Kind Regards, Oussama.
Container Management Support
STELLANTIS
Working days Monday-Friday
Working hours 3 AM-4 PM
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Mohammed Ishan Iqbal <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, October 31, 2024 02:09 To: Containers support - BFCONTA104 <a href="mailto:<EMAIL>"><EMAIL></a>; CHRISTINE TRINGALI <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Ann Thomas <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: [EXTERNAL]04753094AA to 05308
EXTERNAL : This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello Containers Support,
I was able to follow the instructions and clear all aged in transit, as advised.
Could you please confirm if everything’s cleared out?
Please advise if we are all good here and the next step would be to submit inventory adjustment request via portal?
I am at your disposal for further assistance, and I really appreciate your ongoing assistance on this.
Thanks,
Regards
Mohammed Ishan Iqbal | Supply Chain/ Industrial Analyst
US Address: 2410 West Tech Lane, Auburn AL, 36832 - USA
Office: +1 (346) 504-3257 | Internet: <a href="http://www.fonderie2a.com">www.fonderie2a.com</a>
ITA Headquarter: Via Asti, 67/bis,10026 Santena (TO), Italy
<a href="mailto:<EMAIL>"><EMAIL></a>
This message is intended only for the use of the address named herein and contains legally privileged and confidential information&#39;s. If you are not the intended recipient of this facsimile, you are hereby notified that any disseminations, distributions, or copying of this facsimile is strictly prohibited. If you have received this in error, please immediately notify us.
From: Containers support <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, August 8, 2024 9:36 AM To: CHRISTINE TRINGALI <a href="mailto:<EMAIL>"><EMAIL></a>; Odes Jackson <a href="mailto:<EMAIL>"><EMAIL></a>; SHEILA RYAN <a href="mailto:<EMAIL>"><EMAIL></a>; Sri Gogineni <a href="mailto:<EMAIL>"><EMAIL></a>; NELLY ELIZABETH CELIS CASTANEDA <a href="mailto:<EMAIL>"><EMAIL></a>; ANDRE NEAL <a href="mailto:<EMAIL>"><EMAIL></a>; BRIAN MCNEW <a href="mailto:<EMAIL>"><EMAIL></a>; CHALLEN HODSON <a href="mailto:<EMAIL>"><EMAIL></a>; JASON DANIEL <a href="mailto:<EMAIL>"><EMAIL></a>; JASON DANIEL <a href="mailto:<EMAIL>"><EMAIL></a>; JEREMIAH WEBB <a href="mailto:<EMAIL>"><EMAIL></a>; JIM LEBRE <a href="mailto:<EMAIL>"><EMAIL></a>; JONATHAN BRITTAIN <a href="mailto:<EMAIL>"><EMAIL></a>; PJ BOLIN <a href="mailto:<EMAIL>"><EMAIL></a>; ROBYN ANGLETON <a href="mailto:<EMAIL>"><EMAIL></a>; SABRINA HUNT <a href="mailto:<EMAIL>"><EMAIL></a>; CONNOR ADAMS <a href="mailto:<EMAIL>"><EMAIL></a> Cc: CAMILA FOX <a href="mailto:<EMAIL>"><EMAIL></a>; Mohammed Ishan Iqbal <a href="mailto:<EMAIL>"><EMAIL></a>; GREG MCCULLOUGH <a href="mailto:<EMAIL>"><EMAIL></a>; Ashish Patil <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: [EXTERNAL]04753094AA to 05308
Hello all,
@Odes regarding your container&#39;s requirements, you should firstly address all of your aged in transit shipments (check the screenshots below), since you have soo many since the beginning of the year. You should be contacting your carrier for ETA/POD, if they are not responsive, please reach out to your logistics analyst.
Secondly, after confirming with Christine, that you are able to create Containers Only ASN for the outbounds shipments, you can then submit an Inventory Adjustment request to correct your current inventory.
We have another issue which is your float quantity, it shows that you have 0, yet according to your CSDS requirements you do.
Team 5308, could you advise on this situation ? As it is shown below, the supplier has requirements on day 8;12;13;14.
@SABRINA @CONNOR @JONATHAN
Kind Regards,
Sara
Container Management Support
STELLANTIS
Working days Monday-Friday
Working hours 3 AM-4 PM
<a href="mailto:<EMAIL>"><EMAIL></a>
From: CHRISTINE TRINGALI &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Wednesday, August 7, 2024 21:52 To: Odes Jackson &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Containers support - BFCONTA104 &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; SHEILA RYAN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; sri.gogineni/2ausa.com &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; NELLY ELIZABETH CELIS CASTANEDA &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; ANDRE NEAL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; BRIAN MCNEW &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; CHALLEN HODSON &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JASON DANIEL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JASON DANIEL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JEREMIAH WEBB &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JIM LEBRE &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JONATHAN BRITTAIN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; PJ BOLIN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; ROBYN ANGLETON (EXTERNAL) &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; SABRINA HUNT &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Cc: CAMILA FOX &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Mohammed Ishan Iqbal &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; GREG MCCULLOUGH &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Ashish Patil &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Subject: RE: [EXTERNAL]04753094AA to 05308
Odes,
I don’t create the Mock up EDI – your company does that and they need to email it to me in the format that was reviewed in the training you took – one line per segment. This is done using your system – so you probably have to work with your internal IT department.
But again you do not transmit the mockup EDI to Stellantis – you just email it to me. Then I will reply back if you followed the rules in the training and if not I will list the error – but you will need to go back and correct. It might be a good idea for your team to take the training first.
I provided you a scenario for the mock up EDI example for plant 05308:
Part number 04753094AA, shipping 1440 parts in returnable CT121507, CP4845SP, CP4845VL
Part number 04753094AA, shipping 480 parts in backup expendable 00000EXP.
As for the CMS inventory issue and receipts – if you have questions, reach out to your Container Analyst.
Have a good evening.
From: Odes Jackson &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Wednesday, August 7, 2024 4:37 PM To: CHRISTINE TRINGALI &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Containers support - BFCONTA104 &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; SHEILA RYAN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; sri.gogineni/2ausa.com &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; NELLY ELIZABETH CELIS CASTANEDA &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; ANDRE NEAL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; BRIAN MCNEW &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; CHALLEN HODSON &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JASON DANIEL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JASON DANIEL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JEREMIAH WEBB &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JIM LEBRE &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JONATHAN BRITTAIN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; PJ BOLIN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; ROBYN ANGLETON (EXTERNAL) &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; SABRINA HUNT &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Cc: CAMILA FOX &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Mohammed Ishan Iqbal &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; GREG MCCULLOUGH &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Ashish Patil &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Subject: RE: [EXTERNAL]04753094AA to 05308
EXTERNAL : This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Christine,
If you have time, I would like to work with you to get this mock up EDI for containers along with our Web EDI team so we can get this resolved ASAP. Is that something you have time to do? Also is this the example of how you would like for me to email this to until we get our situation resolved. I will also work on fixing the CMS system as well.
Part number 04753094AA, shipping 1440 parts in returnable CT121507, CP4845SP, CP4845VL
Part number 04753094AA, shipping 480 parts in backup expendable 00000EXP.
Very Respectfully,
Odes Jackson
Logistic Manager / Proudction Planner
2A USA Inc.
C: 334.524.6001
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
US Address: 2410 West Tech Lane, Auburn AL, 36832 - USA
Internet: <a href="http://www.fonderie2a.com">www.fonderie2a.com</a>
This message is intended only for the use of the address named herein and contains legally privileged and confidential information. If you are not the intended recipient of this facsimile, you are hereby notified that any disseminations, distributions, or copying of this facsimile is strictly prohibited. If you have received this in error, please immediately notify us.
From: CHRISTINE TRINGALI &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Wednesday, August 7, 2024 3:13 PM To: Odes Jackson &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Containers support &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; SHEILA RYAN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Sri Gogineni &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; NELLY ELIZABETH CELIS CASTANEDA &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; ANDRE NEAL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; BRIAN MCNEW &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; CHALLEN HODSON &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JASON DANIEL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JASON DANIEL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JEREMIAH WEBB &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JIM LEBRE &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JONATHAN BRITTAIN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; PJ BOLIN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; ROBYN ANGLETON &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; SABRINA HUNT &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Cc: CAMILA FOX &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Mohammed Ishan Iqbal &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; GREG MCCULLOUGH &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Ashish Patil &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Subject: RE: [EXTERNAL]04753094AA to 05308
Hello Odes,
I only handle the container/expendable portion of the ASN – so you need to send me a copy of the EDI in the format that was reviewed in the training so each segment is on its own line. When you transmit the ASN.
However, I would have prefer your team work with me on creating a mock up EDI example – but you do NOT transmit the EDI to Stellantis you would just email the EDI in the training format – one segment per line. This is something I do with suppliers that have ASN issues for returnables/expendables and it is helpful.
Since you only use one part number your mock up example would be for one shipment going to plant 05308, however some are shipping in returnable and some in back up expendable. Remember the bill of lading rules and multiple packing slip rules on the same ASN.
Part number 04753094AA, shipping 1440 parts in returnable CT121507, CP4845SP, CP4845VL
Part number 04753094AA, shipping 480 parts in backup expendable 00000EXP.
As for container returnables that is based on your Float, Current Inventory, intransits, the replenishment section on your receipts.
Remember if your CMS inventory is not accurate the system will not return containers to you. Suppliers must always keep their CMS inventory accurate at all times – by following the receipting process and sending your ASN correctly at time of shipment.
You still have not cleaned up all your intransits shipments in CMS – the Container Analysts sent you this email on 6/24 and it has still not been addressed. This was explained in the training on the receipting process and how you need to validate each shipment.
Your aged intransits first need to be cleaned up and only then you need to submit an Inventory Adjustment if your inventory is incorrect. Remember the Inventory Adjustment process is an exception and can take a couple of weeks to be completed.
Thank you
From: Odes Jackson &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Wednesday, August 7, 2024 3:54 PM To: CHRISTINE TRINGALI &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Containers support - BFCONTA104 &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; SHEILA RYAN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; sri.gogineni/2ausa.com &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; NELLY ELIZABETH CELIS CASTANEDA &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; ANDRE NEAL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; BRIAN MCNEW &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; CHALLEN HODSON &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JASON DANIEL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JASON DANIEL &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JEREMIAH WEBB &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JIM LEBRE &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; JONATHAN BRITTAIN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; PJ BOLIN &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; ROBYN ANGLETON (EXTERNAL) &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; SABRINA HUNT &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Cc: CAMILA FOX &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Mohammed Ishan Iqbal &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; GREG MCCULLOUGH &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Ashish Patil &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Subject: RE: [EXTERNAL]04753094AA to 05308
EXTERNAL : This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Christine,
Yes I have completed the training. We are trying to make sure that once when send this ASN for the parts and container that you see this ASN with the totes on it. In the training it does say how you request more containers unless I missed it in the training. If it does I will go back and review.
Very Respectfully,
Odes Jackson
Logistic Manager / Proudction Planner
2A USA Inc.
C: 334.524.6001
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
US Address: 2410 West Tech Lane, Auburn AL, 36832 - USA
Internet: <a href="http://www.fonderie2a.com">www.fonderie2a.com</a>
This message is intended only for the use of the address named herein and contains legally privileged and confidential information. If you are not the intended recipient of this facsimile, you are hereby notified that any disseminations, distributions, or copying of this facsimile is strictly prohibited. If you have received this in error, please immediately notify us.
From: CHRISTINE TRINGALI &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Wednesday, August 7, 2024 12:55 PM To: Odes Jackson &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; Containers support &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; SHEILA RYAN &lt; [ <a href="mailto:<EMAIL>"><EMAIL></a>|mailto
...This comment is truncated as it exceeds the character limit.
Comments
Comment by Ann Thomas [ 01/Nov/24 ]
Comment by Maria Keshwala [ 05/Nov/24 ]
Hello Ann can you please advise exactly what you are requesting ? I did sent all the findings on the other issue do you need a raw data ? if so please provide a message ID date and reference number thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ol>
<h4 id="cs-30452-fwd-edi-transmission-failure-2024-10-31-06-00-05-to-2024-11-01-06-00-05-dts5763-created-01-nov-24-updated-05-nov-24-resolved-05-nov-24">[CS-30452] Fwd: EDI Transmission Failure - 2024-10-31 06:00:05 to 2024-11-01 06:00:05 DTS5763 Created: 01/Nov/24  Updated: 05/Nov/24  Resolved: 05/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-203557.png      image-********-204213.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Description
Can I get more details on why these failed? Mainly rule#1?
---------- Forwarded message ---------From: Datatrans Reports <a href="mailto:<EMAIL>"><EMAIL></a> Date: Fri, Nov 1, 2024 at 8:00 AM Subject: EDI Transmission Failure - 2024-10-31 06:00:05 to 2024-11-01 06:00:05 To: <a href="mailto:<EMAIL>"><EMAIL></a></p>
<h1 id="errors-report">Errors Report</h1>
<ol>
<li>Date/Time File Name Message Batch ID Status
1 2024-10-31 07:23:16 DESADV-COSTCOWHOL-60268-1478.xml Rule #1: Error Creating Object (DOCUMENT(&#39;ANSI&#39;,&#39;5010&#39;,&#39;856&#39;)) 139868087 Message Failed
2 2024-10-31 07:03:07 DESADV-LIPARIDELI-909360-495.xml Rule #127: Error Creating Object (SEGMENT(&#39;T.CTT&#39;)) 139867502 Message Failed
3 2024-10-31 07:03:07 DESADV-LIPARIDELI-909360-494.xml Rule #127: Error Creating Object (SEGMENT(&#39;T.CTT&#39;)) 139867501 Message Failed
4 2024-10-31 07:03:07 DESADV-COSTCOWHOL-60268-1477.xml Rule #1: Error Creating Object (DOCUMENT(&#39;ANSI&#39;,&#39;5010&#39;,&#39;856&#39;)) 139867500 Message Failed
5 2024-10-31 09:04:28 DESADV-COSTCOWHOL-60268-1482.xml Rule #1: Error Creating Object (DOCUMENT(&#39;ANSI&#39;,&#39;5010&#39;,&#39;856&#39;)) 139870262 Message Failed
6 2024-10-31 09:04:28 DESADV-COSTCOWHOL-60268-1481.xml Rule #1: Error Creating Object (DOCUMENT(&#39;ANSI&#39;,&#39;5010&#39;,&#39;856&#39;)) 139870261 Message Failed
--
Comments
Comment by Maria Keshwala [ 01/Nov/24 ]
Hello Tim I will look into this and advise thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Nov/24 ]
restaging batch 139868087 document passed - failed on creating object restaging to see what happens --fixed
second batch 139867502
costco files that failed are all set restaged those files
Maria Keshwala DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Nov/24 ]
Hello Tim for the Lipardi ones you are missing the line Item level on those files Daniel and I looked at those , for the Cotsco ones I have restage those should be fine now. So please send us good files for Lipardi they should be able to process. thank you
batch ID = 139868087 and batch ID 139867501
139867501
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ol>
<h4 id="cs-30348-fw-walmart-edi-update-created-31-oct-24-updated-04-nov-24-resolved-04-nov-24">[CS-30348] FW: Walmart EDI Update Created: 31/Oct/24  Updated: 04/Nov/24  Resolved: 04/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium Reporter: Candie Rogers Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      DADAE73BFD7B4B1A85023F898C5583C2 (1).pdf      image-20241104-143331.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Project
Description
Please see the updated mapping changes for Walmart below. Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
From: Sonny Mendiola <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, October 31, 2024 9:22 AM To: Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a>; John Turcich <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Walmart EDI Update
Comments
Comment by Candie Rogers [ 31/Oct/24 ]
Comment by Maria Keshwala [ 01/Nov/24 ]
Good morning Candie
I only see the image screenshot the EDI mapping updated file is not attached can you please send that over thank you
this might need a project. Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 01/Nov/24 ]
Hi Maria,
Attached is the PDF. Thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
<em>DADAE73BFD7B4B1A85023F898C5583C2 (1).pdf  (51 kB)</em>
Comment by Maria Keshwala [ 04/Nov/24 ]
Nothing that we need to do on our end
<a href="https://datatrans-inc.atlassian.net/browse/CS-30220">https://datatrans-inc.atlassian.net/browse/CS-30220</a>
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 04/Nov/24 ]
Hello Candie spoke with the Analyst there is nothing we need to do on our end everything should be fine. Thank you
Best
Maria
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Candie Rogers [ 04/Nov/24 ]
Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Maria Keshwala [ 04/Nov/24 ]
You’re welcome
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30317-milcut-4511-john-deere-horicon-po-missing-from-asn-0268699-0000-created-30-oct-24-updated-11-nov-24-resolved-11-nov-24">[CS-30317] MILCUT 4511 - JOHN DEERE HORICON - PO # MISSING FROM ASN 0268699-0000 Created: 30/Oct/24  Updated: 11/Nov/24  Resolved: 11/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Glen Houghtaling Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image004.png      D20241029T163821.***************      image-********-211119.png
Request Type: Emailed request
Request language: English
Request participants: None Organizations:
Description
Hi,
I am able to see the PO number ********** on the Global Shop file, is there a reason it is not populating on the DTS portal? I entered the PO number manually and sent the ASN.
Sincerely,
Glen Houghtaling
Accounts Receivable
Blachford Acoustics Group – Milcut
Phone: ************ Ext. 1102
Address: N50W13400 Overview Dr. Menomonee Falls, WI 53051
Comments
Comment by Glen Houghtaling [ 30/Oct/24 ]
<em>D20241029T163821.***************  (3 kB)</em>
Comment by Maria Keshwala [ 31/Oct/24 ]
I will check this one too
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Nov/24 ]
Hi Glen
This is the PO? I see the file you have is from 10/19/2024 this is the only I see from 09/24/
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</p>
<h4 id="cs-30310-re-fw-overdue-po-21d863-vendor-384619-created-30-oct-24-updated-05-nov-24-resolved-05-nov-24">[CS-30310] Re: Fw: Overdue PO 21D863 Vendor 384619 Created: 30/Oct/24  Updated: 05/Nov/24  Resolved: 05/Nov/24</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Marianne Kania Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-2yaunpa0.png      Outlook-2bdwgrc4.png      Outlook-avhsej1i.png      Outlook-evvrilh1.png      Outlook-y3opwyma.png      Outlook-223v0hn4.png     Outlook-mobilePhon.png      Outlook-email.png      Outlook-skype.png      image001.png      image002.png      image003.png      image004.png      image005.png
image006.png      image007.png      image008.png      image009.png      image-20241030-132916.png      image-20241030-133503.png      image-********-205410.png      image-********-210510.png      image-********-210758.png      image-********-210656.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
Description
Hi Nancy, I believe this project was handled by Krista and was completed/closed in July. Our Support team will be your POC for any production inquiries and I&#39;ve copied them here for you.
Hi support, Can you please check on this for Nancy?
Thanks,
<a href="https://www.datatrans-inc.com/">https://www.datatrans-inc.com/</a>
Marianne Kania
Datatrans Solutions : EDI Analyst
p: ************ x 235 w: datatrans-inc.com e: [<a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>">mailto:<EMAIL></a>]</p>
<ul>
<li><a href="https://www.facebook.com/DataTransSolutions/">https://www.facebook.com/DataTransSolutions/</a>
<a href="https://www.instagram.com/datatranssolutions/">https://www.instagram.com/datatranssolutions/</a>  <a href="https://twitter.com/datatrans_edi">https://twitter.com/datatrans_edi</a>  <a href="https://www.linkedin.com/company/datatrans-solutions/">https://www.linkedin.com/company/datatrans-solutions/</a>
On 10/30/2024 8:04 AM, Nancy Burdge wrote:
Hi, Team. Dollar General is not getting any of our 810 invoices. Can you get to the bottom of why these are not transmitting? thank you , Nancy Burdge
From: AP Vendor G-O <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, October 30, 2024 7:59 AM To: Nancy Burdge <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Overdue PO 21D863 Vendor 384619
Hello,
Please note that the most efficient way to send and receive payment is to be setup to submit invoices via EDI.
These invoices do not appear in the system. Email copies are not acceptable. Please send copies of the invoices and I will get permission for your copies to be processed.
Paper copies must be mailed in for invoice processing. Dollar General does not have a manual data entry team and paper invoices are entered as time permits as filler work.
The address is listed below for verification:
Paper invoices must be mailed via USPS to: Dollar General Corporation
PO Box 2128
Goodlettsville, TN 37070-2128
Thank you,
Dollar General
Vendor Relations fm</li>
</ul>
<ul>
<li><a href="mailto:<EMAIL>"><EMAIL></a> +
From: Nancy Burdge <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, October 30, 2024 6:39 AM To: AP Vendor G-O <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: Overdue PO 21D863
I would like to check to see if these were transmitted and received. Thank you so much, Nancy Burdge
22LJB0
22CKH4
22CKH3
Best regards
|
|
&lt;<a href="https://urldefense.com/v3/__https:/www.facebook.com/LittleJoeInternational__;!!ACrnZGTLaTbfiA!VvP4TYo1VP01W2U94wZdo1bTjIAZdhsXzosfP82h7R0-MMij2jpawPb7KgdKZ46-WsuGQsdE0tSd1nzS-">https://urldefense.com/v3/__https:/www.facebook.com/LittleJoeInternational__;!!ACrnZGTLaTbfiA!VvP4TYo1VP01W2U94wZdo1bTjIAZdhsXzosfP82h7R0-MMij2jpawPb7KgdKZ46-WsuGQsdE0tSd1nzS-</a>
Abcg0rUXK3_Cw$&gt; |  &lt;<a href="https://urldefense.com/v3/__https:/www.linkedin.com/company/drive-int-ag/__;!!ACrnZGTLaTbfiA!VvP4TYo1VP01W2U94wZdo1bTjIAZdhsXzosfP82h7R0-MMij2jpawPb7KgdKZ46-">https://urldefense.com/v3/__https:/www.linkedin.com/company/drive-int-ag/__;!!ACrnZGTLaTbfiA!VvP4TYo1VP01W2U94wZdo1bTjIAZdhsXzosfP82h7R0-MMij2jpawPb7KgdKZ46-</a>
WsuGQsdE0tSd1nzS-Abcg0oL0jv3dg$&gt; |  <a href="https://urldefense.com/v3/__https:/www.instagram.com/littlejoeinternational/__;!!ACrnZGTLaTbfiA!VvP4TYo1VP01W2U94wZdo1bTjIAZdhsXzosfP82h7R0-MMij2jpawPb7KgdKZ46-WsuGQsdE0tSd1nzS-Abcg0rKQUUJLA$">https://urldefense.com/v3/__https:/www.instagram.com/littlejoeinternational/__;!!ACrnZGTLaTbfiA!VvP4TYo1VP01W2U94wZdo1bTjIAZdhsXzosfP82h7R0-MMij2jpawPb7KgdKZ46-WsuGQsdE0tSd1nzS-Abcg0rKQUUJLA$</a> |
Nancy Burdge Managing Director
Drive Int. US 660 Cove Lake Dr. Marblehill, GA 30148, USA
| Cell: + ****** 856 5341 + <a href="about:invalid#zCSafez">tel:1%20(0)%20770%20856%2053%2041</a>+
| E-mail: + <a href="mailto:<EMAIL>"><EMAIL></a> +
| <em>+ <a href="http://www.driveint-us.com">www.driveint-us.com</a> +</em>
From: AP Vendor G-O <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, October 30, 2024 7:31 AM To: Nancy Burdge &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: FW: Overdue PO 21D863
Good morning,
I do not see an invoice in the system for PO 21D863. If you are EDI, please transmit. Invoices should be mailed in for processing if not EDI. Please forward a copy of the invoice in question.
Thank you,
Dollar General
Vendor Relations fm</li>
<li><a href="mailto:<EMAIL>"><EMAIL></a> +
From: Nancy Burdge <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, October 30, 2024 6:17 AM To: AP Vendor G-O <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Overdue PO 21D863
THIS COULD BE A PHISHING MESSAGE! The sender is from outside your organization, so carefully inspect this message for indicators of phishing. DO NOT click links, open attachments, or take other actions in a untrusted or suspicious message.
Hi, Team. We would like to check on the PO 21D863 for payment remittance. Thank you, Nancy Burdge
Best regards
|
|
&lt;<a href="https://urldefense.com/v3/__https:/www.facebook.com/LittleJoeInternational__;!!ACrnZGTLaTbfiA!S9mYScnzC1NzGQOkxUpfM5vvpktREr4gczLoiXjcOxSClY5zvF0trjLq4m8D8vwsUlCUGnzxhTJOHjms3Xv6KBQ1wNtnyQ">https://urldefense.com/v3/__https:/www.facebook.com/LittleJoeInternational__;!!ACrnZGTLaTbfiA!S9mYScnzC1NzGQOkxUpfM5vvpktREr4gczLoiXjcOxSClY5zvF0trjLq4m8D8vwsUlCUGnzxhTJOHjms3Xv6KBQ1wNtnyQ</a>
|  &lt;<a href="https://urldefense.com/v3/__https:/www.linkedin.com/company/drive-int-">https://urldefense.com/v3/__https:/www.linkedin.com/company/drive-int-</a>
ag/__;!!ACrnZGTLaTbfiA!S9mYScnzC1NzGQOkxUpfM5vvpktREr4gczLoiXjcOxSClY5zvF0trjLq4m8D8vwsUlCUGnzxhTJOHjms3Xv6KBTtNJLWXw$&gt; | &lt;<a href="https://urldefense.com/v3/__https:/www.instagram.com/littlejoeinternational/__;!!ACrnZGTLaTbfiA!S9mYScnzC1NzGQOkxUpfM5vvpktREr4gczLoiXjcOxSClY5zvF0trjLq4m8D8vwsUlCUGnzxhTJOHjms3Xv6KBTHjhuRMw">https://urldefense.com/v3/__https:/www.instagram.com/littlejoeinternational/__;!!ACrnZGTLaTbfiA!S9mYScnzC1NzGQOkxUpfM5vvpktREr4gczLoiXjcOxSClY5zvF0trjLq4m8D8vwsUlCUGnzxhTJOHjms3Xv6KBTHjhuRMw</a> |
Nancy Burdge
Managing Director
Drive Int. US 660 Cove Lake Dr. Marblehill, GA 30148, USA
| Cell: + ****** 856 5341 + <a href="about:invalid#zCSafez">tel:1%20(0)%20770%20856%2053%2041</a>+
| E-mail: + <a href="mailto:<EMAIL>"><EMAIL></a> +
| <em>+ <a href="http://www.driveint-us.com">www.driveint-us.com</a> +</em>
Comments
Comment by Marianne Kania [ 30/Oct/24 ]
Comment by Maria Keshwala [ 30/Oct/24 ]
Account number 7048
Dry INT USA
Invoices was sent
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 30/Oct/24 ]
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ Comment by Maria Keshwala [ 05/Nov/24 ]
PO dollar general 21D863 810 Has been ack customer has multiple 810s in draft and needs to clean those from the 850 in order to change to green
back in august 08/26/2024
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Nov/24 ]
22LJB0 = files out to 0-AS2-OUT <a href="mailto:<EMAIL>"><EMAIL></a> ******** sent successfully
22CKH4 batch ID ********
22CKH3
delivered
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 05/Nov/24 ]
Hello files show are being delivered to Dollar General
customer can restage from WEBEDI Portal to resend the files but they are being sent successfully via AS2- Thank you
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************</li>
</ul>
<h4 id="cs-30309-fw-external-chargebacks-pending-for-its-1st-offense-approval-urgent-created-30-oct-24-updated-01-nov-24-resolved-31-oct-24">[CS-30309] Fw: [EXTERNAL]Chargebacks Pending for its 1st offense Approval - Urgent Created: 30/Oct/24  Updated: 01/Nov/24  Resolved: 31/Oct/24</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Ann Thomas Assignee: Maria Keshwala
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-2A NA.png      image-20241031-165014.png      image001.png      image002.png      image003.png      image005.jpg      image004.png
Request Type: Emailed request
Request language: English
Request participants: Organizations:
Description
Good Morning DataTrans Team, Please l view the email below and let us know what&#39;s the issue with our ASN process with MOPAR
From: Sri Gogineni <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, October 30, 2024 7:37 AM To: Ann Thomas <a href="mailto:<EMAIL>"><EMAIL></a>; Christy Funderburk <a href="mailto:<EMAIL>"><EMAIL></a>; Mike Hixon <a href="mailto:<EMAIL>"><EMAIL></a>; Ashish Patil <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Fw: [EXTERNAL]Chargebacks Pending for its 1st offense Approval
Please see below we have a notification from Mopar for incorrect ASN, please verify whats going on here.
Sri.
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, October 30, 2024 7:00 AM To: Sri Gogineni <a href="mailto:<EMAIL>"><EMAIL></a> Subject: [EXTERNAL]Chargebacks Pending for its 1st offense Approval First occurrence-
Attention: Mopar Supplier:
This is your First occurrence of a nonconformance, please review the detail attached. Since this is your first compliance failure regarding inaccurate ASN data within the last 12 months; this notification is a courtesy email to ensure you will correct this error going forward. Any additional failures can lead to a chargeback being issued against your location. The link below will allow you to review further details regarding this compliance issue.
Please acknowledge receipt of this communication within 24hrs. To take actions, login to &#34;Mopar Supplier Chargeback&#34; application via eSupplierConnect (<a href="https://fcagroup.esupplierconnect.com">https://fcagroup.esupplierconnect.com</a>)
If you are not the contact for these issues, please advise that contact. Please acknowledge receipt of this communication within 24hrs.
Thank you for your attention to this matter,
Mopar Transportation Team <a href="mailto:<EMAIL>"><EMAIL></a></p>
<h5 id="supplier-code">Supplier code |</h5>
<h5 id="occurdate">OccurDate |</h5>
<h5 id="calltype">CallType |</h5>
<h5 id="natureofcall">NatureOfCall |</h5>
<h5 id="pkgslip">PkgSlip |</h5>
<h5 id="debit-amount">Debit Amount |</h5>
<p>66234 2024-10-22 Compliance-ASN Non-Mexico, TransMode is LT but SCAC code is not CTII 309880 0.00
66234 2024-10-22 Compliance-ASN Non-Mexico, TransMode is LT but SCAC code is not CTII 309881 0.00
!<a href="https://u11021582.ct.sendgrid.net/wf/open?upn=u001.yMudQN5R2nhhKrNrbRDz3d1si4KCz10sPpUg-2F3ql-2Fqc1ZpWhdt9Q27fDhC2jgaJzJ2RRM03FlO43TU14LWNrpJztEo73BcMzDqFEyYEijjpxK8H3kPuxzfTEDFQKoUj4fv8KHhwlsLk8x0H2lZRh7IlNtQXznZWU-2Fb6iP3K5Kw-2BXP13QhWi7n-2FnMUzZxVNBvZqZdvTG4E6tkj2nHFGR0C0DtCi85DrGbc9RtM3XJoaM-3D">https://u11021582.ct.sendgrid.net/wf/open?upn=u001.yMudQN5R2nhhKrNrbRDz3d1si4KCz10sPpUg-2F3ql-2Fqc1ZpWhdt9Q27fDhC2jgaJzJ2RRM03FlO43TU14LWNrpJztEo73BcMzDqFEyYEijjpxK8H3kPuxzfTEDFQKoUj4fv8KHhwlsLk8x0H2lZRh7IlNtQXznZWU-2Fb6iP3K5Kw-2BXP13QhWi7n-2FnMUzZxVNBvZqZdvTG4E6tkj2nHFGR0C0DtCi85DrGbc9RtM3XJoaM-3D</a>!
Comments
Comment by Ann Thomas [ 30/Oct/24 ]
Comment by Ann Thomas [ 30/Oct/24 ]
Maria, Email from MOPAR below
Attention: Mopar Supplier:
This is your First occurrence of a nonconformance, please review the detail attached. Since this is your first compliance failure regarding inaccurate ASN data within the last 12 months; this notification is a courtesy email to ensure you will correct this error going forward. Any additional failures can lead to a chargeback being issued against your location. The link below will allow you to review further details regarding this compliance issue.
Please acknowledge receipt of this communication within 24hrs. To take actions, login to &#34;Mopar Supplier Chargeback&#34; application via eSupplierConnect (<a href="https://fcagroup.esupplierconnect.com">https://fcagroup.esupplierconnect.com</a>)
If you are not the contact for these issues, please advise that contact. Please acknowledge receipt of this communication within 24hrs.
Thank you for your attention to this matter,
Mopar Transportation Team <a href="mailto:<EMAIL>"><EMAIL></a></p>
<h5 id="amount">Amount</h5>
<p>66234 2024-10-22 Compliance-ASN Non-Mexico, TransMode is LT but SCAC code is not CTII 309880 0.00
66234 2024-10-22 Compliance-ASN Non-Mexico, TransMode is LT but SCAC code is not CTII 309881 0.00
Comment by Maria Keshwala [ 31/Oct/24 ]
Hi Ann
Ok so the issue was that you are sending invalid data not that the ASNs are missing the previous request was that the asns were not being delivered on sent status , this is you are sending invalid values on your ASN Non-”Mexico, TransMode is LT but SCAC code is not CTII” I will advise where this is on the file so you can fix it send it I will not know what code you need to add but they clearly provided this information to you .
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by Maria Keshwala [ 31/Oct/24 ]
ASN was accepted no EDI errors
Standard Carrier Alpha Code (SCAC)
RDWY used per TP Invalid please check with your trading partner which SCAC Code you should be using
EDI document has no error file has bee accepted
If file is rejecting on their End is due to the issue explain above that you have to correct
And these are not MOPAR ASNs mentioned on the email is Chrysler Thank you
6234 2024-10-22 Compliance-ASN Non-Mexico, TransMode is LT but SCAC code is not CTII 309880 0.00
66234 2024-10-22 Compliance-ASN Non-Mexico, TransMode is LT but SCAC code is not CTII 309881 0.00
Maria Keshwala DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 31/Oct/24 ]
Good Afternoon,
These are Chrysler Mopar. It has the location at the bottom of ASN where it has “Ship to” and “Party for whom it is ultimately intended” has 90110 which is Mopar location. Chrysler Kokomo is 05308 and Tipton is 05317. As far as SCAC I am researching that one.
Ann – Can you please confirm the location number for Chrysler Mopar? I need to know if the location 90110 is correct or if I should be using another location number.
Thank you in Advance.
Kind Regards,
Christy Funderburk | Accounting / Purchasing | USA Division</p>
