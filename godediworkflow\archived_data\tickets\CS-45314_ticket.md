# CS-45314: RE: DataTrans Solutions Project Update - On Hold (Closed) V# 154311 ECHELON DISTRIBUTION LLC DIV 17 SAP Only 

## Ticket Information
- **Key**: CS-45314
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-06-12T06:55:14.182-0600
- **Updated**: 2025-06-13T06:19:02.386-0600
- **Customer**: Distribution LLC

## Description
{color:#002060}Hi ECHELON DISTRIBUTION LLC Team,{color}{color:#002060}{color} 

  

{color:#002060}As you have already confirmed that your organization is EDI capable, <PERSON><PERSON><PERSON> would like to start the EDI onboarding process.{color} 

{color:#002060}Could you please go through the attached word file, which contains the documents listed below?{color} 
# {color:#002060}Medline EDI specification documents and Sample EDI files{color}
# {color:#002060}Medline’s AS2 details, AS2 certificates and SSL certificates.{color}
# {color:#002060}Updated Medline branch list.{color}
 

{color:#002060}Also, we need the following information to complete the EDI setups and start EDI testing, so could you please fill in the table questionnaires below?{color} 
|  *{color:#172B4D} {color}* |  *{color:#002060} *Please provide your communication information*{color}* | 
| {color:#002060}1{color} | {color:#002060}VAN Name or AS2 information{color} | | 
| {color:#002060}2{color} | {color:#002060}Test Qualifier{color} | | {color:#002060}Test ISA/GS ID :{color} | 
| {color:#002060}3{color} | {color:#002060}Production Qualifier{color} | | {color:#002060}Production ISA/GS ID :{color} | 
|  *{color:#172B4D} {color} {color:#002060} *What EDI transactions are you capable of (4010 preferred)?*{color}* | 
| {color:#002060}1{color} | {color:#002060}850 Purchase Order{color} | {color:#002060}YES / NO{color} | 
| {color:#002060}2{color} | {color:#002060}855 Order Acknowledgement{color} | {color:#002060}YES / NO{color} | 
| {color:#002060}3{color} | {color:#002060}856 Shipping Notification{color} | {color:#002060}YES / NO{color} | 
| {color:#002060}4{color} | {color:#002060}810 Invoice{color} | {color:#002060}YES / NO{color} | 
| {color:#002060}5{color} | {color:#002060}997 Functional Acknowledgement{color} | {color:#002060}YES / NO{color} | 
| {color:#002060}6{color} | {color:#002060}Any other EDI transaction?{color} | | 
|  *{color:#172B4D} {color}* |  *{color:#002060} *Can you handle drop ship POs via EDI?*{color}*  *{color:#002060} *YES / NO*{color}* | 
| {color:#002060}1{color} | {color:#002060}If yes, then we send customer PO number in BEG06 field always, which should be populated on packing slip while delivering the goods to our customer, is it possible at your end?{color} | {color:#002060}YES  */* NO{color} | 
| {color:#002060}2{color} | {color:#002060}For DIR POs, N1*ST_04 segment will always have the value as  *DIR*. So, the shipping address must be fetched from corresponding N1-N4 segments. As these are dropship orders Medline cannot provide you with the list of shipping addresses. Your system must be capable to fetch the address from N1-N4 segments of EDI data, is it possible?{color} | {color:#002060}YES  */* NO{color} | 
| {color:#002060}3{color} | {color:#002060}For your info, we send drop ship fee charges in a separate line on the DIR PO.{color} | {color:#002060}OK  */* NOT OK{color} | 
| {color:#172B4D}{color} |  *{color:#002060} *EDI 856 Questionnaire:*{color}* | 
| {color:#002060}1{color} | {color:#002060}Will the 856 sent by shipment or by PO?{color} | {color:#002060}BY SHIPMENT  */* BY PO{color} | 
| {color:#002060}2{color} | {color:#002060}If sent by shipment and multiple POs are on the shipment, will the 856 sorted by PO?{color} {color:#002060}(In such case “sorted by PO” is mandatory at our end){color} | {color:#002060}YES  */* NO */* NA{color} | 
| {color:#002060}3{color} | {color:#002060}If sent by shipment, will this still be case when there are additions or deletions of line items on the PO?{color} | {color:#002060}YES */* NO */* NA{color} {color:#002060}{color} | 
| {color:#002060}4{color} | {color:#002060}Is the Tracking/BOL/Shipment number used as the ASN number? (BSN02 field in the BSN segment)?{color} | {color:#002060}YES */* NO{color} | 
| {color:#002060}5{color} | {color:#002060}Are you able to send the BOL/Tracking number in the EDI 856? If yes, you will send it in REF^BM or REF^CN or BSN_02 segments?{color} | {color:#002060}REF_BM  */* REF_CN  */* BSN_02 */* NA{color} | 
| {color:#002060}7{color} | {color:#002060}If the 856 is sent by PO, will POs that belong to the same shipment contain the same Tracking number (BSN_02), BOL number (REF_BM_02 or REF_CN_02), same Carrier code (TD5_03) and same Shipment Date (DTM_02)?{color} {color:#002060}(In such case it is mandatory at our end to have same Tracking#, Freight code and shipment date){color} | {color:#002060}YES  */* NO{color} | 
| {color:#002060}8{color} | {color:#002060}We want the same PO line number, vendor material number, Medline material number, and UOM in EDI 855,856 and EDI 810 file as we send in the EDI 850 file. Is it possible at your end?{color} | {color:#002060}YES  */* NO{color} | 
| {color:#002060}9{color} | {color:#002060}If sent by shipment and multiple POs are on the shipment, then their tracking details should match, we want to test this scenario. For that will you send us one EDI 856 file at the time of testing showing that multiple POs are coming in one shipment?{color} {color:#002060}(In such case for all the POs coming in one shipment their Tracking number (BSN_02), BOL number (REF_BM_02 or REF_CN_02), Carrier code (TD5_03) and Shipment Date (DTM_02) must be the same){color} | {color:#002060}YES  */* NO{color} | 
| {color:#172B4D}{color} |  *{color:#002060} *EDI 810 Questionnaire:*{color}*  *{color:#172B4D} ***Note - All credits need to be emailed to*{color}* {color:#172B4D}{color}[{color:#172B4D} *<EMAIL>*{color}|mailto:<EMAIL>]{color:#172B4D} **{color} | 
| {color:#002060}1{color} | {color:#002060}Do you have a TEST system?{color} | {color:#002060}YES  */* NO{color} | 
| {color:#002060}2{color} | {color:#002060}Are you able to send EDI 810 (Invoice) for an EDI Drop Ship 850?{color} | {color:#002060}YES  */* NO{color} | 
| {color:#002060}3{color} | {color:#002060}Are you able to send EDI 810 (Invoice) for a Fax or Email Purchase Order?{color} | {color:#002060}YES  */* NO{color} | 
| {color:#002060}4{color} | {color:#002060}Do you currently charge Medline Industries, a Drop Ship Fee on Drop Ship/ Direct orders to our customers?{color} | {color:#002060}YES  */* NO{color} | 
| {color:#002060}5{color} | {color:#002060}Do you currently charge Medline Industries, Freight/Shipping charges on your invoices{color} | {color:#002060}YES */* NO{color} | 
| {color:#002060}6{color} | {color:#002060}Do you currently charge Medline Industries, Minimum Order Fee on your invoices?{color} | {color:#002060}YES  */* NO{color} | 
| {color:#002060}7{color} | {color:#002060}Are you able to send a valid SCAC (Freight Carrier) code? Only 4 Alpha character allowed and have be all CAPS?{color} | {color:#002060}YES  */* NO{color} | 
| {color:#002060}8{color} | {color:#002060}What Material Number are you able to send?{color} | {color:#002060}Medline material numbers */* Vendor material numbers */* Both Medline and vendor material numbers{color} | 
| {color:#002060}{color} |  *{color:#002060} *Medline Notes to Supplier*{color}* {color:#002060}Medline Industries is Tax Exempt Organization.{color} {color:#002060}Medline does not accept Credits via EDI.{color} {color:#002060}Medline does not accept freight only charges via EDI.{color} {color:#002060}Medline does not accept chargebacks via EDI.{color} {color:#002060}Medline will not accept any 810 without a valid 850 been referenced.{color} {color:#002060}Medline will not accept any additional charges to the Invoice total without proper EDI allocation.{color} |  *{color:#4EA72E} {color}* | 
| | | | | 
| | | | | |
  

  

{color:#002060}Thanks,{color} 
| 
!image001.png|thumbnail! <[http://www.medline.com/?cmpid=eid:signature-logo-US-Sales]>{color:#545454}{color} | {color:#545454}{color} | 
|  *{color:#545454} *Mithilesh Dave*{color}*{color:#545454} \\ Associate - EDI - Partner Integration \\ Information Technology \\ Medline Industries, LP \\ {color}[{color:#545454}{color}{color:#58585B}www.medline.com{color}{color:#545454}{color}|http://www.medline.com/?cmpid=eid:signature-link-US-Sales]{color:#545454}{color} | 
| | 
| {color:#545454}O:02067096900 \\ {color}[{color:#545454}{color}{color:#58585B}<EMAIL>{color}{color:#545454}{color}|mailto:<EMAIL>]{color:#545454}{color} | 
 

  

 *{color:red} *For fast responses related to any Medline’s vendor related EDI issues, directly contact - Vendor EDI Support*{color} [{color:red} *<EMAIL>*{color}|mailto:<EMAIL>]{color:red} {color}*     

  
  

 *From:* info <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Saturday, June 7, 2025 12:40 AM
  *To:* EDI Support <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* FW: DataTrans Solutions Project Update - On Hold (Closed)
  *Importance:* High   

  

Good afternoon,  

  

I am reaching out to see if they might be any update on connection project below. Please let me know if there is anything else needed or if we have just been put on hold due to back log.  

  

Thank you, 

Christine Marler  

Contracting Department  

  

{color:#3A7C22}Echelon Distribution{color}
 T {color:#0563C1}************{color} <tel:9164261608> | F {color:#0563C1}************{color} <tel:9164261609>
 [{color:#0563C1}<EMAIL>{color}|mailto:<EMAIL>] | [{color:#0563C1}www.echelondistribution.com{color}|https://urldefense.com/v3/__http:/www.echelondistribution.com/__;!!DjNdo6avjEs!pxtwTfLLu0U9d2MxG91imfJ7Lv1XegOF5NpQE5AxiHQD88Zw7cWjVWnJ4IEm2xoR76_3pJbBvhj2ITQejR3baPE$]
 Certifications: CA DVBE/SB Certification # 1745218 | SDVOSB|SBE|HUBzone Firm 

NASPO Contract # 7-25-51-01.04 | NYS OGS Contract: PC70062
 UEID#: DKC2DEAV47L4, CAGE Code: 71ED1, DUNS: 078456757  

  

  

   

 *From:* [<EMAIL>|mailto:<EMAIL>] <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Thursday, May 8, 2025 9:09 AM
  *To:* info <[<EMAIL>|mailto:<EMAIL>]>
  *Cc:* [<EMAIL>|mailto:<EMAIL>]
  *Subject:* DataTrans Solutions Project Update - On Hold (Closed)  

  


 
 Hello Echelon Distribution LLC, 
 
 Your project with  *Medline Industries* has been placed on hold and has been removed from our active project board. Once you are ready to have this project re-opened, please contact [<EMAIL>|mailto:<EMAIL>] Your project will be assigned a new start date and estimated due date once re-opened. 
 
 If you wish to view your project status, you can do so by logging into your WebEDI account, and then click on  *"Project Status"* towards the left hand side. Project comments will also be found here, including why the project was placed on hold. 
 
 Since your project has been placed on hold we would like to make you aware that we offer the option to move your account status to â€œHibernateâ€ for $10 per month, keeping your account in an in-active status. During Hibernation you will not be able to access your account, but you may move your account back to Active status at any time and return to the basic account fee. Hibernation saves you a reactivation cost that would incur in the event of cancelling and signing back up at a later date. Please reach out to our accounting department [<EMAIL>|mailto:<EMAIL>] if you wish to Hibernate your account. 
 
 Regards, 
 
 DataTrans Solutions 
 
 Krista Johnson 
 [<EMAIL>|mailto:<EMAIL>] 
 ************ 
----
{color:gray}This communication may contain sensitive, confidential, and proprietary information of Medline and is intended for the addressee only. Any dissemination, distribution, or use by others is prohibited. If you received this communication in error, please destroy it, all copies, and any attachments and notify the sender as soon as possible. Any statements or opinions expressed do not necessarily reflect those of Medline or its subsidiaries and affiliates.{color}

## Components


## Labels

