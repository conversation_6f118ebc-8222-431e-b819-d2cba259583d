<h3 id="friday-chat">Friday Chat</h3>
<p>Meeting started: May 16, 2025, 4:56:27 PM Meeting duration: 40 minutes Meeting participants: <PERSON>, <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="generated-content">Generated Content</h4>
<h1 id="meeting-notes">Meeting Notes</h1>
<h2 id="participants-micha<PERSON>-<PERSON><PERSON>-sandy">Participants - <PERSON> <PERSON></h2>
<hr>
<h2 id="key-discussion-points">Key Discussion Points</h2>
<h3 id="1-phone-call-handling-documentation-always-clarify-who-you-spoke-with-name-company-account-number-note-the-issues-discussed-e-g-missing-invoices-purchase-orders-communication-tips-start-with-a-friendly-greeting-and-ask-how-you-can-help-use-bullet-points-to-summarize-issues-for-clarity-if-unsure-about-an-answer-inform-the-caller-that-you-will-look-into-it">1. <strong>Phone Call Handling</strong>    - <strong>Documentation</strong>:      - Always clarify who you spoke with (name, company, account number).      - Note the issues discussed (e.g., missing invoices, purchase orders).    - <strong>Communication Tips</strong>:      - Start with a friendly greeting and ask how you can help.      - Use bullet points to summarize issues for clarity.      - If unsure about an answer, inform the caller that you will look into it.</h3>
<h3 id="2-customer-interaction-handling-upset-customers">2. <strong>Customer Interaction</strong>    - <strong>Handling Upset Customers</strong>:</h3>
<ul>
<li>Most callers are stressed but not aggressive.      - If a customer becomes disrespectful, you can:        - Put them on hold and seek assistance.        - Send them to a manager&#39;s voicemail if necessary.    - <strong>Common Issues</strong>:      - Display issues (e.g., broken buttons) that do not impact functionality.</li>
</ul>
<h3 id="3-work-environment-flexibility-ability-to-take-breaks-and-manage-time-effectively-encouragement-to-communicate-if-stepping-away-from-the-desk-team-dynamics-supportive-environment-with-opportunities-for-hands-on-learning">3. <strong>Work Environment</strong>    - <strong>Flexibility</strong>:      - Ability to take breaks and manage time effectively.      - Encouragement to communicate if stepping away from the desk.    - <strong>Team Dynamics</strong>:      - Supportive environment with opportunities for hands-on learning.</h3>
<h3 id="4-training-and-development-hands-on-experience-emphasis-on-the-importance-of-practical-experience-over-theoretical-knowledge-upcoming-training-discussion-about-scheduling-a-one-on-one-training-session-with-a-customer">4. <strong>Training and Development</strong>    - <strong>Hands-On Experience</strong>:      - Emphasis on the importance of practical experience over theoretical knowledge.    - <strong>Upcoming Training</strong>:      - Discussion about scheduling a one-on-one training session with a customer.</h3>
<h3 id="5-customer-documentation-document-types-204-load-tender-990-acknowledgment-with-minimal-information-reservation-action-reference-number-214-detailed-load-information">5. <strong>Customer Documentation</strong>    - <strong>Document Types</strong>:      - 204: Load tender.      - 990: Acknowledgment with minimal information (reservation action, reference number).      - 214: Detailed load information.</h3>
<hr>
<h2 id="key-concepts-documentation-importance-essential-for-clarity-and-follow-up">Key Concepts - <strong>Documentation Importance</strong>: Essential for clarity and follow-up.</h2>
<ul>
<li><strong>Customer Service Skills</strong>: Active listening, empathy, and clear communication are crucial. - <strong>Team Support</strong>: Rely on colleagues for assistance and guidance.</li>
</ul>
<hr>
<h2 id="thought-provoking-questions-1-how-can-we-improve-our-documentation-process-to-enhance-customer-interactions-2-what-strategies-can-be-implemented-to-manage-stress-during-high-pressure-calls">Thought-Provoking Questions 1. How can we improve our documentation process to enhance customer interactions? 2. What strategies can be implemented to manage stress during high-pressure calls?</h2>
<hr>
<h2 id="real-world-applications-customer-service-training-implementing-these-strategies-in-various-customer-service-roles-can-lead-to-improved-customer-satisfaction-team-collaboration-encouraging-open-communication-and-support-among-team-members-can-enhance-overall-performance">Real-World Applications - <strong>Customer Service Training</strong>: Implementing these strategies in various customer service roles can lead to improved customer satisfaction. - <strong>Team Collaboration</strong>: Encouraging open communication and support among team members can enhance overall performance.</h2>
<hr>
<h2 id="areas-for-further-research-best-practices-in-customer-service-communication-techniques-for-managing-difficult-customer-interactions">Areas for Further Research - Best practices in customer service communication. - Techniques for managing difficult customer interactions.</h2>
<hr>
<h2 id="glossary-load-tender-204-a-document-that-indicates-a-shipment-is-ready-for-transport-acknowledgment-990-a-response-document-confirming-receipt-of-a-load-tender">Glossary - <strong>Load Tender (204)</strong>: A document that indicates a shipment is ready for transport. - <strong>Acknowledgment (990)</strong>: A response document confirming receipt of a load tender.</h2>
<ul>
<li><strong>Detailed Load Information (214)</strong>: A document providing specifics about the load being transported.</li>
</ul>
<hr>
<h2 id="main-takeaways-effective-communication-and-documentation-are-key-in-customer-service-hands-on-experience-is-vital-for-understanding-systems-and-processes-support-from-team-members-can-alleviate-stress-and-improve-performance">Main Takeaways - Effective communication and documentation are key in customer service. - Hands-on experience is vital for understanding systems and processes. - Support from team members can alleviate stress and improve performance.</h2>
<hr>
<p>This structured format can help in reviewing the material discussed during the meeting and can serve as a reference for future interactions.</p>
<h4 id="generated-content-2">Generated Content</h4>
<p>Kaylee&#39;s problem involved a customer with a complex setup that is not traditional, specifically dealing with low tenders and responses. The customer has been experiencing difficulties with the setup, which includes the following key points:</p>
<ol>
<li><strong>Document Types</strong>:    - The customer receives a <strong>204</strong> document and must reply with:      - <strong>990</strong>: Similar to an 855 but with less information, indicating reservation actions (change, delete, accepted, canceled) along with a reference number.      - <strong>214</strong>: Contains details about the load tender.</li>
<li><strong>Conditional Fields</strong>:    - The 214 document requires specific fields to be filled in:      - Either a <strong>shipment status code</strong> and <strong>shipment reason</strong> or a <strong>shipment appointment status code</strong> and <strong>shipment status</strong>.      - If one set of fields is filled, the other must remain empty.</li>
<li><strong>Required Fields</strong>:    - Three fields (date, time, time code) must all be filled in if one is filled.    - There was a concern about certain fields that were not marked as required but appeared to be necessary for the process.</li>
<li><strong>Mapping Rules</strong>:    - There was a discussion about potentially needing to remove or adjust fields that were not needed in the response.
Kaylee&#39;s issue highlights the complexity of the customer&#39;s requirements and the need for clarification on the necessary fields and document handling.</li>
</ol>
<h4 id="generated-content-3">Generated Content</h4>
<h1 id="meeting-notes-2">Meeting Notes</h1>
<h2 id="participants-michael-lauren-sandy-2">Participants - <strong>Michael</strong> - <strong>Lauren</strong> - <strong>Sandy</strong></h2>
<hr>
<h2 id="key-discussion-points-2">Key Discussion Points</h2>
<h3 id="1-customer-interaction-guidelines-documentation-always-document-who-you-spoke-with-first-and-last-name-company-name-or-account-number-issues-discussed-bullet-points-for-clarity">1. <strong>Customer Interaction Guidelines</strong>    - <strong>Documentation</strong>:      - Always document:        - <strong>Who you spoke with</strong> (first and last name).        - <strong>Company name</strong> or <strong>account number</strong>.        - <strong>Issues discussed</strong> (bullet points for clarity).</h3>
<ul>
<li><strong>Communication Tips</strong>:      - Start with a friendly greeting: &#34;Who do I have the pleasure of speaking with?&#34;      - Ask for the customer&#39;s email at the end of the conversation to avoid confusion.      - Encourage customers to explain their issues in detail; listen actively and summarize their concerns.      - If customers mention specific documents (e.g., invoices, purchase orders), note these down.</li>
</ul>
<h3 id="2-handling-customer-calls-call-management-if-unsure-about-an-answer-inform-the-customer-you-will-look-into-it-and-follow-up-if-a-customer-is-upset-remain-calm-and-professional-offer-to-escalate-to-a-manager-if-necessary-escalation-process-if-a-customer-is-disrespectful-or-uncooperative-place-them-on-hold-and-reach-out-to-a-supervisor-if-no-one-is-available-send-the-customer-to-voicemail-with-an-explanation">2. <strong>Handling Customer Calls</strong>    - <strong>Call Management</strong>:      - If unsure about an answer, inform the customer you will look into it and follow up.      - If a customer is upset, remain calm and professional; offer to escalate to a manager if necessary.    - <strong>Escalation Process</strong>:      - If a customer is disrespectful or uncooperative, place them on hold and reach out to a supervisor.      - If no one is available, send the customer to voicemail with an explanation.</h3>
<h3 id="3-technical-issues-current-system-issues-a-known-issue-with-the-web-edi-button-for-the-current-day-is-a-display-only-problem-it-does-not-affect-functionality-customer-systems-discussed-the-setup-for-a-customer-using-load-tenders-and-responses-204-990-214-documents-importance-of-filling-out-required-fields-correctly-in-the-system">3. <strong>Technical Issues</strong>    - <strong>Current System Issues</strong>:      - A known issue with the <strong>web EDI button</strong> for the current day is a display-only problem; it does not affect functionality.    - <strong>Customer Systems</strong>:      - Discussed the setup for a customer using <strong>load tenders</strong> and responses (204, 990, 214 documents).      - Importance of filling out required fields correctly in the system.</h3>
<h3 id="4-training-and-experience-hands-on-learning-emphasized-the-importance-of-practical-experience-over-theoretical-knowledge">4. <strong>Training and Experience</strong>    - <strong>Hands-On Learning</strong>:      - Emphasized the importance of practical experience over theoretical knowledge.</h3>
<ul>
<li>Suggested that new employees practice with real customer interactions to build confidence.    - <strong>Support from Team</strong>:      - Team members are available for assistance via messaging platforms during calls.</li>
</ul>
<h3 id="5-work-environment-flexibility-employees-can-take-breaks-and-step-away-from-their-desks-as-needed-encouraged-to-communicate-when-stepping-away-to-avoid-confusion">5. <strong>Work Environment</strong>    - <strong>Flexibility</strong>:      - Employees can take breaks and step away from their desks as needed.      - Encouraged to communicate when stepping away to avoid confusion.</h3>
<h3 id="6-upcoming-responsibilities-one-on-one-training-lauren-is-tasked-with-scheduling-a-training-session-for-a-customer-which-may-involve-unique-challenges-due-to-the-customer-s-setup">6. <strong>Upcoming Responsibilities</strong>    - <strong>One-on-One Training</strong>:      - Lauren is tasked with scheduling a training session for a customer, which may involve unique challenges due to the customer&#39;s setup.</h3>
<hr>
<h2 id="important-concepts-documentation-essential-for-clarity-and-follow-up-customer-service-focus-on-empathy-and-active-listening-technical-knowledge-understanding-specific-customer-needs-and-system-functionalities">Important Concepts - <strong>Documentation</strong>: Essential for clarity and follow-up. - <strong>Customer Service</strong>: Focus on empathy and active listening. - <strong>Technical Knowledge</strong>: Understanding specific customer needs and system functionalities.</h2>
<hr>
<h2 id="thought-provoking-questions-1-how-can-we-improve-our-documentation-process-to-enhance-customer-interactions-2-what-strategies-can-be-implemented-to-better-handle-escalated-calls-3-in-what-ways-can-hands-on-training-be-structured-to-maximize-learning-for-new-employees">Thought-Provoking Questions 1. How can we improve our documentation process to enhance customer interactions? 2. What strategies can be implemented to better handle escalated calls? 3. In what ways can hands-on training be structured to maximize learning for new employees?</h2>
<hr>
<h2 id="real-world-applications-customer-service-training-implementing-these-guidelines-can-improve-customer-satisfaction-and-employee-confidence-technical-support-understanding-system-issues-can-lead-to-quicker-resolutions-and-better-customer-experiences">Real-World Applications - <strong>Customer Service Training</strong>: Implementing these guidelines can improve customer satisfaction and employee confidence. - <strong>Technical Support</strong>: Understanding system issues can lead to quicker resolutions and better customer experiences.</h2>
<hr>
<h2 id="areas-for-further-research-best-practices-in-customer-service-documentation-techniques-for-managing-difficult-customer-interactions-training-methods-for-technical-support-roles">Areas for Further Research - Best practices in customer service documentation. - Techniques for managing difficult customer interactions. - Training methods for technical support roles.</h2>
<hr>
<h2 id="glossary-of-terms-edi-electronic-data-interchange-load-tender-a-document-that-outlines-the-details-of-a-shipment-990-a-response-document-indicating-reservation-actions-214-a-document-that-provides-details-about-the-load">Glossary of Terms - <strong>EDI</strong>: Electronic Data Interchange - <strong>Load Tender</strong>: A document that outlines the details of a shipment. - <strong>990</strong>: A response document indicating reservation actions. - <strong>214</strong>: A document that provides details about the load.</h2>
<hr>
<h2 id="main-takeaways-effective-communication-and-documentation-are-crucial-in-customer-service-hands-on-experience-is-essential-for-mastering-customer-interactions-team-support-is-vital-for-managing-challenging-situations">Main Takeaways - Effective communication and documentation are crucial in customer service. - Hands-on experience is essential for mastering customer interactions. - Team support is vital for managing challenging situations.</h2>
<hr>
<h2 id="potential-exam-questions">Potential Exam Questions</h2>
<ol>
<li>What are the key components to document during a customer call? 2. Describe the escalation process for handling difficult customers. 3. Explain the significance of the 990 and 214 documents in customer interactions.</li>
</ol>
<hr>
<h2 id="suggested-resources-books-the-art-of-customer-service-by-john-doe-articles-effective-communication-in-customer-service-available-online-videos-customer-service-training-videos-on-platforms-like-youtube">Suggested Resources - <strong>Books</strong>: &#34;The Art of Customer Service&#34; by John Doe - <strong>Articles</strong>: &#34;Effective Communication in Customer Service&#34; (available online) - <strong>Videos</strong>: Customer service training videos on platforms like YouTube.</h2>
<hr>
<h2 id="mind-map-customer-interaction-documentation-communication-tips-call-management-technical-issues-system-problems-customer-systems-training-hands-on-learning-team-support-work-environment-flexibility-breaks">Mind Map - <strong>Customer Interaction</strong>   - Documentation   - Communication Tips   - Call Management - <strong>Technical Issues</strong>   - System Problems   - Customer Systems - <strong>Training</strong>   - Hands-On Learning   - Team Support - <strong>Work Environment</strong>   - Flexibility   - Breaks</h2>
<hr>
<p>This structured approach provides a comprehensive overview of the meeting, highlighting key points and facilitating further study and application of the
discussed topics.</p>
<h4 id="generated-content-4">Generated Content</h4>
<h3 id="kaylee-s-problem-overview">Kaylee&#39;s Problem Overview</h3>
<ol>
<li><strong>Customer Setup Issues</strong>    - The customer has a <strong>non-traditional setup</strong> involving low tenders and responses rather than standard orders and acknowledgments.    - The setup process has been <strong>lengthy and complex</strong>.</li>
<li><strong>Document Requirements</strong>    - The customer needs to handle specific documents:      - <strong>204</strong>: Load tender received.      - <strong>990</strong>: Acknowledgment response that includes:        - Reservation action (e.g., change, delete, accepted, canceled).        - Reference number.        - Carrier reference number.      - <strong>214</strong>: Document that must include:        - Reference number.        - Standard carrier quote.        - Conditional fields that must be filled based on the selection of either shipment status or appointment status.</li>
<li><strong>Field Requirements and Concerns</strong>    - There are <strong>conditional fields</strong> that must be filled:      - If using shipment status code, the shipment reason must also be provided.      - If using shipment appointment status code, the appointment status must be included.    - <strong>Three mandatory fields</strong> (date, time, time code) must be filled if any one of them is filled.    - There are concerns about certain fields that may be <strong>display only</strong> or unnecessary:</li>
</ol>
<ul>
<li>Some fields currently populated with numbers need clarification on whether they should be removed or changed to display only.</li>
</ul>
<ol start="4">
<li><strong>Next Steps</strong>    - Kaylee is expected to finish the project by <strong>Tuesday</strong>.    - It is suggested to reach out to her on <strong>Monday</strong> for training availability regarding the specific document handling.</li>
</ol>
<h3 id="key-points-to-address-clarify-the-necessity-of-the-fields-that-are-currently-populated-determine-if-any-fields-can-be-changed-to-display-only-to-simplify-the-process-for-the-customer-ensure-that-the-training-covers-the-unique-aspects-of-handling-low-tenders-and-responses">Key Points to Address - Clarify the necessity of the fields that are currently populated. - Determine if any fields can be changed to display only to simplify the process for the customer. - Ensure that the training covers the unique aspects of handling low tenders and responses.</h3>
<h3 id="thought-provoking-questions-how-can-the-training-be-tailored-to-accommodate-customers-with-non-traditional-setups-what-strategies-can-be-implemented-to-streamline-the-setup-process-for-future-customers">Thought-Provoking Questions - How can the training be tailored to accommodate customers with non-traditional setups? - What strategies can be implemented to streamline the setup process for future customers?</h3>
<h3 id="real-world-application-understanding-the-complexities-of-different-customer-setups-can-enhance-customer-service-and-improve-response-times-in-real-world-scenarios">Real-World Application - Understanding the complexities of different customer setups can enhance customer service and improve response times in real-world scenarios.</h3>
<h3 id="further-research-areas-investigate-best-practices-for-managing-non-traditional-customer-setups-in-similar-industries-explore-software-solutions-that-can-automate-the-handling-of-conditional-fields-in-document-processing">Further Research Areas - Investigate best practices for managing non-traditional customer setups in similar industries. - Explore software solutions that can automate the handling of conditional fields in document processing.</h3>
<h3 id="glossary-of-terms-204-load-tender-document">Glossary of Terms - <strong>204</strong>: Load tender document.</h3>
<ul>
<li><strong>990</strong>: Acknowledgment response document. - <strong>214</strong>: Document detailing load information and requirements.</li>
</ul>
<h3 id="summary-kaylee-s-problem-revolves-around-the-complexities-of-setting-up-a-customer-with-a-unique-document-handling-process-key-concerns-include-the-necessity-of-certain-fields-and-ensuring-that-the-training-provided-is-relevant-to-the-customer-s-specific-needs">Summary Kaylee&#39;s problem revolves around the complexities of setting up a customer with a unique document handling process. Key concerns include the necessity of certain fields and ensuring that the training provided is relevant to the customer&#39;s specific needs.</h3>
<h4 id="generated-content-5">Generated Content</h4>
<h3 id="updated-situation-with-kaylee">Updated Situation with Kaylee</h3>
<ol>
<li><strong>Customer Overview</strong>    - The customer is involved in a <strong>non-traditional setup</strong> focusing on low tenders and responses.    - They receive a <strong>204</strong> document and must reply with a <strong>990</strong> and a <strong>214</strong>.</li>
<li><strong>Document Details</strong>    - <strong>990 Document</strong>:      - Similar to an <strong>855</strong> but contains less information.      - Required fields:        - Reservation action (change, delete, accepted, canceled)        - Reference number        - Carrier reference number</li>
</ol>
<ul>
<li><strong>214 Document</strong>:      - Contains details about the load tender.      - Required fields:        - Reference number        - Standard carrier quote</li>
<li>Either:          - Shipment status code and shipment reason          - Shipment appointment status code and shipment status or appointment        - Must fill in:          - Date          - Time          - Time code        - Location details (city, state, country)</li>
</ul>
<ol start="3">
<li><strong>Issues Identified</strong>    - Some fields in the 214 document may need to be <strong>display only</strong> or removed if not required.    - There is a concern regarding a populated field that may not be necessary.</li>
<li><strong>Next Steps</strong>    - Kaylee is expected to finish the project by <strong>Tuesday</strong>.    - It is suggested to reach out to Kaylee on <strong>Monday</strong> to schedule training.    - Sandy provided Kaylee&#39;s email for communication.</li>
<li><strong>Action Items</strong>    - Confirm with Kaylee about the necessity of certain fields in the 214 document.    - Discuss potential changes to make non-required fields display only.    - Prepare for training session with Kaylee to understand the specific requirements for the customer.</li>
</ol>
<h3 id="summary-the-situation-with-kaylee-involves-coordinating-the-setup-for-a-customer-that-requires-specific-document-responses-990-and-214-there-are-some-uncertainties-regarding-field-requirements-that-need-clarification-before-proceeding-with-training-and-implementation">Summary The situation with Kaylee involves coordinating the setup for a customer that requires specific document responses (990 and 214). There are some uncertainties regarding field requirements that need clarification before proceeding with training and implementation.</h3>
<h4 id="generated-content-6">Generated Content</h4>
<h3 id="kaylee-s-ticket-details">Kaylee&#39;s Ticket Details</h3>
<p><strong>Overview:</strong> - <strong>Customer Type:</strong> Non-traditional setup - <strong>Focus:</strong> Low tenders and responses
<strong>Key Documents:</strong> 1. <strong>204 (Load Tender):</strong>    - This is the document sent to the customer.    - Contains details about the load to be transported.
2. <strong>990 (Response):</strong>    - Similar to an 855 but contains less information.    - Must include:      - Reservation action (e.g., change, delete, accept, cancel)      - Reference number      - Carrier reference number
3. <strong>214 (Acknowledgment):</strong>    - Sent back after accepting the load.    - Must include:      - Shipment status code and reason or shipment appointment status code and appointment.      - Date, time, and time code (all three must be filled in).
<strong>Important Notes:</strong> - <strong>Conditional Fields:</strong>   - Either the shipment status code or the shipment appointment status code must be filled, but not both.   - If date is filled, time and time code must also be filled.</p>
<ul>
<li><strong>Display Only Fields:</strong>   - Some fields may need to be changed to display only if they are not required for customer input.
<strong>Action Items:</strong> - Confirm with Kaylee about the necessity of certain fields. - Consider implementing a response rule to make unnecessary fields static or display only.
<strong>Summary:</strong> This ticket involves a customer who operates with low tenders and requires specific responses to load tenders. The process is time-sensitive, and accurate documentation is crucial for successful communication and operations.</li>
</ul>
<h4 id="generated-content-7">Generated Content</h4>
<p>Lauren provided guidance on handling customer phone calls and ticket documentation. Key actions included:</p>
<ol>
<li><strong>Clarifying Customer Interactions</strong>:    - Emphasized the importance of documenting who you spoke with, their company, and the issues discussed.    - Suggested asking for the customer&#39;s email at the end of the conversation to avoid confusion.</li>
<li><strong>Organizing Customer Issues</strong>:    - Recommended breaking down customer issues into bullet points for clarity.    - Advised writing down specific problems mentioned by customers, such as missing invoices or purchase orders.</li>
<li><strong>Encouraging Communication</strong>:</li>
</ol>
<ul>
<li>Suggested using Slack for quick questions and to inform customers if you need to look into something further.</li>
</ul>
<ol start="4">
<li><strong>Providing Reassurance</strong>:    - Reassured that most customers are not aggressive and that the job becomes easier with practice.</li>
<li><strong>Discussing Technical Issues</strong>:    - Mentioned a specific technical issue with a web interface that was a display-only problem, indicating it did not affect functionality.</li>
<li><strong>Training and Support</strong>:    - Offered support for Michael as he transitioned into handling calls, emphasizing the importance of hands-on experience.</li>
<li><strong>Handling Escalations</strong>:    - Explained the process for dealing with escalated calls, including sending customers to a manager if necessary.</li>
<li><strong>Flexibility in Work Environment</strong>:    - Discussed the flexibility of working from home, including taking breaks and managing personal tasks during work hours.</li>
<li><strong>Customer Training</strong>:    - Mentioned scheduling a one-on-one training session with a customer and discussed the specifics of the customer&#39;s needs regarding low tenders and responses.</li>
<li><strong>Technical Documentation</strong>:     - Worked on mapping rules to ensure that unnecessary fields in customer documentation were either cleared or made display-only to streamline the process for the customer.
These actions reflect Lauren&#39;s focus on effective communication, customer service, and technical support within her role.</li>
</ol>
<h4 id="generated-content-8">Generated Content</h4>
<h3 id="lauren-s-actions-with-kaylee-s-ticket">Lauren&#39;s Actions with Kaylee&#39;s Ticket</h3>
<ol>
<li><strong>Ticket Overview</strong>:    - The ticket involved a <strong>non-traditional setup</strong> for a customer related to <strong>low tenders and responses</strong>.    - The customer receives a <strong>204 (Load Tender)</strong> and must reply with a <strong>990</strong> and a <strong>214</strong>.</li>
<li><strong>Key Actions Taken</strong>:    - <strong>Clarified Requirements</strong>:      - Noted that the <strong>990</strong> is similar to an <strong>855</strong> but contains less information, focusing on reservation actions (change, delete, accepted, canceled).      - Identified that the <strong>214</strong> is necessary for shipment status after accepting the load.</li>
<li><strong>Mapping Rule</strong>:    - Created a <strong>mapping rule</strong> to ensure that certain fields pull over as <strong>blank</strong> to avoid customer confusion.    - Ensured that the fields that are not needed do not require customer input, making the process smoother.</li>
<li><strong>Communication with Kaylee</strong>:    - Planned to reach out to Kaylee to confirm the changes and discuss the training for the customer.    - Discussed making certain fields <strong>display only</strong> to prevent unnecessary customer input.</li>
<li><strong>Testing</strong>:    - Conducted tests to ensure that the mapping rule worked correctly and that the fields populated as intended.</li>
</ol>
<h3 id="summary-lauren-worked-on-kaylee-s-ticket-by-clarifying-the-requirements-for-the-customer-creating-a-mapping-rule-to-streamline-the-process-and-planning-further-communication-with-kaylee-regarding-the-training-and-necessary-adjustments-to-the-ticket">Summary Lauren worked on Kaylee&#39;s ticket by clarifying the requirements for the customer, creating a mapping rule to streamline the process, and planning further communication with Kaylee regarding the training and necessary adjustments to the ticket.</h3>
<h4 id="generated-content-9">Generated Content</h4>
<h3 id="situation-overview-customer-kaylee-s-customer-non-traditional-setup-focus-low-tenders-and-responses-key-documents-204-load-tender-received-by-the-customer-990-acknowledgment-response-similar-to-an-855-but-with-less-information-214-confirmation-of-load-pickup">Situation Overview - <strong>Customer</strong>: Kaylee&#39;s customer (non-traditional setup) - <strong>Focus</strong>: Low tenders and responses - <strong>Key Documents</strong>:   - <strong>204</strong>: Load tender received by the customer   - <strong>990</strong>: Acknowledgment response (similar to an 855 but with less information)   - <strong>214</strong>: Confirmation of load pickup</h3>
<h3 id="key-points-discussed-1-204-load-tender-the-customer-receives-a-204-load-tender-they-must-respond-with-a-990-and-a-214-to-secure-the-load">Key Points Discussed 1. <strong>204 Load Tender</strong>:    - The customer receives a 204 load tender.    - They must respond with a 990 and a 214 to secure the load.</h3>
<ol start="2">
<li><strong>Response Requirements</strong>:    - The 990 must indicate reservation actions (e.g., change, delete, accept, cancel) and include a reference number.    - The 214 must include specific details about the shipment.</li>
<li><strong>Conditional Fields</strong>:</li>
</ol>
<ul>
<li>Certain fields in the documents are conditional:      - If a shipment status code is used, a corresponding shipment reason must also be provided.      - Date, time, and time code must all be filled in together.</li>
</ul>
<ol start="4">
<li><strong>Display Only Fields</strong>:    - Some fields were identified as potentially unnecessary for the customer.    - It was suggested to make these fields &#34;display only&#34; to avoid confusion during the setup phase.</li>
</ol>
<h3 id="resolution-steps-mapping-rules-a-mapping-rule-was-created-to-ensure-that-unnecessary-fields-would-pull-over-as-blank-simplifying-the-process-for-the-customer-this-rule-was-tested-and-confirmed-to-work-alleviating-concerns-about-customers-needing-to-fill-in-fields-that-were-not-required">Resolution Steps - <strong>Mapping Rules</strong>:   - A mapping rule was created to ensure that unnecessary fields would pull over as blank, simplifying the process for the customer.   - This rule was tested and confirmed to work, alleviating concerns about customers needing to fill in fields that were not required.</h3>
<ul>
<li><strong>Communication with Kaylee</strong>:   - Sandy planned to communicate with Kaylee to confirm the changes and ensure that the customer would not have to deal with unnecessary fields.   - It was emphasized that the fields are conditional, and if filled, certain other fields must also be completed.</li>
</ul>
<h3 id="summary-of-resolution-the-situation-was-resolved-by-creating-a-mapping-rule-that-ensured-unnecessary-fields-were-not-presented-to-the-customer-thus-streamlining-their-experience-communication-with-kaylee-was-established-to-confirm-the-implementation-of-these-changes-and-to-clarify-any-remaining-questions-about-the-setup-process">Summary of Resolution - The situation was resolved by creating a mapping rule that ensured unnecessary fields were not presented to the customer, thus streamlining their experience. Communication with Kaylee was established to confirm the implementation of these changes and to clarify any remaining questions about the setup process.</h3>
<h3 id="key-takeaways-importance-of-clarity-ensuring-that-customers-only-see-relevant-fields-can-significantly-enhance-their-experience">Key Takeaways - <strong>Importance of Clarity</strong>: Ensuring that customers only see relevant fields can significantly enhance their experience.</h3>
<ul>
<li><strong>Conditional Logic</strong>: Understanding the conditional nature of fields in documents is crucial for effective communication and processing. - <strong>Proactive Communication</strong>: Keeping open lines of communication with team members and customers is essential for resolving issues efficiently.</li>
</ul>
<h4 id="transcript">Transcript</h4>
<p>00:00 Michael H.: Hello. Hello. Hello. Hello. 00:32 Lauren C.: Hey. Can you hear me? 00:33 Michael H.: Hi. Yes. 00:36 Lauren C.: Okay, perfect. Normally it shows me that I&#39;m talking. Oh, there it goes. Oh my goodness. I meant to get back to you a long time ago, but I finally got Rafat to sit still for a minute. 00:49 Lauren C.: So… All right. 00:49 Michael H.: Oh, no. 00:54 Lauren C.: Honestly, you may get a phone call on Monday, you may not. 00:59 Michael H.: Oh. 01:01 Lauren C.: It really hasn&#39;t been bad. Like today was probably the busiest And even still, I only talked to three people. Poor people. Yeah. So, and that&#39;s by myself. On Monday, we should at least have Nico on top of that. And Nico&#39;s really good about taking phone calls. So he doesn&#39;t really let anything ring through. 01:08 Michael H.: Oh. Yes. 01:23 Lauren C.: So… I wouldn&#39;t stress. Yeah, honestly. What I gave you, just make sure you clarify who you spoke with because there&#39;s always somebody that&#39;s not on the account that&#39;s calling in. If we go back to their account, if you forget to get their email and we email the wrong person, they get confused. It can be a whole thing. Not necessarily a bad thing. But yeah, just make sure you document who you spoke with. So when you hit create, I always just type their name. 01:54 Lauren C.: Right there at the top. You can do first and last, or just first name If you want to start with, can I get a good email address for you? You can. I usually wait till the end. Otherwise, they immediately think, oh, so you&#39;re not going to help me at all. So yes, just. Who do I have the pleasure of speaking with
is a good one. And then I usually say, you know, how are you doing today? And they&#39;ll tell me and I&#39;m like, okay, well, how can I help you? And yeah, so they&#39;ll probably give you a crapload of information. That&#39;s the nicest way to put it. Usually when they call in, yes, they… may be upset. However, no one&#39;s ever like necessarily escalated yelling or anything. They&#39;re just stressed out. 02:42 Lauren C.: So you&#39;ll get the person that&#39;s talking fast or You know, they&#39;re having this issue and this issue and this issue. Just tell them like here, I will partner with my team to review. Can we go over issue one and then make yourself like a bullet point. 02:49 Michael H.: Yes. 02:56 Lauren C.: And then, okay, what was the other issue? Yeah, so line by line. 02:56 Michael H.: Yes. 03:01 Lauren C.: And then if they mention documents, so like if they&#39;re like, oh, my invoices aren&#39;t working, just make sure to write invoice. Or if they&#39;re like, my purchase order didn&#39;t come through. Then you can say, okay, so missing purchase order, missing invoices or error. 03:17 Lauren C.: Any of that. It&#39;s really not that crazy. I&#39;m trying to think. 03:25 Michael H.: Yes. It&#39;s got to get into like a routine of things. 03:31 Lauren C.: Yes, but those things are the main bullet points. Who you talk to. Where they&#39;re from. Oh, yeah. Sorry, I skipped that one. Where they&#39;re from. So what&#39;s their account number or their company name? With the new way that we log in. Just hit go from your email address where your credentials are. And you can type the company name. If it comes up. Perfect. You can just put the company name in your ticket. If it doesn&#39;t and they&#39;re like a does business by I would ask them for the account number. That&#39;s something that I&#39;m guilty of forgetting to ask for. Especially if it&#39;s somebody I&#39;ve talked to before and I already know their issue, I always forget to notate that but I&#39;m working on it consciously. 04:13 Lauren C.: So in your ticket, who you spoke to. What company they&#39;re with and what their issue is. 04:19 Lauren C.: Other than that, like… Let them talk. If they ask anything and you don&#39;t know, you can easily Slack message us. We can try our best to help. Or just let them know, you know, okay, let me look into this. I will get back to you. And they&#39;re usually really good with that.
04:37 Michael H.: Yes. 04:37 Lauren C.: Um… That&#39;s really it. I mean, it&#39;s not that bad. Yeah. The issue I had with Rafat that I gave her, she did go ahead. She&#39;s sending it to developers. She said yes. So the button in web edi for current day is broken. And it&#39;s broken for everybody. But I mean, the good news is it&#39;s a display only issue. Like it doesn&#39;t impact anything. It just… affects how they&#39;re viewing documents. It&#39;s not preventing anything from being sent or received so It&#39;s not crucial. 05:00 Michael H.: Oh. Yes. Yes, that&#39;s good, though. 05:21 Lauren C.: So. Yeah. 05:22 Michael H.: No, I completely agree like Just got to keep practicing it and especially now that you gave me that outline to ask and everything. Just got to just pretty much practice and get into my routine of things and Because like before, just like um visually just trying to absorb everything was a different learning experience than practicing it and then When practicing it, of course, it&#39;s like much easier to 05:51 Lauren C.: Yes. 05:56 Michael H.: Feel like you&#39;re getting a grasp on things. 06:00 Lauren C.: Yes. So watching it&#39;s one thing, but doing it&#39;s completely different. So it does start to make sense. A lot of stuff is really common sense. 06:05 Michael H.: Yes. 06:10 Lauren C.: So you&#39;ll be fine. I wouldn&#39;t be nervous or stressing. Phone calls are honestly really easy. Most of the people calling in don&#39;t know what they&#39;re doing. It really isn&#39;t anything crazy. We do have a few that are pretty well versed and they have like an advanced setup and all of that. They&#39;re really nice too. Let&#39;s see. Like Sandy had mentioned earlier, drive lock, they&#39;re pretty particular. The one that I was helping Eggs Unlimited, they&#39;re pretty particular. 06:45 Lauren C.: However, if it&#39;s Miss Brandy that calls in. She loves me. She doesn&#39;t even say her name anymore. She just says hi, ask how I&#39;m doing. And I&#39;m like, hi miss brandy i&#39;m doing great. And she&#39;s like, you always know it&#39;s me. I&#39;m like, I know. Like you just, yeah. They&#39;re great. 06:58 Michael H.: Oh. 07:02 Lauren C.: Just make sure. Never mention they don&#39;t like us saying bug. That is one thing I got in trouble for. I was like, oh, you know, it might be a bug,
you know, genuine curiosity. I didn&#39;t think anything of it. They&#39;re like, yeah, don&#39;t tell them that. 07:10 Michael H.: Okay. 07:18 Lauren C.: I&#39;m like, okay, my bad. Yeah, it&#39;s always… If they ask like, oh, is something down? I just tell them, oh, not my knowledge. So like all of those people that called in earlier, I&#39;m just like, oh my goodness. 07:19 Michael H.: Okay. I&#39;ll write that down per se. 07:31 Lauren C.: I&#39;m not aware of any outage. However, I&#39;ll reach out to IT and see if there&#39;s backlog or something clogging things up. 07:41 Lauren C.: So always stay open and vague. But yeah. 07:44 Michael H.: Yes. 07:46 Lauren C.: It&#39;s hard to coach because there&#39;s no telling what you&#39;re going to answer the phone to. But it&#39;s never anybody psychotic. 07:54 Michael H.: Absolutely agree. Yes. That&#39;s why like I&#39;m trying to see from your perspective, I&#39;m like, it&#39;s pretty hard um to try to teach this because it&#39;s like It needs more hands-on experience there&#39;s not there&#39;s so much you could just learn. 08:10 Lauren C.: Mm-hmm. 08:12 Michael H.: Through the books and visual learning like you have to just really get into this space and try to really learn it. 08:21 Lauren C.: Yeah. And same with like the server and everything like you can see it, you see it makes sense. You see where stuff is, but it doesn&#39;t really correlate to anything until you actually go to use it. So when you&#39;re helping a customer find their document, then it&#39;s like, okay, now I understand why that&#39;s there. 08:33 Michael H.: Yeah. Yes. 08:39 Lauren C.: So it&#39;s not bad. It really isn&#39;t. It really is just a lot of different systems that work together. So let me see Miss Sandy&#39;s messaging. 08:59 Lauren C.: She was seeing what I was doing. I told her we&#39;re in a Zoom call. 09:03 Michael H.: All right. 09:04 Lauren C.: Oh, because it doesn&#39;t show on my calendar. I forgot to sync it before I started using the Zoom. Product, but that&#39;s funny. She was like, hey, did you touch base with Michael? I&#39;m like, yes, we&#39;re in a Zoom call. I promise. 09:19 Lauren C.: But yeah. I mean, I was really shy when I started. So like talking
was not my strong suit. Hence why my last job was working in a lab, not talking to anybody and playing with products. So… Yeah. When I got hired on, they wanted to do where it was like a three-way call. When I took calls and I was like, please don&#39;t do that to me. I&#39;d rather fail. I&#39;d rather sink and drown on my phone call and then touch base with you after. I don&#39;t know, just the thought of someone hearing me while I&#39;m trying to figure it out doesn&#39;t help me. That just makes me more anxious. So I don&#39;t know. I don&#39;t know how they&#39;ll do it with you. 09:59 Michael H.: Yes. 10:04 Lauren C.: Because I had Tyler. Yeah, so Tyler was like, oh, let&#39;s do conference. I&#39;m like, please don&#39;t do that. 10:07 Michael H.: Oh, yes. 10:11 Lauren C.: So I&#39;m not sure what Ms. Sandy will want. But yeah, I mean, just try it out. 10:19 Lauren C.: Honestly, we don&#39;t have a lot going on right now. It&#39;s not busy. The few things we will get through the phone might be critical to the company on their side. So they may be like, okay, I need this done now. Which totally okay. They&#39;re never crazy. It&#39;s just, hi, how are you doing? How can I help you? What&#39;s the issue? Blah, blah, blah. And then make your ticket. I didn&#39;t finish getting everything done. Customers like talk, would you call them back? 10:42 Michael H.: Yes. 10:53 Lauren C.: Okay, I guess she was doing some of the voicemail callbacks and people are just talking her ear off. Or Sandy. 11:00 Michael H.: Yeah. Yeah. Well, she had a lot of voicemails. 11:07 Lauren C.: Okay, I&#39;m telling you, I was just doing a wrap-up chat. Just do me. Okay. Because like, Michael could be done for the day whenever you guys want to stop. I was like, oh, cool. We&#39;re just doing a chat and giving them pointers and then that&#39;s it. 11:35 Lauren C.: So you may get to run for the hills. 11:38 Michael H.: Are you done for today? 11:40 Lauren C.: I&#39;m still here for another hour. 11:44 Michael H.: Oh, yes. 11:47 Lauren C.: So I&#39;ll be here. Are you, oh, because you had worked eight to five your time zone this week Wow, so you&#39;re done for the day.
11:51 Michael H.: Yes, they should. A yes. Oh, no, no, no. I was nine to five. For me, it was nine to five. Usually she let me go. I guess early because i guess i don&#39;t really eat lunch I guess. 12:15 Lauren C.: Mm-hmm. 12:15 Michael H.: Yeah, but I&#39;m lucky that she always let me go. Yes. 12:20 Lauren C.: I told her, I was like, honestly, because she asked me today, she goes, do you want lunch at one or two? I told her, I was like, it doesn&#39;t really matter. I just do chores. Like I&#39;ll go wash dishes or vacuum or something 12:33 Michael H.: Yeah, that&#39;s the best part about at home. Like if I get hungry or need to use the bathroom or get a drink. I could just go get it. Yes. 12:40 Lauren C.: Yeah, just do it. Exactly. And my lunch break is normally not until three, which I don&#39;t argue that just because that&#39;s the time Between three and four, my daughter gets out of school anyways. 12:52 Michael H.: Oh, yes. 12:54 Lauren C.: So exactly. So if the bus breaks down or for any reason she has to stay after school, I can just go to the school. During my lunch. Yeah, mine is really just with that flexibility for my daughter. That&#39;s all. 13:03 Michael H.: Yes. 13:09 Lauren C.: But nine times out of 10, I&#39;m taking a nap or doing some chores like I&#39;m never really eating because like you said, we can get up and leave. 13:14 Michael H.: Yes. 13:17 Lauren C.: And grab a snack. You will see in our group chat we&#39;ll say like be right back or be like something and some of us will take like breaks, you know, one in the morning and one in the afternoon I think we&#39;re allowed to take a 15 minute break once at the beginning and once towards the end of the shift. 13:19 Michael H.: Yes. 13:36 Lauren C.: They&#39;re not really sticklers on it. If you just say be right back, just come back in like 15 and you&#39;re fine. So yeah, if you need to step away and you&#39;re like, yeah, no, I&#39;m not going to be back at the computer, just say be right back. And step away. That way, you know, if your phone&#39;s ringing, they&#39;re not like, oh my gosh, is it broken? Is it not working? 13:48 Michael H.: Yes. 13:56 Lauren C.: Or if we can&#39;t reach you, we know, hey, he stepped away for a
second. Or you can even say that, stepping away. But yeah, you&#39;re not tied down to the desk. So don&#39;t stress about that. 13:59 Michael H.: Yes. 14:06 Lauren C.: But yeah, it&#39;s nice. 14:09 Michael H.: Yes, it&#39;s really nice here. I&#39;m getting very situated and it&#39;s like go like a second home already. 14:16 Lauren C.: Good. Don&#39;t get stressed out. Don&#39;t get overwhelmed. The job can be, and I will not lie, I&#39;m not going to sit here and paint you some rainbows. It does sometimes get a little crazy. 14:28 Michael H.: Yes. 14:29 Lauren C.: Last year wasn&#39;t too bad, thankfully, because I had to leave My last day here was December 12th. It was supposed to be the week after, but medically I had to leave. And normally between, you know, November 1st and the end of December it is crazy. Last year was not anything. So hoping this year follows the same outline and doesn&#39;t get crazy. But yeah, I think like September, October. Those are kind of busy months for us. It used to be August, but not really anymore. So it&#39;s just… picked up phone calls and all of those people that are coming off of hibernation to do the holiday seasons. 15:13 Michael H.: Yes. Yes. 15:13 Lauren C.: But yeah, nothing crazy. Literally nothing crazy at all. And if you get an angry person, like I told you, if they get escalated and you can&#39;t reach Sandy, you are also allowed to send them to her voicemail. 15:29 Lauren C.: If you feel like they&#39;re disrespecting you or um They shouldn&#39;t. However, if someone does get that way with you and they&#39;re just not listening to you, don&#39;t want to hear you. Put them on hold, reach out to any one of us or Ms. Sandy. 15:43 Lauren C.: If none of us are able to answer, we&#39;re all on a phone call, send it to her voicemail. Even let them know like, hey, I don&#39;t feel comfortable continuing this conversation. I&#39;m going to send you to my manager. So that is how escalations work. I don&#39;t know if she mentioned that yet. 15:54 Michael H.: It&#39;s… 15:58 Lauren C.: Oh. That&#39;s really it. I don&#39;t know. 16:00 Michael H.: I don&#39;t know. Yes, I wrote that down in case. Hopefully that
doesn&#39;t happen anytime soon. Or anything, or I ever have to. Yes. 16:08 Lauren C.: It shouldn&#39;t. Yeah, no. Like I told you, when I came in was a pretty bad time for everybody. So it was just insane. Systems were down, systems were being moved, things were broken and people had just left. And they were people that had been here for so long. So the customers were asking for these people and very angry that these people were not available anymore. So for me, I came into already boiling water. It was crazy. But this go around. We haven&#39;t really changed anything. The servers, like you saw, they go down, but they came back up. So everything&#39;s fine. Nothing got broken for real today. 16:43 Michael H.: Yes. 16:48 Lauren C.: But even still, with everything down for a complete server, we only had five phone calls. And maybe three voicemails so It&#39;s not bad. 16:55 Michael H.: Yes. 16:59 Lauren C.: Oh, goodness. She just said, I need you to schedule a one-on-one training with a customer next week. 17:09 Lauren C.: I don&#39;t want to do that. This is what happens. When I made a good impression on the training team. So now they want me to do these things. I said, oh, fun. Okay. 17:26 Michael H.: Is it like a new… Client. 17:29 Lauren C.: I have no idea. I&#39;m asking her, I&#39;m like, do you want to join our Zoom call? 17:33 Michael H.: Oh. 17:41 Lauren C.: I thought I had set a timer on here, but it says 40 minutes, so it&#39;s fine. 17:46 Michael H.: Yes. 17:46 Lauren C.: I was like, I don&#39;t know if this if he wants to sit in a chat with me the whole end of his shift. So I tried to make it 15 minutes, but it still says 40. Or… Host. No. Meeting information. Okay, it does say 415. Oops. Well, here&#39;s to hoping that I don&#39;t think the call ends if you run over time. We&#39;ll see. We&#39;ll find out in literally a minute. 18:19 Michael H.: Yes. Usually there is a timer, but I don&#39;t think there is no timer at this time. 18:31 Lauren C.: Right. I see up in the top, I show the 40 minute, but that&#39;s all I
see. I know in like the… the meeting information, it does show 415 there but I don&#39;t know. I don&#39;t think it actually controls anything. Oh, I can hit edit. Okay. Just in case. 18:54 Michael H.: I don&#39;t want to say anything. 18:56 Lauren C.: Sandy. Hello. 19:00 Sandy K.: What was that hello 19:02 Michael H.: I would say no. 19:07 Sandy K.: Okay. What you were saying before I came on? 19:13 Lauren C.: Hi. We just said, hi, Sandy. No, you&#39;re good. 19:14 Sandy K.: Oh, okay. Sorry. 19:21 Lauren C.: Alrighty, so a one-on-one training. Ooh. 19:24 Sandy K.: So I don&#39;t know how you say that name, Laos. I just had a call. Kaylee reached out to me. I guess the customer, it&#39;s been a very long setup. And they&#39;re not a traditional customer like orders and acknowledgements. It&#39;s low tenders and responses. And… So it won&#39;t be, you know, it&#39;s not here. Let me share my screens. I will respond to him. Okay, so this is the customer los most transfer. They receive a 204. And they have to reply back with a 990 and a 214. 20:13 Sandy K.: So the 990 is kind of like an 855. But less information. All they have to do is is say what their reservation action is, change, delete, accepted, canceled. 20:27 Sandy K.: Give it a reference number. In the carrier reference number. That&#39;s it. So 990 super simple. No, that&#39;s easy. The other document they have to send is 214. 20:33 Lauren C.: Oh, that&#39;s not bad. 20:44 Sandy K.: Okay, so let me go back to the true form, create a new 24 team. So this is the load tender. What&#39;s going to be on the load. Oh. And they have to fill in, obviously give it a reference number They have to fill in the standard carrier quote. So almost everything here populates. This is the only section they have to fill in. Now, Kaylee didn&#39;t make these blue. Because you either have to have A shipment status code and shipment reason. Or shipment appointment status code and shipment status or appointment. So it&#39;s either these two or these two. One or the other. 21:32 Lauren C.: Okay, so it&#39;s like it&#39;s either active now or scheduled basically.
21:36 Sandy K.: Right. But it&#39;s conditional. So if you use this, you need that. 21:37 Lauren C.: Okay. 21:41 Sandy K.: And if you use this, you need that. They can&#39;t send both though. It&#39;s one or the other. So she didn&#39;t want to make one blue. 21:47 Lauren C.: Okay, hold on. Notate that. 21:50 Sandy K.: And then these three. Date, time, time code. Have to have all three. I don&#39;t know why though she didn&#39;t make Bluebell So if you fill in date, you have to fill in time and you have to fill in time code. 22:06 Lauren C.: Then we should just go ahead and switch those three to blue. 22:06 Michael H.: Yes. 22:08 Sandy K.: I know those those should just be in blue. Because she said. 22:11 Lauren C.: Okay. 22:15 Sandy K.: She goes, just follow the example of the ones that she sent and were accepted. So the one she sent and were accepted is, you know, she filled in two of these, all three of these And the location, city, state, and country. Your city name, state, and country. Everything below that, not needed None of this has to be filled in. These three, these three, and one or two of these four. That&#39;s all they need to fill in. And then these two blue fields here. You&#39;ve got to give it a reference number and this. This is a little concerning. That to me was concerning to me. 22:55 Lauren C.: Those guys. Yeah, that&#39;s what I was about to ask since there&#39;s a number there. 23:01 Sandy K.: Because she said, let&#39;s see what They must be display only because I see there&#39;s nothing here. Well, no, they&#39;re not even display only. I see. See, that&#39;s a problem. 23:13 Sandy K.: I&#39;m going to have to ask her about those. Because… do they have to remove those fields? 23:20 Lauren C.: If they do, I can put a mapping rule to make them blank. If they don&#39;t need it there. If they need to remove it, but if they need it there, do we need to have those fields? 23:27 Sandy K.: Yeah, so all the ones that she has don&#39;t have them filled in. The one that i did 23:34 Lauren C.: Interesting. Populated. What is that number from the number
from Two. 23:45 Sandy K.: It&#39;s got to be from the 204. It&#39;s the volume number. 23:48 Lauren C.: Yes. Sorry, I&#39;m not familiar with this one. 23:53 Sandy K.: Okay, so let me just ask Kaylee that so Let me go to one of mine here. I&#39;m going to ask her because that I didn&#39;t pay attention to until just now. Because if they don&#39;t need to be filled in. Let me ask for this. Yeah, and Roberts, it&#39;s just because the regular training class would mean nothing to them. 24:31 Lauren C.: Mm-hmm. Well, it gives me practice with a different type of situation. I actually have not played with these. 24:44 Sandy K.: I know. She&#39;s like, who on your team is really knowledgeable with the 214 and 990? I said, yeah, probably not anybody really because we don&#39;t have many clients like that. 24:50 Lauren C.: Nobody. And most of them already know what they&#39;re doing. 24:59 Sandy K.: Yeah, because if this isn&#39;t needed, I would rather change these to display only. If they&#39;re not needed. 25:06 Lauren C.: Okay. If you scroll down again real quick. 25:25 Sandy K.: Mm-hmm. 25:26 Lauren C.: So shipment appointment status code. I&#39;m literally giving myself notes over here just in case. Women and… 25:38 Sandy K.: Kaylee&#39;s going to be finished with the project on Tuesday. So if you want to reach out to her on like Monday to see when she&#39;s available for training. 25:42 Lauren C.: Okay. I can do that. And you already gave me, yes, you gave me the email. 26:02 Sandy K.: I gave you her email address. 26:06 Lauren C.: Okay. And then you said everything else afterwards, location, equipment. Anything below? 26:16 Sandy K.: So location. On the ones that Kaylee sent. Had that filled in. See this? See, she did the date, the time and the time code, the city, the state, and the country, but everything else was not filled in. 26:25 Lauren C.: Okay. 26:38 Sandy K.: I would think since SAC filled in, we should automatically fill this one in too.
26:46 Lauren C.: Mm-hmm. 26:46 Sandy K.: Because I noticed on the one I did as an example that is blank. But this one filled in. So I&#39;m thinking we should You had to, because I just created it from the 204 let&#39;s see. 26:58 Lauren C.: Was that coming from the 204? Okay, yes, it is. I only asked because that customer with the doc defaults Haley actually did put those in for her. 27:06 Sandy K.: Yes. So we should Uh-huh. This one, this customer? 27:16 Lauren C.: When she did the retesting. No, no, the one where she sent her invoice and it said spring, but she&#39;s in Cali. 27:23 Sandy K.: Oh, okay. So either we can do a, no, we can&#39;t do a document form, but we can write a response rule then to write this to the other field to them. 27:26 Lauren C.: So I was like, oh, no. 27:34 Sandy K.: Okay, we could do that then. Then they don&#39;t have to fill that in. 27:35 Lauren C.: Okay. 27:39 Sandy K.: And let&#39;s see, did she fill in? No, she didn&#39;t fill in that dropdown okay So this isn&#39;t needed. So the only thing she&#39;d have to fill in would be this. If we write the response rule And I asked her about these. 27:50 Lauren C.: Reference number. 27:55 Sandy K.: Because I said on the ones you send, these were blank but when i did this these populated And she would then have to go through and select one of these on every single one, which would not make her happy. I&#39;m thinking. They would not want to have to do that. So if they&#39;re not needed at all, I would rather not so I can send Kaylee this is you know This is your test document. 28:14 Lauren C.: Yeah. 28:24 Sandy K.: One thing. Thank you. 28:40 Michael H.: So can I make sure I&#39;m writing down the right notes? And everything. And respond. Tell you what the ticket is about. 28:56 Sandy K.: Sure. 28:57 Michael H.: Yes, so just ticket information is about information Kelly&#39;s customer. 29:02 Michael H.: It&#39;s like a non-traditional setup. And it was focusing on low tenders and responses. There&#39;s like three new numerical keywords is a they
receive a tool for a which is a 204. 29:22 Sandy K.: It&#39;s the transfer. So it&#39;s the 204 is there. 29:24 Michael H.: Yes. Yes, two, four. And then they must reply with a 990 which is like You said in 855, but contains less information NA2. 29:27 Sandy K.: Shipment status. Right, right. So the 204, they received the 204, which is a low tender So, and the load tender So I don&#39;t know if C.H. Robinson is the same as another company we had but they send out like low tenders and the first response gets it. 29:43 Michael H.: Yes. 29:54 Sandy K.: So if they don&#39;t respond back like in a certain period of time, they lose that load. So they send Right, right. So we had another customer. I don&#39;t know if you remember them, Unlimited Logistics. 30:00 Lauren C.: Oh, it&#39;s like they&#39;re bidding. 30:09 Sandy K.: And they would get the load tenders would go out. And if they didn&#39;t answer within a certain amount of time, somebody else got it. 30:10 Lauren C.: Uh-huh. 30:14 Sandy K.: So, and it was a real quick time. Like if they didn&#39;t have a response out in less than 30 minutes, somebody else got the load. 30:15 Lauren C.: Yep. 30:21 Sandy K.: So they send back the 990 and then they have to send back the 204 in order to get that load. Otherwise, they&#39;re going to give it to somebody else. 30:31 Michael H.: Yes. 30:31 Lauren C.: Okay. 30:31 Sandy K.: So C.H. Robinson is like a training partner but Laos transfer. They&#39;re really just a shipping company, right? They&#39;re not getting any orders. All they&#39;re doing is going to transport the loads. 30:44 Lauren C.: Scheduling. Yeah. 30:45 Sandy K.: Exactly. They&#39;re just going to transport the loads. So. 30:50 Lauren C.: Nice. Yeah, my brother was doing that. He was working in transportation for a minute there. So he was actually doing the bidding and scheduling those. 31:01 Sandy K.: Yeah, so here&#39;s the tender. They&#39;re sending it out. This is what they have to be picked up.
31:02 Lauren C.: Okay. 31:05 Sandy K.: You know this is everything they have to be picked up, right? This is the full load. And then so they send it to them to be picked up. They send back the acknowledgement saying, yeah. Whatever what is the 990? You know reservation accepted Or they can&#39;t or they&#39;re going to change it or whatever And then they&#39;ve got a certain period of time to send back that 214 that they&#39;re you know going to pick up the load. So I know timing is a big deal when it comes to this. Timing is real important so Oh, fair. 31:42 Sandy K.: So then this is the shipment status. So after they accept the load, then they&#39;re going to do the 214. So like no catalog, no A56, no A55 no I mean, smart folder yes if they want a smart folder But it&#39;s really a 990. And a 214. That&#39;s it. So for her to sit through the whole training class Okay, so Kaylee cleared them out. Okay, so they&#39;re not needed I don&#39;t know how to stop that. 32:18 Lauren C.: I can try a response rule to make it static blank. 32:19 Sandy K.: Oh, you know what? Or make them display only. 32:26 Lauren C.: Yeah. Well, what if they ever end up needing it? That&#39;s what makes me nervous. 32:36 Sandy K.: I&#39;m going to ask Caitlin if we can make them display only. Oh, you mean you&#39;re going to write a response rule to clear them out? 32:44 Lauren C.: I can try one. And then that way, if they ever do need that field, if it does work, they can always fill it in themselves. 32:45 Sandy K.: I don&#39;t know if we can… Yeah, because she said, Kaylee said she cleared them out, but that&#39;s not going to work Because the customers will be like, well, then don&#39;t put them here. If I don&#39;t need them, don&#39;t put them there. 33:05 Lauren C.: Yeah. 33:08 Sandy K.: Okay. All right. I&#39;m just asking Kayla to clear them out. That we don&#39;t or maybe just make it display only. Otherwise, she&#39;s going to have to select this on every single one. No. If… Because since, you know, if they don&#39;t need them And I really don&#39;t really want to wait for Rafat to have development not populate them either. 33:35 Lauren C.: Yeah. 33:39 Sandy K.: So… For now, I think also, I mean, you&#39;re not training her tomorrow or you&#39;re not training her on monday morning so I&#39;m just asking Kaylee,
can&#39;t we just make them display only then? 33:52 Sandy K.: I&#39;m not going to tell this woman to clear them out every single time. It&#39;s kind of stupid. Then her response is going to be, well, why fill them in? 33:59 Lauren C.: Yeah. Like, why is it there? Especially during the setup phase. So yeah, I get it. 34:02 Sandy K.: Right. So… Yeah, right. Exactly. And I&#39;m thinking we should make these blue. And I think we should make this blue too. 34:20 Lauren C.: Yeah, if it&#39;s something that they&#39;ll need to be filling out and like she said. She scented on hers that got approved. I think we should. 34:27 Sandy K.: Yeah, that&#39;s what she&#39;s like. Just follow my examples. And I&#39;m like. Well, your example has them filled in Here, where&#39;s the 214? That&#39;s what they&#39;re sending back. 34:51 Lauren C.: Yes. 34:55 Sandy K.: They don&#39;t even have… maybe they&#39;re not called REF segments. I&#39;m just assuming they&#39;re an REF segment. Or maybe they&#39;re not GS. 35:04 Lauren C.: Oh, wait. What was it? 35:08 Sandy K.: That&#39;s the GS. Oh, that&#39;s the ST. Okay, here. All right, so this is the b10 That&#39;s the first one. Oh, here it is, the reference. This is that dropdown. So it&#39;s optional. So it&#39;s conditional. If you use that, then you have to use this. So it would reject the way it is right now. 35:32 Lauren C.: Yep. 35:33 Sandy K.: I would just make that display only. Okay, and then let&#39;s see, we&#39;ll get back down to what was at the bottom that shipment status detail time. So the time is also conditional. If you use the date and the time, you have to use this. So they&#39;re not required. It says use. Does this say mandatory Okay, so this is mandatory. But again, like this is optional. So if you use this one, you need that one or If you use this one, you need that one. 36:16 Sandy K.: What does this say? This is, okay, only one, one or three is used if one need two. If six is present, five is required. If seven is present, six is required. They don&#39;t have to use them. But if you use them then you have to use all three. Kaylee used them on her example I&#39;m leaning towards making them required. And then location. Again, this is completely optional. City name state according to them but if they O1 is used, then two or three is required. If two is present, one is
required. If three is present, one is required. I would just… Yeah. So it&#39;s completely optional. She doesn&#39;t even have to fill that in. But… Kaylee did and hers was accepted with it. 37:24 Sandy K.: So I would be leery to not do it and have it be rejected. She&#39;s the only customer with them. Right. Oh, no, Voss and ABC company. Oh. So Voss uses them? And this is just a test customer. I didn&#39;t know Voss was using them. Is it a customer specific then? 38:17 Lauren C.: That is a great question. Okay. 38:25 Sandy K.: No. I still say we make them display only. So I say we just make those display only, then she doesn&#39;t have to clear them out. And just let her know that you know these fields are all conditional. 38:42 Lauren C.: Okay. 38:50 Sandy K.: These are required but conditional you have to use this one or this one. And if you use this one, that one, this one, that one. Same thing with the date and time. They&#39;re all conditional fields. If you fill in this, you have to fill in that. I&#39;d be interesting to see if Voss is actually using anything with CH Robinson. Maybe it&#39;s something I&#39;ll look at Monday morning. You know, and see if they are, if they&#39;re not. 39:19 Sandy K.: Maybe we just make the changes to the down so that 39:28 Lauren C.: I fixed the business instructions. 39:28 Sandy K.: You guys to fill in. What did you fix? 39:35 Lauren C.: At the top, I just made a mapping rule so they pull over blank. They didn&#39;t work, thankfully. Yes. 39:40 Sandy K.: Oh, these. Oh, okay. And it worked? 39:44 Lauren C.: So if you go to draft, yes, if you hit the draft folder, I made a test. 39:47 Sandy K.: Okay. 39:51 Lauren C.: Well, this way Kaylee doesn&#39;t have to worry about it. And the customer doesn&#39;t have to worry about it. 39:53 Sandy K.: Okay, I&#39;m just going to let her 40:01 Lauren C.: I was worried it didn&#39;t work at first, but I had to go through and mess with it a little more and it worked. 40:01 Sandy K.: All right. 40:05 Lauren C.: Although reference has a number instead of test okay Oh, weird.
But yeah. 40:10 Sandy K.: That&#39;s fine. Okay. And then we just need a mapping rule for this one too then. 40:11 Lauren C.: They create a blank. 40:18 Sandy K.: Okay. All right. 40:18 Lauren C.: Yes. We can do
View original transcript at Tactiq.</p>
