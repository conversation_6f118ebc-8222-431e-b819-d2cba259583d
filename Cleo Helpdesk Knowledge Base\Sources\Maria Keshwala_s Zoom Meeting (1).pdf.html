<h3 id="maria-keshwala-s-zoom-meeting"><PERSON>&#39;s Zoom Meeting</h3>
<p>Meeting started: May 20, 2025, 4:33:46 PM Meeting duration: 68 minutes Meeting participants: <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="transcript">Transcript</h4>
<p>00:00 <PERSON>.: Hello! 00:01 Maria K.: So, this customer was a great example for me to train you, because it was a phone call. She requested a few things like document default, which is Tier 1 level stuff. And… and uh… and then she requested, like, a hard-coded mapping rule. Uh, to, uh… to her invoices, okay? And I&#39;ll show you what exactly… She was requesting. Um, okay. Let me share my screen. 00:34 Michael <PERSON>.: Yes. 00:38 Maria K.: Alrighty. So, this is my ticket that I created with FAR. Um, so… you know, she said, oh, the A10s are showing total zeros in the column, and then I open it, and then… you know, something funky was doing with the web portal. I saw it with my own eyes, and I&#39;m like, what the heck? 00:50 <PERSON>.: Yes. 01:05 Maria K.: You know, like, I was showing zero totals when they actually have totals in them, right? What I did was I went to the server just to make sure that raw data we sent out was actually, um, sending it with totals so she can get paid, right? Uh, another thing she requested was… the first request was, I want my… my invoice number. I want to match with the PO number from the trading partner. So, that way, it auto-populates for her, right? Um, only for UniFi, because for Mayor, it&#39;s already set up, and I put a note for me, I need a mapping book, too. She said. 01:40 <PERSON> H.: Yes. 01:49 Maria K.: I want to hardcode or add a default advanced number. This 9 is
the DUNS number code. From the map, and I&#39;m going to walk you there to show you what I did. So, I&#39;m gonna show you how to do open documents default. When the customer asks you. Uh, hey, can you… allow me to auto-save, so it auto-populates a data always for me, okay? 02:17 Michael H.: Yes. 02:18 Maria K.: So, I&#39;m gonna show you that. Uh, give me one second… I&#39;m gonna stop sharing and share again. Um, this one. So, when a customer calls and says, hey. I want my… My A chance to reflect my… we make to address, right? Which is their address to get paid, right? My… this is the company name, the dance number. The address, Sadie, blah blah blah. See, I don&#39;t… I don&#39;t want… every time I create a document, I don&#39;t want it manually to keep this info in all the time, right? Which makes sense. So, I always said to the customer, if the data is always the same. 03:07 Maria K.: Repeatedly, go ahead. And add it as a default. That way, you don&#39;t have to manually put that data in all the time, right? But if it varies, no, that&#39;s not a good idea, right? Um, because then it will populate the same information. So, right here, there is default. 03:21 Michael H.: Yes. 03:27 Maria K.: And the training partner I was working was UniFi. And this is the A10. Right? The customer can do this, but I usually do it because for some reason, when the customer does is not saving it. But if I do, it saves it. Which is so weird. Um, I don&#39;t know if it&#39;s because when they refresh, it doesn&#39;t reflect to me. I just do it just to make sure it&#39;s done, you know? So, here. So, she entered this… And I enter this. I told her, I asked her, what&#39;s your DUNS number? Okay, we&#39;ll put it in, I save it, meaning once she creates an 810 for this specific training partner, it will always populate this information. 04:13 Maria K.: Does that make sense? Okay, um, also, she said, ah, Maria, for the additional information in terms of sale, the description, discount amount. 04:14 Michael H.: Yes. 04:24 Maria K.: It&#39;s always net 45. Put an F45 in. I did put Net 45. Then she said, oh, for the method payments of origin shipping point, it&#39;s always fattest ground. So I pulled Fetter&#39;s ground. Then she said, then in the bottom. What if this invoice treatment summary, I want to to default is always each.
04:47 Maria K.: There&#39;s always this weight N is pump, right? Okay. Before, Michael, it was grayed out like these fields right here. They&#39;re grayed out. 05:00 Maria K.: When they&#39;re grayed out. It means that… in the map, in the dump. The check of, uh. To allow to default is off, and we need to turn that switch on. So that field populates in gear. Does that make sense? So, for instance. Uh, okay, so let me explain. So, let&#39;s just… let&#39;s pretend Uh, this one&#39;s right here were off. 05:19 Michael H.: Yes. 05:31 Maria K.: Right? I know which one those are, because I&#39;ve been doing this for a while. 05:33 Michael H.: Okay. 05:37 Maria K.: If you don&#39;t know which one… no, if you don&#39;t know which one those are. 05:37 Michael H.: Yes. 05:42 Maria K.: I always say in the beginning. Export… NHAN that&#39;s been sent, okay? 05:51 Michael H.: Okay. 05:53 Maria K.: See where this data is entered, which segment? Okay, that way you know which one you&#39;re gonna open. But I assure you. And sometimes they&#39;re playing and simple. I know the codes for UneroMeasure, or way, or… right? So, you&#39;re gonna get it once you get used to it. So I&#39;m gonna share my other screen to show you the dome, okay? Or what we do. So let me stop sharing and reshare again. And… yeah, it&#39;s been more. Okay. Oh, I have to… All right, so this is… this is the global dump. In the global dump, we have to be very careful. 06:36 Maria K.: Prayerful to make changes, because Global means… A lot of customers might use this done as well, okay? Uh, we gotta be careful when we make changes, the time and stuff, because some of the trading partners are huge, like Amazon, Walmart, Target. And we have a lot of customers constantly sending documents to us. If the dumb is open. For editing. And the customer sends data. That… that file might have failed. Okay? Because… the DOM is open for editing. It&#39;s not saved, okay? It&#39;s not… If it&#39;s in edit mode. Is gonna kick out the document that is coming in for translation, if that makes sense. 07:31 Maria K.: So, always ask me, or Sandy. Or, you know, you know… Usually, Sandy and I have a good eye when we can, because even Nico calls, Maria, I
need to make a change in the dome. 07:45 Maria K.: Can I do it? Or I need to make this kind of change. He always checks, you know, because This is, you know, gums are a little… again, this is what… literally controls document flow, right? Um, so, some of the maps are integrated. Okay, I&#39;m gonna… I&#39;m gonna give you this… this good, uh… you know, I&#39;m happy that I&#39;m… that we get to this, because Integrated maps. They will have some red line up here. Something, and you know is integrated, right? Um, those maps are linked with the links map, which is this right here. Those are the customers that are our global shop. Right? There are integrated, and they might use this DOM as their target map. 08:42 Maria K.: Okay? Um, so my source will be the flat file, right? 08:42 Michael H.: Yeah. 08:49 Maria K.: L Record, T record, whatever. And my target will be this map. So if I have it open. You know, could cause issues, right? So, integrated ones are the most tedious one. 08:58 Michael H.: Yes. 09:02 Maria K.: To be honest. Um… Yeah, stay away. So, so if you need, again, even Laura, Sandy. 09:02 Michael H.: Yeah, that what, um, Lauren told us to stay away from, besides QuickBooks. 09:12 Maria K.: You can ask me, okay, you say, Maria, this customer wants this, and the map change. Always ask, okay? 09:20 Michael H.: Yeah. 09:20 Maria K.: Just to be in the same zone, okay? Sometimes, I know we&#39;re not allowed to create uh, clones. But… the project team does it. I clone when. The customer is asking for too many specific customizations. 09:43 Michael H.: Oh, yes. Okay, yes. 09:44 Maria K.: Not everybody&#39;s the same, like I said. Um, some customer might say, hey, I want this hard code. I won this, this hardcore and this and that, and I&#39;m like, you know what? Let me clone this. And assign this map just to you. Right? And do all my customizations. That&#39;s what I do. Just to be on the safe side. Now… If they are integrated. 10:02 Michael H.: Yes.
10:11 Maria K.: And they need mapping changes. When we are ready, completed a project. It requires a work authorization. Because integrated customers have to pay. Any mapping changes, okay? Um, alright. So, unless it&#39;s something like… That&#39;s why I told Lauren. To take… to send me that Globo Shop one that you have, because GloboShop is a little tedious, okay? 10:44 Maria K.: And MailCup is tidious. So, like, signed that one, we&#39;re good at it together. 10:49 Michael H.: Yes. Yeah, Nico and Laurent Lauren told me Stay away from tractor Supply, John Deere, and Global Shop. 10:52 Maria K.: Okay. Well, jump, dear! I mean, they send 830s, you know, those are complex that… and… and I… And Caterpillar, but you can take it because you don&#39;t want to stay away from anything. 11:14 Michael H.: Be a caterpillar, yes. Yes. 11:16 Maria K.: Yeah, you want to learn, right? But… For now, you want to take baby steps. 11:23 Michael H.: Yes, yes. I didn&#39;t know that I took the hardest, one of the hardest people. 11:28 Maria K.: Yeah, you don&#39;t want to run before you walk. I… okay, but I eventually… we want you to learn everything. 11:33 Michael H.: Yes. 11:39 Maria K.: Okay? All right. We just don&#39;t want to overwhelm you, uh, and we want you to learn the baby steps. 11:40 Michael H.: Yes. Yes. 11:49 Maria K.: Okay, so, alright, so for instance, like we mentioned, like, she says, Maria. I want the invoice treatment summary. I know those are in the bottom. It&#39;s the ISS segment, which is this one right here. 12:04 Maria K.: Um, this is one year. So, if I… I want to… it was grayed out before. It was grayed out. So what I do, I come here Go to the segment, right here, right? Go click, and I click allow default. Oh, can you hear me now? 12:20 Michael H.: Well, I can&#39;t hear you, Maria. Okay, yes, I hear you now, yes. 12:25 Maria K.: Oh, why it goes in both. It&#39;s Zoom. Alright, so I click on the one she… you know, she said, oh, Maria, I want a default I&#39;m like, okay, you want to allow default? This switch right here.
12:28 Michael H.: Oh, yeah. 12:42 Maria K.: Will allow… to reflect the field And the default session in the web portal. That way, you can key in the value. We do this because the customer is hard-coding and waving their own total. That will not affect anybody else, right? Now, if the customer have their own mapping, and there is certain things where hard coding or whatever. I can come here and hard code that, okay? Um, but in this case, this is a global dump, allowing a default would not affect anybody. 13:22 Maria K.: But it will affect Everybody in the terms of that this will be available for anybody who would like to enter the data to save it for default, but it&#39;s not going to break anything, if that makes sense, right? So, once I&#39;m done, I save it. It takes a little bit, right? I just opened for all the units of measure, so you can see visually how it works the whole process, okay? Um, it takes about, you know. I did this while she was on the phone, just to… you know, have her take a look at it. But mapping rules, I used to do a web of customers on the phone, I used to do mapping and stuff, not anymore. 14:00 Michael H.: Yes. 14:05 Maria K.: Take a tick, I get in line. Dude, I have other customers that were first, right? They send their ticket back. Some customers are clever. 14:16 Maria K.: And they, like, oh, you know, let me quote, right? Because I&#39;ll get somebody right away. No. Sometimes, if… If it&#39;s, um… holding production, meaning… They have an emergency, they need to send ASNs, POs are not… I&#39;m, like, right on the phone taking care of that. Right? Uh… But if it&#39;s gonna take forever, you&#39;re gonna know as you go, right? If it&#39;s something that I need to address with Sandy, we need to get to IT, right? 14:51 Maria K.: I tell the customer, I&#39;m gonna create a ticket for you, I&#39;m gonna set it up as high. 14:57 Maria K.: And as soon as I&#39;m done with you on the call, I&#39;m gonna get with my manager, and we&#39;re gonna get this done. I&#39;m gonna call IT and try to figure it out what&#39;s going on. Right? And get back to you. I do that. You know when something you can do right and then, something that you cannot do right and then. Okay? Because I would take… All your time. You have to use your time very wisely here. 15:16 Michael H.: Yes.
15:20 Maria K.: So, one thing I do a lot, I multitask a lot, and you&#39;re gonna become a pro at multitasking here. You can… you can be working on a mapping problem, me, I could be working on Delta. 15:33 Maria K.: And then a phone call, uh, like today, you were with Lauren, Nico was at lunch, I was in the middle working with Global Shop. 15:40 Maria K.: And I have to answer the phone for two customers, and this one took forever, because I was doing all this, right? So I had to stop what I was doing. With Milka, and focus on this. So you&#39;re gonna… you&#39;re gonna manage that. All right, so I&#39;m going to stop sharing and show you how it reflects. And, uh, WebVDI. So, first of all, once you make an update in the DOM, You always need to refresh, because… It might not reflect you know, life, like, while you&#39;re having it open like that. You have to refresh. 16:04 Michael H.: Okay. 16:22 Maria K.: Um, what was I gonna say? I was gonna say something else about the job. Oh! Any dumb. Changes you make. Let&#39;s say the customer has an invoice error, ASN error. 16:37 Maria K.: And we need to fix the sec… Add a sec. You know, update the DOM. Uh, they need to recreate a brand new document. They cannot use the ones they sent out. Can edit them, because edit them, which is restage. It&#39;s not gonna reflect the mapping changes, okay? All right. So, let&#39;s go to default. And here… 18… All right, so see? It&#39;s available now. So I can say, uh, I&#39;m, you know, if I&#39;m always cheap 10, I can cheat 10, save it. Always gonna populate 10, but… That can change, right? We&#39;re not gonna set up a unit ship, and that can change anytime. Things like that change. Amounts change. So that, you&#39;re not going to settle for default. You will know, as you go. 17:42 Maria K.: Why it&#39;s not defaulting, you know? Like, addresses can be default, right? 17:46 Michael H.: Yes. 17:46 Maria K.: Things like this, so you&#39;ll know. All right, so that&#39;s… that&#39;s this. So you know the things I had to do. And insert the data, save it, so always gonna populate. My next step request was that we&#39;re gonna do now. Is set up a mapping group, right? So, first, I go to the inbox. And she wants… oh, this is not my… Hold on… Okay, right here. And we want to open it. Okay. So she wants this number…
to… once she create an ATAN, to populate on the… as an invoice number. 18:38 Maria K.: Does that make sense? 18:41 Michael H.: So… Did you say the A-T-A-N? 18:46 Maria K.: No. A10, the invoice. She wants. She wants. 18:48 Michael H.: Okay. Oh, okay. Atam. 18:54 Maria K.: The purchase order number to be their invoice number for her A10. Does that make sense? So, once she creates a response. 19:01 Michael H.: Okay. Yes. Yes. 19:07 Maria K.: That will automatically populate on that. Okay? From the doctor. 19:13 Michael H.: Yes. 19:16 Maria K.: If she comes in here. To create a new document manually, meaning not from the PO, and comes in here, and creates an 810, That&#39;s not going to out-of-populate. Because it doesn&#39;t have a source to reference from. Does that make sense? Alright, I&#39;m gonna explain it to you again. When you create a document from a A50, a purchase order. I receive a purchase order, now I need to bill them, right? I&#39;m gonna create an invoice. Right? 19:58 Michael H.: Yes. 19:59 Maria K.: Some other fields from this purchase order. I&#39;m gonna auto-populate on this 8 times, because I&#39;m creating a document based on this source, which is my A50, so the data values Some of them will come over. Okay? And I can set up rules, I can even have a lot of things coming over to my A10, that way I save a manual entry. Does that make sense? 20:25 Michael H.: Yes. 20:27 Maria K.: Now, the customer has two choices to create documents. They can&#39;t create it from the A50, Or they can create it from the… from the file, new. But it&#39;s manual, it&#39;s bland. There is nothing there. They have to complete everything. The only thing that will populate is the data that we set up as default on that document type for that specific trading partner. 20:51 Michael H.: Okay. 21:02 Maria K.: Okay. How you followed me now. Okay, let me know when you&#39;re confused. 21:02 Michael H.: Yes. Yes. 21:09 Maria K.: And I&#39;ll go back. Because I… go ahead.
21:11 Michael H.: So the two choices to create the documents is from the 850, or they could just create it from the file and select no. 21:23 Maria K.: Right. And another thing, you&#39;ll learn as you go. Uh, we&#39;re talking in this scenario is the A50. Even the A75, same thing, right? 21:23 Michael H.: Okay. 21:34 Maria K.: The A75, which is the grocery store one. They can do the same thing. They can also create an invoice. From? The A56. Okay? You see? They can&#39;t. 21:51 Michael H.: Yes. 21:53 Maria K.: Um, I… recommend… always to do it from day 50. That&#39;s my choice. And the reason why is… the data is more accurate. You never know, the customer is doing this one, right? Some data might be required from the A50 rather than the A56. So I would like to do it from here, you know? It is what it is, whatever preference. Some customers like to do it from the A56, to be honest. 22:26 Maria K.: You know, uh, you know… Now… 22:28 Michael H.: Okay. So $8.50 is always usually more accurate than 856? 22:36 Maria K.: Yeah, because an 856 is… is the advanced shipment notice. So the APC, as you know. 22:40 Michael H.: Yes. 22:44 Maria K.: It&#39;s a document that tells the purchaser, hey. This is the items you&#39;re gonna get when the truck arrives, right? So, pretty much… Once the customer receives an A50, They respond with this… Right? And… What this is, is actually showing how they&#39;re packaging the shipment level by carton, how many pounds. 23:16 Maria K.: Is it by palette, right? Um, who&#39;s the carrier, right? I think this is FedEx. Yeah, Freddice Grant. Um… Uh, the routing number. They get this information, the carrier details, they get it from the carrier. Okay? They need to get this from the carrier. Um, so… You know, the ship from… ship to, ship from, is there a warehouse, right? In shape to the warehouse that&#39;s going from, uh, to UniFi, right? Um, and this is a purchase order detail, the day, whatever. 23:48 Michael H.: Yes. 23:52 Maria K.: Uh, this is generated automatically, the UCC, um, that comes in the labels, okay? And… and this is where the customer puts the labels from,
before they ship the boxes, you know, the ones you get at home when you order something, right? You get a label. 23:59 Michael H.: Yes. 24:09 Maria K.: This is the same thing. So, um, um… This goes electronically, let&#39;s say, if it&#39;s, you know, it&#39;s going to UniFi. Unified gets it in the warehouse. Uh, every… you know, the ASNs, and looks like, oh. We&#39;re getting 90… uh, quantities of this order, what they, you know, they scan it, and they&#39;re like, okay, once the truck arrives. They need to have this document ahead. Otherwise, they&#39;ll be fine. 24:39 Michael H.: Yes. 24:42 Maria K.: Okay? That&#39;s why some of them find the customer if they don&#39;t receive this on time, and the truck arrives without this information. Okay, so that way you have an idea. Okay? Um… Because this helps them a lot, like, they have the order number, they have everything, and they know what&#39;s coming, they have the routing number of the shipment, you know? Um, it helps them less. And here, as you know, this is where they, they print the labels, right? 25:08 Michael H.: Yes. 25:14 Maria K.: Um, actually, I guess they don&#39;t bring labels for this one. Let me see why they print. Maybe they printed in-house. Some customers, they print this in-house. Okay, haven&#39;t, yeah. Yeah, they&#39;re printing this in-house. Okay. Yeah, they have their own labels, it looks like. When you don&#39;t see that, because we have to set up the labels for them. That means they&#39;re printing them in-house, their labels. Okay? Okay. 25:42 Maria K.: So, let&#39;s settle the mapping pool. So, how do I know, okay, we, uh… Let&#39;s go to the… 25:47 Michael H.: So, um, just clarifying, um, Maria, so if no ma… when we don&#39;t see labels from them. Um, they… it&#39;s most likely all the time that it&#39;s gonna be in-house. Printing that they are doing. Okay. Yes. 26:01 Maria K.: Yeah, it might be that they&#39;re printing and elsewhere. Yep, and… or if they&#39;re brand new customer, maybe, you know, they might… some customer, don&#39;t get me wrong. So a customer might call you and say, hey, I want… I want labels. I heard from Sandy, because, you know, after we migrated with Clio, I don&#39;t know if we were going to start charging for that. 26:21 Maria K.: If they pay for that upfront with sales, so we need to check with
sales if they actually pay for it. Before we… we create a development ticket to create the labels, okay? 26:31 Michael H.: Yes. 26:31 Maria K.: Uh, but that is something else that… when it gets to where we&#39;ll walk you through and help and, you know, help you do that. Um, okay. So, right now. We have a purchase order, but I… in order for me to create a rule. I need this segment. You know, I need the raw data, so I&#39;m gonna go back to inbox. And… I&#39;m going to… X4… A document. And my purchase order number is… This one, I believe. Yep, you see? It&#39;s the BG… BEG03 segment. 27:27 Maria K.: Okay? So… Right? So what I&#39;m gonna do… Um, adding notes as I go, right? Eo equals… B… G… BG03. Segment… All right. And now… I want to know my 810. 28:20 Maria K.: My A10… oh, where did I go? I&#39;m feeling exhausted right now. I had a good lunch, though. Uh, let me see… This is a 10… okay. And it will be my BG02, okay? Bgi. B-i-g-02 site. B, I, P,02. Right? I&#39;m going to share my other screen in the DOM. 29:14 Michael H.: Okay. 29:14 Maria K.: Uh, to show you how we set up mapping groups, okay? Sharing… All right, are you seeing my… the DOM, right? Okay, cool. 29:34 Michael H.: Yes. 29:37 Maria K.: All right, so we go to mapping rules. Give me one second… 30:08 Maria K.: Maybe I&#39;ve got something that I have not yet. 30:46 Maria K.: Okay, okay. All right, so… We&#39;re gonna find the customer. Actually, never mind. Let&#39;s take on the air. We set up the customer, right? The customer is… Uh, ventures… venture outdoor? Fentura. Okay? Trading partner is Yoda 5 Natural. Document type is… my A50. 31:20 Michael H.: Yes. 31:20 Maria K.: And the target segment is… Uh, hold on a minute… Oh, no, my document type is ATAM. Sorry about that. My target segment is… My A10? I&#39;m sorry, the segment, BG… B, I, G, Oh my god, what am I doing that? So, B-I-G… Then asterisk… Two… I don&#39;t know if it would take the 2 or 1, but… I don&#39;t know, I think of two. Let me try GoTo and see if that works. And then my source is the A50.
32:26 Maria K.: And then my source is… EG… So PG03, let me double check. We, uh, BEGO. Okay, got it. 32:57 Michael H.: Each year. 32:59 Maria K.: Beg, B-E-G. 33:20 Maria K.: And then, I guess… that&#39;s them… Hopefully, it takes the, um… Oh, yeah, every month&#39;s the zero. But let me double check. Now, let me go back to… It might, you know, might be wrong or right? Like, let&#39;s just double-check sometimes. Let me just take a look. Let me stop sharing and share my other screen. All right. I&#39;m gonna test it out by going to Inbox. Uh, I&#39;m gonna refresh my… my thing first. 34:32 Maria K.: And I&#39;m gonna click on one of the unify… 850s? It doesn&#39;t matter if they send a document or not, this is just testing. When I create a respawn. I will know immediately if it worked. 34:56 Michael H.: Okay. 35:00 Maria K.: It worked. Okay. You see how it populated the PO? I know it worked, so… I&#39;m gonna do this. I&#39;m gonna take a screenshot of that. 35:01 Michael H.: Oh, wow. Yes. 35:12 Maria K.: Because I have any proof that I did, and take it that way, you know, something comes back and be like, hey! 35:18 Michael H.: Amazing. That&#39;s great. 35:19 Maria K.: Am I… I… I did it. Oh, I did it. All right, let me… Okay… Set up, or… 18… 2 left. You know, invoice… Number, uh, C3O… 36:06 Maria K.: Okay, so what I&#39;ll do, I&#39;m gonna… the task saved, and then I&#39;ll go back to… once it&#39;s safe. I&#39;ll go to Draft. And I&#39;ll delete my task. I&#39;m gonna reshare my screen, um, to show you my ticket. 36:28 Michael H.: Yes. How long does it… how long does… did this ticket took you? 36:35 Maria K.: So, legally… This ticket took me? Oof, so… Okay, let me see. I answered the call. 36:49 Maria K.: Uh… She calls… Uh… This is… oh, so 40 minutes was the call. 37:03 Michael H.: Oh, wow. And you went all… walk through this with her. 37:06 Maria K.: 20? And look unto now. 37:10 Michael H.: Yes.
37:12 Maria K.: Yep. So I&#39;m gonna take us, might take you very long. I have to, because I have taken you, oh my god. When I create dev tickets, you know, mapping, troubleshooting, am I right here for a couple of hours. Um… All right, so… So, pretty much, you see in my ticket, right? I literally added an internal note for me, you know, I put the VG7, and what I did, mapping rule. 37:38 Maria K.: You know, I might be rules set up, blah blah blah, save it. I reply to the customer. So, hello, what was her name, Michelle? One scope. Um, so… 38:59 Maria K.: I haven&#39;t talked this to the customer. Because you know what they do? I&#39;m not gonna lie, there&#39;s customer size Maria there, or I want Maria. Um, they think I&#39;m their designated person, and it&#39;s not like that. I do have some designated customers, right? But, uh, anybody can work on anything, right? They just… they&#39;re just like some people, and… And why they do is, they will reopen the same email or the same ticket we had previously. 39:20 Michael H.: Yes. 39:30 Maria K.: And open something else. I don&#39;t like that, and this is why it&#39;s not good for you, or for anybody. One, he already closed a ticket related to an issue, right? And if they have a separate Um, it&#39;s case scenario, which is totally different, and it requires a debt ticket. 39:43 Michael H.: Yes. 39:51 Maria K.: Once you created that ticket, plus adding this ticket and the dev ticket, that might confuse the developers, being like, what the heck is this? Is it this the problem, or that problem? You know what I mean? 40:01 Maria K.: So, I don&#39;t like combining problems in one. Now. In this situation. 40:02 Michael H.: Yes. 40:09 Maria K.: I knew I&#39;m not gonna need a developer. So, that&#39;s why I kept on the phone while she was asking me for… if they&#39;re on the phone with me. And they had different problems, I&#39;ll add them all in one ticket. Okay? But if I already… address their issues. Worked it, and you have something new? You have to create a new ticket. I&#39;m already done with this, you know what I&#39;m saying? Because… That can create problems. 40:39 Michael H.: Yes. 40:40 Maria K.: Alright, so I should say thank you, save, and then… I&#39;ll close this… do we solve, and that&#39;s it. Done. And you see how long it took. Do you have any
tickets open? 40:57 Michael H.: Oh, yes. No, no, I was just assigned… I just decided to assign myself after Lauren, before Lauren went to lunch. 40:57 Maria K.: That you want me to address, or that you need to know, or…? We can go… 41:09 Michael H.: She was going over tickets that should I should take on. So, I always thought, um, when you were messaging me, that&#39;s when I just… got started and everything. 41:09 Maria K.: Mm-hmm. Mm-hmm. Okay, did you have any… ooh, Andrew. Oh. 41:23 Michael H.: Oh, oh, yeah, they told me not to, uh, Lauren was, like, more cry, I would say. This is Mariah&#39;s time. 41:27 Maria K.: No. No. Ranner is either in Sandy or I take Banner. 41:37 Maria K.: They&#39;ll stay away from Andrew. Uh, April 16, we say in 18 filed for brass. Motion AI with invoice data, which is for 120, I… However, the translator file is incomplete. I attached Herman&#39;s A10 before For that day, we showed success for all of the ideals. You know, they&#39;re gonna be… So, let me tell you a little story. You&#39;re not recording anymore, right? Okay, good. So… Banner is a big customer of us. 42:08 Michael H.: Or no. Oh, great. 42:18 Maria K.: They are… they are integrated. Okay? And they send… XML files. 42:26 Michael H.: Okay. 42:27 Maria K.: Um, and, you know, we translate those. Um, they were having… issues… even before I went on vacation. They were having issues with files they send to us. Like, 8, 10, or either A56s, let&#39;s say they sent 279 items, right? Uh, they sent 270 line items. 43:02 Michael H.: Oh, that&#39;s… 43:02 Maria K.: And wait, and wavering. Our system. Once the document translate. It will send back, this is majority of our customers that integrated, we have that settled for them. We set up a notification thing, saying, hey. We got your documents, passed. Success, right? Great. But once the X12… got to the trading. It was missing lying irons. So, from 2-7. 43:38 Maria K.: 70 of them arrive only. And the customer was told. We sent everything. Uh-huh. You know why that is. Babe… Okay?
43:50 Michael H.: Yeah. 43:53 Maria K.: That&#39;s their money, then, okay? So… so… I… I… we checked there was a splitter issue. 44:07 Michael H.: Oh, those… that thing, again, splitter issues, that&#39;s the key word that keep popping up all the time. 44:10 Maria K.: Yeah, so let me explain to you what that is. So, when… And I&#39;m gonna show you quickly so you know. 44:15 Michael H.: Yes. 44:23 Maria K.: So it&#39;s not foreign to you anymore. So… When customers send us document. 44:24 Michael H.: Okay. 44:32 Maria K.: They might send multiple invoices in one big batch. Right? We have it assisted a program called the Splitter Program, so once we receive that large batch. The splitter was split into individual documents, right? Then, once they&#39;re split. They go through their map. The Delta. The Delta mattress from Enslave, that XML, whatever format they said. Into an as well. And it jacks into over the eye. 45:13 Michael H.: Mm-hmm. 45:15 Maria K.: And it goes out to the trading partner. That&#39;s how it works. Uh, if it&#39;s a splitter issue, we… go to the developers to address that. 45:30 Maria K.: If it&#39;s the Delta map. We have to check why it&#39;s not translating everything, check with the, uh, project analyst and see what&#39;s up. But… because I checked others, there&#39;s one person that I go to, Jennifer. She&#39;s in charge of Banner. She&#39;s a project manager for Banner. And Kenra is one of our contractors. He&#39;s amazing. Kindred, I had learned so much from him. He&#39;s actually He used to teach EDI, okay, just so you know. Like, he… he&#39;s a… he&#39;s like a book on this. Um, he&#39;s amazing. I wish I had his brains. Um, so… He does banner setups. If I show you… an image. A banner setup. Watch. I&#39;m gonna show it to you. 46:28 Maria K.: Where&#39;s Banner at? Can you see my screen? 46:34 Michael H.: I&#39;m still on the Andrew Rice Yes. 46:35 Maria K.: Oh. 46:38 Michael H.: Okay, I see it now. 46:41 Maria K.: This was before when we have AS2B, AS2A, Now they change it,
but I&#39;m just letting you know how complex banana is. Um… So… So, in this case… he… he told me. That this invoice… So, I don&#39;t know if you have time, we can take a look at it so you can see it visually what&#39;s going on. That way. Is not foreign to you, you know what I mean? Um. 47:17 Michael H.: Yes. No, I&#39;m gonna look at this and everything. 47:22 Maria K.: Okay, so… This is just the view, I&#39;m not training you on this, because I, you know, I know this is more up But it&#39;s just for you to know how they integrate it works, and Delta Map, and so forth, okay? This isn&#39;t good at sample, Ashley. Um, so… I&#39;m going to share the server Because that&#39;s where I usually go first. So, they&#39;re saying… invoice number on April 16th. Andrew&#39;s pretty good at giving you details, you know? And sending you information. I know he&#39;s angry. I know he is. Um, not at the point that he offends you, but… Honestly, I don&#39;t blame him. 48:07 Michael H.: He gets really angry? 48:18 Maria K.: It&#39;s been a while this keeps happening. They threaten wants to leave us. 48:21 Michael H.: Yeah. Oh, wow. 48:24 Maria K.: We were… we were actually talking… I think they were actually talking to Cleo. Because I think they&#39;ll be better with Clio. So, data trans… you know, like, we do the WebDI thing and all of that, but Clio handles Large, large… volumes of data movement. Right? And it&#39;s… they don&#39;t use, like, a portal. There are clients that, you know, imagine, we have… Walmart is one of our clients. 48:46 Michael H.: Yes. 48:56 Maria K.: We manage all the data movement for Wilmar. But not in this department, okay? 48:59 Michael H.: Yes. 49:01 Maria K.: That&#39;s in another department. So it&#39;s all, like, more the backhand stuff. More files going in and out, kind of thing. Um, so… Let&#39;s check here. So, would I do banner, the integrated, I&#39;m gonna… I&#39;m gonna clear this. So, banner… I&#39;m gonna go straight… to, um… I&#39;m gonna go to the splitter. First… Yeah, and then go to April 16th. 49:36 Michael H.: Yes, the A10 splitter. 49:41 Maria K.: And they don&#39;t know. You know why? Because we&#39;re telling them,
success! Pass. You know, they&#39;re not gonna come back and say, hey, can you really check this file if it&#39;s working or not? They are… they&#39;re having a peace of mind, because it&#39;s a success thing, right? Once I get to the trading partner, the trading partner tells them, hey, you only send us this amount of items, we&#39;re spending this much. 50:05 Maria K.: And that&#39;s when it gets, you know… that I reply to? 50:44 Maria K.: Um, alright, so April 16… Okay, so we have all these invoices. The good thing is. He gave me the invoice number, okay? So I go here, the monoculus, and then I… and I type in the number. 50:56 Michael H.: Yes. 51:10 Maria K.: Okay. So I created two invoices. 51:16 Michael H.: Yes, there&#39;s two of them. 51:16 Maria K.: Yeah… All right, I cannot view it like that. Alright, so go on. 52:27 Maria K.: Let me check if that invoice came in a single batch by itself. Because we… We&#39;re going to enable Uh, I think we enable that for Helmet. Um, to, uh… to not run this splitter for, uh, single-cent invoice, you know what I mean? So it doesn&#39;t happen that, like, it&#39;s missing items. 52:58 Maria K.: So let me see… Let me see, uh… 52:59 Michael H.: Yep. 53:27 Maria K.: Ah, see… This was sent with invoice number 5394719. 54:08 Maria K.: I&#39;m just… I know Andrew back, and he said, Andrew, just confirming. I checked on my end. This invoice was sent with other invoices, correct? You know, I just want him to tell me, yes. Because it was sent with other invoices. Um. Let me take a look… 55:01 Maria K.: Many parts did you say? Program 23. I only saw 98. Let me check that again, maybe I didn&#39;t copy it. It&#39;s the one, 36, right? 56:00 Maria K.: All right. This is like, you see, this is… this is huge. Why you send a huge-ass invoice? With older ones, you know what I mean? 56:13 Michael H.: And they look… and this format looks so disorganized, too, like… Oh, oh, yeah. 56:17 Maria K.: No, oh, this is a flat file. They&#39;re used to it. Yep, this is a flat 5. 56:26 Michael H.: Yeah, just so much… it looks like it&#39;s lagging the computer, the server.
56:31 Maria K.: Yeah, right? This is… oh my god, dude, I deal with these all the time. And I can identify errors. These files. But you&#39;ll learn that. It will promise we&#39;ll have to start sending it individually, because… It was a really large. I&#39;m probably gonna end up feeling that today. 58:03 Maria K.: Not what I do understand you need the other ones. Because it looks like the biggest one is that 3-6 one. Okay, there you go. 58:24 Michael H.: I&#39;m just gonna grab some water real quick. 58:26 Maria K.: Yeah, no, no 01:00:35 Michael H.: Okay, I&#39;m Becky. Oh, he has a lot of… you have a lot of files of Baylor. 01:00:46 Maria K.: Dude, I can&#39;t… Like, that&#39;s what I mean, it&#39;s just, like, open this thing, and… So, I need the 361, okay. I&#39;m just gonna get rid of… like this… 01:01:02 Michael H.: It&#39;s, um, does… does he always, like Andrew, does… Is it… is it, like, the same problems all the time? 01:01:10 Maria K.: Not really. It has mapping issues sometimes. 01:01:17 Michael H.: Oh, okay. 01:01:18 Maria K.: Yeah. Yeah, me… They&#39;ve signed and received a lot of data. At some point, that&#39;s another one. Okay, um… the engineering, like, you know, I take the… I take that one of those. No, of course not too big, but they&#39;re a global shop, their stuff is complex. No, you&#39;ll get that, you&#39;ll understand it. Am I bogging that this is the leading while I&#39;m out of time? You know, I literally grab everything. You see what I&#39;m saying? 01:02:10 Michael H.: Yeah. 01:02:13 Maria K.: I&#39;m just gonna go, like, from down up. And… and see why. Uh, this is the biggest file, the 3-6. I can&#39;t do anything. Yeah, that&#39;s why those sweet mangoes. You know what? I&#39;m gonna say that&#39;s my fault. Like, why are you sending… Made in me. They just kicked me out. Yeah, look at this. My answer was tomorrow. No, like, I have to do this for tomorrow. Susan is saying. 01:03:05 Michael H.: Yeah. 01:03:07 Maria K.: Yeah, I am… no. It&#39;s just… why would you send Listen, why would you… and you know what I have to do? Just so you know. I will go into… They leave the other data. 01:03:24 Michael H.: Yes.
01:03:24 Maria K.: We drop this file. To process again. And resend it to the trading partner we are correcting. Okay? Tomorrow. 01:03:36 Michael H.: Yeah, so you&#39;re just gonna drop this file and then process it all again tomorrow. 01:03:42 Maria K.: Just with this… Charles with us, this one alone. 01:03:47 Michael H.: Yeah, this one is massive. 01:03:50 Maria K.: I wanna… I wanna… I wanna test… And C… you know, I know it&#39;s a splitter. I know it&#39;s not the Delta one. It&#39;s a splitter problem, you know? 01:04:04 Maria K.: So, let&#39;s see what happens. Well, I&#39;m gonna go now. I will see you tomorrow morning, and, you know, if you have any questions, like, again, like I always say, you know, just let me know. 01:04:20 Michael H.: As I think… I think I start the same time you do in the morning as well. 01:04:26 Maria K.: What the heck is this? Police Advice Assistant Shipment Time Out with EFIS. Even though we canceled or committed. To the low within 2 hours, there&#39;s… Either a delay in data trans receiving, sending a status. What the heck? Let me assign this one to myself. 01:04:49 Maria K.: Um, rain, or… Welcome to those and out-wise. What else is out there? I&#39;m checking on a sign right now. 01:04:58 Michael H.: Yes. 01:04:58 Maria K.: Um, let me go… let me share so you know how I… I know the… Is this the… okay. Uh, this one is just saying that they are notifying a new project. Blah blah blah, someone set up a project, they got notified you know, I… we just… I just cancel these. Processing Arrow, Cabela, scan, uh. Processing the error. Gsd, still incorrect. What&#39;s that? 01:05:33 Maria K.: Our invoices are still calculating GST off the pre-discounted totals, where it should be, based off the post-discounted amount. I flagged this earlier, but it&#39;s not… it does not seem to be addressed. Always… always contacting us regarding our mowing issues. I was expecting a call from from an account rep to remedy the persisting and ongoing issues we are experiencing. I don&#39;t know. I mean, I want to take this, but… I want to show it to Sandy, because I don&#39;t know if someone is working on this, you know? 01:06:09 Michael H.: Yes.
01:06:11 Maria K.: Right? Because he said, I addressed this issue and it hasn&#39;t been taken care of. I don&#39;t know who worked on that. Because I remember… I&#39;m pretty good at remembering my tickets. You&#39;re my work on stuff, because you know how long it takes, right? 01:06:25 Michael H.: No, that&#39;s what I mean. There&#39;s some people that already remember. I have, like, two people that… keep going back and forth with Dylan and, uh, Candy. And so, just… Oh, yes, yes. 01:06:35 Maria K.: Oh, wait, you have Candy Ticket? Oh, that&#39;s lubrication, that&#39;s NetSuite integrator. What&#39;s the problem? 01:06:45 Michael H.: Um, she&#39;s having an ASN problem again, so it&#39;s like… Oh, yes, let me, um, grab it real quick. 01:06:49 Maria K.: Can you show me that ticket? What&#39;s the ticket number? Uh-huh. 01:07:02 Maria K.: Yes, Amaya! And in one second. No, my daughter&#39;s AFM, don&#39;t only work. 01:07:05 Michael H.: Yes. 01:07:08 Maria K.: What is a CS what? 01:07:11 Michael H.: Oh, yes, let me, uh, pull… go there real quick. Let me… 01:07:13 Maria K.: Okay, cool. Okay. Okay. 01:07:26 Michael H.: 44… 061. Yeah, uh, I just took it at 3. 01:07:40 Maria K.: Okay, this one you can check. Okay, so… Yeah, she just wanted to know if they accepted the ASM. 01:07:43 Michael H.: Yes. 01:07:49 Maria K.: If I went to them, we can look at it tomorrow if you want, okay? We can… I can walk you through the whole thing, okay? 01:07:56 Michael H.: Yes. 01:07:57 Maria K.: All right, so let&#39;s do it tomorrow. Um… I&#39;m gonna go now. What&#39;s your schedule like? 01:08:04 Michael H.: Um, uh, mine is a, uh, usually 9AM our time, but I&#39;ve been starting at 7. Uh, because I have doctor&#39;s appointment. And, um, so I clock in with Ms. Sandy, and I show her the tickets that I&#39;m working on. 01:08:18 Michael H.: So I&#39;m like… because I usually wake up at 4 or 5, no matter what.
01:08:19 Maria K.: No, but what… so what time you&#39;re here? 01:08:25 Michael H.: Yes. Oh, and I leave… 6PM Eastern. 01:08:30 Maria K.: Oh, gotcha. Okay, so you&#39;re leaving soon. Okay, cool. That&#39;s good 01:08:32 Michael H.: Oh, yes. Mm-hmm.
View original transcript at Tactiq.</p>
