# CS-44819: Action Needed - Confirm Test Document Details: Testing & Certification Project for (Chewy Dot Com): Kaier Inc

## Ticket Information
- **Key**: CS-44819
- **Status**: Canceled
- **Priority**: Medium
- **Assignee**: Unassigned
- **Reporter**: <EMAIL>
- **Created**: 2025-06-02T06:07:04.152-0600
- **Updated**: 2025-06-02T09:10:12.525-0600
- **Customer**: Kaier Inc

## Description
| | 
| |   !https://cdn.dev.spsc.io/web/framework/assets/20.09.01/images/sps-logo-blue.png! | 
| | 
|  *Testing & Certification Project (Chewy Dot Com): \\Kaier Inc* \\  \\Hi, <PERSON><PERSON>. \\  \\ *Action Needed.* It looks like steps 3 and 4 still need to be completed in the [ +Testing & Certification portal+|https://url9207.spscommerce.com/ls/click?upn=u001.1BcK1iWLIsTdqxmExt4XFxfV5SmQGFcNVdNIkpHoERSOOqbLjoflBLykpVtNCX83NB915TLZcalqdqcBLfJ-2F9tdQeytUBC0skRrQnQQ5oDw-3DQGil_wmc-2BViHeaSAOdjPZKiSJ-2FYs6CfG2C-2BiSLXMtz36zDlZgOYSBn66wdahqYS5HU0rDVso7b2lvkApzkVd6bMHbTlIIQWGJS-2BgaC-2FHYHhClUOIEJu2vkna7tPHj-2BamfHChHx-2BVJqav3bhc8ZSF6wemnWpr4EW97aZkwCXidEeLTIch9xLsOdV6BLeX48XbwKsTDod5mTykl-2BYniJwvfGGl9UHZMfxxD1XTphEoL-2B6IKZ-2Bqf3n-2F-2FmG1jh-2BOK3Z5w2t2Z] before test documents are generated. \\  \\ *Step 3.* Under the  +_Test Document Details_+ tab, enter information to customize the test purchase orders (only fields with a red asterisk next to the field name are required). Hovering over the blue  _“i”_ icon next to each field name will show the additional corresponding information or requirements. \\  \\ *Step 4.* Under the  +_Testing Scenarios_+ tab, generate and send buyer-initiated test documents either by using the  _“Send All Test Documents”_ button at the top right (or send one at a time with the blue  _“Send Test”_ option within each testing-scenario box). After you have generated the test documents, please return subsequent documents from your system for each testing scenario via the chosen test connection method. Document validation will also be available within this area of the testing profile. (Note: If your testing agreement does not include buyer-initiated EDI documents, please transmit any test documents based on what is expected in the Testing Platform Process document.) \\  \\ *Step 5.* Once testing is completed, you will be asked to provide  +_Production Setup Details_+ (such as production contact information, production connection method, and Qualifier/ISA/GS production identifiers). \\  \\For questions relating to testing, please respond directly to this email thread at [<EMAIL>|mailto:<EMAIL>?subject=Re:Action Needed - Confirm Test Document Details: Testing %26 Certification Project for (Chewy Dot Com): Kaier Inc] \\  \\EDI Testing & Certification Team \\ _editesting@spscommerce.com_ \\Emilie Dunn \\  \\proj:_a02Nt00000CuMf3:proj \\ | 
| 
| {color:#000} *SPS Commerce Inc.*{color} {color:#000}333 S 7th St #1000, Minneapolis, MN 55402{color} Having issues? [{color:#172B4D}Contact our support team{color}|https://url9207.spscommerce.com/ls/click?upn=u001.1BcK1iWLIsTdqxmExt4XF4Otr-2BLsuFn-2FxwYDU95jSNI1X1Fb9UTy8hk1BuQqAILHvobnxo-2B0ykwDT1nbVjQ0gQ-3D-3DiMWr_wmc-2BViHeaSAOdjPZKiSJ-2FYs6CfG2C-2BiSLXMtz36zDlZgOYSBn66wdahqYS5HU0rDVso7b2lvkApzkVd6bMHbToF-2FOpmUSwuVfVEkddRNo1hHGEzVcglSJHQWzjs0-2FvbRZPZMRzWdKeIBKjO47mFYB2p-2FP3kICuL1VH4u5Ik8C2FuCuUZMYHze-2BIXFN0VqzfJE-2FggxDzyViGokvvAri4HjVBvo-2Bxh5DUt0dKXCWISblp5ilpcnaqdfSwCCdV3WnHW]{color:#172B4D}{color} {color:#000}{color} 
Message Reference ID: SPS-RZJ-9J-WIH {color:#000}{color} | 
   !https://url9207.spscommerce.com/wf/open?upn=u001.0aIP81AzdOWreoEpnn5mgnBkK2-2Bz3eAxGQ35VhJwWMDWb39J7cZdZpYwjYvYai5KVvMmh6SaxDWk5g3Bu2-2FDd7QxVz8pctRtBHsUt8XkJrl88b-2F8-2BsaVrnsk4i2wMrJy-2FVma3olGdayuTermKAzo5IwxgQFaX-2B-2BEdoYv-2F6Y6ANTOAyHNqr3U4oQ8LAtrzNQSUQH79HgG4jA-2Bb8tF41eDzWMT8yvvpjGU-2FDQkk40WOOasPFOnsdlUtFv1nKNweFcA!

## Components


## Labels

