# EDI Support Ticket Knowledge Base

This knowledge base contains exemplary support tickets that demonstrate best practices in EDI troubleshooting.


### Ticket CS-36972: PO&#39;s not received
**Score**: 105/115
**Categories**: connectivity, document_flow
**EDI Types**: SFTP, FTP, EDI
**Priority**: Medium

#### Problem Description:
Starting last week, our global team has not been receiving our iDOCs. We have verified that the document was successfully generated and uploaded to the DTS folder (todts). However, the global team has informed us that the iDOCs are not appearing in SAP. It is unclear whether the issue lies with the FTP transfer or the file import into SAP. Could you please assist us in resolving this?...

#### Root Cause:
you don&#39;t remove the files from the server once they are picked , you have an old sftp and i think jason went over and explain why you are experiencing these issues
we also fixed the ownership on the directory because we saw something wrong there as well
you should be able to pick up your files we are not sure why on your side you can pick up the files not sure if you made changes on your end because we got no way to clean this up as it is on your side
they are in your fromdts directory waiting to be picked up you need to check this on your end
maria keshwala datatrans solutions support@datatrans-inc.

#### Resolution:
the issues on our end are you sending us documents?
maria keshwala datatrans solutions support@datatrans-inc.

#### Key Troubleshooting Steps:
1. have verified that the document was successfully generated and uploaded to the dts folder (todts).
2. updated on our end for the ftp you should be able to received your files
maria keshwala datatrans solutions support@datatrans-inc.
3. fixed the issues on our end are you sending us documents?
maria keshwala datatrans solutions support@datatrans-inc.
4. fixed on our site
we cant tell if the files have been picked up because you don&#39;t remove the files from the server once they are picked , you have an old sftp and i think jason went over and explain why you are experiencing these issues
we also fixed the ownership on the directory because we saw something wrong there as well
you should be able to pick up your files we are not sure why on your side you can pick up the files not sure if you made changes on your end because we got no way to clean this up as it is on your side
they are in your fromdts directory waiting to be picked up you need to check this on your end
maria keshwala datatrans solutions support@datatrans-inc.
5. the problem is on your end please schedule a time see below
https://calendly.

---


### Ticket CS-30414: Re: ACTION REQUIRED: Caterpillar EDI Enablement: 30105 | Supplier Code: E0562J0 | | CAT 89 SANTA CATARINA
**Score**: 105/115
**Categories**: connectivity, data_format, mapping, performance, configuration, document_flow, error_handling
**EDI Types**: 850, 856, 810, 997, AS2, SFTP, FTP, X12, EDI
**Priority**: Medium

#### Problem Description:
Adding the DataTrans Support team as they&#39;d always be your first point of contact. I&#39;m actually off for vacation in 1/2 an hour.
Have you reached out to Caterpillar and asked them what the issue is or have they advised what is in error? They approved moving this to production on the 18th. Last time... they had not turned on the relationship on their end so all failed.
Krista
On 10/31/2024 5:23 PM, Vi Ngo wrote:
Hi Krista,
Somehow there’re lot of CAT 89 invoices got “rejected”. We couldn’...

#### Root Cause:
of #7 we were not getting the edi information pertaining to our supplier account our product numbers we make.

#### Resolution:
01/nov/24
status: resolved
project: customer support
components: none
affects versions: none
fix versions: none
type: support priority: medium
reporter: albert assignee: nicholas sanchez resolution: done votes: 0
labels: none
remaining estimate: not specified
time spent: not specified
original estimate: not specified
request type: emailed request
request language: english
request participants: organizations: label: asn label
description
hi.

#### Key Troubleshooting Steps:
1. tested further.
2. tested further.
3. change certain settings.
4. update on the po.
5. updated quantity from the 860 documents
comment by albert [ 29/oct/24 ]
&lt;!doctype html public &#34;-//w3c//dtd xhtml 1.

---


### Ticket CS-37129: RJW 940s
**Score**: 95/115
**Categories**: connectivity, data_format, document_flow
**EDI Types**: 997, AS2
**Priority**: Medium

#### Problem Description:
Customer: Adams Extracts
Customer ID: 6061
Trading Partner: RJW Logistics
Document Type: 940 – Warehouse Shipment Order
I have not yet received 997s for these orders that were transmitted this morning at 0922. I retransmitted them at 1642. Please review and determine if these 940s are reaching RJW. Initially there was an issue with using the wrong AS2 connector.
Scott W. Shippee
Nestell &amp; Associates| 5000 Birch Street | West Tower, Suite 3000 | Newport Beach, CA | 92660, USA
<a href="http://...

#### Root Cause:
another document is expected to be completed therefore it wont overwrite that and changed it but once you open the document you can see it has been accepted.

#### Resolution:
a few hours after see below
invalid as2
correct as2
maria keshwala datatrans solutions support@datatrans-inc.

#### Key Troubleshooting Steps:
1. change success blog comment by maria keshwala [ 31/jan/25 ]
hi scott
i will look into this as well thank you
maria keshwala datatrans solutions support@datatrans-inc.
2. fixed a few hours after see below
invalid as2
correct as2
maria keshwala datatrans solutions support@datatrans-inc.
3. i checked as2 connection is correct , 997s are being received the reason why you dont see the 997 “accepted” status is because another document is expected to be completed therefore it wont overwrite that and changed it but once you open the document you can see it has been accepted.
4. cause another document is expected to be completed therefore it wont overwrite that and changed it but once you open the document you can see it has been accepted.

---


### Ticket CS-36065: FW: Ticket Updated: Your Finance Request ********* has been updated
**Score**: 85/115
**Categories**: data_format, mapping, configuration, document_flow, error_handling
**EDI Types**: 850, 810, X12, EDI
**Priority**: Medium

#### Problem Description:
Maria
Does this help?
Michelle Rice
Vee Engineering/Air Side Systems
<a href="mailto:<EMAIL>"><EMAIL></a>
************ ext. 27
From: Caterpillar EDI Enablement <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, January 21, 2025 12:41 AM To: <a href="mailto:<EMAIL>"><EMAIL></a>; Michelle Rice <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Ticket Updated: Your Finance Req...

#### Root Cause:
eh baare is not having any issues .

#### Resolution:
See troubleshooting steps

#### Key Troubleshooting Steps:
1. updated maria
does this help?
michelle rice
vee engineering/air side systems
<a href="mailto:mrice@plant4.
2. updated
hi @michelle rice,
the invoice is not processed due to the following error.
3. updated
for caterpillar employees - to verify the authenticity of this email, visit cat@work and search for: em%62ngw0
fyi only: the following updates have been made to your request.
4. changed this is how the by will populate for cat34 and this mapping rule is customized only for integrated document “not created in webedi” as we setup this condition only for cat34, logic if h record = cat34 in position 22 then the by which is the buying party will populate i just pulled a file from 11/25/2024 that looks correct so make sure you dont do this in webedi they have to be integrated
message id 42446727 reference 255168 thank you
maria keshwala datatrans solutions support@datatrans-inc.
5. cause eh baare is not having any issues .

---


### Ticket CS-35612: Large File
**Score**: 80/115
**Categories**: performance, document_flow
**EDI Types**: 856, EDI
**Priority**: Medium

#### Problem Description:
DataTrans,
This EDI 856 file did not make it over to Redwood due to the size. Henry dropped yesterday and they did not receive last night. I re-dropped earlier this morning and still no luck.
Please split this file into smaller files and resend.
Thanks,
MARIA ZAPATA I CUSTOMER CARE MANAGER I 972-829-2366 I UDFC...

#### Root Cause:
now those files have been delivered multiple.

#### Resolution:
a year ago for you guys to process large files.

#### Key Troubleshooting Steps:
1. fixed a year ago for you guys to process large files.
2. cause now those files have been delivered multiple.

---


### Ticket CS-42990: Re: [EXTERNAL] Re: AAFES/Exchange x Radial System to System Integration - A Better Tomorrow LLC (07847402)
**Score**: 85/115
**Categories**: connectivity, data_format, configuration, document_flow
**EDI Types**: 850, 856, 810, 997, AS2, EDI
**Priority**: Medium

#### Problem Description:
Hi Marianne,
Can we get an order from AAFES from testing? We are not seeing the shipstation integrator pick up the ASN when we make it in Shipstation manually or when my warehouse sends the fulfillment:
We want to test to ensure that the fulfillment passes up from our warehouse into EDI effectively.
Thank you, Boris Chung
On Thu, Jan 30, 2025 at 7:28 AM Marianne Kania <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Hi Boris,
You&#39;ll need to send them the Inventor...

#### Root Cause:
your organization is scheduled to begin trading data with our dropship manager on behalf of aafes/exchange.

#### Resolution:
See troubleshooting steps

#### Key Troubleshooting Steps:
1. change x radial system to system integration - a better tomorrow llc (07847402) hi marianne,
can we get an order from aafes from testing? we are not seeing the shipstation integrator pick up the asn when we make it in shipstation manually or when my warehouse sends the fulfillment:
we want to test to ensure that the fulfillment passes up from our warehouse into edi effectively.
2. change x radial system to system integration - a better tomorrow llc (07847402)
hi purushothaman, updated files have been sent.
3. updated files have been sent.
4. changed accordingly with inclusion of additional fees as mentioned in technical questionnaire document
846 inventory
cold you please send us the 846 inventory file with quantity on hand as mentioned in the technical questionnaire.
5. change x radial system to system integration - a better tomorrow llc (07847402)
hi purushothaman, an updated 810 has been sent.

---


### Ticket CS-31221: FW: Your Invoice(s) 159763 for Order(s) 0018457-0372
**Score**: 90/115
**Categories**: data_format, document_flow, error_handling
**EDI Types**: 856, 810, EDI
**Priority**: Medium

#### Problem Description:
Some additional information for Ticket  CS-31218 RESOLVED  , the last ASN they received was
Message ID 40830852 on 7/8/2024.
<em>Best Regards, Brian Wall – IT Manager</em>
EXACTO SPRING CORP 1201 Hickory St, Grafton, WI 53024 Phone: (************* Ext: 202 Direct: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a> Website: <a href="http://www.exacto.com">http://www.exacto.com</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a hre...

#### Root Cause:
is for all the parts not just this one.

#### Resolution:
, the last asn they received was
message id 40830852 on 7/8/2024.

#### Key Troubleshooting Steps:
1. cause is for all the parts not just this one.

---


### Ticket CS-40470: Upload EDI
**Score**: 85/115
**Categories**: connectivity, configuration, document_flow, error_handling
**EDI Types**: 810, SFTP, EDI
**Priority**: Medium

#### Problem Description:
Hello @Support!
We tried to upload an old EDI file that was requested to invoice against, but the portal is showing the following error:
The EDI files are attached. Can you help us with uploading them? Thank you in advance,...

#### Root Cause:
your maps are setup via integration thats the only way.

#### Resolution:
See troubleshooting steps

#### Key Troubleshooting Steps:
1. cause your maps are setup via integration thats the only way.

---


### Ticket CS-30354: Errors Report
**Score**: 85/115
**Categories**: document_flow, error_handling
**EDI Types**: 810, X12, EDI
**Priority**: Medium

#### Problem Description:
We received an error report for two iDOC files we uploaded. Can you tell us what’s the cause of the error ?
Thanks,
Charles Reeves
From: Datatrans Reports <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, October 31, 2024 11:00 AM To: ChempaxSupport, Dystar/US <a href="mailto:<EMAIL>"><EMAIL></a>; Deal, Melissa, DyStar/US <a href="mailto:<EMAIL>"><EMAIL></a>; Clarke, Rich, DyStar/US <a href="...

#### Root Cause:
of the error ?
thanks,
charles reeves
from: datatrans reports <a href="mailto:alerts@datatrans-inc.

#### Resolution:
See troubleshooting steps

#### Key Troubleshooting Steps:
1. cause of the error ?
thanks,
charles reeves
from: datatrans reports <a href="mailto:alerts@datatrans-inc.

---


### Ticket CS-40382: SuperSalu Invoiced
**Score**: 80/115
**Categories**: data_format, configuration, document_flow
**EDI Types**: 810
**Priority**: Medium

#### Problem Description:
Customer: Adams Extracts
Customer ID: 6061
Trading Partner: UNFI – Conventional (SuperValu)
Document Type: 810 - Invoice
Transmitted 10-12 invoices today, but they did not show up in WebEDI. Please advise.
Scott
Scott W. Shippee
Nestell &amp; Associates| 5000 Birch Street | West Tower, Suite 3000 | Newport Beach, CA | 92660, USA
<a href="http://www.NestellAssociates.com">www.NestellAssociates.com</a>
Mobile: (401)529-0678
Timezone: EST/EDT
<a href="https://www.linkedin.com/company/nestell-associ...

#### Root Cause:
the files are not splitting into single files.

#### Resolution:
the issue and injected the files
maria keshwala datatrans solutions support@datatrans-inc.

#### Key Troubleshooting Steps:
1. change success blog comment by maria keshwala [ 13/mar/25 ]
good morning scott
i will look into this and advise thank you
maria keshwala datatrans solutions support@datatrans-inc.
2. checked and the invoices themselves are going out, but it is not injecting into wededi.
3. change success blog
comment by maria keshwala [ 13/mar/25 ]
found the invoices but they are not splitting not sure if this is the reason they are not injecting into webedi
maria keshwala datatrans solutions support@datatrans-inc.
4. fixed the issue and injected the files
maria keshwala datatrans solutions support@datatrans-inc.
5. fixed the file name on the target- fixed the dom map added required segments that were missing.

---


### Ticket CS-37189: Missing 810/856 Reports DTS-494
**Score**: 80/115
**Categories**: document_flow, error_handling
**EDI Types**: 856, 810
**Priority**: Medium

#### Problem Description:
Hello DTS Support,
We are no longer getting report emails with the processing status for 810 and 856 files. (We are still receiving them for 855s)
The last 810 report we received was on the 29th at 10AM CDT. The last 856 report we received was on the 23rd at 6PM CDT.
Please look into this ASAP and send through all missing reports. I want to confirm there have been no issues with outbound file processing.
Thanks, Andrew The content of this email is confidential and intended for the recipient spec...

#### Root Cause:
ecs receives the documents once a day, and that is the only time when tests can be performed and the error can be identified.

#### Resolution:
thank you
maria keshwala datatrans solutions support@datatrans-inc.

#### Key Troubleshooting Steps:
1. verified that the 810 reports are now being received correctly.
2. fixed the issue please let me know if you still not receiving the reports.
3. cause ecs receives the documents once a day, and that is the only time when tests can be performed and the error can be identified.

---


### Ticket CS-37167: Issue opening file - in Webedi -DS10530
**Score**: 80/115
**Categories**: document_flow
**EDI Types**: 810
**Priority**: Medium

#### Problem Description:
Hello - you called because you are having issues with opening an invoice that was already sent and is just loading I have addressed this with our dev team as we have an ongoing ticket for this issue....

#### Root Cause:
you are having issues with opening an invoice that was already sent and is just loading i have addressed this with our dev team as we have an ongoing ticket for this issue.

#### Resolution:
the issue thank you
maria keshwala datatrans solutions support@datatrans-inc.

#### Key Troubleshooting Steps:
1. have fixed the issue thank you
maria keshwala datatrans solutions support@datatrans-inc.
2. cause you are having issues with opening an invoice that was already sent and is just loading i have addressed this with our dev team as we have an ongoing ticket for this issue.

---


### Ticket CS-33537: Production Dataflow Error  Action Required (Coast To Coast - ALLPRO Corporation)
**Score**: 80/115
**Categories**: document_flow, error_handling
**EDI Types**: 810, EDI
**Priority**: Medium

#### Problem Description:
Greetings Coast To Coast Computer Products, Inc! We have received production dataflow for your trading partnership with ALLPRO Corporation and there is at least one error detailed below. Please review and send revised documents as soon as possible. Doc Type: 810 Document Sender: Coast To Coast Computer Products Inc Document Receiver: ALLPRO Corporation Document IDs: [&#39;********&#39;, &#39;********&#39;] Error Message: If InvoiceTypeCode is CR or DI, then BuyerPartNumber is required.
You can v...

#### Root Cause:
i see the document was restaged , and entered manually you added a line item “2” when it should be item line “1” make sure if you are only sending one item then the line items should should be 2 unless this part number was in line item 2 on the po.

#### Resolution:
the line item number
maria keshwala datatrans solutions support@datatrans-inc.

#### Key Troubleshooting Steps:
1. fixed the line item number
maria keshwala datatrans solutions support@datatrans-inc.
2. fixed the issue if so because i see the document was restaged , and entered manually you added a line item “2” when it should be item line “1” make sure if you are only sending one item then the line items should should be 2 unless this part number was in line item 2 on the po.
3. cause i see the document was restaged , and entered manually you added a line item “2” when it should be item line “1” make sure if you are only sending one item then the line items should should be 2 unless this part number was in line item 2 on the po.

---


### Ticket CS-33298: CRITICAL: Dell POs being routed to unknown partner instead of Dell
**Score**: 80/115
**Categories**: document_flow
**EDI Types**: 850
**Priority**: Medium

#### Problem Description:
Good Morning,
Somehow our Dell Pos are getting routed to a partner we are unfamiliar with. Please help us get this fixed ASAP, and re-send all the Pos we have placed to Dell. This appears to have started 11/13. I can’t tell you a msg id because it tells me I don’t have permission for that trading partner.
Chris McLaird Director of Enterprise IT
Toll-Free (*************
General (*************</p>
<ul>
<li>Website * | [ LinkedIn |https://www.linkedin.com/company/traferaofficial/] | [ Twitter |http...

#### Root Cause:
it tells me i don’t have permission for that trading partner.

#### Resolution:
asap, and re-send all the pos we have placed to dell.

#### Key Troubleshooting Steps:
1. fixed asap, and re-send all the pos we have placed to dell.
2. cause it tells me i don’t have permission for that trading partner.

---


### Ticket CS-33294: Class error
**Score**: 80/115
**Categories**: mapping, document_flow, error_handling
**EDI Types**: 810
**Priority**: Medium

#### Problem Description:
Good Morning,
Please advise on this error….
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |...

#### Root Cause:
they are working on other things for you.

#### Resolution:
we can troubleshoot again i cant give this to development if there are mapping errors that needs to be address first.

#### Key Troubleshooting Steps:
1. fixed we can troubleshoot again i cant give this to development if there are mapping errors that needs to be address first.
2. update
maria keshwala datatrans solutions support@datatrans-inc.
3. fixed it.
4. cause they are working on other things for you.

---


### Ticket CS-31270: FW: FW: Prod | Error in workflow : 841946508 | BP Name : FM_SPEDeEnvelope_I
**Score**: 80/115
**Categories**: data_format, document_flow, error_handling
**EDI Types**: 810, EDI
**Priority**: Medium

#### Problem Description:
Good afternoon,
When I am sending the Fred Meyer invoices and I put in the allowance, the allowance amount doesn’t come off of the total (see below). Can this please be fixed? Thank you!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
From: Jennifer Martin <a href="mailto:jmartin@datatrans-in...

#### Root Cause:
the edi goes out with the correct amount.

#### Resolution:
thank you
maria keshwala datatrans solutions support@datatrans-inc.

#### Key Troubleshooting Steps:
1. fixed thank you
maria keshwala datatrans solutions support@datatrans-inc.
2. cause the edi goes out with the correct amount.

---


### Ticket CS-31110: Auto pick and pack issue- DS-10433
**Score**: 80/115
**Categories**: document_flow
**EDI Types**: 856
**Priority**: High

#### Problem Description:
Hi team,
I hope you&#39;re well and having a good week so far.
We&#39;re having a lot of problems with our auto pick and pack option on Datatrans. Can we organise a google meet call for Thursday 7pm GMT to go through this and get a fix in place?
All the best, Alexandria...

#### Root Cause:
is going to multiple distribution centers and i asked this question while on the call, once you create the asn please check your draft as it creates multiple asn that you need to work on.

#### Resolution:
is the auto packing issue
po number 15250628 customer is working
maria keshwala datatrans solutions support@datatrans-inc.

#### Key Troubleshooting Steps:
1. cause is going to multiple distribution centers and i asked this question while on the call, once you create the asn please check your draft as it creates multiple asn that you need to work on.

---


### Ticket CS-43217: Fwd: HSN Urgent Communication - SSL certificates expiring for EDI services 6/1/2025.
**Score**: 75/115
**Categories**: connectivity, document_flow, error_handling
**EDI Types**: AS2, EDI
**Priority**: Medium

#### Problem Description:
Hello DataTrans,
Can you help me understand if we will need to do something manually or if the new certificate will be automatically connected?
---------- Forwarded message ---------From: Joseph Julian <a href="mailto:<EMAIL>"><EMAIL></a> Date: Thu, May 1, 2025 at 4:03 PM Subject: HSN Urgent Communication - SSL certificates expiring for EDI services 6/1/2025. To:
Hello HSN EDI Partner,
HSN’s SSL certificates for production FTPS and HTTPS connection will update on 05/2...

#### Root Cause:
a service interruption to your ftps scripting.

#### Resolution:
See troubleshooting steps

#### Key Troubleshooting Steps:
1. update on 05/28/2025 at 9am eastern (est).
2. update the current certificates will no longer be valid.
3. update may cause a service interruption to your ftps scripting.
4. update on 05/28/2025 at 9am eastern (est).
5. cause a service interruption to your ftps scripting.

---


### Ticket CS-43024: RE: [EXTERNAL] CS-42354 Data Trans missing 855 for Grainger and 810 unread in Draft folder
**Score**: 75/115
**Categories**: connectivity, configuration, document_flow, error_handling
**EDI Types**: 850, 810, SFTP
**Priority**: Medium

#### Problem Description:
Hello Nicolas,
We never received the 850 file, please check why this PO was not translated.
Thank you. Amit
Amit Das Senior Applications Analyst : Burlington, Ontario Hadrian &amp; World Dryer : Office: (************* Mobile: (************* Email: <a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.linkedin.com/company/hadrian-manufacturing-inc./">https://www.linkedin.com/company/hadrian-manufacturing-inc./</a>
<a href="https://www.facebook.com/HadrianInc">https:/...

#### Root Cause:
Not explicitly stated

#### Resolution:
data trans missing 855 for grainger and 810 unread in draft folder
———-—
reply above this line.

#### Key Troubleshooting Steps:
1. found in d:\maps and boms\world dryer\wesco international
wesco 850 to world dryer event rule exists
850 raw data shows “}“ as delimiters instead of “”.
2. updated event rule to match tps delimiter.
3. i updated the error preventing the map to process the po and restaged its delivery.

---


### Ticket CS-39922: DTS SFTP Migration BSP Filing Solutions
**Score**: 75/115
**Categories**: connectivity, performance, document_flow, error_handling
**EDI Types**: SFTP, FTP, X12
**Priority**: Medium

#### Problem Description:
Hello,
We are in the process of migrating our FTP to a new SFTP over the next 60 days. Failure to complete the migration will result in document delivery issues. Please use the calendar link below to schedule a time.
Thank you,
<a href="https://calendly.com/tdaughrity/30min">https://calendly.com/tdaughrity/30min</a>...

#### Root Cause:
Not explicitly stated

#### Resolution:
the issue that was holding the files from being processed.

#### Key Troubleshooting Steps:
1. change in production data.
2. update you once i have more details.

---


### Ticket CS-40310: Invoice Issues- DTS-569
**Score**: 75/115
**Categories**: data_format, mapping, performance, configuration, document_flow, error_handling
**EDI Types**: 856, 810, X12, EDI
**Priority**: Medium

#### Problem Description:
Hello,
When trying to send invoices to Chewy, the message below pops up. Is there anything you could do to assist?
The PO number for this is **********
Thanks,
Cody Worthy Operations Engineer
1700 Central Plank Rd
Wetumpka, AL 36092
334.541.4070 | c: 334.557.6880
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.TheOhSoCo.com">www.TheOhSoCo.com</a>
<a href="http://www.BahamaBos.com">www.BahamaBos.com</a>
<a href="http://www.WorthyPromo.com">www.WorthyPromo.com</a...

#### Root Cause:
Not explicitly stated

#### Resolution:
today i see the last comment added last night.

#### Key Troubleshooting Steps:
1. update on this?
cody worthy operations engineer
1700 central plank rd
wetumpka, al 36092
 mandatory data missing at isa*16 &amp; extra segment delimiter in the isa segment
gsindts6256dltrcan202502121510100000005x004010~
st8100005~
big202501242025017202409120001312435199411~
curbycad~
refvr7609~
n1stdelta dc 4119299411~
n37530 hopcott road~
n4deltabcv4g 1j1can~
n1refoley dog treat company~
n31945b bollinger rd~
n4nanaimobcv9s5w9can~
itd013 x12-0  (0.
2. update this is still in development almost done they are in the last phase of this .
3. update is complete and if i can send dollar tree the invoices?
thank you,
daniel
daniel stiefvater
owner/operator
foley dog treat company
1945-b bollinger rd
nanaimo, bc, v9s 5w9
mobile: ************
www.
4. update i had sent this back to our dev team but please send this to dollar tree canada below their specs only allows one delimiter as the segment terminator they requested to add an extra terminator at the end of the isa16 which we did but we need that stated on the specs can you please have dollar tree canada sent their updated specs stating this rule ? please advise thank you
the specs we have are from 07/14/2015
maria keshwala datatrans solutions support@datatrans-inc.
5. updated specification i received another email from you with a required delimiter that is different from this email and we need to get this right.

---


### Ticket CS-37128: Bad Date/Times DTS-496
**Score**: 75/115
**Categories**: connectivity, performance, document_flow
**EDI Types**: 856, 997, AS2
**Priority**: Medium

#### Problem Description:
Customer: Adams Extracts
Customer ID: 6061
Trading Partner: Woods Distribution
Document Type: 940/997
Taking a look at the snippet below
Time Sent: 01/30/2025 @ 03:04PM – I confirm that this timestamp is expected based on time transmitted and expected processing time.
Last Modified Time: 01/30/2025 @ 09:04 AM – That is 6 hours before it was sent to DTS, how was it modified before it was received? If the clock was 12 hours off, it is still incorrect, as that time hasn’t yet arrived. If it is GMT/...

#### Root Cause:
Not explicitly stated

#### Resolution:
and changed to cst - please let us know if you still having any issues thank you
maria keshwala datatrans solutions support@datatrans-inc.

#### Key Troubleshooting Steps:
1. updated to cst?
please advise.
2. change success blog comment by maria keshwala [ 31/jan/25 ]
hello scott
i will look into this very odd if it was sent already being modified before not sure could be it was edited in the morning then sent in the afternoon but i will check the entire process and advise.
3. fixed and changed to cst - please let us know if you still having any issues thank you
maria keshwala datatrans solutions support@datatrans-inc.

---


### Ticket CS-36571: Franklin International - Problem with Walmart 850&#39;s received on 01/22/2025
**Score**: 75/115
**Categories**: data_format, mapping, document_flow
**EDI Types**: 850, X12, EDI
**Priority**: High

#### Problem Description:
Hello Data Trans Support,
Franklin received 30 Walmart PO’s this morning that we are unable to process.
It looks like there is missing data in the “flat files”
The first attachment is one of the Walmart PO’s received today (01/22/2025)
The second attachment is one of the Walmart PO’s received last week (01/15/2025)
When I compare the two files, the 01/22/2025 file is missing big chunks of data that are present on the 01/15/2025 file.
To me, it looks like something happened to the Walmart 850 map...

#### Root Cause:
Not explicitly stated

#### Resolution:
they want all the files restaged
file from 01/15/2025 batch id 141365687, message id 42954205, reference number 4481207071
file from 01/22/2025 batch id 141510921, message id in webedi 43027929, reference number 6736166211
maria keshwala datatrans solutions support@datatrans-inc.

#### Key Troubleshooting Steps:
1. checked the first po
message id in webedi 43027929
reference number 6736166211
batch id translated file 141510921
po from 01/15/2025 message id 42954205
reference number 4481207071
batch id 141365687
comparing to see what is it that is missing
maria keshwala datatrans solutions support@datatrans-inc.
2. fixed they want all the files restaged
file from 01/15/2025 batch id 141365687, message id 42954205, reference number 4481207071
file from 01/22/2025 batch id 141510921, message id in webedi 43027929, reference number 6736166211
maria keshwala datatrans solutions support@datatrans-inc.
3. update waiting to hear back.
4. changed to ignore the td5 segment?
thanks,
franklin international
joe mcmahan
comment by maria keshwala [ 23/jan/25 ]
td5 is a key element used in the mapping.
5. updated to tell it what to do if it&#39;s missing
a map mod is required for us to rerun the files asking jennifer if a work auth is required to update the map
maria keshwala datatrans solutions support@datatrans-inc.

---


### Ticket CS-33860: FW: Finance Request ********* has been resolved
**Score**: 75/115
**Categories**: data_format, configuration, document_flow, error_handling
**EDI Types**: 856, 810, X12, EDI
**Priority**: Medium

#### Problem Description:
We are still having issues with CAT England’s 810s. Is the map missing something?
Michelle Rice
Vee Engineering/Air Side Systems
<a href="mailto:<EMAIL>"><EMAIL></a>
************ ext. 27
From: Caterpillar EDI Enablement <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, December 2, 2024 2:51 AM To: <a href="mailto:<EMAIL>"><EMAIL></a>; Michelle Rice <a href="mailto:<EMAIL>">MRice@Pl...

#### Root Cause:
Not explicitly stated

#### Resolution:
we are still having issues with cat england’s 810s.

#### Key Troubleshooting Steps:
1. resolution notes:
additional comment by michelle rice [ 03/dec/24 ]
i just learned that baare is not having issues with cat england.

---


### Ticket CS-31716: FW: FL2 Plant City Expansion - v#88531 NATION WIDE PRODUCTS
**Score**: 75/115
**Categories**: data_format, document_flow
**EDI Types**: 850, X12, EDI
**Priority**: Medium

#### Problem Description:
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a>
From: Kila Rose Sent: Tuesday, November 19, 2024 9:41 AM To: &#39;Sara Doyle&#39; <a href="mailto:<EMAIL>"><EMAIL></a>; Se...

#### Root Cause:
Not explicitly stated

#### Resolution:
fw: fl2 plant city expansion - v#88531 nation wide products
———-—
reply above this line.

#### Key Troubleshooting Steps:
1. update for ?
maria keshwala datatrans solutions support@datatrans-inc.
2. update for ?
maria keshwala datatrans solutions support@datatrans-inc.

---


### Ticket CS-30452: Fwd: EDI Transmission Failure - 2024-10-31 06:00:05 to 2024-11-01 06:00:05 DTS5763
**Score**: 75/115
**Categories**: data_format, document_flow, error_handling
**EDI Types**: 856, X12, EDI
**Priority**: Medium

#### Problem Description:
Can I get more details on why these failed? Mainly rule#1?
---------- Forwarded message ---------From: Datatrans Reports <a href="mailto:<EMAIL>"><EMAIL></a> Date: Fri, Nov 1, 2024 at 8:00 AM Subject: EDI Transmission Failure - 2024-10-31 06:00:05 to 2024-11-01 06:00:05 To: <a href="mailto:<EMAIL>"><EMAIL></a></p>
<h1 id="errors-report">Errors Report</h1>
<ol>
<li>Date/Time File Name Message Batch ID Status
1 2024-10-31 07:23:16 DESAD...

#### Root Cause:
Not explicitly stated

#### Resolution:
second batch 139867502
costco files that failed are all set restaged those files
maria keshwala datatrans solutions
support@datatrans-inc.

#### Key Troubleshooting Steps:
1. fixed
second batch 139867502
costco files that failed are all set restaged those files
maria keshwala datatrans solutions
support@datatrans-inc.

---


### Ticket CS-30350: Halliburton/DataTrans EDI invoice file problems DTS-334
**Score**: 75/115
**Categories**: connectivity, data_format, configuration, document_flow
**EDI Types**: 810, SFTP, EDI
**Priority**: Medium

#### Problem Description:
DataTrans Support,
<em>We continue to receive several 0kb EDI invoice files from DataTrans each day. On Oct 17 we received the following “ Maria Keshwala commented: Good morning Bill I was told by our IT that your setup is different and you cant be migrated to the new sftp but they looked into the 0kb issue and they resolved the problem please let us know if you still having any 0kb files so I can alert the IT to continue and investigate the root cause. But it was confirmed they have fixed it.”....

#### Root Cause:
Not explicitly stated

#### Resolution:
the problem please let us know if you still having any 0kb files so i can alert the it to continue and investigate the root cause.

#### Key Troubleshooting Steps:
1. have fixed it.

---


### Ticket CS-44353: RE: For domestic fill- Vayda US corp
**Score**: 70/115
**Categories**: data_format, document_flow
**EDI Types**: EDI
**Priority**: Medium

#### Problem Description:
...

#### Root Cause:
the pos will b
nicolas sanchez senior support engineer i cleo communications webedi-support@cleo.

#### Resolution:
re: for domestic fill- vayda us corp
———-—
reply above this line.

#### Key Troubleshooting Steps:
1. change your company name we can certainly help you with that, however, if you want “vayda us corp“ used on only domestic pos, we cannot accommodate your request.
2. change your company nam
nicolas sanchez senior support engineer i cleo communications webedi-support@cleo.
3. cause the pos will b
nicolas sanchez senior support engineer i cleo communications webedi-support@cleo.

---


### Ticket CS-43280: Fwd: [EXTERNAL] Re: Rejected EDI Invoice(s) for vendor 364930 (2025-05-05)
**Score**: 70/115
**Categories**: data_format, document_flow, error_handling
**EDI Types**: 810, EDI
**Priority**: Medium

#### Problem Description:
...

#### Root Cause:
they are not taking your al
nicolas sanchez senior support engineer i cleo communications webedi-support@cleo.

#### Resolution:
asap
invoice# 2025-086
total of 9 line items (prior to allowance) should be 30301.

#### Key Troubleshooting Steps:
1. cause they are not taking your al
nicolas sanchez senior support engineer i cleo communications webedi-support@cleo.

---


### Ticket CS-43214: FW: POPTIME SNACK BRANDS - 850 Failure
**Score**: 70/115
**Categories**: data_format, mapping, performance, document_flow, error_handling
**EDI Types**: 850, EDI
**Priority**: Medium

#### Problem Description:
Hello, We received this PO 304370 which failed because the Ship to information (N102) is longer than the maximum length of 35 characters.
Please correct and resend.
Thanks
EDI Production Support
<em><a href="mailto:<EMAIL>"><EMAIL></a> North America Quote-to-Cash</em>
1 Becton Drive Franklin Lakes, NJ 07417 US
BD Restricted
From: WMProd <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 2, 2025 1:01 AM To: E_Business_Prod <a href="mailto:E_Business_P...

#### Root Cause:
the ship to information (n102) is longer than the maximum length of 35 characters.

#### Resolution:
See troubleshooting steps

#### Key Troubleshooting Steps:
1. changed the ship to info but i don’t see that the order was entered.
2. cause the ship to information (n102) is longer than the maximum length of 35 characters.
3. root cause : root cause: translation failed due to value in n1_02 exceeded the allowable maximum length of 35 characters, per map specs.
4. root cause : root cause: translation failed due to value in n1_02 exceeded the allowable maximum length of 35 characters, per map specs.

---
