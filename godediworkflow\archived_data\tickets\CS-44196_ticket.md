# CS-44196: Unable to save or send ASN Message ID : 44358854 Reference #: 25052233603002

## Ticket Information
- **Key**: CS-44196
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON><PERSON>hakur
- **Reporter**: <EMAIL>
- **Created**: 2025-05-21T23:11:05.753-0600
- **Updated**: 2025-05-23T02:56:49.987-0600
- **Customer**: <EMAIL>

## Description
Hello,
This is <PERSON> from Lia<PERSON><PERSON>.
While inputting our data through our created ASN, we have been experiencing the inability to save our ASN; normally when we click "save" on the document it would take awhile to save, which is fine, but recently when we clicked "save" on this particular document it is promptly ended as if it did not attempt to save.

We are not sure what seems to be the cause of this, but we seem to think it is a data overload or bug in the system due to our large data input into the document so that saving this large document itself does not seem possible.

I have attached an image regarding the affected ASN including message ID and reference number for you folks to locate this document. Please advise on what can be done.

!ASN Message ID - 44358854.jpg|thumbnail!


Thank you for your understanding and cooperation.

Best regards
Michael--{adf}{"type":"expand","content":[{"type":"paragraph","content":[{"type":"text","text":"   "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://ci3.googleusercontent.com/mail-sig/AIorK4ws3Wi0I7BMOzLuiYu0ZDYOyn9HQxTwZkao0zzZhAdA1urBKJUXypSg8z-OL84nqXjuBG0b-t3Qveq9","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":"248 Mokauea Street","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"hardBreak"},{"type":"text","text":"Honolulu, HI 96819","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"hardBreak"},{"type":"text","text":"Tel: (*************","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"hardBreak"},{"type":"text","text":"Fax: (*************","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":"  "}]},{"type":"paragraph","content":[{"type":"text","text":"2601 Walnut Ave Ste D.  "},{"type":"hardBreak"},{"type":"text","text":"Tustin, CA 92780 Tel: (*************  "},{"type":"hardBreak"},{"type":"text","text":"www.lialoha.com","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}},{"type":"link","attrs":{"href":"http://www.lialoha.com/"}}]},{"type":"text","text":"    "}]},{"type":"paragraph","content":[{"type":"text","text":"This email and any files transmitted with it are confidential and","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":" "},{"type":"text","text":"intended solely for the individual or entity to whom they are","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":" "},{"type":"text","text":"addressed. If you have received this email in error destroy it","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":" "},{"type":"text","text":"immediately.","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

