<h3 id="meeting-transcription">Meeting Transcription</h3>
<p>Meeting started: May 19, 2025, 11:33:23 AM Meeting duration: 65 minutes Meeting participants: <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>
View original transcript at Tactiq.</p>
<h4 id="generated-content">Generated Content</h4>
<h3 id="finding-customer-edi-address">Finding Customer EDI Address</h3>
<ol>
<li><strong>Purchase Order (PO) Document</strong>:    - Check the <strong>&#34;Ship To&#34;</strong> section on the purchase order. This section typically contains the EDI address or relevant shipping information.</li>
<li><strong>EDI System or Portal</strong>:    - Log into your EDI portal where you manage trading partner information.    - Navigate to the <strong>Trading Partner Setup</strong> or <strong>Customer Information</strong> section to find the EDI address associated with the customer.</li>
<li><strong>Contact Your Analyst</strong>:    - If you cannot find the EDI address in the documents or portal, reach out to your EDI analyst or support team. They can provide the necessary information or assist in locating it.</li>
<li><strong>EDI Address Table</strong>:    - Some systems maintain an <strong>address table</strong> that maps customer names to their EDI addresses. Check if your system has this feature.</li>
<li><strong>Vendor Prefix</strong>:    - If applicable, ensure that your vendor prefix is set up correctly, as this may
affect how addresses are retrieved.</li>
</ol>
<h3 id="summary-look-in-the-po-document-edi-portal-or-contact-support-for-assistance-always-ensure-that-your-vendor-prefix-and-address-tables-are-correctly-configured-to-facilitate-easy-access-to-customer-edi-addresses">Summary - Look in the PO document, EDI portal, or contact support for assistance. Always ensure that your vendor prefix and address tables are correctly configured to facilitate easy access to customer EDI addresses.</h3>
<h4 id="transcript">Transcript</h4>
<p>00:00 Lauren C.: Alrighty good morning everybody can everyone hear me. 00:04 Parnell : Good morning. 00:05 Lauren C.: Good morning. 00:07 Michael C.: Good morning. 00:08 Lauren C.: Awesome, thank you guys. Just making sure we have audio so we can get started 00:13 Michael C.: 00:13 Lauren C.: all right. Perfect let me know if I go to faster if you guys have any questions in the middle of literally anything I&#39;m here to help okay. All right, so we&#39;ll go ahead. I&#39;m going to sign in. To our training session for the start we will go through just the generic overview of the portal so when I go in I usually start over here in the top right. These are where you can locate your settings? So over in here, you&#39;ll be able to have access as the admin side note secondary users you may not have access to this but your admin will they&#39;ll be able to access all of the users so you&#39;ll see here. I have four I have the admin and then my three users that I&#39;ve added in after that you&#39;ll notice when you scroll down. You have access to modify your username you can change the real name change your email phone number at any point you can also change your password. 01:13 Lauren C.: And then as the admin you will not have to change anything under permissions. You&#39;ll automatically have everything enabled for every single partner that you&#39;re set up for. As far as your secondary user so when I click my second one same thing you&#39;ll still have access to update the username real name email phone number and their password if they ever get logged out locked out
you can change that as well below that you&#39;ll be able to see that we have the permissions here you can completely control anything they view if they can delete anything create all of that if you&#39;re wanting to go bipartner you can as well. So if they are only going to be doing like your invoicing for advanced auto you could come in here and fully customize that as well. 01:58 Lauren C.: if you&#39;re ever making a change to let me move this guy any form of permissions passwords names any of that just make sure you hit save at this bottom right corner this will give you confirmation up in the top and confirm that the changes were made otherwise it may not save so if you&#39;re doing like a password change definitely make sure you&#39;re hitting save so that way it does process through and Your user can log in. 02:24 Lauren C.: So that is just the overview of the users the next tab over here in the top left. This is your labels. This is how you&#39;d be printing if you have any questions as far as what structure you&#39;re needing or what we set up initially with the project you can reach out to us at support or your analyst this the 02:44 Lauren C.: most generic label. We usually have is this 4x6 singular one per sheet or you can go through and select literally anything. It&#39;s based off of how you&#39;re printing what your printer set up for any of that same thing here if you do make a change make sure you hit save so that way the changes will be secured. So this is just your label, so if you&#39;re printing through our BDI go ahead and check this out make sure you are set up if you&#39;re not no worries. You don&#39;t have to use this tab the next one will be your company information. 03:17 Lauren C.: This is just behind the scenes. It doesn&#39;t affect anything as far as your actual response documents this just says this is my account here&#39;s my name here&#39;s my address your vendor prefix if you are using the labels with nybdi. You will need to go ahead and have this setup here if you are unsure if you set up one before or if you need to register for one you can hit the little eye in there are two links here first Link will give you the database so you can click the link and go through and research to see if you do have one if you forgotten. 03:53 Lauren C.: That&#39;s already set up great. It will actually give it to you and you can just copy and paste if you haven&#39;t actually signed up for where a prefix before you can go ahead. It&#39;ll break down all the information for it and also help you get started and register for one.
04:10 Lauren C.: Below that this is just your contact information always recommended to have this up to date if there&#39;s ever a reason say someone left the company or the admin is
04:20 Lauren C.: being handed over to someone else definitely try to have this up to date as much as possible so we can contact the right person if there&#39;s ever any issues with you know billing or anything at all this will definitely help especially if the admin leaves and it&#39;s been passed over to somebody else and a secondary
user gets logged out and can&#39;t get back in so all of that this will give us the primary person to contact again always it&#39;s safe when you make any changes so that way it updates. 04:51 Lauren C.: The billing information tab mine isn&#39;t set up your should automatically bring you into another screen. You will have access to all of your billing details so previous invoices what you&#39;re set up as far as any recurring charges any cards that you have on file all of that should be located there if your maxio screen does not populate please let us know I can get with our accounting team and we can get that set up if you&#39;re seeing a screen just like this you&#39;re not supposed to be there it should be giving you something else. So, please reach out if you have any issues.
05:28 Lauren C.: Below that we have our terms and conditions. This is just to give you another copy in case you didn&#39;t receive it the first time everybody when they sign up should have received a copy of this. Just so you have what you signed up for and all of the just terms and agreements here. Some people like to have copies so we make sure that&#39;s readily available for you guys. 05:50 Lauren C.: So close that one next we&#39;ll be our preferences tab this screen will always pop up on your initial sign in the reason for that it just needs you to set your time zone. So definitely make sure you have that in there. The first one is our Show stoplights and scent folder. This is just that so it&#39;s red green and yellow this
will show you anything that you have in your scent status it will tell you whether it&#39;s green if it&#39;s been accepted by your trading partner yellow will mean it&#39;s either in a scent status or it&#39;s an a draft and red means rejected. I usually recommend having these enabled so you can keep up with the current status of these otherwise if it&#39;s not on there will still be text and the status column, so you&#39;ll see you know acknowledged or rejected but the Little Lights are a little bit handy and they help with scrolling through like your scent folder or anything else. 06:47 Lauren C.: Below that we have the create single pack order per store. This one I don&#39;t always recommend enabling if you have multiple Partners and they have different shipping standards when you set up initially through the project phase they will go ahead and set up the packing on the back in for you if there&#39;s a specific configuration for it. If you only have one partner and your partners requested this you can come in here and enable it but just remember anything done within the settings right here. We&#39;ll impact every partner that you&#39;re set up with so just be careful with checking that if you ever have any questions feel free to reach out to your analysts or even come over into support we can look into it for you. 07:30 Lauren C.: Right here was that time zone that you should have set once you hit save you should be able to go into your portal. And then message archive interval so if there&#39;s anything that you&#39;re not necessarily wanting to delete but you&#39;re wanting to archive just to get it out of view whether it&#39;s your inbox your draft or your scent folder you can go ahead and archive those messages to move them over you can set this interval for literally anything you could even set it for five days, so that way if you ever forget about them and they&#39;re just kind of sitting there the archive will clean itself out for you. Um so that one&#39;s always handy we&#39;ll hit save and go. 08:09 Lauren C.: Down to the bottom you may not actually have these guys here it depends on if you did enable you know FedEx QuickBooks netsuite any of those guys this would be another way to access your integration. We&#39;re not going to click on those today. We can go through those one-on-one with you if you have any questions on them or any issues with those so at the bottom we have our notifications. This is where you can set up email notifications for this top one is all but 997 so anything that comes inbound into your account you can get notified
on so that way you&#39;re not having to check the portal all the time if you&#39;re not wanting to be in it all every single day or if you&#39;re receiving a ton of orders and you&#39;d like to have just something that says hey on this date you receive these poes at
08:58 Lauren C.: this time from this person. You can set that up as well. So within the notifications you can. Set whether you want all of your partners or just a specific partner you can put as many email addresses as you want. Just separate them by a column no spaces required and then you can scroll through the list of available documents to you and you can set those up you could check as many as you want as view as you want or again. You can put the all but 997-997 is just acknowledgement from server to server so it&#39;s not a real document type. That&#39;s why it says but so don&#39;t. 09:33 Lauren C.: Her you&#39;re not missing anything this would just enable you for any document type. So for me, I&#39;ll go ahead and put that and then since I made a change. I&#39;ll go 09:43 Lauren C.: ahead and hit save. So you&#39;ll see I have email one at Outlook and email to atletic. They are both set to receive any inbounds that come into the portal every day. Already, so this is just the general settings overview does anyone have any questions or has anyone had any issues with going through their settings?
10:10 Michael C.: Not yet, but I&#39;m I&#39;ve yet to look at him. So I&#39;ll take I&#39;ll check that out and we&#39;ll let you know. 10:15 Lauren C.: No worries. Yeah, definitely let us know you shouldn&#39;t really encounter any issues our most common one is this vendor prefix, so if you have any questions on that feel free to reach out. We are here to help. 10:29 Lauren C.: Alrighty, so let&#39;s go ahead when you hit up here if you click on webbdi this will take you back home. So this will be the main screen. Yes. 10:37 Parnell : I&#39;m quick questions the 997. It was just notify us when there is a purchase or 10:50 Parnell : when there&#39;s an 850 that was submitted or it would also tell us about any other problem that we may have. 10:58 Lauren C.: So the all but 997 or you&#39;re asking about the 997 document or acknowledgement. 11:04 Parnell : the 997 notification to the email 11:07 Lauren C.: Okay so the all but 997 that&#39;ll tell you anything inbound for your account whether you&#39;re receiving an 850 po you&#39;ll see like I have 8 30s 820s any 11:17 Parnell : okay 11:18 Lauren C.: document that would come inbound for you. You&#39;ll get a notification for. 11:20 Parnell : right Awesome okay, so it&#39;s not just purchase order is any inbound document we will get a notification. 11:28 Lauren C.: Correct so even if you&#39;re receiving like an 812 which is an adjustment or an 864. I believe that is the it&#39;s like a text document where it&#39;s like a rejection some partner send that you&#39;ll receive all of those via email. 11:40 Parnell : right So if an A10 fails and there&#39;s a rejection document we would get a 997 on that also. 11:52 Lauren C.: So the 997 here I&#39;ll click through to show you really quick the 997 is what controls this status over here some partners. You&#39;ll see like Sally they don&#39;t send a 997 but if you look like as they do right here so if you look over in the right side, this is where those 997s are shown so it&#39;s not an email. This is just Directly from the server whether it was accepted if it&#39;s waiting or rejected. So those will be in that status column, so within your settings. I always recommend enable those stop lights so you can see this.
12:26 Parnell : Okay awesome, thank you. 12:27 Lauren C.: Okay of course already so let&#39;s go ahead we will start. I normally go down this way, but we&#39;ll just go across the top we&#39;ll work our way down alright. So you can click webbie the eye or home. They did add that there.
12:43 Lauren C.: I always forget that buttons there. Sorry guys the next button to that if you haven&#39;t already enabled a trading partner. I know sometimes people come in they&#39;ll set up an account. Just to get a look around and feel for it you can come in and add trading partner. You will have an access here to the list of any in network partners, so these are people that we&#39;ve already been set up with we&#39;ve already done testing through and we can get you squared away fairly quickly with these guys. 13:14 Lauren C.: So you can scroll through that say if you want to add Academy Sports you would select that if you have your vendor number great. I know sometimes they give those an advanced if they did not give you one totally okay hit I don&#39;t have a number and that&#39;ll just bypass that for you. um Okay for me. I&#39;m go ahead. 13:35 Parnell : So on that list. On that list um we&#39;re buyer it would only show vire or it would show all the other vendors that you guys do business with. 13:49 Lauren C.: This will show you everybody that we&#39;ve currently set up with so if there&#39;s
13:53 Parnell : okay 13:55 Lauren C.: anyone that&#39;s not on the list you can still hit other and type them in. 14:00 Parnell : okay 14:00 Lauren C.: So we can still connect with anybody this just gives you a quick view to who we already have so you don&#39;t have to type it all out. 14:08 Parnell : Thanks. 14:09 Lauren C.: Of course below that you would give a good context so if you have a direct contact with your trading partner go ahead and type that in here. It&#39;ll help the analyst get in Direct contact when they go ahead and do your setup if you don&#39;t that is okay, as well. It&#39;s not required. You&#39;ll see everything on this page is not required. So you can buypass really anything so the more information the better it definitely helps move things along but if you don&#39;t have it. That is okay, too. 14:36 Lauren C.: So once you go ahead and select we&#39;ll just grab somebody Academy Sports normally for an active account. It will give you the actual fee here. So when you do select it. It should give you something for mine since. It&#39;s a test. I don&#39;t have access to pricing and building and all of that so it doesn&#39;t populate but the price should show up there You&#39;ll notice below. It&#39;ll say that there may be additional fees it depends on. 15:01 Lauren C.: Your setup so if you&#39;re integrated with like an FTP or global shop there may be
15:06 Lauren C.: a map fee but your analyst will go over that with you before anyone&#39;s charged or anything. I need to add somebody that&#39;s okay, so once you&#39;ve gone ahead and filled in all your information and added your partner go ahead and submit payment and request and we will process that for you. 15:25 Lauren C.: Once that has been sent over we&#39;re going to skip this tab. We&#39;ll come back to it. You&#39;ll see this project status tab. This will show you any request that you&#39;ve made so on here. I added a test trading partner. There&#39;s no name there you can click within and you&#39;ll be able to access when it was created the scheduled start date and once they set a estimated due date. 15:50 Lauren C.: Below that you&#39;ll have access to the analysts so there will be an analyst name here as well as their email address. If you ever need to reference the trading partner contact that we made contact 16:01 Lauren C.: with that will be up here as well and just really everything every milestone for your project. So you&#39;ll have access to actually follow along with your setup and all of the details below. Um since this one&#39;s a test. There&#39;s not really much. Sorry. There&#39;s no really big milestones in mine, but this is how your should look it&#39;ll have titles for every single thing that at the analyst worked on with all of their notes and any dates. 16:29 Lauren C.: Alrighty so back to this AD Service button here in the middle if you have set up and say your integrated with QuickBooks and you&#39;d like to
connect your invoicing and purchase orders within the portal to us. You can come over here and select this so we have QuickBooks desktop and QuickBooks online if you select the wrong one it is okay. We can get that fixed for you, but yes you can add QuickBooks
16:54 Lauren C.: you can add users labels if you decide to print within a the portal rather than FedEx ups any of that ship station we also partner with and target we do automated inventory reports. So you can always add these at any point hit add service and it&#39;ll submit it over to accounting. You should get an email confirmation once it&#39;s active and ready for you. 17:21 Lauren C.: Alright, so this last button appear at the top is your reports. Loading so once it loads you&#39;ll be able to access here we go you can select by month and hit go you&#39;ll see all of the documents received document sent and you can actually export these so if you&#39;re wanting to just keep record of what you&#39;ve received for that month you can go through and do all of that. 17:50 Lauren C.: Same thing for these guys, so once you hit documents receive your document sent you can actually select a date range here you can view all of your partners or you can go ahead and select if you only want to see stuff from advanced auto from this timeframe to this time frame and go it&#39;ll do the same thing. It&#39;ll give you that display and a detailed report. So that just helps you keep up with what&#39;s received during those dates.
18:16 Lauren C.: We&#39;ll go ahead and go to the next one does anyone have any questions on these top buttons up here? 18:25 Parnell : 18:26 Lauren C.: Perfect, I&#39;ll take that as a green light let&#39;s go ahead.
18:30 Lauren C.: Alright so going back home. The next area is this little bar here so newer users sometimes. They don&#39;t always have their purchase order within their inbox. That&#39;s completely okay. If you&#39;ve already received that po whether that&#39;s via email somehow sent directly to you. You can create standalone documents so this file new document. You&#39;ll see everything that you are set up for and when you hover over these you&#39;ll see who they&#39;re set up for so if you&#39;re needing to create. We&#39;ll do the generic 810. You have your purchase order. It was emailed to you prior to your setup, but they want you to go ahead and invoice through edi file new document 810. You can hover over that. It&#39;ll show you who your enabled with and you can go ahead and select them and it&#39;ll put you into a standalone invoice where you can fill everything in based off of that purchase order you receive before. 19:29 Lauren C.: This goes for every document type you can create anything that you&#39;re enabled for as a standalone. File create response does the same thing this just gives you a little bit. easier access to those Our view tab there is an option for this preview pane some people prefer it rather than going through an opening
every single document within their inbox the amount a little bit you can click. 19:58 Lauren C.: My internet is really slow. I am so sorry guys when you click. It&#39;ll give you just that it&#39;s a preview you can scroll through and click one by one with each document and you can see in to the document however there isn&#39;t a respond button or anything here. You would have to open it to go ahead and do that but this just gives you a quick glance at everything in your inbox. 20:21 Lauren C.: Um some people have accidentally enabled it and they don&#39;t care for it the quick way to fix that view uncheck the preview pane and it goes away. Alrighty so the next options within view we have expand all if you notice we have these little blue buttons here this just shows you that there is something associated with europeo. Or your 8:30 or any document in your inbox when you hit view and expand all. 20:48 Lauren C.: It will expand those lines, so you can see what&#39;s associated, so for me this top po I have three invoices that I&#39;ve generated one reference test these have not been sent. They&#39;re sitting here and a draft status. When I scroll down the next purchase order, it&#39;ll show that I&#39;ve sent three and voices and I have two in draft this just gives you another way to see everything that&#39;s in progress and the current state of the documents that you&#39;ve responded with. 21:19 Lauren C.: Once you&#39;re done with that and you want to collapse you can hit the collapse all and it&#39;ll give you just everything in your inbox. Next tab is our default tab this one is pretty crucial if you&#39;re doing invoices and there&#39;s a lot of Fields that you&#39;re needing to fill out that will be the
21:36 Lauren C.: same on every single invoice let&#39;s grab. Grab Autos on let&#39;s see if this one we&#39;ll do an AutoZone 810 as an example. See what we have here okay, so on here. You&#39;ll see that there are blue Fields those are required you&#39;ll notice that this is the remit 2 so this is your information if this is the same across the board for every single invoice you can go ahead and put your company name your address steady steady. Sorry Cindy States zip and it doesn&#39;t look like the country is set up as a default we can enable that if you&#39;d like to if it&#39;s required any of that you can hit save and every 810 that you generate for AutoZone will automatically have your information populated for you. So just saves a little bit of time and it does automate that if there are any Fields that you need enabled and they&#39;re not showing here like the terms. If your terms are the same every single time they&#39;re basic net 30. Let us know we can open up the fields for you and this way those will also populate. It&#39;s just another way to help streamline the process so when. 22:49 Lauren C.: You&#39;re creating all your really having to do is go through and just verify quantities and pricing the system should automatically pull it from your purchase order however. It&#39;s just always recommended double check make sure everything&#39;s good and adding up correctly, but yes any field within these document defaults you can set up you can adjust at. Point so say you&#39;ve defaulted this address, but now your company&#39;s set somewhere else you can always come back in here AutoZone 810 update it and that next invoice you
create will adopt that. 23:24 Lauren C.: So that is just the defaults the next tab is our help tab. There is a
23:30 Lauren C.: pre-recorded version of this class, so if you&#39;re ever needing to reference and you just want to go back and you know skim through to see how the packing was how the document creation was or if you need help with your settings and say it&#39;s after business hours. You can click that and it&#39;ll take you straight to that video. Below that is our context support this will give you all of our contacts, so we have sales technical accounting and then the training tutorial. 23:57 Parnell : Hello and we can reach out to any one of these folks right. 24:01 Lauren C.: Yes sir, so sales if you have any questions about adding anything to your account go ahead and reach out there technical will be our team so the support team you can send an email or you can call in we have English and Spanish just 24:15 Lauren C.: make sure you hit option to that&#39;ll shoot you right over to our queue. 24:18 Parnell : okay 24:19 Lauren C.: And then accounting so if you ever need to double check on Billing or say you&#39;re having an issue with your card and you need to update it, but you&#39;re not sure about the system feel free to email there same thing you can also call for
24:33 Lauren C.: English or Spanish and select three and the accounting team will help you out if you don&#39;t hear back within. Want to say like an hour. I usually recommend go ahead and send an email over to accounting. Just make sure it include your company name and who you are and what the issue is and they&#39;ll get back to you. If you&#39;re ever trying to put a Pause on like a trading partner if you&#39;re not trading with them for like an extended amount of time you can shoot an email there if you don&#39;t hear back within 24 hours. I always say you can reach out to us and we can follow up on it for you, but normally accounting&#39;s really good. They get back to you within 24 to 48 hours with some form of update so That is that team below that here&#39;s the training tutorial so for this class. It 25:20 Lauren C.: is the Monday class you can click here if you ever lose the link and you can come back to here our class below that the click here. I&#39;ll go ahead and open it for you. Just so you can see this screen. We have some pre-recorded videos for you. There&#39;s the If it allude we have how to pack using autopack how to create a catalog some generic videos that were set up okay, I think it&#39;s just me sorry guys, but there are some videos there they&#39;ll pop up you can open those guys. They are YouTube videos so if you can&#39;t access that on your work computer you should be able to copy the link and open it say on your phone or anything else. It is just YouTube I know some users have had that issue. So they&#39;ve had to copy that guy. 26:11 Lauren C.: So that is this area does anyone have any questions on this area? Already so next let&#39;s go ahead and look at our inbox. Yes sir. 26:27 Parnell : Since we are going to be sending the a10s. Do we have to build the a-10s from scratch once we ship the products out we receive the 850s and we ship the product and we come back into the portal we have to go now and build the A10 from scratch or we just the system automatically brings data over from the 850 into the E10 that is required and that was done in testing. 26:56 Lauren C.: So if you have an 850 in your inbox, I&#39;ll show you in a second how to generate the eight ten and pull information from the 850. I&#39;ll actually go through that will create an eight ten and an 856 here in a second I can show you the process. 27:10 Parnell : Okay, thanks. 27:12 Lauren C.: I promise we&#39;re almost there already so over to the left. It&#39;s set
up pretty much like your email. So you have your inbox anything that&#39;s been sent to you your draft anything that&#39;s been created. Let&#39;s go ahead and all messages. So
27:26 Lauren C.: you&#39;ll see I have a few things here in my draft folder. It&#39;s always recommended when you&#39;re making any changes again. I harp on it hit save so that way your stuff does save the system does the best that it can but it doesn&#39;t always get At certain things so just make sure you do hit save so those changes stick. Below that we have our scent folder, so this is everything that has gone outbound. That&#39;s actively in your scent folder, so I have a few documents here. 27:54 Lauren C.: You&#39;ll see those stoplights and what document it is any reference numbers when it was sent current status. Below that we have our smart folders if you have. Say you&#39;re receiving a series of documents Like Home Depot I know they send the 850 which is a purchase order they send the 812 which is an adjustment they send it quite a few documents inbound some people get overwhelmed with that so to make that a little easier you can hit smart folders and you can actually create we&#39;ll go ahead and create one. 28:28 Lauren C.: So right click new folder you can put I&#39;ll just put it Home Depot since I mentioned that. You can filter by whether it&#39;s an inbound outbound joint or just inbound or outbound, so we&#39;ll hit inbound we can hit roll. So trading partner, I hope I&#39;m set up with Home Depot that I mentioned it. Yes so Home Depot and you
can have a folder Just For Home Depot or if you&#39;d like to take it a step further you can select the document type and you can have it. Set up to only show the 850s from Home Depot in this folder.
29:03 Lauren C.: So you can break that down and have it to where it literally has just the 850s here the 812 is there and whatnot so that does help break it down. Just a little bit that way if you&#39;re overwhelmed in your inbox page. There&#39;s just a lot happening you can click through these smart folders to see just your invoices your 850s all of that. Anything created you can always modify so like Nordstrom invoices you can right click on it and edit you can delete you can make any changes at any point. 29:35 Lauren C.: So that is those guys below that this is the archive so if you have anything in your inbox that you&#39;re just not wanting to see right here anymore. You can select this checkbox. And hit archive that will move it over here into the archive inbox. This can be done for any of the three folders so you have your inbox draft and sent anything you move from either of those folders will come to their corresponding folders so I archive for my inbox. 30:03 Lauren C.: It&#39;ll be in my archive end box it should sit here for only 90 days like I haven&#39;t my setting setup after that it should go ahead and move it to our deleted folder so anything in here. You can go ahead and if you&#39;re decided you want this po no longer in the archive. We just want to believe it clear things out.
We can go ahead and select action and delete or if you decided hey never mind I do actually want that you can go back to archive unarchive. 30:32 Lauren C.: And that&#39;ll put it back in your inbox or whatever the file was before so that goes the same for all of these guys. So all of these are fully accessible because they haven&#39;t been deleted yet. You can still open. And use the file itself. The last folder under here is our deleted folder this pretty much just removes the data from your account however if you ever say this 214 for Walmart you&#39;ve decided just kidding. I did not want to delete that you can come back in here and restore the document it might take a couple minutes. It does need to source the information, but it will pull that back to whichever folder that you&#39;re needing so if you ever delete anything by mistake. It is not fully gone we do save the data on the back end, so we can restore that at any point but this just helps you clean up if you do have a lot of documents in your not wanting them all sitting in your inbox draft or scent folders. 31:31 Lauren C.: So that is our archive tab. Below that you&#39;ll have the integrators. You&#39;ll see for me. I have everything highlighted. This is just a tutorial class account so I can click through those guys, but you&#39;ll see I&#39;m not abled for Global shop, so that will be great out so for you if you&#39;re not enabled for any integration. You do not have to worry about these tabs if you are set up you can go ahead and click through them. 31:57 Lauren C.: And it&#39;ll actually take you over to your settings and you can modify your setup configuration and all of that. So that is just the left side here. I&#39;m not just a general overview of those guys does anyone have any questions on these smart folders or anything over here? 32:20 Michael C.: Not yet. No, thank you. 32:22 Lauren C.: So that was the easy stuff, let&#39;s go ahead we will actually open. We&#39;ll open this 850 for dillard&#39;s that&#39;s fine, so when you double click. You should be pulled into the actual document so here. You&#39;ll have access to everything that was sent to us. We process one to one so anything sent to us will come over exactly how it came over so if the purchase order doesn&#39;t match. 32:49 Lauren C.: What you were expecting let us know we could reach out to whoever the thunder was and let him know hey I&#39;m not sure if something didn&#39;t process correctly from you guys, but here&#39;s what we&#39;re expecting and we can
help you know get that situated for you, but everything is sent to you as it&#39;s sent to us. So if 33:06 Lauren C.: there&#39;s an extra zero that you&#39;re not expecting or if it&#39;s completely wrong. We can help you and Work with your partner. So as far as this goes you&#39;ll see this po structure has all of the information here. so we&#39;ve got it&#39;s an original order what kind of order it is this may not show up on yours this varies bipartner so Just keep that in mind below that I have a purchase order number as well as a date. These two should populate over to your responses, so when you create your shipment or you create your invoice those should put Let&#39;s go ahead and scroll over here so we have our terms that is present on this purchase order so that should pull over to our invoice. 33:49 Lauren C.: Below that some partners prefer to send this text message. It really is just third terms. You do not have to worry about those unless you&#39;re curious. You can come through and grab like their email or any of that. Below that it shows we have a ship too. So it looks like this is going to be
34:07 Lauren C.: like a DC or just a store number do not worry if you do not see a name and an address here. We do have an address table on the back end so for dillards. We know where this is going so when you create your shipment. We should have the address for you if for any reason it&#39;s a new store new location udc and it&#39;s not populating you&#39;ll get a notification up here this will go red go
ahead and click it. 34:33 Lauren C.: And it&#39;ll give you the actual error. So if it says not located can&#39;t find this address on the sdq table. Copy that error email it over to support and we&#39;ll go ahead and help you out with getting that address added. Over to the right this field may vary you may get this you may not it just kind 34:52 Lauren C.: of depends on what the requirements are for your return document so for this one. We have the party for whom. It&#39;s ultimately intended since this is a dropship order. It&#39;s going directly to someone so that is why that is there. Below that you should have all of your item details. So this will give you. For this one we have just one item. It&#39;ll have the UPC the quantity that they ordered and the price. 35:19 Lauren C.: So always recommended like I say go through make sure you&#39;re this is the expected quantity make sure this is the agreed upon pricing. I know sometimes in the beginning it can be a little bumpy. I&#39;ve had a few customers where you know they agree it upon 150, but the purchase order says 1:30. So we&#39;ve had the help them go back and forth with their partner to go ahead and make sure that everything is set up correctly and it is the agreed upon price. 35:48 Lauren C.: Below that they sent over some part numbers do not worry if you&#39;re purchase
35:52 Lauren C.: order does not have these the more information the better, but it&#39;s not always necessarily required so you&#39;ll see all of these guys here. And then it&#39;ll show so at the top. We had what looks like. It was a DC this is going directly to a different location before it goes directly to a person as well as the tracking information. Alright, so this is just a preview of a purchase order if we go up to the top this respond button will show you every document that you have access to create for this class. We are going to just do the 856 and the 810. So the first thing you would do is this 856.
36:33 Lauren C.: We are going to do the pick and pack option for this instance autopack is available I can show you the catalog here in a second if you&#39;re ever wanting to
36:44 Lauren C.: autopack say it&#39;s two items on a purchase order they&#39;ll be packed the exact same every single time but they have to be broken into like 50 buckses and you don&#39;t want to go one by one I can show you in the catalog how to set that up in a moment so for this let&#39;s go ahead and hit pick and pack. The
first screen that pops up it&#39;s going to ask you how many cartons I&#39;ll go ahead and put two so this way you can see the two different ones and hit next.
37:25 Lauren C.: Already so on our left side here. These are items that well item that&#39;s available on the purchase order the quantity that came in and the quantity that we have left so if you&#39;ve already packed an asn before incent it it should be updating this quantity, so if you&#39;re doing a shipment that&#39;s broken up into three you&#39;d have the updated quantity here that just helps you out a little bit
37:46 Lauren C.: if there are multiple items here. You&#39;ll have view of here&#39;s line one here&#39;s the UPC all of that so let&#39;s go ahead we&#39;ll grab one item. It&#39;ll ask you how many you&#39;d like to pack we&#39;ll go ahead and just put one and okay. Perfect so over here on the right we have two packs so I have pack 1 and I have packed it with one item. I&#39;ll go ahead and grab. I know our quantity will be negative one. That&#39;s okay. It&#39;s just for testing. I&#39;ll go ahead and pack one more. 38:21 Lauren C.: and okay Already so on this right side. You&#39;ll see that We Created two packs and we&#39;ve packed both with the quantity of one so right now. I show that everything has been packed but all of my packs are filled we can go ahead and hit finish or if you&#39;ve decided you don&#39;t want these items in separate packs. You can delete the second pack and you can actually move that item into here. So you could pack this as two and one pack or in this instance. I have two packs with one. So let&#39;s go ahead and hit finish. 38:55 Lauren C.: This will pull us into the 856 that has been built based off of the dillard&#39;s requirements so anything in blue is required anything in white is optional or if there&#39;s a little eye over here to the right go ahead and hover over so it&#39;s this one becomes required if we have. Like a load ID for their TMS system, so for me if I don&#39;t have a load ID I don&#39;t have to use that field. 39:24 Lauren C.: So at the top your document should always be original unless you have to resend it shipments normally do not have to be resent so for them.
They don&#39;t even
39:33 Lauren C.: allow an update or replacement so just original. Shipment identification this is completely up to you. You can put anything here for reference whether it&#39;s a PO number you could use a tracking number a BL number anything in there. Completely up to you the date. It&#39;ll be based off of the date that the document was generated on so if you created this say today, but you don&#39;t need to send it till Thursday you can hit save leave it in your draw folder come back and modify that at any point anything on here can be changed up until you hit that sun button. 40:09 Lauren C.: So this will be the date and time below that will be your structure. This is how your 856 is set up so the top is the shipment details order details package and item. You shouldn&#39;t have another option there some cases are special but for the most part it should automatically hardcode that for you. Below that we have our shipment level so this is our packing details how I&#39;m sending it, so I only have carton enabled. 40:36 Lauren C.: How many of the leading quantity are there I can put my gross weight my unit of measure this one usually does have more options for this testing scenario. They&#39;re just aren&#39;t codes enabled, but they&#39;re usually are more so if you do pounds cases any of that you can select those there. My scac this is my carrier code if you&#39;re using like FedEx ground you would use the fedg if you
aren&#39;t sure you can actually hit this little wizard over here the little wand. 41:08 Lauren C.: We have gone through and pulled together a list for you. So there&#39;s a lot of common carriers a few trucking companies that we&#39;ve seen before we&#39;ve copied those codes over so if you&#39;re not sure you can scroll through and look through this. List here and see if your trucking companies there if it is great you can go ahead and hit insert and it&#39;ll pull that code for you. 41:35 Lauren C.: Over to the right, this is the reference information again it may look different on your side. Just make sure anything in blue is filled out if it is white go ahead and hover make sure you don&#39;t need it. That&#39;ll really just depend on your partner. For me I have contact information this would be whoever&#39;s on the edi side and their email address. So I would fill that in. Below that I have my ship, too. 42:03 Lauren C.: We have the party for whom. It&#39;s ultimately intended which should be the same so since this isn&#39;t required it does not have to be filled in we would just need to make sure that if there&#39;s an assigned by buyer code that was sent on your po go ahead and pull that over. Or for this instance it wants a DC number so we would just put a DC coat there
42:23 Lauren C.: if you&#39;re ever unsure and it has a little eye go ahead click on those and just take a look. Your ship from so this is something that you can default so this would be your information if it&#39;s the same every single time you
can go ahead and put that in your document default and that should populate for me it looks like I do already have that set up so training tutorial once you through fake Street and the Woodlands that will always pop up for me. 42:51 Lauren C.: So that is our address details below that we have our order so you&#39;ll see that the purchase order number did pull through from rpo the date did also come believe it set yes, so six seven is the actual date on there so that pulled over. Next to that we have the buying party this was also on your po so this also should pull for you. So you shouldn&#39;t have to type that each time if it isn&#39;t let us know I can go through and help you get these mapped. 43:23 Lauren C.: So that way we can make sure everything on your purchase order is actually pulling from the po and onto your response documents. Below that we have the actual packs, so you&#39;ll see I have pack one and I have packed two within pack 1 I have a quantity of one and it&#39;s in each is. Below that it did pull all of the part numbers from the purchase order these may not necessarily be required. So you can send them if you&#39;d like or if it says over here on the right. It&#39;s used on the packing slip go ahead and leave those there that way when you create your label. It&#39;ll all pull over for you so for me at the bottom. All I would need to fill out is this tracking number if it is required? So yes for this one since it&#39;s going directly to someone it shows that it is required for dropship. Which is Direct and so I would need to put a tracking number for each pack. 44:17 Lauren C.: And then that would be it so scroll back through make sure everything in blues filled out white if it&#39;s conditional or you&#39;re needing to add it based off of an additional requirement. Otherwise, I always say go ahead hit save. And that would be it for your actual 856, so you&#39;ve already built it you&#39;ve
44:35 Lauren C.: created all of your information put it in there so tracking details all of that you can stop here and save it as a draft so like I said if you&#39;re sending it two days. You just wanted to get this pre-done and just ready to hit send you can hit save and close out with this little x and just leave it for now. It&#39;ll sit in your draft folder until you&#39;re ready. 44:57 Parnell : Hi, this is John where do you add freight for the what it cost to ship to the end user is there a line for that? 45:05 Lauren C.: That&#39;s usually on your invoice um this would just notify them. So this is just a notice of here&#39;s what&#39;s coming here&#39;s the tracking details and dates. 45:15 Parnell : Got it all right. 45:16 Lauren C.: So no worries for this one. Yes, so this usually doesn&#39;t have any pricing on it this just says who you are where it&#39;s coming from where it&#39;s going and when it&#39;s expected so all of that if you need to ever edit. 45:35 Parnell : Sorry, go ahead. 45:36 Lauren C.: Now you&#39;re okay already, so if it was an a draft state you come back and two days later. You&#39;ve decided like I said you don&#39;t want to separate packs. You&#39;ve only created one tracking number you just want all this. You can hit edit packing you can change that within there so you can delete that second pack put the item back into pack one. So you have a quantity of two. And hit save and it&#39;ll fix that for you everything else will stay the same. It&#39;ll just update those two
packs at the bottom. 46:04 Lauren C.: If you ever need to add items or delete items you can click those buttons and then the next one would be this print label button. I always recommend starting there some people prefer to click whether it&#39;s a mixed label master label. I always say start with the print label. It&#39;ll automatically print out whatever label. You&#39;re set up for so you shouldn&#39;t have to click through to those other guys unless you&#39;re already enabled for. 46:26 Lauren C.: Them if you are not the user that will be actually physically printing from your computer you can hit this email label to me button. That&#39;ll email it to you and it can go. Anywhere after that so it comes over in a PDF you can send it to whoever is printing it. I&#39;ve had some users where someone&#39;s in the office, but somebody&#39;s actually in the warehouse doing the shipment. So they&#39;ll do the email to me and send it over to that person. 46:52 Lauren C.: Over here we have the print button once your document is fully filled out you can wait till you hit send or you can print from here as well this will just give you a PDF of this file here some people keep it for you know just record keeping you can print that at any time.
47:09 Lauren C.: And then additional documents you have access to create a packing slip packing list any of these guys here based off of what you filled out on the 856. Just make sure it&#39;s done before you click this we&#39;ll all the numbers do
populate appropriately. So, this is the shipment we&#39;re done. We&#39;ve printed our labels. Everything is ready all you have to do is hit send and from there. You are good. Just make sure to send your items to your trading partner. 47:41 Lauren C.: Alrighty, so let&#39;s go back to our purchase order. We&#39;ll hit that respond button again and let&#39;s look at an invoice. Remind me look a little different from yours. They all have pretty much the same structure the original bones, but the fields that populate may be a little different so let me know if you have any questions if you&#39;re looking at yours, we can try to work through those. 48:03 Lauren C.: So same thing once you generate you&#39;ll see all of these fields that populate anything in blue is required anything in White might be conditional so if you&#39;re giving a discount. I would have to have a percentage. We&#39;ll go down there in a second so the invoice state again will be the date you generated the document if you need to save this and come back to it later. You can always come back and change this at any point. 48:27 Lauren C.: Your invoice number is just like your shipment ID it is something that you&#39;ve created. It is completely up to you. It is just a reference you could use the purchase order number you can use your UPS tracking number you could put anything in that field. It&#39;s just a reference number so don&#39;t stress about that. Below that we have the purchase order date that should pull over from the po again so June 7th is what we had on here. 48:53 Lauren C.: And your purchase order number so that should pull over yes, so it came over on the 850 that should come over here unless you&#39;ve been notified. Otherwise you should just send it as it&#39;s been received. So it should pull that for you. Below that I just have the buying party it just needs to pull over that 26 for us nothing else is required here yours may still have you know an address name and all of that everything should be coming from the po for those since that&#39;s who you&#39;re sending it to but you can always modify so if they have you update an address for the actual billing you can come in and update that as well. 49:36 Lauren C.: below that we have our terms of sale, this is pretty generic this is Should look about the same on yours so your terms type code you can do 10 days after the end of month end of month basic discount offered or discount some people just have basic up here at the top you can use that that&#39;s the most common. There&#39;s nothing next to it or for this one since there&#39;s no discount. I&#39;ll
just select that. 50:06 Lauren C.: Our Type code is invoice date. So it&#39;ll go based off of the invoice date and 60 days after for me or if yours is 30 45 90 you can always change that if you are adding a discount you would go ahead and put that in here. You&#39;ll see that. They&#39;re white but if you&#39;re using a percentage you will also need to put a date. So if you&#39;re offering a discount, we&#39;ll do discount offered and you&#39;re doing like a 2% discount make sure you put when it would be required by so if they pay within 45 days they&#39;ll get 2% Off of that otherwise everything standard if they pay on the 60th or after. 50:50 Lauren C.: The shipment date that may be there that may not be I just depends on the partner that should pull over from the 856 that you&#39;ve created last so since I
50:59 Lauren C.: created one today. It&#39;ll show today&#39;s date. Below that you will see the actual item the quantity and the price these came from the po so it should match but always recommended go ahead and hop back over make sure they are matching. And then you can continue the descriptions numbers those should populate for you as well. If they were provided on the purchase order. I know sometimes they&#39;ll ask for description and it wasn&#39;t sent on the PO you can go ahead and default this to put anything that you&#39;d like in there or each Time You generate an invoice you can put you know any details that you&#39;re wanting there.
51:40 Lauren C.: Already so at the bottom we have our summary currently. I show that my amount is 51:46 Lauren C.: 13 30. They&#39;re asking for my carrier details so that was that FedEx ground so for doing FedEx ground you would put our code there. What kind of reference number we&#39;re using? and the actual reference number And then like you had mentioned earlier your your freight code. So if you&#39;re needing to have an allowance or charge. This is where you would add that so for that you would add charge. 52:14 Lauren C.: And then below that we can have well years maybe a little different but you should have codes that populate based off of what your partner is set up for you. So I&#39;ll just go ahead and do postage. Okay, so we&#39;ll just use postage as an example your should have like shipping free all of that enabled the amount so if you need to add, let&#39;s say. A hundred dollars that will go ahead and add into the actual total here. 52:45 Lauren C.: You can adjust if it&#39;s to be paid by the customer now or off invoice and then you can also put a description here so if you&#39;re wanting to put this is for shipping. You would go ahead and type that there so for me. I have charge will pretend that says shipping for $100 and to be paid by the customer so you&#39;ll have your total plus your charge and that brings us to 1430. 53:12 Lauren C.: So for me, I always go back through scroll make sure anything in blue is filled
53:17 Lauren C.: out so I need to put something there. Our description is put description. and safe So if you&#39;re done and you&#39;re ready to send great you can go ahead and hit the sun button. If not you can hit save that will update in the draft folder for you and you can come back to it at any point. Um same like the 856 you do have an add item and delete item and you also have this print button, so you can print this as a PDF if you&#39;re wanting to keep it as a physical copy or save just the PDF on your computer. But that is the invoice we&#39;re good to hit send and we would be good to go. 53:57 Lauren C.: So those are the two document types that you can create that we just went over does anyone have any questions on those two guys. Already well, let&#39;s hit we&#39;re going to look at the catalog. So within the catalog you can set up your actual items like I said earlier. Let&#39;s look at publix.
54:22 Lauren C.: So this item for publix if I&#39;m creating an 856 and say we need to have 10 of this item per box and however many items per palette so if we can have. I&#39;ll just keep it the same so 10 items can fit in the box and 10 boxes can fit on this palette as long as these two Fields are filled out when you create your right 56. You&#39;ll be able to hit the autopack and the system will generate that for you. So we wouldn&#39;t have to go through that pick and pack where you drag from one side to the other you can go ahead and set this up. It&#39;ll do that and bypass it. 54:58 Lauren C.: Um just because you bypass doesn&#39;t mean you can&#39;t go back in you&#39;ll still have that edit packing button and you can go back through and set it up. So this is all you would need make sure you have some form of description for these two and then make sure whatever number that&#39;s coming over from the purchase order is visible here on the actual catalog this way. They can go one to one and they&#39;ll map to each other. 55:23 Lauren C.: So go ahead and save so that&#39;s the catalog. That&#39;s how you can add or edit we have an add delete import and export so add item. We&#39;ll give you this template where you can type in as much information as you want nothing here is required. So you can put any field that you&#39;re wanting here. So I could just put a skew description isn&#39;t required but we could put something. 55:51 Lauren C.: Trading partner you can make it trading partner specific or you can leave it set to all and then it&#39;ll apply to every purchase order where response
document that you receive or create. Below that you&#39;ll have the option to hit this little Plus button and you can add your part numbers. So, let&#39;s see I&#39;ll do UPC one-5-5 this just equals 11, so your UPC number just needs to be 11 characters there. There are different Fields so if yours as longer than that you can select the 1551 or anything that matches up with what you&#39;re needing to create. Make sure you add your quantities. And hitting save and that will add it to your catalog for you. 56:38 Lauren C.: Your import if you do already have a file set up for all of your items you can go through and this is a step-by-step. You can do the same thing all or make it trading partners specific. This will tell you what to title the columns on your spreadsheet, so make sure if you select this one column does say UPC and you have your upcs under that and so on so you would just go ahead and make sure you have these four or however many that you&#39;re pulling from so if you have your vendor seller number. 57:12 Lauren C.: your Buyer item number that&#39;s a common one as well. Let&#39;s see we&#39;re just gonna keep grabbing and the EA in you can put all of these here make sure that these are the titles of all of your columns and keep going and you can actually import through this so you wouldn&#39;t have to go through and create something each time it would actually import for you and then all you&#39;d have to do is just come in and if you&#39;re trying to do autopack you can. Add these but everything should pull in from that file. 57:43 Lauren C.: If you&#39;re ever wanting to create you can check all of these guys and hit the export button. It&#39;ll actually create a file for you up here. I believe it creates a CSV so you can export it to you know Google sheets, Excel any of that. We&#39;ll go ahead and check my item that I created when you hit new document. It&#39;ll give you these two Fields so let&#39;s do create an eight ten. 58:10 Lauren C.: And we&#39;ll just I know I keep picking on advanced auto and AutoZone AutoZone there at the top once I click this. It&#39;ll pull over. The information that I just put in the catalog for this item. So if you don&#39;t have the po I know some trading partners. Don&#39;t always send it through edi but they want their invoice this is a quicker way to go ahead and pull that item and generate a purchase order for you or an invoice for you. So it should be pulling all of your descriptions and anything that you had enabled I didn&#39;t put a UPC or anything so
it&#39;s not pulling that but it should pull whatever you enable for that. 58:48 Lauren C.: so you can generate any document type from here some people use this if you&#39;re like with tractor supply you can create the Oh, it&#39;s not letting me search the 846 is one of the document types that is for like inventory. You can create the inventory option there too. So this is just the catalog this is just a preview of what it can look like if you do have items in there. You can always come in and make changes at any point whether it&#39;s to add update or remove. 59:20 Lauren C.: So that I believe that is everything I tried to touch on everything. Sorry. I know it is a lot of information, but is there anything you guys want to look at or have any further questions on? 59:42 Parnell : Hey Lauren this has been puree bamboo. I have a quick question now. This is all 59:43 Lauren C.: Yes. 59:48 Parnell : new to us. This is new territory for us. So when we get a real purchase order Is there somebody that&#39;s going to be able to work with us basically holding our hands as we navigate this whole process and you&#39;re doing a great job in showing us all of this but until we get a real purchase order from our customer and do the whole process then we would get a better feel of this. 01:00:15 Lauren C.: And that makes sense yeah, this is just showing you it is very different you know just watching and seeing versus actually getting to put your hands on it and doing it. You&#39;re welcome to call in to support. We can do a phone call with 01:00:23 Parnell : right 01:00:28 Lauren C.: you and walk you through it or if you&#39;d like we do have the option to set up like a zoom call or a Google meets and we can go through it and watch you on your screen and help you out. 01:00:36 Parnell : Great yeah, because this is you know something to alleviate the stress. I&#39;m glad that you&#39;re telling me that thank you. 01:00:44 Lauren C.: Of course yes sir, so you can go ahead you can always call or if it&#39;s easier you can just email us and just ask us if we can set up a call with you and we can send a link back in a timeframe. 01:00:54 Parnell : awesome Great, thank you. Thank you. 01:00:58 Lauren C.: Of course of course do you guys have any other questions?
01:01:04 Parnell : Oh, you did a good job so far. I mean we&#39;re getting an overview and understanding of the whole process is just that when once we get a purchase order and then we have that purchase or then we can understand what&#39;s on it and all the things the information with the 856 you know the 810s and all that. 01:01:21 Lauren C.: Yes. 01:01:22 Parnell : would be 01:01:23 Lauren C.: So this is like you said it is just an overview it. Just lets you see it before you actually put your hands on it, but just remember bullet points anything in blues required check the little information tabs, but like I said let us know when you receive it you can give us a call. We can schedule a call. We can help you out. You&#39;re not by yourself. 01:01:41 Parnell : Yeah, and it&#39;s tutorial that we received with Touch I was looking at it. You know a lot of this stuff you know. Regarding anything blue is it at one time set up or changes every time? 01:01:55 Lauren C.: So, this is the same structure every single time. 01:01:59 Parnell : So can we can we preload everything and only have to do it once? 01:02:02 Lauren C.: Yes sir, so if you have the fields over here like I&#39;d shown you on the defaults you can go in even on the A56 any document is available for defaults so you can come in Select Your trading partner come over here if there&#39;s a field that you&#39;re gonna keep the same. 01:02:18 Parnell : okay 01:02:19 Lauren C.: Every single time let us know if it&#39;s not enabled I can open that for you and this way. It&#39;ll stay the same and you&#39;ll have that option and if it ever changes go back to your defaults update it and it&#39;ll be ready for you the next document. 01:02:32 Parnell : Perfect that&#39;s awesome 01:02:38 Lauren C.: already All right guys well, that&#39;s all I have for you. So it really is just Q&amp;A if anyone has questions I&#39;m here. 01:02:45 Parnell : And we ask our questions, but then like I said once we get the real thing then we&#39;re gonna have a lot of questions because then we&#39;re looking at the actual purchase order data. That is required and that&#39;s when you&#39;re gonna
have a lot of questions and we gonna need somebody to all the hands to go through this and you know for the first few and then we&#39;re ready to save after that. 01:03:10 Lauren C.: Yes. 01:03:10 Parnell : And you only you only do the purchase order you don&#39;t print a packing slip correct. 01:03:16 Lauren C.: do I um are you talking about on your 01:03:18 Parnell : That there&#39;s a yeah. There&#39;s a score. Yeah, this do you guys provide a packing slip. 01:03:24 Lauren C.: Yes. So when you create your eight 56 under additional documents there is the option for packing slip in packing list. 01:03:34 Parnell : Stage your answer okay, so we only said you know a system right but if they do the packing so you can still let it go and then we can put the order on leg. But no no, I have the label stays level involves that has to automatic email alright, they answered the question. Thank you. 01:03:54 Lauren C.: Of course of course. 01:03:56 Parnell : I&#39;ll be a look at the packing slip we had printed and look at it and see I thought yeah non-profen. Yes, so what we&#39;ll do because we have to bring those 850s and Put them into our backend system, so that we can generate with the UPS FedEx all of that we&#39;ll look at your backing slip and we can generate our information and then compare it to your packing slip and then we don&#39;t even need to print the packings, but we can use your packing slip to do the shipment you have to see what&#39;s coming. Yeah. That&#39;s right. I would try to make it simple as possible. 01:04:32 Lauren C.: Yes, so all in one. Yes, if you like that that preview how it looks you can print it directly from here. It&#39;ll print based off of how it set up on the 856. 01:04:40 Parnell : All right okay all right. Thank you. 01:04:43 Lauren C.: Now of course alrighty well, that&#39;s all I have for you guys today this class runs every Monday so you guys are welcome to sit in at any point but like I said if you want to schedule a call once you do receive it. You can go through contact support sun doesn&#39;t email we can send you a link back and let&#39;s usually the easier way or you can go ahead and just give us a call and we can
email you
01:05:04 Lauren C.: directly either way, but just let us know how we can help okay guys. 01:05:09 Parnell : Thank you, so we&#39;re live right now with buyers so we can expect 997 at any time it gives a purchase order I hit 50 that hit your site right. 01:05:22 Lauren C.: Yes, so you should be receiving if you went through your settings and you set up the notifications. You should be getting those emails for are those. 01:05:28 Parnell : okay 01:05:32 Lauren C.: Already guys well, that is all I have for today. So I hope you all have a great day. Let us know if you need anything at all. 01:05:40 Parnell : Thank you. 01:05:41 Lauren C.: Of course of course have a good day guys, you are most welcome. 01:05:41 Michael C.: Thank you. 01:05:43 Parnell : Thank you. Thank you. 01:05:44 Michael C.:
View original transcript at Tactiq.</p>
