<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EDI Support Tool - Claude Desktop Integration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
        }

        /* API Status Indicators */
        .status-bar {
            position: fixed;
            top: 0;
            right: 0;
            display: flex;
            gap: 10px;
            padding: 10px;
            z-index: 1000;
        }

        .status-indicator {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator.connected {
            background: #48bb78;
            color: white;
        }

        .status-indicator.disconnected {
            background: #e53e3e;
            color: white;
        }

        .status-indicator.connecting {
            background: #ed8936;
            color: white;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-container {
            display: flex;
            gap: 20px;
            padding: 60px 20px 20px;
            max-width: 1800px;
            margin: 0 auto;
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            padding: 30px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .ticket-panel {
            width: 350px;
        }

        .claude-panel {
            width: 400px;
        }

        .flow-panel {
            flex: 1;
        }

        h1, h2, h3 {
            color: #2a5298;
            margin-bottom: 20px;
        }

        h1 { font-size: 2em; }
        h2 { font-size: 1.5em; }
        h3 { font-size: 1.2em; }

        /* Claude Panel Styles */
        .claude-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .claude-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .claude-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .claude-btn {
            flex: 1;
            padding: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .claude-btn:hover {
            transform: translateY(-2px);
        }

        .claude-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .claude-btn.secondary {
            background: #718096;
        }

        .claude-queue {
            margin-bottom: 20px;
        }

        .queue-item {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            position: relative;
        }

        .queue-item.processing {
            border-color: #5a67d8;
            background: #e9ecef;
        }

        .queue-item.completed {
            border-color: #48bb78;
            background: #f0fff4;
        }

        .queue-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .queue-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            background: #e2e8f0;
            color: #4a5568;
        }

        .queue-status.pending { background: #fef3c7; color: #92400e; }
        .queue-status.processing { background: #dbeafe; color: #1e40af; }
        .queue-status.completed { background: #d1fae5; color: #064e3b; }

        .queue-content {
            font-size: 14px;
            color: #4a5568;
        }

        .claude-response {
            background: #f0f4ff;
            border: 1px solid #5a67d8;
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
        }

        /* Request History */
        .history-section {
            margin-top: 20px;
        }

        .history-item {
            background: #f7fafc;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .history-item:hover {
            background: #e9ecef;
        }

        .history-time {
            font-size: 12px;
            color: #718096;
        }

        /* Other existing styles... */
        .ticket-input {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 14px;
            resize: vertical;
            font-family: inherit;
        }

        .analyze-btn {
            width: 100%;
            padding: 15px;
            margin-top: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .analyze-btn:hover {
            transform: translateY(-2px);
        }

        .analyze-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #5a67d8;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #fed7d7;
            border: 1px solid #fc8181;
            color: #742a2a;
            padding: 12px;
            border-radius: 8px;
            margin-top: 10px;
            display: none;
        }

        .success-message {
            background: #c6f6d5;
            border: 1px solid #68d391;
            color: #22543d;
            padding: 12px;
            border-radius: 8px;
            margin-top: 10px;
            display: none;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-indicator disconnected" id="apiStatus">
            <span class="status-dot"></span>
            <span id="apiStatusText">API Offline</span>
        </div>
        <div class="status-indicator disconnected" id="claudeStatus">
            <span class="status-dot"></span>
            <span id="claudeStatusText">Claude Offline</span>
        </div>
    </div>

    <div class="main-container">
        <!-- Ticket Panel -->
        <div class="panel ticket-panel">
            <h2>📋 Ticket Analyzer</h2>
            
            <textarea class="ticket-input" id="ticketInput" placeholder="Paste your support ticket here...

Example:
Customer: Walmart
Issue: Missing 850 Purchase Orders
Error: No documents received since yesterday"></textarea>
            
            <button class="analyze-btn" id="analyzeBtn" onclick="analyzeTicket()">Analyze Ticket</button>
            
            <div class="loading" id="loadingIndicator">
                <div class="spinner"></div>
                <p>Analyzing ticket...</p>
            </div>
            
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
        </div>
        
        <!-- Claude Panel -->
        <div class="panel claude-panel">
            <div class="claude-header">
                <h2>🤖 Claude Assistant</h2>
                <div class="claude-status">
                    <span id="claudeQueueCount">0 pending</span>
                </div>
            </div>
            
            <div class="claude-actions">
                <button class="claude-btn" onclick="sendToClaude()" id="sendToClaudeBtn">
                    Ask Claude
                </button>
                <button class="claude-btn secondary" onclick="clearClaudeQueue()">
                    Clear Queue
                </button>
            </div>
            
            <div class="claude-queue" id="claudeQueue">
                <!-- Queue items will be displayed here -->
            </div>
            
            <div class="history-section">
                <h3>Recent Requests</h3>
                <div id="requestHistory">
                    <!-- History items will be displayed here -->
                </div>
            </div>
        </div>
        
        <!-- Flow Panel -->
        <div class="panel flow-panel">
            <h1>EDI Document Flow Map</h1>
            
            <div id="flowContent">
                <!-- Flow visualization will be displayed here -->
            </div>
        </div>
    </div>

    <!-- Include Claude Bridge Client -->
    <script src="claude_bridge_client.js"></script>
    
    <script>
        // Initialize Claude Bridge Client
        let claudeClient = null;
        let currentRequests = new Map();
        
        // Initialize on page load
        window.addEventListener('DOMContentLoaded', () => {
            initializeClaudeBridge();
            checkAPIStatus();
            loadRequestHistory();
            
            // Check status every 5 seconds
            setInterval(checkAPIStatus, 5000);
            setInterval(updateQueueStatus, 2000);
        });
        
        // Initialize Claude Bridge
        function initializeClaudeBridge() {
            try {
                claudeClient = new ClaudeBridgeClient({
                    apiUrl: 'http://127.0.0.1:8001',
                    wsUrl: 'ws://127.0.0.1:8001/ws'
                });
                
                // Register event handlers
                claudeClient.on('connected', () => {
                    updateClaudeStatus('connected');
                    console.log('Connected to Claude Bridge');
                });
                
                claudeClient.on('disconnected', () => {
                    updateClaudeStatus('disconnected');
                    console.log('Disconnected from Claude Bridge');
                });
                
                claudeClient.on('new_request', (data) => {
                    console.log('New request:', data);
                    addToQueue(data.request_id, data.type);
                });
                
                claudeClient.on('request_processing', (data) => {
                    console.log('Request processing:', data);
                    updateQueueItem(data.request_id, 'processing');
                });
                
                claudeClient.on('request_completed', (data) => {
                    console.log('Request completed:', data);
                    updateQueueItem(data.request_id, 'completed', data.response);
                    loadRequestHistory();
                });
                
                claudeClient.on('error', (error) => {
                    console.error('Claude Bridge error:', error);
                    showError('Claude Bridge error: ' + error.message);
                });
                
            } catch (error) {
                console.error('Failed to initialize Claude Bridge:', error);
                updateClaudeStatus('disconnected');
            }
        }
        
        // Check API status
        async function checkAPIStatus() {
            // Check main API
            try {
                const response = await fetch('http://127.0.0.1:8000/api/status');
                if (response.ok) {
                    updateAPIStatus('connected');
                } else {
                    updateAPIStatus('disconnected');
                }
            } catch {
                updateAPIStatus('disconnected');
            }
            
            // Check Claude Bridge API
            try {
                const response = await fetch('http://127.0.0.1:8001/');
                if (response.ok) {
                    updateClaudeStatus(claudeClient && claudeClient.isConnected() ? 'connected' : 'connecting');
                } else {
                    updateClaudeStatus('disconnected');
                }
            } catch {
                updateClaudeStatus('disconnected');
            }
        }
        
        // Update status indicators
        function updateAPIStatus(status) {
            const indicator = document.getElementById('apiStatus');
            const text = document.getElementById('apiStatusText');
            
            indicator.className = `status-indicator ${status}`;
            text.textContent = status === 'connected' ? 'API Online' : 'API Offline';
        }
        
        function updateClaudeStatus(status) {
            const indicator = document.getElementById('claudeStatus');
            const text = document.getElementById('claudeStatusText');
            
            indicator.className = `status-indicator ${status}`;
            text.textContent = status === 'connected' ? 'Claude Online' : 
                              status === 'connecting' ? 'Claude Connecting' : 'Claude Offline';
        }
        
        // Send to Claude
        async function sendToClaude() {
            const ticketInput = document.getElementById('ticketInput').value;
            
            if (!ticketInput.trim()) {
                showError('Please enter ticket information first');
                return;
            }
            
            if (!claudeClient || !claudeClient.isConnected()) {
                showError('Claude Bridge is not connected');
                return;
            }
            
            const btn = document.getElementById('sendToClaudeBtn');
            btn.disabled = true;
            
            try {
                const result = await claudeClient.analyzeEDI(ticketInput, '', {
                    priority: 5,
                    waitForResponse: false
                });
                
                currentRequests.set(result.request_id, {
                    type: 'analyze',
                    data: ticketInput,
                    created: new Date()
                });
                
                addToQueue(result.request_id, 'analyze');
                showSuccess('Request sent to Claude');
                
            } catch (error) {
                showError('Failed to send to Claude: ' + error.message);
            } finally {
                btn.disabled = false;
            }
        }
        
        // Queue Management
        function addToQueue(requestId, type) {
            const queue = document.getElementById('claudeQueue');
            
            const item = document.createElement('div');
            item.className = 'queue-item pending';
            item.id = `queue-${requestId}`;
            item.innerHTML = `
                <div class="queue-item-header">
                    <strong>${type.toUpperCase()}</strong>
                    <span class="queue-status pending">Pending</span>
                </div>
                <div class="queue-content">
                    Request ID: ${requestId.substring(0, 8)}...
                </div>
            `;
            
            queue.insertBefore(item, queue.firstChild);
            updateQueueCount();
        }
        
        function updateQueueItem(requestId, status, response) {
            const item = document.getElementById(`queue-${requestId}`);
            if (!item) return;
            
            item.className = `queue-item ${status}`;
            const statusEl = item.querySelector('.queue-status');
            statusEl.className = `queue-status ${status}`;
            statusEl.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            
            if (status === 'completed' && response) {
                const responseDiv = document.createElement('div');
                responseDiv.className = 'claude-response';
                responseDiv.innerHTML = `<strong>Claude's Response:</strong><br>${formatResponse(response)}`;
                item.appendChild(responseDiv);
            }
            
            updateQueueCount();
        }
        
        function updateQueueCount() {
            const pending = document.querySelectorAll('.queue-item.pending').length;
            const processing = document.querySelectorAll('.queue-item.processing').length;
            const total = pending + processing;
            
            document.getElementById('claudeQueueCount').textContent = 
                total > 0 ? `${total} active` : '0 pending';
        }
        
        function clearClaudeQueue() {
            if (confirm('Clear all completed requests from the queue?')) {
                const completed = document.querySelectorAll('.queue-item.completed');
                completed.forEach(item => item.remove());
                updateQueueCount();
            }
        }
        
        // Update queue status periodically
        async function updateQueueStatus() {
            if (!claudeClient) return;
            
            for (const [requestId, request] of currentRequests) {
                try {
                    const status = await claudeClient.getRequestStatus(requestId);
                    
                    if (status.status === 'processing') {
                        updateQueueItem(requestId, 'processing');
                    } else if (status.status === 'completed') {
                        updateQueueItem(requestId, 'completed', status.response_data);
                        currentRequests.delete(requestId);
                    }
                } catch (error) {
                    console.error(`Error checking status for ${requestId}:`, error);
                }
            }
        }
        
        // Load request history
        async function loadRequestHistory() {
            if (!claudeClient) return;
            
            try {
                const history = await claudeClient.getHistory(10);
                const historyDiv = document.getElementById('requestHistory');
                historyDiv.innerHTML = '';
                
                history.requests.forEach(request => {
                    const item = document.createElement('div');
                    item.className = 'history-item';
                    item.onclick = () => showRequestDetails(request.id);
                    
                    const time = new Date(request.created_at).toLocaleString();
                    item.innerHTML = `
                        <div><strong>${request.type}</strong> - ${request.status}</div>
                        <div class="history-time">${time}</div>
                    `;
                    
                    historyDiv.appendChild(item);
                });
                
            } catch (error) {
                console.error('Error loading history:', error);
            }
        }
        
        // Show request details
        async function showRequestDetails(requestId) {
            try {
                const response = await claudeClient.getResponse(requestId);
                
                if (response.status === 'completed' && response.response) {
                    alert('Claude\'s Analysis:\n\n' + JSON.stringify(response.response, null, 2));
                } else {
                    alert(`Request Status: ${response.status}`);
                }
            } catch (error) {
                showError('Error loading request details');
            }
        }
        
        // Format Claude's response
        function formatResponse(response) {
            if (typeof response === 'string') {
                return response;
            }
            return JSON.stringify(response, null, 2);
        }
        
        // Basic ticket analysis (local)
        function analyzeTicket() {
            const ticketInput = document.getElementById('ticketInput').value;
            
            if (!ticketInput.trim()) {
                showError('Please enter ticket information');
                return;
            }
            
            // For now, just send to Claude
            sendToClaude();
        }
        
        // UI Helper functions
        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            setTimeout(() => {
                errorEl.style.display = 'none';
            }, 5000);
        }
        
        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            setTimeout(() => {
                successEl.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>