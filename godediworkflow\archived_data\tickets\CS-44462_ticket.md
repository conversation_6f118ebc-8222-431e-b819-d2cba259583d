# CS-44462: Fwd: Amazon EDI Monitoring – Your EDI message failed to process in Amazon Systems

## Ticket Information
- **Key**: CS-44462
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-05-28T13:48:04.092-0600
- **Updated**: 2025-05-29T06:48:21.728-0600
- **Customer**: <EMAIL>

## Description
*{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}
 
 
 
 
Hi <PERSON>, 
  
I attempted to Invoice some existing Dropship orders from within WebEDI and Amazon sent back these errors. 
Hopefully this points you and the VAN team in a direction to make some progress on this issues. 
Please confirm you received this.
 
 
 
---------- Forwarded message ---------
 From:  *Amazon Vendor Central*  <[<EMAIL>|mailto:<EMAIL>]>
 Date: Wed, May 28, 2025 at 2:46 PM
 Subject: Amazon EDI Monitoring – Your EDI message failed to process in Amazon Systems
 To: <[<EMAIL>|mailto:<EMAIL>]> 
{color:#5a5a5a} {color} 
|{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a} Vendor code: VendorContactId:83a10f2e-f9bc-4045-894d-2347518c3d78_48e61cd8-bcb5-4f06-b048-ae1e95fae047 {color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a}{color}!http://g-ecx.images-amazon.com/images/G/01/tmtdefaulttemplate/img/logo-selling_coach.png!{color:#5a5a5a} {color}{color:#5a5a5a} {color}| {color:#5a5a5a} {color}{color:#5a5a5a} 
{color} {color:#5a5a5a} {color}{color:#5a5a5a} 
{color} {color:#5a5a5a} {color}{color:#5a5a5a} 
{color} {color:#5a5a5a} 
{color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a}{color} 
h1. {color:#5a5a5a}Gateway Routing Failure{color}
{color:#5a5a5a}{color} 
h2. {color:#5a5a5a}Visit Vendor Central to resolve the errors{color}
{color:#5a5a5a} 
Greetings from Amazon,  \\ 
We received an EDI from your system or your EDI provider on date  *2025-05-28T18:45:11Z* with filename ${businessItem.BUSINESS_ID}. 
Our gateway could not properly identify your incoming message, and therefore, it was not processed in our system. 
Review the information below, and follow the list of required actions. For more information, including troubleshooting steps, go to {color}[{color:#5a5a5a} Gateway routing failure{color}|https://vendorcentral.amazon.com/nms/redirect/2a20881e-e066-3248-923a-be36a757ff64?nt=K9_GATEWAY_ROUTING_FAILURE&sk=Zw_VT0Ie2k2TsV2iQKvlo7fkf7CE2ZT-kcPmJacE3s6WsY1wwPwVTlIsgBsTNlweewX5SfejJGN44S0a96Usvw&n=1&u=aHR0cHM6Ly92ZW5kb3JjZW50cmFsLmFtYXpvbi5jb20vaHovdmVuZG9yL21lbWJlcnMvc3VwcG9ydC9oZWxwL25vZGUvR0ZRNFJVUEUyNlU1MlA2Ug]{color:#5a5a5a}.  *=== Metadata of the EDI Message ===* \\ 
{color}{color:#5a5a5a} 
{color} 
||{color:#5a5a5a}Type{color}{color:#5a5a5a} {color}||{color:#5a5a5a}Value{color}{color:#5a5a5a} {color}| 
|{color:#5a5a5a} *Transmission Date*{color}{color:#5a5a5a} {color}|{color:#5a5a5a}2025-05-28T18:45:11Z{color}{color:#5a5a5a} {color}| 
|{color:#5a5a5a} *ISAControlNumber*{color}{color:#5a5a5a} {color}|{color:#5a5a5a}*********{color}{color:#5a5a5a} {color}| 
|{color:#5a5a5a} *File Name*{color}{color:#5a5a5a} {color}|{color:#5a5a5a}500-**************-927e0637.x12{color}{color:#5a5a5a} {color}| 
|{color:#5a5a5a} *RECEIVER_ID*{color}{color:#5a5a5a} {color}|{color:#5a5a5a}AMAZONDS{color}{color:#5a5a5a} {color}| 
|{color:#5a5a5a} *SENDER_ID*{color}{color:#5a5a5a} {color}|{color:#5a5a5a}DTS7273DS{color}{color:#5a5a5a} {color}| 
|{color:#5a5a5a} *Connection Name*{color}{color:#5a5a5a} {color}|{color:#5a5a5a}GOOQD_VAN_AS2_20240806T174701813{color}{color:#5a5a5a} {color}| 
|{color:#5a5a5a} *Document Type*{color}{color:#5a5a5a} {color}|{color:#5a5a5a}810{color}{color:#5a5a5a} {color}| 
|{color:#5a5a5a} *Date/Time of transmission*{color}{color:#5a5a5a} {color}|{color:#5a5a5a}2025-05-28T18:45:11Z{color}{color:#5a5a5a} {color}| 
{color:#5a5a5a}
 

 You can analyze all transmissions in detail using the{color} [{color:#5a5a5a} Operational Analytics dashboard{color}|https://vendorcentral.amazon.com/nms/redirect/afdbf4d3-4fca-3d4a-8d1c-89dca99337e1?nt=K9_GATEWAY_ROUTING_FAILURE&sk=vpTMmyO-fcbUBpqpwnWmxnTyrb3r7YXht3hPhF2Wi58_cQCjB32JLieR-4vwUFkcxwbvk6PYElePEDtyoGiVtA&n=1&u=*******************************************************************************************************************************************************************************************************************************************************************************]{color:#5a5a5a}, accessible in Vendor Central by selecting “Integration (EDI/API)” and then “Operational analytics.” You can verify the receipt or delivery of transmissions, review failed transmissions, and request re-delivery of transmissions from Amazon.
 

Go to your EDI setup in Vendor Central to review your settings. Select  *Integration*, then select  *EDI Integration* and click  *View EDI setup* for your vendor code to access the Messages dashboard. 

The most common issues include: 
{color}
# {color:#5a5a5a}The sender or receiver ID is incorrect. Your identifiers are set in  *Global settings* in the Messages dashboard or individually per message. Verify that your current settings are promoted to the production environment. 
{color}
## {color:#5a5a5a}For EDI EDIFACT messages, the UNB sender and receiver IDs in the file must match the identifiers configured in the EDI setup in Vendor Central.
{color}
## {color:#5a5a5a}For EDI X12 messages, the ISA and GS sender and receiver IDs in the file must match the identifiers configured in the EDI setup in Vendor Central.
{color}
## {color:#5a5a5a}For XML messages, the sendingPartyID and receivingPartyID in the file must match the identifiers configured in the EDI setup in Vendor Central. 
{color}
# {color:#5a5a5a}You must transmit the file using the AS2, VAN, or SFTP connection endpoint assigned to the message for your vendor code as shown on your account EDI setup. Click  *Show message details* for the message type in the Messages dashboard to check the connection name.
{color}
# {color:#5a5a5a}The EDI message has not yet been promoted to production. To ensure that our system can recognize and process your file properly, the production environment must be in a configured status for the EDI message that you’re transmitting. Click  *Show message details* for the message type in the Messages dashboard to check the status in the production environment.
{color}
# {color:#5a5a5a}The UNB segment in EDIFACT or the ISA or GS segment in X12 is formatted incorrectly.
{color}
# {color:#5a5a5a}The EDI file that you’re sending is empty.
{color}
# {color:#5a5a5a}The file sent is not encoded in UTF-8.
{color}
# {color:#5a5a5a}The message type is not supported.
 

To learn more about other possible causes of this error, go to our{color} [{color:#5a5a5a} Gateway routing failure{color}|https://vendorcentral.amazon.com/nms/redirect/2a20881e-e066-3248-923a-be36a757ff64?nt=K9_GATEWAY_ROUTING_FAILURE&sk=Zw_VT0Ie2k2TsV2iQKvlo7fkf7CE2ZT-kcPmJacE3s6WsY1wwPwVTlIsgBsTNlweewX5SfejJGN44S0a96Usvw&n=1&u=aHR0cHM6Ly92ZW5kb3JjZW50cmFsLmFtYXpvbi5jb20vaHovdmVuZG9yL21lbWJlcnMvc3VwcG9ydC9oZWxwL25vZGUvR0ZRNFJVUEUyNlU1MlA2Ug]{color:#5a5a5a} guide. 

After correcting the issue in your EDI setup or your file, resend the transmission and verify the successful receipt from the{color} [{color:#5a5a5a} Operational Analytics{color}|https://vendorcentral.amazon.com/integration/oa/home?utm_source=SPIRAL&utm_medium=EMAIL&attributeKey=AmazonReferenceNumber&attributeValue=41161d09-5685-4304-8bfd-c1c58ccce7da-**************&partnerId=Not%20Available&prodSediAccountId=A2MON0CUXQZME4&eventTimestamp=2025-05-28T18:45:11Z]{color:#5a5a5a} dashboard. 


 Best Regards,{color} 
 [{color:#5a5a5a}www.amazon.com{color}|http://www.amazon.com]{color:#5a5a5a} 
 EDI Monitoring
 Amazon reference: 41161d09-5685-4304-8bfd-c1c58ccce7da-************** 
{color}
----
{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} {color:#5a5a5a} {color}{color:#5a5a5a} 
{color} {color:#5a5a5a} {color}{color:#5a5a5a} 
{color} {color:#5a5a5a} 
{color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a} {color}{color:white}{color}[{color:white}Was this email helpful? {color}|https://vendorcentral.amazon.com/gp/satisfaction/survey-form.html?ie=UTF8&HMDName=NotificationBusEmailHMD&customAttribute1Value=K9_GATEWAY_ROUTING_FAILURE]{color:white}{color}{color:#5a5a5a} 
{color}{color:#ffffff}If you have any questions visit: {color}[{color:#ffffff} {color}{color:#ffffff}Vendor Central{color}|https://vendorcentral.amazon.com/nms/redirect/19a71d52-fabb-38c5-94b3-595aeca8fde0?nt=K9_GATEWAY_ROUTING_FAILURE&sk=3y8rB9KfJAQJ7ESrzWen9l9a77SFejY0mN78R3HrQxZGFlrRNbI-PmgS42Z_n7pWOxFONy33tLkzz05oxn9fPg&n=1&u=aHR0cHM6Ly92ZW5kb3JjZW50cmFsLmFtYXpvbi5jb20]{color:#ffffff}{color}{color:#ffffff} \\  \\ To change your email preferences visit: {color}[{color:#ffffff} {color}{color:#ffffff}Notification Preferences{color}|https://vendorcentral.amazon.com/nms/redirect/d41b3dec-e4bb-3852-b5da-d72619f56272?nt=K9_GATEWAY_ROUTING_FAILURE&sk=XtscBqRSfJ0C86PDWy6zQ_uBDvSLfcoIEXjc9U-gMKBLXgO47R7NgJeSUK8W24Az3Z6DC_b-3TnH04XFmymHvQ&n=1&u=aHR0cHM6Ly92ZW5kb3JjZW50cmFsLmFtYXpvbi5jb20vc3QvdmVuZG9yL21lbWJlcnMvbm90aWZjb25maWcvc3Vic2NyaXB0aW9u]{color:#ffffff}{color}{color:#ffffff} \\  \\ Copyright 2025 Amazon, Inc, or its affiliates. All rights reserved. \\{color} {color:#5a5a5a} 
{color}{color:#5a5a5a} 
{color} 
| 
{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} {color:#5a5a5a} {color}{color:#5a5a5a} 
{color} {color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
{color:#5a5a5a} 
{color}{color:#5a5a5a} 
{color} 
|{color:#5a5a5a}{color}{color:#5a5a5a} {color}| 
{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
{color:#5a5a5a} {color}{color:#5a5a5a} 
{color} 
{color:#5a5a5a}{color}
   !https://vendorcentral.amazon.com/nms/img/f0da6029-e636-3b56-a50b-b2dcc8c3de97?sk=WD4diQ9Zeht46b3dav_MQpu77rTg8dXj1limPEl75L8SwA0tN7JwLrTx01JatfgLmG1a5Biwt34a0msXR0eeJg&n=1!  

{color:#a2a2a2!important} SPC-USAmazon-**************{color}    
  
  --{adf}{"type":"expand","content":[{"type":"paragraph","content":[{"type":"text","text":"Joe Sadloski","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#233c9c"}}]},{"type":"text","text":" "},{"type":"text","text":"| Director of Marketing & E-commerce | Hot Leathers | Good Sports, Inc.","marks":[{"type":"textColor","attrs":{"color":"#888888"}}]},{"type":"text","text":"   "},{"type":"hardBreak"},{"type":"text","text":"349 Progress Dr | Manchester, CT 06045","marks":[{"type":"textColor","attrs":{"color":"#7f7f7f"}}]},{"type":"hardBreak"},{"type":"text","text":"800.845.0084 | 860.647.0880 Ext 237 | Fax: 860.647.0104","marks":[{"type":"textColor","attrs":{"color":"#7f7f7f"}}]},{"type":"text","text":"  "},{"type":"text","text":"www.Hotleathers.com","marks":[{"type":"textColor","attrs":{"color":"#888888"}},{"type":"link","attrs":{"href":"http://www.hotleathers.com/"}}]}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

