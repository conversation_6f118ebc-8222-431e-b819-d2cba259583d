<h4 id="cs-44354-urgent-asn-failed-to-generate-document-tractor-supply-po-9016987033-message-id-44356129-created-24-may-25-updated-02-jun-25-resolved-02-jun-25">[CS-44354] *** URGENT *** ASN Failed to generate document - Tractor Supply - PO 9016987033 Message ID: 44356129 Created: 24/May/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: <PERSON><PERSON>
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-qvdkvjug.png      DataTrans ASN failed - already exist in batch.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hi,
Please help ASAP! We cannot create the ASN for this PO. Please see attached screenshot with the error message. We cannot find where this &#34;Batch&#34; is. Thanks for your prompt response in advance.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 24/May/25 ]
Comment by Sujay Ramesh [ 26/May/25 ]
Hello Robert,
I could see that you were able to send the ASN and Invoice for the PO. Do you require any further assistance here?
Thanks, Sujay</li>
</ul>
<h4 id="cs-44844-cannot-asn-invoice-within-data-trans-created-02-jun-25-updated-02-jun-25-resolved-02-jun-25">[CS-44844] Cannot ASN/ invoice within Data-Trans Created: 02/Jun/25  Updated: 02/Jun/25  Resolved: 02/Jun/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: kila rose Assignee: Sujay Ramesh Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  wal-mart - data trans.msg      image-20250602-192549.png
Request Type: Support
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
We cannt send and ASN or invocie within the Data- Trans portal.
None of our trading partner should be turned off .
Please advise as we need this corrected today.
Thank you
Comments  Comment by kila rose [ 02/Jun/25 ]
wal-mart - data trans.msg
Comment by kila rose [ 02/Jun/25 ]
po # 464702191 we cannot asn/invoice
Comment by kila rose [ 02/Jun/25 ]
close ticket</p>
<h4 id="cs-43708-missing-810-file-in-web-edi-created-14-may-25-updated-29-may-25-resolved-29-may-25">[CS-43708] Missing 810 file in web edi Created: 14/May/25  Updated: 29/May/25  Resolved: 29/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Brian Fincham Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.jpg      image002.jpg      image003.jpg      image004.jpg      CNH-810-EXP08338.edi      image-20250514-173723.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello,
We submitted an 810 edi file twice now and we still are not seeing it show up in Web EDI. Not sure if this errored out in Cleo’s system or not. We also do not get email alerts when files error out. Mentioned this a few times before and never got it resolved. For now though we need to make sure the 810 file has been delivered correctly to the Trading partner. The file we submitted is attached.
Thanks
Brian
| BRIAN FINCHAM IT Manager p: ************** x110 c: ************ f: 419.422.7207 |
<a href="https://www.facebook.com/WerkBrau/">https://www.facebook.com/WerkBrau/</a> |  <a href="https://www.instagram.com/werkbrau/">https://www.instagram.com/werkbrau/</a> |  <a href="https://www.linkedin.com/company/werk-brau/">https://www.linkedin.com/company/werk-brau/</a> | WERK-BRAU.COM |
Comments
Comment by Brian Fincham [ 14/May/25 ]
<em>CNH-810-EXP08338.edi  (0.6 kB)</em>
Comment by Sujay Ramesh [ 14/May/25 ]
Hi Brian,
I downloaded the file and it appears that it is missing the mandatory N3*01 field in address.
Thanks, Sujay
Comment by Brian Fincham [ 14/May/25 ]
Perfect! I am sure that is it. I fixed this and restaged. I will keep a look out for it to show up in Web EDI. As far as getting an email when something fails out on Cleo’s side can you look into why I do not receive anything.
Thanks
Brian
BRIAN FINCHAM
IT Manager
p: ************** x110
c: ************ Comment by Sujay Ramesh [ 19/May/25 ]
Hi Brian,
Do you use to receive notification(s) when something errors out?
Thanks, Sujay
Comment by Brian Fincham [ 19/May/25 ]
If possible, please make <a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Sujay Ramesh [ 28/May/25 ]
Hi Brian, I checked this internally and unfortunately currently we don’t have a way to validate this errors when sent to DTS.
Thanks, Sujay
Comment by Brian Fincham [ 28/May/25 ]
That’s too bad. Thanks for the update
Thanks
Brian
BRIAN FINCHAM
IT Manager
p: ************** x110
c: ************</li>
</ul>
<h4 id="cs-44449-asn-chrysler-311441-5-21-not-received-by-customer-created-28-may-25-updated-29-may-25-resolved-29-may-25">[CS-44449] ASN Chrysler 311441 5/21 not received by customer Created: 28/May/25  Updated: 29/May/25  Resolved: 29/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: KSENIIA LAPINA Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified Original estimate: Not Specified
Attachments:  image.png      Outlook-jwlzrhco.png      Outlook-vaiknlcf.png      Chrysler_856_2460.txt      Chrysler_997_2460.txt      Chrysler_997_2460 (0070b805-9b7a-4eab-ad55-14d319249fb9).txt      Chrysler_856_2460 (70327df6-bb1a-4525-80cf-9543c4fb7648).txt
Request Type: Emailed request
Request language: English
Request participants: None Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello,
Please check ASN 311441 dd. 5/21 The Customer complains that they did not receive it, however I do not see any problems in the system for it If I need to resend it, please provide the instruction how I can do it safely.
Comments
Comment by KSENIIA LAPINA [ 28/May/25 ]
Comment by KSENIIA LAPINA [ 28/May/25 ]
Please check ASN 311441 dd. 5/21
The Customer complains that they did not receive it, however I do not see any problems in the system for it
Comment by Sujay Ramesh [ 28/May/25 ]
Hi Kseniia,
For ASN# 311441, you can see the status as Accepted which means that they have received the ASN file successfully at their end. Could you please reach out to Chrysler and send them the below attached file for reference? There are raw EDI data files that includes the sent 856 ASN and the 997 Acknowledgment which we had received back.
Thanks, SujayChrysler_856_2460.txt  Chrysler_997_2460.txt
Comment by KSENIIA LAPINA [ 28/May/25 ]
I communicated with the customer.</p>
<ol>
<li>They say the problem is not on their side, they are not receiving anything. 2. We have same problem with ASN with reference number 311454, so this is a recurrent issue.
Please, escalate this problem, we need to find out what the problem is
Comment by Sujay Ramesh [ 29/May/25 ]
Hi Kesniia,
We have received the 997 functional acknowledgment which means that they have accepted the file at their end. Could you please share the attached two files? That should help them to track the documents at their end.
Thanks, SujayChrysler_856_2460 (70327df6-bb1a-4525-80cf-9543c4fb7648).txt  Chrysler_997_2460 (0070b805-9b7a-4eab-ad55-14d319249fb9).txt
Comment by KSENIIA LAPINA [ 29/May/25 ]
Issue resolved! this can be closed now</li>
</ol>
<h4 id="cs-42649-assistance-needed-for-missing-edi-810-created-21-apr-25-updated-28-may-25-resolved-09-may-25">[CS-42649] Assistance Needed for Missing EDI 810 Created: 21/Apr/25  Updated: 28/May/25  Resolved: 09/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      image (f2e16bf8-2e72-4d14-aff2-7af724742480).png      image-20250421-152830.png      image-20250422-152252.png      image (b6740a19-5609-4a38-ae1f-811aaccdf51a).png      image (ce23910b-aceb-448b-b18e-4c5ae6d7e373).png      image-20250522-220726.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Document not delivered to TP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Team,
Could you please assist us in deleting Draft 810? The invoice number is not showing in the Chewy portal. We would appreciate it if you could resolve this issue at your earliest convenience.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 21/Apr/25 ]
Comment by Sujay Ramesh [ 21/Apr/25 ]
Hello Mohit,
I checked invoice# 105338 and I could see the invoice was accepted with accepted status.
Do you want me to resend the invoice from the server and see if it helps?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/Apr/25 ]
Hi Sujay,
Could you please resend the invoice from the server so it reflects correctly on the customer portal? Additionally, could you confirm whether this invoice number is associated with a different PO? The customer mentioned that it m
On April 21, 2025 at 8:59 PM, Sujay Ramesh (<a href="mailto:<EMAIL>"><EMAIL></a>) wrote:
Comment by Sujay Ramesh [ 22/Apr/25 ]
Hello Mohit,
The invoice belongs to PO# RS41527508. I have meanwhile resent the document from our server.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/Apr/25 ]
Hi Sujay,
Thanks.
On April 22, 2025 at 8:56 PM, Sujay Ramesh (<a href="mailto:<EMAIL>"><EMAIL></a>) wrote:
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 24/Apr/25 ]
Hi Sujay,
We’ve contacted our customer, and they mentioned that three invoices were submitted using invoice number 105338. Upon checking DataTran, we found three invoices under that number — one marked as Complete (in green) submissions with the same invoice number. Please review and address this at your earliest convenience.
On April 23, 2025 at 11:18 AM, Mohit (<a href="mailto:<EMAIL>"><EMAIL></a>) wrote:
Hi Sujay,
Thanks.
Best Regards Mohit, Accountant <a href="https://www.foundmyanimal.com/?srsltid=AfmBOopgsMcSxJ_wO6ITL0B-PXoWFwT9jiM7KTaOyuY8q6hT6wrri3o7%5B">https://www.foundmyanimal.com/?srsltid=AfmBOopgsMcSxJ_wO6ITL0B-PXoWFwT9jiM7KTaOyuY8q6hT6wrri3o7[</a> Found My Animal
<a href="https://www.foundmyanimal.com/">https://www.foundmyanimal.com/</a>? srsltid=AfmBOoq3SUSozGXah1L5LcNpA_FYwx13b5quWY1921yvg64Y0VZWAG0U] Book a Meeting -<a href="https://calendly.com/bethany-64/15min%5Bhttps://calendly.com/bethany-64/15min">https://calendly.com/bethany-64/15min[https://calendly.com/bethany-64/15min</a>
<em>h C</em>
On April 22, 2025 at 8:56 PM, Sujay Ramesh (<a href="mailto:<EMAIL>"><EMAIL></a>) wrote:
Comment by Sujay Ramesh [ 24/Apr/25 ]
Hi Mohit,
I can see only one invoice being sent on WebEDI portal for #105338. On server, I can see the two invoices files being sent from server; one being sent on 4/10 and another being resent few days back.
I believe you would have to recreate and send the invoice with different invoice# to get it accepted. Also, I could see that you have invoiced 20 lines out of the 23 lines present in the PO (not sure if it affects anything but thought o
Thanks, Sujay
Comment by Sujay Ramesh [ 02/May/25 ]
Hi Mohit,
Do you require any assistance here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Hi Team,
Could you please assist with order number **********? We are currently unable to generate the EDI 856 and EDI 810 for this order. Attached is the snip for your reference.
!image (ce23910b-aceb-448b-b18e-4c5ae6d7e373).png|thumbnail!
Comment by Sujay Ramesh [ 22/May/25 ]
Hi Mohit,
It appears the the PO# ********** was deleted and is preset Under Archive → Deleted.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/May/25 ]
Hi Sujay,
Thanks.
On May 23, 2025 at 3:38 AM, Sujay Ramesh (<a href="mailto:<EMAIL>"><EMAIL></a>) wrote:</p>
<h4 id="cs-43699-asn-on-5-13-created-14-may-25-updated-28-may-25-resolved-28-may-25">[CS-43699] ASN on 5/13 Created: 14/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image001 (01d7f757-445d-44b0-b9f4-8818e1029f0f).png      image-20250516-195904.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: WebEDI Questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Datatrans team,
Was the ASN 2508133 sent out successfully yesterday, if not, please let us know, you don’t need to resend.
Thanks,
Yan Chen
YINCHANG INC.
6889 S. International Dr. Suite B., Columbus, IN 47201
812-552-2585
<a href="mailto:<EMAIL>"><EMAIL></a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 14/May/25 ]
Comment by Sujay Ramesh [ 15/May/25 ]
Hi Yan,
I cannot see the file being sent via VAN. There appears to be some disconnect when trying to send from server.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/May/25 ]
Hello,
It happens again today, we sent an ASN, it shows sent failed. We try to send again, still the same. Please fix the issue as soon as possible, we are supposed to send ASN right after the shipment is gone.
Account #: 6113.
Thanks,
Yan Chen
YINCHANG INC.
6889 S. International Dr. Suite B., Columbus, IN 47201
812-552-2585
<a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Sujay Ramesh [ 16/May/25 ]
Hi Yan,
It was an temporary issue. The files were sent with accepted status..
Thanks, Sujay
Comment by Sujay Ramesh [ 22/May/25 ]
Hi Yan,
Do you require any further assistance here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Hi, We are good for now. Thanks for asking.
Thanks, Yan Chen
On May 22, 2025, at 4:54 PM, Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> wrote:</p>
<h4 id="cs-44229-fw-document-processing-error-buc-ee-s-2025-05-22-17-39-05-created-22-may-25-updated-28-may-25-resolved-28-may-25">[CS-44229] FW: Document Processing Error: Buc-ee&#39;s 2025-05-22 17:39:05 Created: 22/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-20250522-204917.png      image-20250522-205251.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Support Team,
Can you help us figure out why we’re getting this invoice rejection and how to fix it?
<a href="http://www.BirdieandLouie.com">www.BirdieandLouie.com</a>
<a href="http://www.TheOhSoCo.com">www.TheOhSoCo.com</a>
<a href="http://www.WorthyPromo.com">www.WorthyPromo.com</a>
From: SPS Commerce noreply <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 22, 2025 12:46 PM To: <a href="mailto:<EMAIL>"><EMAIL></a>; Accounts Receivable <a href="mailto:<EMAIL>"><EMAIL></a>; Chas Worthy <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Document Processing Error: Buc-ee&#39;s 2025-05-22 17:39:05
The following document was rejected due to EDI syntax validation errors: Serial Number: PCL60581710914 Document Originator: Worthy Promotional Products, LLC Document Recipient: Buc-ee&#39;s Document Type: 810 Document ID: INV35140 EDI Interchange Control Number: ********* EDI Group Control Number: 4846 EDI Transaction Control Number: 4846
Source Filename: todts<del>SPSCAS2</del>*********.edi Error Information: The length of Element CTT04 (Unit or Basis for Measurement Code) is &#39;10&#39;. The maximum allowed length is &#39;2&#39;. Segment CTT is defined in the guideline at position 0700.
This error was detected at: Segment Count: 21 Element Count: 4 Characters: 892 through 902 Element CTT04 (Unit or Basis for Measurement Code) does not contain a valid identification code: &#39;7007581672&#39; is not allowed. Segment CTT is defined in the guideline at position 0700.
<a href="https://url9207.spscommerce.com/ls/click?upn=u001.1BcK1iWLIsTdqxmExt4XF4Otr-2BLsuFn-2FxwYDU95jSNI1X1Fb9UTy8hk1BuQqAILHvobnxo-2B0ykwDT1nbVjQ0gQ-3D-3D4SF8_J8Nzhml2FdiDnp6BcRtgWHBB53MwJMxxcOICFNqxZ4DESVsc1Ejf2bkHM8nCC0u-2B1-2FJhM4JeGQ0f2sEKqpvlFUJoYjfd43VfAL-2FnWLHu-2Fkpzpy6hyN9qfmHzK-2FxsHFSp-2BXsV4pdSHTz-2BngAvU7pnmfmKisUM7SXts6z8b1tyjTQ09-2BxZI0its-2F27OXqhzf77dHggiyxPKW1QrVha-2BD1L-2Bg9MHnnlCgScSYwLQMtGJVk-3D%5D">https://url9207.spscommerce.com/ls/click?upn=u001.1BcK1iWLIsTdqxmExt4XF4Otr-2BLsuFn-2FxwYDU95jSNI1X1Fb9UTy8hk1BuQqAILHvobnxo-2B0ykwDT1nbVjQ0gQ-3D-3D4SF8_J8Nzhml2FdiDnp6BcRtgWHBB53MwJMxxcOICFNqxZ4DESVsc1Ejf2bkHM8nCC0u-2B1-2FJhM4JeGQ0f2sEKqpvlFUJoYjfd43VfAL-2FnWLHu-2Fkpzpy6hyN9qfmHzK-2FxsHFSp-2BXsV4pdSHTz-2BngAvU7pnmfmKisUM7SXts6z8b1tyjTQ09-2BxZI0its-2F27OXqhzf77dHggiyxPKW1QrVha-2BD1L-2Bg9MHnnlCgScSYwLQMtGJVk-3D]</a> Kind regards, SPS Customer Operations Team
This error was detected at: Segment Count: 21 Element Count: 4 Characters: 892 through 902
An error has occurred while processing the document referenced above. You should also receive a 997 rejection. Please correct the errors and resend the transaction. If you would like assistance to resolve the error or need additional information, please visit our Support Center and choose one of our convenient contact channels to connect with a support representative. For complimentary expert training visit Training Center and for complimentary support visit [ Customer Support
SPS Commerce Inc. 333 S 7th St #1000, Minneapolis, MN 55402 Having issues? Contact our support team Message Reference ID: SPS-4SS-F3-HM4
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Comment by Sujay Ramesh [ 22/May/25 ]
Hi Chas,
Could you confirm if you had created the Invoice via Web EDI Portal?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Yes, that is confirmed.
Comment by Sujay Ramesh [ 22/May/25 ]
Hi Chas,
The issue is because of one of the bad data which Buc-ee had sent in the PO
This bad data was then mapped to 810 while sending them back.
I have manually edited the file from the server by removing the bad data and sent it. Hopefully, the same should be accepted by TP.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/May/25 ]
Thank you!</li>
</ul>
<h4 id="cs-44047-walgreens-850-s-5-19-created-20-may-25-updated-28-may-25-resolved-28-may-25">[CS-44047] Walgreens 850&#39;s - 5/19 Created: 20/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: mark paine Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-20250520-182007.png      image-20250520-192540.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
We always receive 9 orders every 2 weeks, except yesterday there were 7. Can you check to make sure there were only 7 yesterday? Thank you.
|
Inspired Ideas for Healthier Lives.
Mark Paine VP of Operations 105 Baylis Road, Melville, NY 11747 Direct: ************ <a href="about:invalid#zCSafez">tel:************</a> Office: ************ <a href="about:invalid#zCSafez">tel:************</a> e: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="http://www.ascentconsumerproducts.com">www.ascentconsumerproducts.com</a>
Comments
Comment by mark paine [ 20/May/25 ]
Comment by Sujay Ramesh [ 20/May/25 ]
Hello Mark,
Could you please provide more details like for which Trading partner are you asking the details about?
Thanks, Sujay
Comment by mark paine [ 20/May/25 ]
Walgreens.
|
Inspired Ideas for Healthier Lives. Mark Paine VP of Operations 105 Baylis Road, Melville, NY 11747 Direct: ************ <a href="about:invalid#zCSafez">tel:************</a> Office: ************ <a href="about:invalid#zCSafez">tel:************</a> e: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="http://www.ascentconsumerproducts.com">www.ascentconsumerproducts.com</a>
Comment by Sujay Ramesh [ 20/May/25 ]
Hi Mark,
There were indeed 9 POs. I can see two POs being not present. I am checking on why it was not processed.
Thanks, Sujay
Comment by mark paine [ 20/May/25 ]
We need those asap as they fine for late orders. Thanks
Mark Paine VP of Operations P- ************ E- <a href="mailto:<EMAIL>"><EMAIL></a>
On May 20, 2025, at 2:21 PM, Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Comment by Sujay Ramesh [ 20/May/25 ]
Hello Mark,
The POs were repushed. Just FYI, I can see two more POs from Walgreen on 13th.
Thanks, Sujay
Comment by mark paine [ 20/May/25 ]
Great, I see these now. The ones on the 13th we received and they are placeholder orders. Do you know what this issue was on this week’s orders so it doesn’t happen again? Thank you!
|
Inspired Ideas for Healthier Lives. Mark Paine VP of Operations 105 Baylis Road, Melville, NY 11747 Direct: ************ <a href="about:invalid#zCSafez">tel:************</a> Office: ************ <a href="about:invalid#zCSafez">tel:************</a> e: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="http://www.ascentconsumerproducts.com">www.ascentconsumerproducts.com</a>
Comment by Sujay Ramesh [ 22/May/25 ]
Hi Mark,
We believe the issue was with the injector. Our team had fixed the issue going forward.
Thanks, Sujay</li>
</ul>
<h4 id="cs-43795-re-missing-walmart-invoice-po-**********-created-15-may-25-updated-28-may-25-resolved-28-may-25">[CS-43795] Re: Missing Walmart Invoice - PO ********** Created: 15/May/25  Updated: 28/May/25  Resolved: 28/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Jeremy King Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-un2cwli4.png      Outlook-nmefigu2.png      Outlook-https___ww.png      Outlook-Facebook.png      Outlook-Linkedin.png      Outlook-Google.png     image-20250515-190825.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Adding @<a href="mailto:<EMAIL>"><EMAIL></a>
From: Maureen Saliba <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, May 15, 2025 1:19 PM To: Jeremy King <a href="mailto:<EMAIL>"><EMAIL></a>; Brantley Hamby <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: Missing Walmart Invoice - PO **********
Good afternoon -
This invoice is still missing from our system.
**********
Do either of you have visibility on this WALMART invoice? PO # is the same as invoice #. Please advise.
Thank you,
Maureen Saliba Operations Manager 105 Baylis Road, Melville, NY 11747 p: ************ e: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="http://www.ascentconsumerproducts.com">www.ascentconsumerproducts.com</a>
<em>Wire Fraud is Real. Before wiring ANY money, call the intended recipient at a number you know is valid to confirm the instructions.</em>
From: Maureen Saliba <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 9, 2025 10:02 AM To: Jeremy King <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Missing Walmart Invoice - PO **********
Hi Jeremey
Do you have any visibility on this PO #********** for Walmart? It&#39;s the last &#34;missing&#34; invoice from April as the other 4 have imported. Please advise.
Thank you,
Maureen Saliba Operations Manager 105 Baylis Road, Melville, NY 11747 p: ************ e: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="http://www.ascentconsumerproducts.com">www.ascentconsumerproducts.com</a>
<em>Wire Fraud is Real. Before wiring ANY money, call the intended recipient at a number you know is valid to confirm the instructions.</em>
Comments
Comment by Jeremy King [ 15/May/25 ]
Comment by Sujay Ramesh [ 15/May/25 ]
Hello Maureen,
I can see the invoice being sent to Walmart and is in Accepted Status.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 15/May/25 ]
Thank you - Jeremy, can you advise if you can figure out why it hasn&#39;t imported into our system?
Comment by Sujay Ramesh [ 21/May/25 ]
Hello Team,
Do you require any further assistance from Cleo DTS end?
Thanks, Sujay</li>
</ul>
<h4 id="cs-42924-cannot-acknowledge-this-order-created-25-apr-25-updated-28-may-25-resolved-13-may-25">[CS-42924] CANNOT ACKNOWLEDGE THIS ORDER Created: 25/Apr/25  Updated: 28/May/25  Resolved: 13/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250425-225456.png      image-20250513-214916.png      image-20250513-214804.png      image-20250519-205310.png
Request Type: Support
Request language: English Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
ACKNOWLEDGEMENT CODE NOT APPEARING
Comments
Comment by Sujay Ramesh [ 25/Apr/25 ]
Hello Fred,
Can you please tell me which document you are referring to here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 25/Apr/25 ]
855 order acknowledgements
Comment by Sujay Ramesh [ 25/Apr/25 ]
Hi Fred,
I could select acknowledgment code and types.
Could you try to log out and log in again?
Thanks, Sujay
Comment by Sujay Ramesh [ 05/May/25 ]
Hi Fred,
Do you require any further assistance here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 05/May/25 ]
I think we are OK on this now, thanks Sujay.
Fred
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 13/May/25 ]
???????
I have received information on submitting 856 ( DTC 4010 856 EDI GUIDELINE) but it is not clear as it does not match the format shown on my 856 screen.
Comment by Sujay Ramesh [ 13/May/25 ]
Hello Fred,
The ticket was created because there were some issues with selecting acknowledgment code and types. Are you still facing any issues there?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 13/May/25 ]
Yes.
When I open 856 from the Respond menu, all that appears on the 856 is pick and pack and then # of containers.
Then the message appears UCC VENDOR PREFIX NOT SET.
However, there is no field for Tracking numbers.
Both outstanding orders, 9016869164 and 9016892527 are shown as Delivered.
Fred
Comment by Sujay Ramesh [ 13/May/25 ]
Hello Fred,
Below is the error detail:
Please go the settings and company information to fill the same.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 13/May/25 ]
Sujay,
Thanks and will do. Will let you know.
Fred
Comment by Sujay Ramesh [ 19/May/25 ]
Hi Fred,
Could you please let us know if we are good to close this ticket?
Thanks, Sujay Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 19/May/25 ]
??????
Which PO number, Sujay?
Fred
Comment by Sujay Ramesh [ 19/May/25 ]
Hi Fred,
I was talking about the error you were receiving while creating 856? Was the issue resolved.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 19/May/25 ]
I’ll check it out and let you know!</p>
<h4 id="cs-43895-glaudia-centeno-12103897052-has-left-you-a-message-42-second-s-long-created-16-may-25-updated-28-may-25-resolved-16-may-25">[CS-43895] GLAUDIA CENTENO (+12103897052) has left you a message 42 second(s) long Created: 16/May/25  Updated: 28/May/25  Resolved: 16/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  message.wav
Request Type: Emailed request
Request language: English
Request participants: None Organizations: Label: Invoice rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Dear Voicemail to support : You received a message from GLAUDIA CENTENO (+12103897052). The message is in mailbox 140, dated Friday, May 16, 2025 at 2:19:59 PM. You can get this message via the phone, or use the links below to download the message. Download the message and mark it as read Download the message and delete it Important: This will completely delete the message and this link will no longer work. Thank you!
Terms of Use | Privacy Policy | ©2025 Sangoma All Rights Reserved
<a href="https://www.facebook.com/sangoma">https://www.facebook.com/sangoma</a>  <a href="https://www.twitter.com/sangoma">https://www.twitter.com/sangoma</a>  <a href="https://www.linkedin.com/company/sangoma/">https://www.linkedin.com/company/sangoma/</a>  <a href="https://www.sangoma.com/">https://www.sangoma.com/</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/May/25 ]
<em>message.wav  (658 kB)</em>
Comment by Sujay Ramesh [ 16/May/25 ]
Called customer back via Zendesk and helped them.</p>
<h4 id="cs-43805-getting-asn-in-edi-856-format-1-segment-per-line-created-15-may-25-updated-26-may-25-resolved-15-may-25">[CS-43805] Getting ASN in EDI 856 format (1 segment per line) Created: 15/May/25  Updated: 26/May/25  Resolved: 15/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: KSENIIA LAPINA Assignee: Sujay Ramesh
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-gtrx0od5.png      Outlook-c0uol3h0.png      image-20250515-214351.png      ASN_311402.pdf      ASN_3114022A_USA_000002458.docx
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: WebEDI Questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello! In order to check if ASN is filled in correctly on our side, our customer asks us to provide them the ASN in EDI 856 format (1 segment per line). The corresponding ASN saved as pdf is attached. Please advise, if i can do it myself or you can help me pull it from the datatrans portal?
Comments
Comment by KSENIIA LAPINA [ 15/May/25 ]
Comment by Sujay Ramesh [ 15/May/25 ]
Hi Kseniia,
In order to download the file in raw EDI format, you will have to first select the file, then right click → Export → Message as EDI file. The EDI file will be downloaded in x12 format.
Thanks, Sujay
Comment by KSENIIA LAPINA [ 15/May/25 ]
Thank you! Resolved!
Comment by KSENIIA LAPINA [ 19/May/25 ]
Hello! This task needs to be addressed again.
The customer cannot accept the formats that are available for export from datatrans. They insist on receiving an ASN EDI file in WORD format with 1 segment per line
Is there anything that can be done about it? I tried opening the EDI file with WORD, but it puts everything in one line, not 1 segment per line.
Comment by Sujay Ramesh [ 20/May/25 ]
Hi Kseniia,
Could you tell me which ASN# are yo looking the file for? I cannot see the a PDF attached in earlier mail.
Thanks, Sujay
Comment by KSENIIA LAPINA [ 20/May/25 ]
Hello,
Please see the ASN attached
<em>ASN_311402.pdf  (222 kB)</em>
Comment by Sujay Ramesh [ 20/May/25 ]
Hi Kseniia,
I have attached the file for your reference.
Thanks, SujayASN_3114022A_USA_000002458.docx
Comment by Sujay Ramesh [ 23/May/25 ]
Hi Kseniia,
Do you require any further assistance here?
Thanks, Sujay
Comment by KSENIIA LAPINA [ 23/May/25 ]
Hello Sujay,
Than you, this has been resolved, you can close it</p>
<h4 id="cs-44097-new-wal-mart-orders-05-21-2025-created-21-may-25-updated-23-may-25-resolved-23-may-25">[CS-44097] New Wal-Mart orders 05/21/2025 Created: 21/May/25  Updated: 23/May/25  Resolved: 23/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: kila rose Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250521-175603.png      image004.jpg      image003.jpg      image005.jpg      image002.png      image006.jpg      image001.png
Request Type: Support
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
We receive Wal-mart( RvAir) orders every Wed ususally in the morning . We do not see any new orders yet. Do you see any orders that are coming through for today?
Thank you /
Comments
Comment by Sujay Ramesh [ 21/May/25 ]
Hello Kila,
I can see few orders in the portal.
Thanks, Sujay
Comment by kila rose [ 21/May/25 ]
Thank you
We do see those now too.
Kila Rose
Logistics Manager | <a href="https://www.nationwide-products.com">https://www.nationwide-products.com</a> | <a href="https://rvair.com">https://rvair.com</a>
Nation Wide Products, LLC. | 1301 S. Treadaway Blvd. | Abilene, TX 79602
📞: (325) 675 – 5062 ext. 117
✉: <a href="mailto:<EMAIL>"><EMAIL></a></p>
<h4 id="cs-43720-fw-banner-canada-edi-missing-pos-created-14-may-25-updated-23-may-25-resolved-23-may-25">[CS-43720] Fw: Banner Canada EDI - missing POs Created: 14/May/25  Updated: 23/May/25  Resolved: 23/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Andrew Rice Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.png      image004.png      image005.png      image-20250514-192422.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello DTS Support,
Two 850s were sent to you from Banner Canada yesterday:
PO5011916 PO5011918
But they are not visible in WebEDI. There was no error on the trading partner side, there must have been an issue somewhere in the van, splitter, translation, WebEDI injection process. Can you please review activity from yesterday and see if there were any issues logged on the DTS side?
This issue has some up several times in the last few weeks, leading me to believe this is not a one-off issue or temporary network outage. Typically, when the 850s are sent a second time everything works as expected.
Thanks, Andrew
From: Alicia Evans Sent: Wednesday, May 14, 2025 12:29 PM To: Andrew Rice
Cc: Lexy Wanka Subject: Banner Canada EDI - missing POs
Hi Andrew,
Banner Canada sent over PO # PO5011916 and PO # PO5011918 last night.
These are not in Oracle or the Oracle Corrections table.
Will you please take a look?
Thank you,
Alicia
<a href="http://www.bannerengineering.com/">http://www.bannerengineering.com/</a>
<a href="https://www.facebook.com/BannerEngineering">https://www.facebook.com/BannerEngineering</a> <a href="https://twitter.com/BannerSensors">https://twitter.com/BannerSensors</a>
<a href="https://www.linkedin.com/company/banner-engineering">https://www.linkedin.com/company/banner-engineering</a> <a href="https://www.youtube.com/user/BannerEngineering">https://www.youtube.com/user/BannerEngineering</a>
Alicia Evans Sr. Customer Care Manager ----
Banner Engineering Corp. 9714 10th Avenue North, Plymouth, MN 55441 E <a href="mailto:<EMAIL>"><EMAIL></a> P 763-582-3283, 1-800-379-4849
The content of this email is confidential and intended for the recipient specified in message only. It is strictly forbidden to share any part of this message with any third party, without a written consent of the sender. If you received this message by mistake, please reply to this message and follow with its deletion, so that we can ensure such a mistake does not occur in the future.
Comments
Comment by Andrew Rice [ 14/May/25 ]
Comment by Sujay Ramesh [ 14/May/25 ]
Hi Andrew,
I can’t see the PO being received on our end. Below is the snapshot from our server.
Thanks, Sujay
Comment by Sujay Ramesh [ 21/May/25 ]
Hi Andrew,
Do you require any further assistance here?
Thanks, Sujay</p>
<h4 id="cs-43791-topco-created-15-may-25-updated-23-may-25-resolved-23-may-25">[CS-43791] Topco Created: 15/May/25  Updated: 23/May/25  Resolved: 23/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Ashley Rivera Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250515-200831.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Please resubmit PO for Topco
Reference numbers are
000004502332744 000004502332743
They came in as one XML file not two. These are two separate POs
Thank you!
Comments
Comment by Sujay Ramesh [ 15/May/25 ]
Manually dropping the file
Comment by Sujay Ramesh [ 15/May/25 ]
Hi Ashley,
I have redropped the file with different file name. Please let me know if the same is visible at your end.
Thanks, Sujay
Comment by Sujay Ramesh [ 21/May/25 ]
Hi Ashley,
Following up here. Do you require any further assistance here?
Thanks, Sujay
Comment by Ashley Rivera [ 22/May/25 ]
This ticket can be closed.
Thank you!</p>
<h4 id="cs-43887-adams-rjw-945-not-received-created-16-may-25-updated-22-may-25-resolved-16-may-25">[CS-43887] Adams - RJW 945 not received Created: 16/May/25  Updated: 22/May/25  Resolved: 16/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Christopher Morrison Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image-20250516-173607.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Customer: Adams Extracts
ID: 6061
Trading Partner: RJW
Document: 945
The following 945s are visible in Web EDI but were not delivered to our ERP system. Can you please re-stage them?
Christopher Morrison | ERP Lead Developer Adams Flavors, Foods &amp; Ingredients 3217 Johnson Rd | PO Box 1726 Gonzales, TX 78629 C: 610-513-6680 Adams1888.com
Adams EDI Team: <a href="mailto:<EMAIL>"><EMAIL></a>
Adams ERP Admin: <a href="mailto:<EMAIL>"><EMAIL></a>
Book time with Morrison, Christopher
Comments
Comment by Christopher Morrison [ 16/May/25 ]
Comment by Sujay Ramesh [ 16/May/25 ]
Files were redropped
Comment by Christopher Morrison [ 16/May/25 ]
The 945s have been received and processed.
Comment by Sujay Ramesh [ 16/May/25 ]
Thanks for the confirmation, Chris. I had redropped the file. I will go ahead and close the ticket.
Thanks, Sujay Comment by Sujay Ramesh [ 16/May/25 ]
Hi Chris,
Could you let me know if you require any further assistance since you re-opened the issue?
Thanks, Sujay</p>
<h4 id="cs-43714-configure-backup-invoice-delivery-method-created-14-may-25-updated-20-may-25-resolved-20-may-25">[CS-43714] Configure Backup Invoice Delivery Method Created: 14/May/25  Updated: 20/May/25  Resolved: 20/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  FW_ Urgent_ Configure Backup Invoice Delivery Method in APP Portal.eml      image-20250514-214536.png
Request Type: Support
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi, I recently received an email instructing me to setup a backup invoice delivery method in the APP portal. I have the instructions to do this but do not see where to get started. It looks like I should see a Vendor Configuration button on an invoice page, I do not see where this button is located.
Comments
Comment by Sujay Ramesh [ 14/May/25 ]
Hello,
Could you please provide the mail which you had received at your end? We need to check details regarding this.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 14/May/25 ]
Please see attached email.
<em>FW_ Urgent_ Configure Backup Invoice Delivery Method in APP Portal.eml  (158 kB)</em>
Comment by Sujay Ramesh [ 14/May/25 ]
Hello Jon,
The mail doesn’t seem to be originated from Cleo DTS. It says it is from Albertsons and their Partner Portal. You would have to reach out to Albertsons regarding this.
Thanks, Sujay
Comment by Sujay Ramesh [ 19/May/25 ]
Hello Jon,
Do you require any further assistance here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 20/May/25 ]
You can close this ticket, thank you for looking into this.</p>
<h4 id="cs-42845-re-fw-fw-edi-810-question-created-24-apr-25-updated-16-may-25-resolved-16-may-25">[CS-42845] Re: Fw: Fw: EDI 810 Question Created: 24/Apr/25  Updated: 16/May/25  Resolved: 16/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Krista Johnson Assignee: Sujay Ramesh
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image007.png      image008.jpg      image009.jpg      image006.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hi...
I&#39;ve copied in the Support team to check on these transmissions.
I show that we are accurately sending out via AS2 to their VAN - Nubridges. It would appear that Nubridges may have things in error on their system.
Customer - Spectrum Furniture
TP - CDW
Last 810 sent today - ECS03. Batch - 143566329
Thanks,
Krista
On 4/24/2025 3:31 PM, Lisa Johnson wrote:
Krista,
Can you help me understand what is going on with this? We began sending 810&#39;s to CDW, but they aren&#39;t getting them or at least aren&#39;t acknowledging them. What has to change to make this work? I has been going on for a week now and I just can&#39;t get any answers.
From: Lisa Johnson <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, April 24, 2025 1:45 PM To: Kayleigh Benedict <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: Fw: EDI 810 Question
Kayleigh,
Do you have an update for this? How can we get the Invoices to be received and acknowledged correctly?
Lisa Johnson
Vice President, Information Systems | Spectrum Industries, Inc.
Cell: ************
<a href="http://www.spectrumfurniture.com">www.spectrumfurniture.com</a>
Wisconsin Green Masters Company | Equal Opportunity Employer
From: Lisa Johnson <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, April 23, 2025 10:13 AM To: Kayleigh Benedict <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Fw: Fw: EDI 810 Question
Is this how we did our testing? Or is there a change that we need to make?
Lisa Johnson
Vice President, Information Systems | Spectrum Industries, Inc.
Cell: ************
<a href="http://www.spectrumfurniture.com">www.spectrumfurniture.com</a>
Wisconsin Green Masters Company | Equal Opportunity Employer
From: Renee Walker <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, April 23, 2025 9:54 AM To: Lisa Johnson <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Fw: EDI 810 Question
Hi Lisa,
We are setup to send the files via AS2 to you and you’re sending them via FTP. See below.
Have a great day! 😊
Thank you.
Renee Walker
Sr. Eng Tech Support &amp; Svc Ops
CDW B2B Team
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
Phone: ************
From: Lisa Johnson <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, April 23, 2025 9:07 AM To: Renee Walker <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Re: Fw: EDI 810 Question
EXTERNAL EMAIL
Just updating the thread. Another invoice was sent on 4/22 but remains unacknowledged.
Lisa Johnson
Vice President, Information Systems | Spectrum Industries, Inc.
Cell: ************
<a href="http://www.spectrumfurniture.com">www.spectrumfurniture.com</a>
Wisconsin Green Masters Company | Equal Opportunity Employer
From: Kayleigh Benedict <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, April 21, 2025 4:32 PM To: Renee Walker &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Lisa Johnson &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: Re: Fw: EDI 810 Question
Hi Renee,
It looks like we aren&#39;t receiving 997&#39;s for any of the documents we&#39;ve sent to CDW, even the ones you said you have received. Can you please look into this on your end? I have resent the invoice in question and have attached the raw data below.
Thank you,
Kayleigh
<a href="https://urldefense.com/v3/__https:/www.datatrans-inc.com/__;!!HUqgN_M!vEXpgACr-aUoDExtP--HGOLvYSWyd7SUHqT5AI0cpVc4PggdG60bJYTXXb07zLRCiTb--F1NtwumV9SP4sD9_GU$">https://urldefense.com/v3/__https:/www.datatrans-inc.com/__;!!HUqgN_M!vEXpgACr-aUoDExtP--HGOLvYSWyd7SUHqT5AI0cpVc4PggdG60bJYTXXb07zLRCiTb--F1NtwumV9SP4sD9_GU$</a>
Kayleigh Benedict
DataTrans Solutions : Technical EDI Analyst
p: ************ x 111 w: datatrans-inc.com* *e: [ <a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]
DTS is now a part of Cleo. Read about the acquisition.
<a href="https://urldefense.com/v3/__https:/www.facebook.com/DataTransSolutions/__;!!HUqgN_M!vEXpgACr-aUoDExtP--HGOLvYSWyd7SUHqT5AI0cpVc4PggdG60bJYTXXb07zLRCiTb--F1NtwumV9SPj6N4cls$">https://urldefense.com/v3/__https:/www.facebook.com/DataTransSolutions/__;!!HUqgN_M!vEXpgACr-aUoDExtP--HGOLvYSWyd7SUHqT5AI0cpVc4PggdG60bJYTXXb07zLRCiTb--F1NtwumV9SPj6N4cls$</a>
<a href="https://urldefense.com/v3/__https:/www.instagram.com/datatranssolutions/__;!!HUqgN_M!vEXpgACr-aUoDExtP--HGOLvYSWyd7SUHqT5AI0cpVc4PggdG60bJYTXXb07zLRCiTb--F1NtwumV9SPKm4QRBk$">https://urldefense.com/v3/__https:/www.instagram.com/datatranssolutions/__;!!HUqgN_M!vEXpgACr-aUoDExtP--HGOLvYSWyd7SUHqT5AI0cpVc4PggdG60bJYTXXb07zLRCiTb--F1NtwumV9SPKm4QRBk$</a>
<a href="https://urldefense.com/v3/__https:/twitter.com/datatrans_edi__;!!HUqgN_M!vEXpgACr-aUoDExtP--HGOLvYSWyd7SUHqT5AI0cpVc4PggdG60bJYTXXb07zLRCiTb--F1NtwumV9SPn2t-9V4$">https://urldefense.com/v3/__https:/twitter.com/datatrans_edi__;!!HUqgN_M!vEXpgACr-aUoDExtP--HGOLvYSWyd7SUHqT5AI0cpVc4PggdG60bJYTXXb07zLRCiTb--F1NtwumV9SPn2t-9V4$</a>
<a href="https://urldefense.com/v3/__https:/www.linkedin.com/company/datatrans-solutions/__;!!HUqgN_M!vEXpgACr-aUoDExtP--HGOLvYSWyd7SUHqT5AI0cpVc4PggdG60bJYTXXb07zLRCiTb--F1NtwumV9SPjyo_N00$">https://urldefense.com/v3/__https:/www.linkedin.com/company/datatrans-solutions/__;!!HUqgN_M!vEXpgACr-aUoDExtP--HGOLvYSWyd7SUHqT5AI0cpVc4PggdG60bJYTXXb07zLRCiTb--F1NtwumV9SPjyo_N00$</a>
On Mon, Apr 21, 2025 at 10:45 AM Lisa Johnson <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Kayleigh,
Is there a way to determine what might have happened here? The doc shows SENT, but was never acknowledged. Note that our Accounting Dept did not send an 855 or 856. I think this was some kind of emergency order.
Lisa Johnson
Vice President, Information Systems | Spectrum Industries, Inc.
Cell: ************
<a href="http://www.spectrumfurniture.com">www.spectrumfurniture.com</a>
Wisconsin Green Masters Company | Equal Opportunity Employer
From: Renee Walker <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, April 21, 2025 10:18 AM To: Lisa Johnson &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Cc: Help Desk - EDI Support &lt;[<a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]&gt; Subject: RE: EDI 810 Question
Hi Lisa,
Please resend this invoice as we did not receive it.
Have a great day! 😊
Thank you.
Renee Walker
Sr. Eng Tech Support &amp; Svc Ops
CDW B2B Team
Email: <a href="mailto:<EMAIL>"><EMAIL></a>
Phone: ************
From: Lisa Johnson <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Monday, April 21, 2025 10:07 AM To: Renee Walker <a href="mailto:<EMAIL>"><EMAIL></a> Subject: EDI 810 Question
EXTERNAL EMAIL
Renee,
Our Accounting dept sent and Invoice via EDI on 4/17 that remains unacknowledged in our system. Can you check to see if you received this transmission?
Thanks!
Lisa Johnson
The contents of this email are intended only for the recipient(s) listed above. If you are not the intended recipient, you are directed not to read, disclose, distribute, or otherwise use this transmission. If you have received this email in error, please notify the sender immediately and delete the transmission. Delivery of this message is not intended to waive any applicable privileges.
The contents of this email are intended only for the recipient(s) listed above. If you are not the intended recipient, you are directed not to read, disclose, distribute, or otherwise use this transmission. If you have received this email in error, please notify the sender immediately and delete the transmission. Delivery of this message is not intended to waive any applicable privileges.
–
<a href="https://www.datatrans-inc.com/">https://www.datatrans-inc.com/</a>
Krista Johnson
Datatrans Solutions : EDI Analyst
p: ************ x 206 w: datatrans-inc.com e: [ <a href="mailto:<EMAIL>"><EMAIL></a>
<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]
<a href="https://www.facebook.com/DataTransSolutions/">https://www.facebook.com/DataTransSolutions/</a>  <a href="https://www.instagram.com/datatranssolutions/">https://www.instagram.com/datatranssolutions/</a>  <a href="https://twitter.com/datatrans_edi">https://twitter.com/datatrans_edi</a>  <a href="https://www.linkedin.com/company/datatrans-solutions/">https://www.linkedin.com/company/datatrans-solutions/</a>
Comments
Comment by Krista Johnson [ 24/Apr/25 ]
Comment by Lisa J Johnson [ 28/Apr/25 ]</li>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
We now have 7 invoices unacknowledged by CDW dating from two weeks ago. What needs to be done to correct this? How soon can I expect a fix?
Comment by Lisa J Johnson [ 29/Apr/25 ]
Krista and Support Crew -
My abject apologies. I have just released your messages from our spam filter. Why only support emails are blocked, I am not sure.
I will read previous communications and respond.</li>
</ul>
<h4 id="cs-42975-810-997-documents-created-28-apr-25-updated-16-may-25-resolved-16-may-25">[CS-42975] 810 &amp; 997 documents Created: 28/Apr/25  Updated: 16/May/25  Resolved: 16/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: Lisa J Johnson Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-184627.png      CDW_I000520969.txt      image-20250429-193916.png      image-20250429-203141.png
Request Type: Support
Request language: English
Request participants: None
Organizations: None CSAT Comment: {{webhookResponse.body.value.comment}}
Description
CDW is not receiving 997 acknowledgements we send for the 850&#39;s we receive.
CDW is not receiving the 810 invoices we send
Comments
Comment by Sujay Ramesh [ 28/Apr/25 ]
Hello Lisa,
I can see that on our end, we are sending the 810s to their VAN Nubridges. Below are the details of the latest invoice# I000520969 we sent earlier today. Could you please reach out to CDW to check with the below details:
Below are the logs:
Ocurred Computer Description
2025-04-28 18:32:10
dts-cirrus-01
AS2SenderModule: message sender invoked []
2025-04-28 18:32:10
dts-cirrus-01
AS2SenderModule: Save Original mic &amp; message id information into file: C:\opt\OpenAS2-v20211005\config/../data/pendinginfoMDN3/CIRRUSAS2-28042025133210-0500-455f2414-9c8f-411
2025-04-28 18:32:10
dts-cirrus-01
AS2SenderModule: Connecting to: <a href="http://van.nuBridges.com:4080">http://van.nuBridges.com:4080</a> []
2025-04-28 18:32:11
dts-cirrus-01
AS2SenderModule: Message sent and response received in 582 millisecondsms []
2025-04-28 18:32:11
dts-cirrus-01
AS2Util: received MDN [automatic-action/MDN-sent-automatically; processed] []
2025-04-28 18:32:11
dts-cirrus-01
MDNFileModule: Started Handling Storing mdn data Msg: []
2025-04-28 18:32:11
dts-cirrus-01
AS2Util: Message sent and MDN received successfully. [&lt;CIRRUSAS2-28042025133210-0500-455f2414-9c8f-411c-aa09-f3fbbfa67c13@todts_EDINETAS2&gt;]
2025-04-28 18:32:11
dts-cirrus-01
AS2Util: moved C:\opt\OpenAS2-v20211005\config..\data\pendingMDN3\CIRRUSAS2-28042025133210-0500-455f2414-9c8f-411c-aa09-f3fbbfa67c13@todts_EDINETAS2 to C:\opt\OpenAS2 f3fbbfa67c13@todts_EDINETAS2 []
2025-04-28 18:32:11
dts-cirrus-01
DirectoryPollingModule: processing C:\opt\OpenAS2-v20211005\config..\data\outbox\temp\todts<del>007941230C</del>143667056.x12
2025-04-28 18:32:11
dts-cirrus-01
MessageBuilderModule: File assigned to message: todts<del>007941230C</del>143667056.x12 []
Thanks, Sujay
CDW_I000520969.txt
Comment by Lisa J Johnson [ 29/Apr/25 ]
I will forward this message to CDW. Please clarify a couple of thing for me:</p>
<ol>
<li>Is there a support portal or some way for me to see our tickets?</li>
<li>Are 997 documents being sent to and received from CDW? How can I see them? (Per NuBridges, none of the CDW 850 documents have been acknowledged by Spectrum. None of our 810&#39;s have been acknowledged by</li>
<li>How can I see our Trading Partners and their settings?</li>
<li>Who is responsible for the relationship with NUBridges? Are they contracted by Data Trans or CDW?
Thank you so much for your assistance. My frustration with this issue has absolutely boiled over. I will do what I can to get message flowing correctly, as Data Trans support communications are being blocked.
Comment by Sujay Ramesh [ 29/Apr/25 ]
Hi Lisa,
Is there a support portal or some way for me to see our tickets? We use Jira Portal for ticketing on customer front. You should be able to see a link in this mail below somewhere to access the same. Please note that you would h
Are 997 documents being sent to and received from CDW? How can I see them? (Per NuBridges, none of the CDW 850 documents have been acknowledged by Spectrum. None of our 810&#39;s have been acknowledged by CDW) Once we receive the 997 for the 810s, the status in the WebEDI portal will change to Ack as you can see with Bestbuy. In this case, since they never received the 810s we had sent, the status is not updated.
How can I see our Trading Partners and their settings? You can see the Trading Partner information under the project status in the WebEDI portal. I am afraid, EDI Partner setting won’t be visible to customer.
Who is responsible for the relationship with NUBridges? Are they contracted by Data Trans or CDW? I believe Nubridges is the VAN partner for CDW to which Cleo Data Trans send the files.
Thanks, Sujay
Comment by Sujay Ramesh [ 05/May/25 ]
Hi Lisa,
Following up here. Do you require any further assistance here??
Thanks, Sujay
Comment by Sujay Ramesh [ 13/May/25 ]
Hi Lisa,
Following up here. Do you require any further assistance?
Thanks, Sujay</li>
</ol>
<h4 id="cs-43593-invalid-xml-file-arrival-of-data-from-kehe-distributors-850-created-12-may-25-updated-16-may-25-resolved-16-may-25">[CS-43593] Invalid XML File : Arrival of data from KeHE Distributors-850 Created: 12/May/25  Updated: 16/May/25  Resolved: 16/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Sujay &amp; Team,
The below order from KeHe cannot be brought into our ERP. The Xml file is invalid.
Can you correct and send to us by end of day correctly?
Jennifer Johnson
Customer Service &amp; Logistics Manager
Mobile: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, May 9, 2025 4:29 PM To: VOSS Purchase Orders <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Arrival of data from KeHE Distributors-850
Arrival of data from KeHE Distributors to Voss USA
This email is to inform you that the following documents have arrived from KeHE Distributors. If you are a WebEDI Account holder you may log into WebEDI at <a href="http://www.datatrans-inc.com/login">http://www.datatrans-inc.com/login</a>.
Note WebEDI Users: For reports that contain 997 Functional Acknowledgements (FA) please look at the Status column in your Sent Folder to see which messages have been acknowledged.
Date Time Document Interchange Control Reference Message Batch Id
05/09/2025 4:28 pm ET 850 ********* 4019032 3056005 ******** *********
This is an EXTERNAL email. Please exercise caution. DO NOT open attachments or click links from unknown senders or unexpected email. ****
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 12/May/25 ]
Comment by Sujay Ramesh [ 13/May/25 ]
Hi Jennifer,
I have reprocessed the PO. Please let me know if you could see it at your end.
Thanks, Sujay
Comment by Sujay Ramesh [ 16/May/25 ]
Hi Jennifer,
Following up here. Do you require any further assistance?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/May/25 ]
We are all set! Thank you for your help!
Jennifer Johnson
Customer Service &amp; Logistics Manager
Mobile: (*************
<a href="mailto:<EMAIL>"><EMAIL></a></p>
<h4 id="cs-43701-new-van-partner-for-trading-partner-created-14-may-25-updated-16-may-25-resolved-16-may-25">[CS-43701] new VAN partner for trading partner Created: 14/May/25  Updated: 16/May/25  Resolved: 16/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png
Request Type: Emailed request
Request language: English Request participants: Organizations: Label: WebEDI Questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello: One of our trading partners is in testing for a new VAN partner. To complete the test, I need the following info – please send:
Test EDI Qualifier &amp; ISA ID
Test EDI GS ID
Test Connection Method Type
Thanks,
Jim Braun
Supply Chain Manager
180 East Goodale Ave
Battle Creek, MI 49037
<a href="mailto:<EMAIL>"><EMAIL></a>
330-608-8066
<a href="http://www.snackwerks.com">www.snackwerks.com</a>
<em>CONFIDENTIALITY NOTICE: This email and any data files transmitted with it are the property of Snackwerks of Michigan, LLC and intended to be kept strictly confidential and solely for the use of the intended recipients. Any improper use, dissemination, distribution or copying of confidential information contained within the email or an attachment is strictly prohibited. If you have received this email in error please notify the sender immediately and delete the email and its attachment(s), without viewing, completely from your computer system. CONTRACT DISCLAIMER: Any negotiations between the parties to this email relating the provision of services by Snackwerks of Michigan, LLC are subject to the parties&#39; final written contract. __</em>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 14/May/25 ]
Comment by Sujay Ramesh [ 15/May/25 ]
Hi Jim,
Below are the requested details:
Test EDI Qualifier &amp; ISA ID: ZZ/DTS5660
Test EDI GS ID: DTS5660
Test Connection Method Type: Loren VAN
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/May/25 ]</li>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hi all: Please let me know the following information as soon as possible.
Test EDI Qualifier &amp; ISA ID
Test EDI GS ID
Test Connection Method Type
Thanks,
Jim
Comment by Sujay Ramesh [ 16/May/25 ]
Hi Jim,
I had sent the information yesterday. Resharing the same.Hi Jim,
Below are the requested details:
Test EDI Qualifier &amp; ISA ID: ZZ/DTS5660
Test EDI GS ID: DTS5660
Test Connection Method Type: Loren VAN
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/May/25 ]
Thanks!</li>
</ul>
<h4 id="cs-43869-adams-aldi-s-po-not-received-created-16-may-25-updated-16-may-25-resolved-16-may-25">[CS-43869] Adams: Aldi&#39;s PO Not Received Created: 16/May/25  Updated: 16/May/25  Resolved: 16/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Scott Shippee Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image002.png      image003.png      image004.png      image005.png      image006.png      image-20250516-172908.png    Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Missing documents FTP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Customer: Adams Extracts
Customer ID: 6061
Trading Partner: Aldi’s
Document Type: 850 – Purchase Order
Message 44293910 Aldi’s po 245207 not received for our ERP system, please restage
Scott W. Shippee
Nestell &amp; Associates| 5000 Birch Street | West Tower, Suite 3000 | Newport Beach, CA | 92660, USA
<a href="http://www.NestellAssociates.com">www.NestellAssociates.com</a>
Mobile: (401)529-0678
Timezone: EST/EDT
<a href="https://www.linkedin.com/company/nestell-associates/">https://www.linkedin.com/company/nestell-associates/</a>  <a href="https://twitter.com/NestellAssocia1">https://twitter.com/NestellAssocia1</a>  <a href="https://www.facebook.com/nestellandassociates">https://www.facebook.com/nestellandassociates</a> <a href="https://www.youtube.com/channel/UCJY2x8gU2h_PeT9fv7wTxDQ">https://www.youtube.com/channel/UCJY2x8gU2h_PeT9fv7wTxDQ</a>
The ERPocj Podcast
The ERP Organizational Change Success Blog
Comments
Comment by Scott Shippee [ 16/May/25 ]
Comment by Sujay Ramesh [ 16/May/25 ]
Translated file was dropped.
Comment by Sujay Ramesh [ 16/May/25 ]
Hi Scott,
I had redropped the file. Please let me know if you have received the same.
Thanks, Sujay
Comment by Christopher Morrison [ 16/May/25 ]
Aldi purchase order 245207 was received in our ERP system. Thank you.</p>
<h4 id="cs-43896-glaudia-centeno-12103897052-has-left-you-a-message-47-second-s-long-created-16-may-25-updated-16-may-25-resolved-16-may-25">[CS-43896] GLAUDIA CENTENO (+12103897052) has left you a message 47 second(s) long Created: 16/May/25  Updated: 16/May/25  Resolved: 16/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  message.wav
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Invoice rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Dear Voicemail to support : You received a message from GLAUDIA CENTENO (+12103897052). The message is in mailbox 140, dated Friday, May 16, 2025 at 2:23:24 PM. You can get this message via the phone, or use the links below to download the message. Download the message and mark it as read Download the message and delete it Important: This will completely delete the message and this link will no longer work. Thank you!
Terms of Use | Privacy Policy | ©2025 Sangoma All Rights Reserved
<a href="https://www.facebook.com/sangoma">https://www.facebook.com/sangoma</a>  <a href="https://www.twitter.com/sangoma">https://www.twitter.com/sangoma</a>  <a href="https://www.linkedin.com/company/sangoma/">https://www.linkedin.com/company/sangoma/</a>  <a href="https://www.sangoma.com/">https://www.sangoma.com/</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/May/25 ]
<em>message.wav  (739 kB)</em></p>
<h4 id="cs-43888-urgent-edi-invoices-sent-today-showing-sent-failed-for-our-customer-pool-corp-36-of-them-over-108k-worth-of-invoices-created-16-may-25-updated-16-may-25">[CS-43888] URGENT---EDI invoices sent today showing sent failed for our customer Pool Corp - 36 of them - over $108K worth of invoices Created: 16/May/25  Updated: 16/May/25</h4>
<p>Resolved: 16/May/25
Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Good morning!
When I went on the DataTrans Portal to see the status of the invoices sent today for our customer Pool Corp. It shows as &#34;Sent Failed&#34;
See screenshot below:
36 invoices were sent today for this customer.
This needs to be fixed as soon as possible.
******I left a message today as well on the support line.
Thank you and have a great day! --
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/May/25 ]</li>
</ul>
<h4 id="cs-43874-cardinal-15703851244-has-left-you-a-message-52-second-s-long-created-16-may-25-updated-16-may-25-resolved-16-may-25">[CS-43874] CARDINAL (+15703851244) has left you a message 52 second(s) long Created: 16/May/25  Updated: 16/May/25  Resolved: 16/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  message.wav
Request Type: Emailed request Request language: English
Request participants: None
Organizations: Label: WebEDI Questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Dear Voicemail to support : You received a message from CARDINAL (+15703851244). The message is in mailbox 140, dated Friday, May 16, 2025 at 10:05:49 AM. You can get this message via the phone, or use the links below to download the message. Download the message and mark it as read Download the message and delete it Important: This will completely delete the message and this link will no longer work. Thank you!
Terms of Use | Privacy Policy | ©2025 Sangoma All Rights Reserved
<a href="https://www.facebook.com/sangoma">https://www.facebook.com/sangoma</a>  <a href="https://www.twitter.com/sangoma">https://www.twitter.com/sangoma</a>  <a href="https://www.linkedin.com/company/sangoma/">https://www.linkedin.com/company/sangoma/</a>  <a href="https://www.sangoma.com/">https://www.sangoma.com/</a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/May/25 ]
<em>message.wav  (815 kB)</em></p>
<h4 id="cs-43722-asn-rejected-kangyan-qpm-acct-7269-to-costco-message-id-44158189-created-14-may-25-updated-15-may-25-resolved-15-may-25">[CS-43722] ASN Rejected - Kangyan QPM acct 7269 to Costco - Message ID 44158189 Created: 14/May/25  Updated: 15/May/25  Resolved: 15/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-fwlr1jfn.png      image-20250514-214126.png      image-20250514-213948.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: ASN rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hi,
Could you please see what&#39;s the reasons of rejection for this message? Thank you!
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 14/May/25 ]
Comment by Sujay Ramesh [ 14/May/25 ]
Hi Robert,
It is because it is missing the Date Qualifier. You have only mentioned the Time and not the Delivery Appointment Date and Time.
I checked the earlier successful ASN and I could see that you had not filled it since it was not mandatory. I guess you can skip it and resend it as blank or you could give Delivery Appointment Date and Time and proceed to send it.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 15/May/25 ]
Hi Sujay,
Thank you for your reply. This is a bit strange because I remember I had deleted the time before sending, and now I just deleted it but it came back after I went to Edit Packing. Anyhow, I just sent again and it got accepted. Thanks!</li>
</ul>
<h4 id="cs-43779-re-goldencorr-asns-created-15-may-25-updated-15-may-25-resolved-15-may-25">[CS-43779] Re: GoldenCorr ASNs Created: 15/May/25  Updated: 15/May/25  Resolved: 15/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250515-193040.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: ASN questions CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good morning,
We&#39;ve entered and submitted quite a few ASN&#39;s to our customer GoldenCorr but they are reporting a lot that have not come through to them. Are you able to check to see if there is any issue on our end with the transmission of these ASN&#39;s?
On Tue, May 21, 2024 at 7:21 AM DataTrans Support <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Hello Daniel,
Since the shipment is on a single ASN, there would only be a single shipment ID, BOL, and Manifest ID. They would all be combined on one single form.
Regards,
How would you rate my reply? Great Okay Not Good
– If urgent, please call us with the ticket number and details of the issue.
Sandy Karidas DataTrans Solutions <a href="mailto:<EMAIL>"><EMAIL></a> ************ {#HS:2603089715-2016386#}
On Mon, May 20, 2024 at 4:59 PM CDT, Daniel Nanula <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
Good afternoon:
We&#39;re running into an issue where we have multiple orders on a single railcar. Our customer is asking that we submit the multiple orders as 1 ASN since they are on the same rail car. I have an instance right now, where I am attempting to enter 3 separate packing lists/P.O&#39;s as 1 ASN, but it will not let me submit it through since I have each of the 3 numbers in the bill of lading field. Do you have any suggestions on a workaround for this issue?
!!
Thank you,
Daniel Nanula
(716) 698-7848 <a href="mailto:<EMAIL>"><EMAIL></a> | <a href="http://www.wwfproducts.com/">http://www.wwfproducts.com/</a>
Comments
Comment by Sujay Ramesh [ 15/May/25 ]
Hi Daniel,
I can see that the ASNs sent are in Accepted status which means that GoldenCorr has accepted the ASNs. I can also confirm that they have sent back accepted acknowledgment as well. I have attached the latest ASN raw data reference.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 15/May/25 ]
Thanks, it looks like it was an issue on the receiving end. I appreciate you taking a look into it.</p>
<h4 id="cs-43702-please-resend-invoice-for-kroger-created-14-may-25-updated-15-may-25-resolved-15-may-25">[CS-43702] Please resend invoice for Kroger Created: 14/May/25  Updated: 15/May/25  Resolved: 15/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Brandi Wooldridge Assignee: Sujay Ramesh
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-iqjdehtz.png      image-20250514-171422.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Invoice questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello
Can you please resend this invoice for Kroger ?
KNW20250512F70100030
Thank you Brandi
Comments
Comment by Brandi Wooldridge [ 14/May/25 ]
Comment by Sujay Ramesh [ 14/May/25 ]
Restaged batch.
Comment by Sujay Ramesh [ 14/May/25 ]
Hi Brandi,
We have resent the file from server.
Thanks, Sujay
Comment by Brandi Wooldridge [ 14/May/25 ]
Thank you Sujay
We appreciate your help
Brandi</p>
<h4 id="cs-43351-mckesson-missing-invoice-created-06-may-25-updated-15-may-25-resolved-15-may-25">[CS-43351] Mckesson Missing Invoice Created: 06/May/25  Updated: 15/May/25  Resolved: 15/May/25</h4>
<p>Status: Resolved
Project: Customer Support Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Chicketa Barrett Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      McKesson PO #********** (Inv. #332035).pdf
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Missing documents WebEDI
CSAT Comment: {{webhookResponse.body.value.comment}}
Description  Attn: Support Team,
I checked Data Trans portal, and there is no invoice for the attached order. Please confirm if we create an 810 for this purchase order.
I appreciate your kind help with this matter.
|
Inspired Ideas for Healthier Lives.
Chicketa Barrett Accounts Receivable Analyst 105 Baylis Road, Melville, NY 11747 p: ************ <a href="about:invalid#zCSafez">tel:************</a> e: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="http://www.ascentconsumerproducts.com">www.ascentconsumerproducts.com</a>
Comments  Comment by Chicketa Barrett [ 06/May/25 ]
<em>McKesson PO #********** (Inv. #332035).pdf  (329 kB)</em>
Comment by Sujay Ramesh [ 06/May/25 ]
Hi Chicketa,
I do not see an invoice being sent for this PO. I can only see PO Acknowledgment being sent.
Thanks, Sujay
Comment by Sujay Ramesh [ 13/May/25 ]
Hi Chicketa,
Do you require any further assistance here?
Thanks, Sujay
Comment by Chicketa Barrett [ 14/May/25 ]
Hi Sujay,
The order for this invoice was restage, and DT showing that the invoice was sent.
Thank you.
|
Inspired Ideas for Healthier Lives. Chicketa Barrett Accounts Receivable Analyst 105 Baylis Road, Melville, NY 11747
p: ************ <a href="about:invalid#zCSafez">tel:************</a> e: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="http://www.ascentconsumerproducts.com">www.ascentconsumerproducts.com</a></p>
<h4 id="cs-42986-lpe-rejected-invoices-created-28-apr-25-updated-13-may-25-resolved-13-may-25">[CS-42986] LPE Rejected Invoices Created: 28/Apr/25  Updated: 13/May/25  Resolved: 13/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image-********-214440.png      image-********-214206.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Invoice rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello,
We are working through processing invoices for old shipments and I received a rejection error for the following. Can you please confirm why these are rejected so that we can correct our process?
44080070 810 (Invoice) Walmart / Sams Club E-6380 04/28/2025 03:52 PM 07/12/2023 $94.11 WAL-MART DC 6048A-ASM DIS Rejected
44079985 810 (Invoice) Walmart / Sams Club E-6377 04/28/2025 03:52 PM 07/12/2023 $23.53 WAL-MART DC 6010A-ASM DIS Rejected
44080329 810 (Invoice) Walmart / Sams Club E-6382 04/28/2025 03:30 PM 07/12/2023 $47.06 WAL-MART DC 6006A-ASM DIS Rejected
44080046 810 (Invoice) Walmart / Sams Club E-6379 04/28/2025 03:04 PM 07/12/2023 $23.53 WAL-MART DC 6010A-ASM DIS Rejected
&lt;[pepperexhange.com]&gt;
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/Apr/25 ]
Comment by Sujay Ramesh [ 28/Apr/25 ]
Hi Joel,
The invoices were rejected due to issue in SAC segment i.e. Service, allowance information in line level.
As per the EDI standards, if you are populating UoM under Allowance, then you should provide Rate as well.
You can correct this by providing rate as well or you can chose not to populate UoM and just provide Allowance charge as you had done for previous invoices.
Let me know if you require any further assistance.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/Apr/25 ]
Thank you, Sujey. I restaged these invoices, removed the UoM from the Allowance section and resent them but they still show rejected. Can you please advise why?
Joel
Comment by Sujay Ramesh [ 29/Apr/25 ]
Hello Joel,
I can see them as accepted. Could you please provide list of invoices where it says rejected?
Thanks, Sujay
Comment by Sujay Ramesh [ 05/May/25 ]
Hi Joel,
Following up here. Could you please provide an update?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 05/May/25 ]
Thank you. I think we got this resolved</li>
</ul>
<h4 id="cs-43341-fw-action-required-autozone-parts-lubrication-specialties-llc-inbound-error-created-06-may-25-updated-13-may-25-resolved-13-may-25">[CS-43341] FW: ACTION REQUIRED / AutoZone Parts / Lubrication Specialties LLC / INBOUND ERROR Created: 06/May/25  Updated: 13/May/25  Resolved: 13/May/25</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Candie Rogers Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  ~WRD3802.jpg      image001.png      image-20250506-193557.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: ASN rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good afternoon,
Can you please advise on what the errors below are? Thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 6, 2025 2:36 PM To: Candie Rogers <a href="mailto:<EMAIL>"><EMAIL></a> Cc: <a href="mailto:<EMAIL>"><EMAIL></a>; <a href="mailto:<EMAIL>"><EMAIL></a> Subject: ACTION REQUIRED / AutoZone Parts / Lubrication Specialties LLC / INBOUND ERROR
|
Action Required By: Lubrication Specialties LLC Trading Partner: Lubrication Specialties LLC Action Required: Please correct the document based on the provided error details and resend. If you need further assistance, please open a ticket with support. Error Message: Document rejected. Unable to map source document. The find column or return column for value &#39;DTS4984&#39; in the filename &#39;AUTOZ_vendors.txt&#39; specified in the formula convertCodeFromFile() was not valid.\n Specified find column = 1.\n Specified return column = 2.
Transaction(s) failed:
Reference Number
Sender Receiver Doc Type
Sender Ref/Control Nbr
Alert Received
Direction
F-26256304691
ZZ:DTS4984 ZZ:AZPROD 856 000001315 2025-05-06 18:16:02Z GMT
INBOUND
F-26256092320 ZZ:DTS4984 ZZ:AZPROD 856 000001309
2025-05-06 18:04:05Z GMT
INBOUND
F-26256248975 ZZ:DTS4984 ZZ:AZPROD 856 000001313
2025-05-06 18:10:20Z GMT
INBOUND
F-26256248995 ZZ:DTS4984 ZZ:AZPROD 856 000001312
2025-05-06 18:10:30Z GMT
INBOUND
F-26256417626 ZZ:DTS4984 ZZ:AZPROD 856 000001318
2025-05-06 18:22:14Z GMT
INBOUND
F-26256417205
ZZ:DTS4984 ZZ:AZPROD 856 000001317 2025-05-06 18:22:11Z GMT
INBOUND
F-26256158749
ZZ:DTS4984 ZZ:AZPROD 856 000001310 2025-05-06 18:06:24Z GMT
INBOUND
F-26256287163
ZZ:DTS4984 ZZ:AZPROD 856 000001314 2025-05-06 18:14:46Z GMT
INBOUND
F-26256304903
ZZ:DTS4984 ZZ:AZPROD 856 000001316 2025-05-06 17:58:20Z GMT
INBOUND
F-26256225016
ZZ:DTS4984 ZZ:AZPROD 856 000001311 2025-05-06 18:09:12Z GMT
INBOUND
-------------------------------------<em><strong>If your data is formatted to EDI standards (such as ANSI X12, EDIFACT, TRADACOMS, and HL7), you can download and use EDI Notepad to easily view validation errors on your data. More information here: The Ultimate EDI Editor. This application also helps you view your EDI data in multiple ways, such as HTML (in human readable format), hex, XML, and text views. You only need to download this software once.</strong></em>
This is a system-generated email; please do not reply to this message. If there are any questions regarding this error message, please email <a href="mailto:<EMAIL>"><EMAIL></a>. ------------------------------------- Business Network Support
Automation ID: *********** Website: support.opentext.com  <a href="http://www.opentext.com/">http://www.opentext.com/</a>
This email message is confidential, may be privileged, and is intended for the exclusive use of the addressee. Any other person is strictly prohibited from disclosing or reproducing it. If the addressee cannot be reached or is unknown to you, please inform the sender by return email and delete this email message and all copies immediately.
Comments
Comment by Candie Rogers [ 06/May/25 ]
Comment by Sujay Ramesh [ 06/May/25 ]
Hi Candie,
I could not see any issues with respect to the EDI document. The Functional Acknowledgment document they have sent doesn’t have the rejection reason also. Could you please request in details the reason for rejection?
Thanks, Sujay
Comment by Sujay Ramesh [ 06/May/25 ]
Thanks, Sujay
Comment by Candie Rogers [ 06/May/25 ]
I have made that request. I will let you know. Thanks!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Candie Rogers [ 07/May/25 ]
Good Morning,
They replied that the error is fixed and that it was on their end. Thank you!</p>
<h4 id="cs-43340-fwd-po-rejected-85445278-created-06-may-25-updated-13-may-25-resolved-13-may-25">[CS-43340] Fwd: PO Rejected 85445278 Created: 06/May/25  Updated: 13/May/25  Resolved: 13/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-ymononk1.png      Outlook-pfcmhhma.png      Outlook-deurl2fe.png      image-20250506-213416.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Document Rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good Afternoon,
We pull the purchase orders (850) directly from your portal. Are you able to see this purchase order on your end, 85445278? Do you know why this has not come through?
Thank you,
---------- Forwarded message ---------From: Erika Augenstein <a href="mailto:<EMAIL>"><EMAIL></a> Date: Tue, May 6, 2025 at 2:23 PM Subject: PO Rejected 85445278 To: Kaitlynn Koback <a href="mailto:<EMAIL>"><EMAIL></a>, Amanda Tomeo <a href="mailto:<EMAIL>"><EMAIL></a>, Tish Alvarez <a href="mailto:<EMAIL>"><EMAIL></a>, Matthew Jolly <a href="mailto:<EMAIL>"><EMAIL></a> Cc: DL EDISupport <a href="mailto:<EMAIL>"><EMAIL></a>
Hello, The 850 for PO 85445278 was rejected by your system. Please let us know the cause so we may make the correction and resend. Here is the EDI Data:
ISA<em>00</em> 00 12<em>9858925521 <em>ZZ</em>DTS4337 <em>250506</em>1233</em>U<em>00401</em>000019071<em>0</em>P&gt;~ GS<em>PO</em>9858925521<em>DTS4337</em>20250506<em>1233</em>19071<em>X</em>004010~ ST<em>850</em>29918~ BEG<em>00</em>NE<em>62314914**20250506~ PER</em>BD<em>JON BITELY ************~ DTM</em>010<em>20250513~ N9</em>ZZ<em>1~ MSG</em>A/P Terms: 1% 20, NET 30~ N9<em>ZZ</em>1~ MSG<em>Q3~ N9</em>ST<em>62~ N1</em>ST<em>62-MONTGOMERY-SCP DIST.<em>92</em>62~ N3</em>806 OLIVER CT~ N4<em>MONTGOMERY</em>AL<em>36117-2249~ TD5</em>O<em><em><em><em>PREFERRED CARRIER~ PO1</em>1</em>10</em>CA</em>290.55<em>PE</em>VP<em>T2W-S08X15</em>BP<em>CAR-811-001~ PID</em>F<em><em><em><em>T2W-S08X15 TM 2&#34; WIDE 15/CS TRACK T2 8&#39; SRAIGHT ALUM EA~ PO1</em>2</em>4</em>CA</em>115.24<em>PE</em>VP<em>CRW-6RAD4</em>BP<em>CAR-811-1010~ PID</em>F<em><em><em><em>CRW-6RAD4 4/CS WHITE 6&#34; RADIUS CR CP2 COPING CORNER (EA)~ PO1</em>3</em>5</em>CA</em>91.72<em>PE</em>VP<em>T2W-N6RAD4</em>BP<em>CAR-811-008~ PID</em>F<em><em><em><em>T2W-N6RAD4 T2 4/CS WHITE COPING TRACK NOTCHED 6&#34;Rx2&#39; EA~ PO1</em>4</em>2</em>CA</em>241.8<em>PE</em>VP<em>SMW-N08X15</em>BP<em>CAR-811-013~ PID</em>F<em><em><em><em>SMW-N08X15 15/CS WHITE______ COPING TRAC 8&#39; SIDE MT NOTCHED~ PO1</em>5</em>10</em>EA</em>6.03<em>PE</em>VP<em>CS903FC</em>BP<em>CAR-801-117~ PID</em>F<em><em><em><em>CS903FC FOAM INSERTS~ CTT</em>5</em>31~ SE</em>25</em>29918~
ST<em>850</em>29919~ BEG<em>00</em>NE<em>85445278**20250506~ PER</em>BD<em>MATT JOLLY~ DTM</em>010<em>20250513~ N9</em>ZZ<em>1~ MSG</em>MATT JOLLY~ MSG<em>PHONE: ************~ MSG</em>FAX: ************~ MSG<em>A/P Terms: 1% 20, NET 30~ N9</em>ZZ<em>1~ MSG</em>Q4~ N9<em>ST</em>85~ N1<em>ST</em>85-SPRINGFIELD-SCP DIST.<em>92</em>85~ N3<em>4632 W JUNCTION ST~ N4</em>SPRINGFIELD<em>MO</em>65802-1012~ TD5<em>O</em><em><em><em>PREFERRED CARRIER~ PO1</em>1</em>2</em>BX<em>288.46</em>PE<em>VP</em>CAR-BN-FG<em>BP</em>CAR-55-1061~ PID<em>F</em><em><em><em>CAR-BN-FG 14/BX 8â€™ BULLNOSE FIBERGLASS~ PO1</em>2</em>2</em>BX<em>138.35</em>PE<em>VP</em>CAR-CC-FG100<em>BP</em>CAR-55-1081~ PID<em>F</em><em><em><em>CAR-CC-FG100 100/BOX CANT CLIPS FG STAIRS~ PO1</em>3</em>1</em>EA<em>84</em>PE<em>VP</em>CAR-S002<em>BP</em>CAR-55-1089~ PID<em>F</em><em><em><em>CAR-S002 SLIDE HAMMER TOOL CLIP REMOVER~ CTT</em>3</em>5~ SE</em>24<em>29919~ ST</em>850<em>29920~ BEG</em>00<em>NE</em>K1316971**20250506~ PER<em>BD</em>JON BITELY ************~ DTM<em>010</em>20250513~ N9<em>ZZ</em>1~ MSG<em>A/P Terms: 1% 20, NET 30~ N9</em>ZZ<em>1~ MSG</em>Q3~ N9<em>ST</em>401A~ N1<em>ST</em>401A-BIRMINGHAM-SPP DIST.<em>92</em>401A~ N3<em>5481 VANN PL~ N4</em>BIRMINGHAM<em>AL</em>35235-3217~ TD5<em>O</em><em><em><em>FEDEX GROUND - COMMERCIAL~ PO1</em>1</em>2</em>CA<em>150.52</em>PE<em>VP</em>CRW-4RAD4<em>BP</em>CAR-811-1014~ PID<em>F</em><em><em><em>CRW-4RAD4 CR WHITE CP2 COPING 4&#39; RAD CORNER 4/CS EA~ CTT</em>1</em>2~ SE</em>17<em>29920~ GE</em>3<em>19071~ IEA</em>1*000019071~
Thank you
Erika Augenstein  <a href="http://www.poolcorp.com/">http://www.poolcorp.com/</a>
EDI Coordinator
<a href="mailto:<EMAIL>"><EMAIL></a> 109 Northpark Blvd., Covington, LA 70433
Cell: (*************
<a href="http://www.poolcorp.com">www.poolcorp.com</a>
<a href="http://www.swimmingpool.com">www.swimmingpool.com</a>  <a href="https://www.facebook.com/SwimmingPoolcom/">https://www.facebook.com/SwimmingPoolcom/</a>  <a href="https://www.instagram.com/SwimmingPoolcom/">https://www.instagram.com/SwimmingPoolcom/</a>
Disclaimer
This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. If you have received this email in error, please notify the system manager. This message contains confidential information and is intended only for the individual named. If you are not the named addressee you should not disseminate, distribute or copy this e-mail.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 06/May/25 ]
Comment by Sujay Ramesh [ 06/May/25 ]
Hi Amanda,
It appears that the PO#85445278 didn’t process due to invalid character in the PID segment.
Please request Trading Partner to correct the same and resend the PO.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 07/May/25 ]
Thank you!</p>
<h4 id="cs-43216-entering-invoice-created-02-may-25-updated-13-may-25-resolved-13-may-25">[CS-43216] Entering invoice Created: 02/May/25  Updated: 13/May/25  Resolved: 13/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.png      image003.png      image004.png      image005.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Invoice errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello,
I am having issues entering an invoice.
It will not send because it says I need to put something in these fields - - which I do.
Please advise on how I can get this through.
Thank you!
<a href="https://www.builders-hardware.net/">https://www.builders-hardware.net/</a> | Cindy Westerlund Office Manager
208 Overlook Drive Suite C Sewickley, PA 15143
Email: <a href="mailto:<EMAIL>"><EMAIL></a> Phone: ************ Ext 301 <a href="about:invalid#zCSafez">tel:************%20Ext%20301</a>
<a href="https://www.linkedin.com/company/builders&#39;-hardware-and-specialty-company/">https://www.linkedin.com/company/builders&#39;-hardware-and-specialty-company/</a>  <a href="https://www.facebook.com/profile.php?id=100063579908375">https://www.facebook.com/profile.php?id=100063579908375</a> |
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/May/25 ]
Comment by Sujay Ramesh [ 02/May/25 ]
Hello Cindy,
I believe the issue is because the amount is $0. If you are giving allowance, there should be some amount in it. If there is no allowance please make the entire fields under Service, promotions, allowance as blank.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 03/May/25 ]
I did leave it blank and I still got the error.
<a href="https://www.builders-hardware.net/">https://www.builders-hardware.net/</a> | Cindy Westerlund Office Manager
208 Overlook Drive Suite C Sewickley, PA 15143
Email: <a href="mailto:<EMAIL>"><EMAIL></a> Phone: ************ Ext 301 <a href="about:invalid#zCSafez">tel:************%20Ext%20301</a>
<a href="https://www.linkedin.com/company/builders&#39;-hardware-and-specialty-company/">https://www.linkedin.com/company/builders&#39;-hardware-and-specialty-company/</a>  <a href="https://www.facebook.com/profile.php?id=100063579908375">https://www.facebook.com/profile.php?id=100063579908375</a> |
Comment by Sujay Ramesh [ 05/May/25 ]
Hi Cindy,
Could you try to create a new invoice from scratch and see if it helps? If you still getting the error, we can got into a quick zoom call.
Thanks, Sujay
Comment by Sujay Ramesh [ 09/May/25 ]
Hi Cindy,
Do you require any further assistance here?
Thanks, Sujay</p>
<h4 id="cs-43357-fw-asn-reject-meijer-created-06-may-25-updated-13-may-25-resolved-13-may-25">[CS-43357] FW: ASN Reject - Meijer Created: 06/May/25  Updated: 13/May/25  Resolved: 13/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: mark paine Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: ASN errors
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Please see message below on ASN failure and advise? Thanks
====================================================================== EDI ISA Number: 000010221 EDI Transaction Control Number: 10221 EDI Beginning Ship Notice (BSN) Nbr: 335113 Purchase Order Number: 217299558
----Original Message----From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, May 6, 2025 5:13 PM To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject: ASN Reject - ASCENT CONSUMER PRODUCTS on 2025-05-06 at 17:06</p>
<hr>
<p>ATTENTION REQUIRED *** ******************************** Vendor Name: ASCENT CONSUMER PRODUCTS Vendor ID: 001005644 Partner Name: ASCENTCONSUMER1 Partner ID: DTS5627
Information was rejected on your ASN. Details are listed below. PLEASE CORRECT AND RESEND.
To review the mapping requirements on Meijer vendornet visit WWW.MEIJERVENDORNET.COM
====================================================================== EDI ISA Number: 000010221 EDI Transaction Control Number: 10221 EDI Beginning Ship Notice (BSN) Nbr: 335113 Purchase Order Number: 217299558
Shipped To Location: ERROR : DUPLICATE BSN ======================================================================
Please contact EDI Business Area at email <a href="mailto:<EMAIL>"><EMAIL></a> with any questions or concerns
Comments
Comment by Sujay Ramesh [ 07/May/25 ]
Hi Mark,
I can see the error in the original mail as Duplicate BSN which means Duplicate Shipment ID. I believe Meijer requires their ShipmentID in ASNs to be unique.
For PO#217299558, I can see that there are two ASNs sent with the same Shipment ID: 335113. I guess they rejected the second one saying it is duplicate.
Thanks, Sujay
Comment by mark paine [ 07/May/25 ]
Got it, Thank you.
|
Inspired Ideas for Healthier Lives. Mark Paine VP of Operations 105 Baylis Road, Melville, NY 11747 Direct: ************ <a href="about:invalid#zCSafez">tel:************</a> Office: ************ <a href="about:invalid#zCSafez">tel:************</a> e: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="http://www.ascentconsumerproducts.com">www.ascentconsumerproducts.com</a></p>
<h4 id="cs-42906-asn-error-please-help-message-id-44041590-zoro-po-52312106-created-25-apr-25-updated-13-may-25-resolved-13-may-25">[CS-42906] ASN Error - please help - Message ID 44041590 - Zoro PO 52312106 Created: 25/Apr/25  Updated: 13/May/25  Resolved: 13/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      Outlook-1wdsly23.png      image-20250425-180000.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: ASN rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi,
Please see below email our trading partner saying they found an error in an ASN but the ASN is shown as &#34;Accepted&#34; in WebEDI. Could you check? Thanks!
From: Ben Wang <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, April 25, 2025 11:32 PM To: Robert Chou <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: KangYan QPM Enterprise dba KANGYAN GROUP/Zoro Errored ASN Attention Required
Best regards,
Ben
From: Zoro Go Live <a href="mailto:<EMAIL>"><EMAIL></a> Sent: 2025年4月25日 21:07 To: Ben Wang <a href="mailto:<EMAIL>"><EMAIL></a>; Yen-Ping Shan <a href="mailto:<EMAIL>"><EMAIL></a> Subject: KangYan QPM Enterprise dba KANGYAN GROUP/Zoro Errored ASN Attention Required
Hello,
After review, I found that the ASNs (856) we received on 4/24/2025 errd due to the incorrect line sequence number.
PO 52312106
To prevent these types of errors, please ensure that the line sequence number matches what was sent on the Purchase Order.
Please take a minute to correct the errd 856 and retransmit the document.
Thank you,
Chelsea Holland | Sr. Order Management Specialist
500 W. Madison Street, Suite 4000, Chicago, IL 60661
<a href="http://www.zoro.com">www.zoro.com</a> | Supplier Resource Center
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 25/Apr/25 ]
Comment by Sujay Ramesh [ 25/Apr/25 ]
Hi Robert,
In some cases what happens is that if the EDI file format looks good i.e. the structure, mandatory fields and stuffs are correct, Trading Partner may send us accepted status for the file. If the file has data error like wrong SKUs, in system. I believe the latter has happened here.
Here, I guess the issue is due to incorrect Assigned Identification number in the ASN# 52312106. There is only one line item in the PO. So, here the Assigned Identification# should come as 1.
Thanks, Sujay
Comment by Sujay Ramesh [ 02/May/25 ]
Hi Robert,
Following up here. Could you please let us know if you require any further assistance?
Thanks, Sujay
Comment by Sujay Ramesh [ 09/May/25 ]
Hi Robert,
Do you require any further assistance?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 10/May/25 ]
Hi Sujay,
Thanks for your email. My apologies, I thought I replied. Thanks to your finding, we have corrected the problem and resent successfully. Thank you again!</p>
<h4 id="cs-43301-inbox-shows-1-unread-created-05-may-25-updated-09-may-25-resolved-09-may-25">[CS-43301] Inbox shows 1 unread Created: 05/May/25  Updated: 09/May/25  Resolved: 09/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250505-214946.png      image002.jpg      image001.png      image-20250506-150641.png      image-20250506-152422.png
Request Type: Support
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Inbox Shows 1 Unread and we received notice of arrival for the new 850, but we are not able to pull up
Comments
Comment by Sujay Ramesh [ 05/May/25 ]
Hello,
I could see one PO from Walmart/Sams Club. Below is the snapshot of the same:
If you are not able to see, could you try logging out and logging in again? Sometimes refreshing the session helps.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 05/May/25 ]
We have tried a couple times with no success.
I just tried again now, to log back in.
This is what I see:
Gaby Galan
Sales Associate
14827 W. Harding Rd.,
Turlock, CA 95380
Main Office: 209-668-2471 / Ext. 114
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.selectharvestusa.com">www.selectharvestusa.com</a> <a href="http://www.selectharvestalmondsnacks.com">www.selectharvestalmondsnacks.com</a>
Growers, Processors &amp; Shippers of California Almonds
P Please consider the environment before printing this email.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 06/May/25 ]
Good Morning,
Do you have any update on this?
It looks like maybe the PO is only visible in the administrator account, per Marianne Kania from your company.
How can we get this fixed so that it is visible in the user account that we use to pull any PO’s?
That username is SelectHarvestUSA
Gaby Galan
Sales Associate
14827 W. Harding Rd.,
Turlock, CA 95380
Main Office: 209-668-2471 / Ext. 114
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.selectharvestusa.com">www.selectharvestusa.com</a> <a href="http://www.selectharvestalmondsnacks.com">www.selectharvestalmondsnacks.com</a>
Growers, Processors &amp; Shippers of California Almonds
P Please consider the environment before printing this email.
Comment by Sujay Ramesh [ 06/May/25 ]
Hi Gaby,
I can see that the Admin for your account is Gabriela Galan. You would have to request Gabriela to provide full access for User SelectHarvestUSA for TP Walmart.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 06/May/25 ]
I don’t have that information. I am Gabriela Galan, but I log in as SelectHarvestUSA.
What can we do? I believe Juan Carlos Veraza was the administrator but he is no longer with the company and he might have put my name down, but I don’t have log in information for the Admin.
We need some help, this PO is very important and we need to be able to see it.
What can we do?
Gaby Galan
Sales Associate
14827 W. Harding Rd.,
Turlock, CA 95380
Main Office: 209-668-2471 / Ext. 114
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.selectharvestusa.com">www.selectharvestusa.com</a> <a href="http://www.selectharvestalmondsnacks.com">www.selectharvestalmondsnacks.com</a>
Growers, Processors &amp; Shippers of California Almonds
P Please consider the environment before printing this email.
Comment by Sujay Ramesh [ 06/May/25 ]
In that case, can you try to login via username: 3655. Below is the admin details:
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 06/May/25 ]
Ok, done.
How would I give permission to SelectHarvestUSA to view any documents from Walmart/Sam’s
Gaby Galan
Sales Associate
14827 W. Harding Rd.,
Turlock, CA 95380
Main Office: 209-668-2471 / Ext. 114
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.selectharvestusa.com">www.selectharvestusa.com</a> <a href="http://www.selectharvestalmondsnacks.com">www.selectharvestalmondsnacks.com</a>
Growers, Processors &amp; Shippers of California Almonds
P Please consider the environment before printing this email.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 06/May/25 ]
Got it. Thanks for your help.
Gaby Galan
Sales Associate
14827 W. Harding Rd.,
Turlock, CA 95380
Main Office: 209-668-2471 / Ext. 114
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.selectharvestusa.com">www.selectharvestusa.com</a> <a href="http://www.selectharvestalmondsnacks.com">www.selectharvestalmondsnacks.com</a>
Growers, Processors &amp; Shippers of California Almonds
P Please consider the environment before printing this email.</p>
<h4 id="cs-42893-bobcat-error-duplicate-asn-created-25-apr-25-updated-05-may-25-resolved-02-may-25">[CS-42893] Bobcat Error duplicate ASN Created: 25/Apr/25  Updated: 05/May/25  Resolved: 02/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.jpg      image-20250425-181533.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Duplicate Document
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello,
We have received notification regarding ASN Errors stating that it is a duplicate number. It appears we have been getting a lot of these errors and I am wanting to know if we are doing something wrong when we do the ASN to create the error. Please see below, where we shipped out 3 different items on the same PO but there is an error for a duplicate ASN number.
Thank you! 😊
| Dana Morrison : Customer Service Representative II 2100 Commerce Drive, Carver, MN 55315 Tel: ******.368.5153 <em>+ <a href="mailto:<EMAIL>"><EMAIL></a> +</em>: <a href="http://www.lakeviewindustries.com">www.lakeviewindustries.com</a> |
LAKEVIEW INDUSTRIES WILL BE CLOSED THE FOLLOWING DAYS:
2025 Schedule
Monday May 26th
Friday July 4th
Monday September 1st
Thursday November 27th
Friday November 28th
Thursday December 25th
Friday December 26th
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 25/Apr/25 ]
Comment by Sujay Ramesh [ 25/Apr/25 ]
Hi Dana,
The duplicate errors are due to multiples ASN being sent. I can see that ASN# 0112202-0003 was sent twice with a gap of 18 mins between each of those documents.
Could you please check on your end why it was sent twice?
Thanks, Sujay
Comment by Sujay Ramesh [ 02/May/25 ]
Hi Dana,
Do you require any further assistance here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/May/25 ]
Hello,
No, I don’t believe so. I do appreciate your help.
Thank you! 😊
| Dana Morrison : Customer Service Representative II 2100 Commerce Drive, Carver, MN 55315 Tel: ******.368.5153 <em>+ <a href="mailto:<EMAIL>"><EMAIL></a> +</em>: www
LAKEVIEW INDUSTRIES WILL BE CLOSED THE FOLLOWING DAYS:
2025 Schedule
Monday May 26th
Friday July 4th
Monday September 1st
Thursday November 27th
Friday November 28th
Thursday December 25th
Friday December 26th</li>
</ul>
<h4 id="cs-43048-corrupt-files-001710428449-007290428061-created-29-apr-25-updated-02-may-25-resolved-02-may-25">[CS-43048] Corrupt Files - 001710428449 &amp; 007290428061 Created: 29/Apr/25  Updated: 02/May/25  Resolved: 02/May/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      ~WRD4092.jpg      image001 (00a95b58-86cf-4c79-95d8-ce9c4e12f856).png      image002.png      DA89038a5_3 (2).xml
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Delayed document delivery
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi Team,
We need the files for the two 850’s below resent to us ASAP. The files came into our ERP as corrupt and cannot be processed:
001710428449
007290428061
Jennifer Johnson
Customer Service &amp; Logistics Manager
Mobile: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 29/Apr/25 ]
Comment by Sujay Ramesh [ 29/Apr/25 ]
Hi Jennifer,
I have resent the file from server.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/Apr/25 ]
We are still not seeing these orders in BC.
Please let me know if you need further assistance.
Stefanie Goretzka
Customer Service Representative
Cell: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/Apr/25 ]
The files have not come over the orders are not in our system.
Is there a reason they file sent first time where corrupt?
Jennifer Johnson
Customer Service &amp; Logistics Manager
Mobile: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Sujay Ramesh [ 30/Apr/25 ]
Hello Team,
We are looking into this.
Thanks, Sujay
Comment by Sujay Ramesh [ 30/Apr/25 ]
Hi Team,
I have again dropped the XML file with different file name. Please let me know if you can see the same at your end.
Thanks, Sujay Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/Apr/25 ]
Hi there,
They came over with errors again and did not go in BC…
Please let me know if you need further assistance.
Stefanie Goretzka
Customer Service Representative
Cell: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, April 30, 2025 1:16 PM To: Stefanie Goretzka <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-43048 RESOLVED  Corrupt Files - 001710428449 &amp; 007290428061
———-—
Reply above this line.
Sujay Ramesh commented:
Hi Team,
I have again dropped the XML file with different file name. Please let me know if you can see the same at your end.
Thanks, Sujay
View request · Turn off this request&#39;s notifications
Sent on April 30, 2025 11:16:12 AM MDT
**** This is an EXTERNAL email. Please exercise caution. DO NOT open attachments or click links from unknown senders or unexpected email. ****
Comment by Sujay Ramesh [ 30/Apr/25 ]
Hello Stefanie,
I have redropped the files just now after correcting few of the XML syntax errors. Let me know if that fixed the issue.
Thanks, Sujay
Comment by Sujay Ramesh [ 30/Apr/25 ]
Did some XML syntax check and redropped the file.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/Apr/25 ]
I am not seeing these yet…
Please let me know if you need further assistance.
Stefanie Goretzka
Customer Service Representative
Cell: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, April 30, 2025 4:06 PM To: Stefanie Goretzka <a href="mailto:<EMAIL>"><EMAIL></a> Subject:  CS-43048 RESOLVED  Corrupt Files - 001710428449 &amp; 007290428061
———-—
Reply above this line.
Sujay Ramesh commented:
Hello Stefanie,
I have redropped the files just now after correcting few of the XML syntax errors. Let me know if that fixed the issue.
Thanks, Sujay
View request · Turn off this request&#39;s notifications
Sent on April 30, 2025 2:06:00 PM MDT
**** This is an EXTERNAL email. Please exercise caution. DO NOT open attachments or click links from unknown senders or unexpected email. ****
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 30/Apr/25 ]
The below is what it is saying in error.
Please let me know if you need further assistance.
Stefanie Goretzka
Customer Service Representative
Cell: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Sujay Ramesh [ 30/Apr/25 ]
Hi Stefanie,
Is the below error in the latest file or the previous files as well?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 01/May/25 ]
That was the first time, I have attached the file for the other 6 times they have come over with errors. I have also put below what the latest error message shows.
Please let me know if you need further assistance.
Stefanie Goretzka
Customer Service Representative
Cell: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
<em>DA89038a5_3 (2).xml  (9 kB)</em>
Comment by Sujay Ramesh [ 02/May/25 ]
Hi Stefanie,
I will work on reprocessing (instead of resending it) the entire document and see if it helps. I will let you know once it is done.
Thanks, Sujay
Comment by Sujay Ramesh [ 02/May/25 ]
Hi Stefanie,
I re-processed the message. Please let me know if it looked good at your end?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/May/25 ]
Yes, thank you! I am seeing them on our side now!
Please let me know if you need further assistance.
Stefanie Goretzka
Customer Service Representative
Cell: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
Comment by Sujay Ramesh [ 02/May/25 ]
Reprocessed the file copy to new batch</p>
<h4 id="cs-42109-810-invoice-issue-po-8155743-created-09-apr-25-updated-29-apr-25-resolved-29-apr-25">[CS-42109] 810 Invoice Issue | PO 8155743 Created: 09/Apr/25  Updated: 29/Apr/25  Resolved: 29/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  810 Invoice PO 81554477.pdf      image-20250409-192755.png      image-20250409-193131.png      image-20250409-193100.png      Document Processing Error_ Cabelas Canada 2025-04-09 19_47_46.eml      image-20250409-203154.png      image.png      image-20250409-223914.png      image-20250409-224246.png     image-20250409-224030.png      image-20250424-120629.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: WebEDI Questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi There: Unfortunately, there is another separate issue. For an 810 invoice, We need two line items for the various discounts applied, 3% for &#34;Market Development Funds&#34; and 2% for &#34;Ad Allowance&#34;, and currently there is only 1 input area available.
We have had this issue with previous invoices, and the changes made for the additional line items don&#39;t seem to be carried over. I have attached a previous invoice that had the correct data.
Please advise, we need to send an 810 invoice within one business day of the product being shipped, which was yesterday.
Thank you, Devon (*************
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Apr/25 ]
<em>810 Invoice PO 81554477.pdf  (444 kB)</em>
Comment by Sujay Ramesh [ 09/Apr/25 ]
Hi Devon,
I checked invoice# 5235368 and I can see two discounts here
You can achieve the same in any invoice by clicking the + icon
It will allow you to add one more allowance detail.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Apr/25 ]
Ok, thanks, now I am getting an error message when I send (attached) and the totals are doing the weird thing it did in the past.
The internet is guiding to the below as an issue, but its unclear to me why this is the case, as I think I&#39;m following the steps we did previously for it to work.
Can you please take a look and let me know? I&#39;m sure its a small issue, but we&#39;re really under the gun on getting this out by end of day, or we&#39;ll face non-compliance fines
<em>In your EDI file, there are SAC segments (which define allowances or charges), and in these segments, SAC06 or SAC07 is present without the other one. That’s not allowed. SAC06 = Allowance or Charge Method of Handling Code SAC07 = Allowance or Charge Rate Per the syntax rule P0607, if you include one, the other must also be there.</em>
<em>Document Processing Error_ Cabelas Canada 2025-04-09 19_47_46.eml  (18 kB)</em>
Comment by Sujay Ramesh [ 09/Apr/25 ]
Hi Devon,
It is missing percentage.
Once you fill up the percentage which I believe here is 3% and 2%, it should automatically deduct in the total.
Thanks, Sujay
Comment by Sujay Ramesh [ 09/Apr/25 ]
Hi Devon,
Ignore the last line in the previous mail since I am not sure about the mappings here. You can also unselect the allowance charge qualifier since I can’t see them being present in the previous invoice# 5235368. That way you don
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Apr/25 ]
No, the percentage calculator does not work, this is an issue that has been flagged before, and was supposed to be fixed, but to get it work previously, those line items were left blank to force it through the system. Comment by Sujay Ramesh [ 09/Apr/25 ]
Hi Devon,
So, were you able to send the invoice# 5235580 successfully now unselecting the allowance charge percent qualifier?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Apr/25 ]
No, the issues persist.
Comment by Sujay Ramesh [ 09/Apr/25 ]
Could you join zoom call to troubleshoot <a href="https://cleo.zoom.us/j/99730478281">https://cleo.zoom.us/j/99730478281</a>
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Apr/25 ]
Logging in now.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Apr/25 ]
Here are the correct totals for Invoice 4124470 / PO 8155743!image.png|thumbnail!
Comment by Sujay Ramesh [ 09/Apr/25 ]
Got into a zoom call with customer. There is an issue on how the amount is being calculate at the end. Below is the math as per Devon
The total amount is coming correct which here is 5582.12. After we give discounts the math is also correct.
The issue arises when we add the GST. The GST as per Devon should be added on the the discounted amount i.e 5303.02. But it is being calculated on the Merchandise Total. This is messing up with the total as below
Customer is frustrated here.
Thanks, Sujay
Comment by Sandy Karidas [ 10/Apr/25 ]
<a href="https://dtsprojects.atlassian.net/browse/DP-5430">https://dtsprojects.atlassian.net/browse/DP-5430</a>
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 10/Apr/25 ]
Hello Devon,
This is with our developers. Currently in testing. I will let you know once I hear if it passes or fails testing.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 10/Apr/25 ]
Current support ticket related to discount issue. <a href="https://datatrans-inc.atlassian.net/browse/CS-40306">https://datatrans-inc.atlassian.net/browse/CS-40306</a>
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 11/Apr/25 ]
Hi There:
Is there a status update to provide? We need to send another invoice today, I really hope this issue has been resolved.
Please let me know. Thanks, Devon (*************
Comment by Sandy Karidas [ 11/Apr/25 ]
Hello Devon,
I met with development testing team and the 810 failed testing. It is back in progress with the developer. It is at a high priority and I understand the frustration of manually creating your invoices. As soon as I have additional inform
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 11/Apr/25 ]
Sandy: Please call me. It&#39;s not simply a matter of manually inputting items, which is definitely a pain, but more importantly, its that I can&#39;t get the correct totals on the invoice. Even if I manually enter the values, they adjust automatically t
For today, Is there not a way to manually override this point for this issue? The invoice in question is Invoice 1 that was supposed to be sent within 24 hours of delivery, and now we&#39;re at 48 hours. There is a second shipment tha now needs to be billed, so we are running the risk of not just one, but two non-compliance fines for these delays.
Please call me, I need to understand timelines.
Thank you, Devon (*************
Comment by Sandy Karidas [ 14/Apr/25 ]
Hello Devon,
The invoice issue failed testing and is back in progress with the development team. I will provide an update later this morning. I also provided Drew Fisher, from customer success, with your contact information and provided deta issues you have experienced and the request for credit. Please let me know if he has been in contact with you.
Please contact me if you have any questions.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 14/Apr/25 ]
Hi Devon,
Just wanted to provide you with an update, the development team is still working on the issue and I will provide additional details in the morning.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 14/Apr/25 ]
HI Sandy:
Is it reasonably going to be done tomorrow? We&#39;re getting pinged from Cabela&#39;s about the issues, and really need to give them an update.
Thank you, Devon (*************
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 15/Apr/25 ]
Hi There: Is there an update you can provide? We are now one week past when the invoice should have been sent, please advise.
I just tried to call but had no answer. Thanks, Devon (*************
Comment by Sandy Karidas [ 15/Apr/25 ]
Hello Devon,
I am waiting to hear back from the development team. I ran a test this morning. The values are working correctly, but the total is not updating until the 810 is saved, closed and then reopened. I have asked them to fix that issue.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/Apr/25 ]
Hi there:
Are there any updates on this?
Thanks, Devon (*************
Comment by Sandy Karidas [ 16/Apr/25 ]
Hi Devon,
I was just informed the issue passed testing. Waiting to hear when it will be moved into production.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 17/Apr/25 ]
Is this in production yet? I need to send it today, we’re getting flagged at cabelas. Please advise.
Comment by Sandy Karidas [ 17/Apr/25 ]
Hello Devon,
The 810 issue is in production on your account for newly created invoices. This will not affect invoices already in the draft folder.
Please contact me if you have any questions.
Regards
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 17/Apr/25 ]
The invoice is still wrong. I called and it went to voicemail. The GST is being calculated on the pre-discounted merchandise total, whereas it should be off the discounted values. Merchandize totals also do not seem to be correct (it works until then, similar to before).
Please call me to resolve this, and also have whoever is handling the client relationship (re: reimbursement for continued issues).
Thanks, Devon (************* Comment by Sandy Karidas [ 18/Apr/25 ]
Hello Devon,
I will look at this and address it with our development team.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 23/Apr/25 ]
Hello Devon,
The updates are in testing, once the QA team lets me know if they pass or fail I will let you know.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2
Comment by Sandy Karidas [ 24/Apr/25 ]
Hello Devon,
The issue has been resolved on the 810. The tax amount is being calculated on the discounted dollar amount. I tested it in your account with message ID ********.
Merchandise line item amount - 318.04
Discount 2% - 6.36
Tax on discounted amount of 311.68 - 15.58
Total amount - 327.26
Please contact me if you need additional assistance or have any questions.
Regards,
Sandy Karidas
Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2</p>
<h4 id="cs-41860-re-fw-tsc-drop-ship-inventory-file-not-received-803083-created-03-apr-25-updated-28-apr-25-resolved-28-apr-25">[CS-41860] Re: Fw: TSC Drop Ship Inventory File Not Received - 803083 Created: 03/Apr/25  Updated: 28/Apr/25  Resolved: 28/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Krista Johnson Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-********-213215.png    Request Type: Emailed request
Request language: English
Request participants: Organizations:
Label: Notification CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi...
I checked your Draft folder and there were 846 documents there. So they are creating automatically but something is off that is not allowing them to send.
Tomorrow... if the 846 has not both created and sent... reach out to me and I&#39;ll use that latest copy to research.
For now, I&#39;ve sent the 846 that was created around 2 a.m.
And I&#39;ve copied in DataTrans Support to address on your not receiving any email. Although I don&#39;t see any new purchase orders since 03/31/25.
Thanks,
Krista
On 4/3/2025 12:18 PM, Steele Mixer wrote:
Good morning, Krista. I received this email again. Also, I didn&#39;t receive an email letting me know I receive a new order? Thanks
Stephen Steele 415-845-3127
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, April 3, 2025 6:04 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Cc: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Subject: TSC Drop Ship Inventory File Not Received - 803083
Good Morning,
It has been at least 32 hours since the last inventory file update was received. Inventory files are required at least once a day, with the exceptions of weekends and TSC observed holidays. Please submit your file now. If there are any questions, please email the drop ship team at <a href="mailto:<EMAIL>"><EMAIL></a>.
Thank you, Tractor Supply Drop Ship Team <a href="mailto:<EMAIL>"><EMAIL></a>
FFM Center ID DSV Inventory File Name DSV Inventory File Email
Comments
Comment by Sujay Ramesh [ 03/Apr/25 ]
Thanks, Sujay
Comment by Sujay Ramesh [ 03/Apr/25 ]
Hi Stephen,
As Kirsta mentioned, the last PO was received on 3/31 which was PO# 9016776546. Could you please confirm if you had received email for the same?
Thanks, Sujay
Comment by Stephen Steele [ 03/Apr/25 ]
Hi Sujay. I never received an email that day letting me know I received an order. Thanks
Stephen Steele
Comment by Sujay Ramesh [ 03/Apr/25 ]
Hi Stephen,
Could you check once if the mail was marked as Junk, spam, promotion or is present in other tabs?
Thanks, Sujay
Comment by Stephen Steele [ 05/Apr/25 ]
Hi Sujay. I check my spam and junk folders and no email was sent notifying me. I have a new purchase order. Another one came through this morning and I did not receive an email for it as well. Please make sure the email is <a href="mailto:<EMAIL>"><EMAIL></a>. Thanks Stephen Steele Steelemixer.com 415-845-3127
On Apr 3, 2025, at 3:54 PM, Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
———-— Reply above this line.
Sujay Ramesh commented:
Hi Stephen,
Could you check once if the mail was marked as Junk, spam, promotion or is present in other tabs?
Thanks, Sujay
View request · Turn off this request&#39;s notifications Sent on April 3, 2025 4:54:52 PM MDT
Comment by Sujay Ramesh [ 07/Apr/25 ]
Hi Stephen,
I have again updated the email to <a href="mailto:<EMAIL>"><EMAIL></a> for receiving the notifications. Please let me know if you still don’t receive the email.
Thanks, Sujay
Comment by Stephen Steele [ 07/Apr/25 ]
Will do thanks. Stephen Steele Steelemixer.com 415-845-3127
On Apr 7, 2025, at 12:24 PM, Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
———-— Reply above this line.
Sujay Ramesh commented:
Hi Stephen,
I have again updated the email to <a href="mailto:<EMAIL>"><EMAIL></a> for receiving the notifications. Please let me know if you still don’t receive the email.
Thanks, Sujay
View request · Turn off this request&#39;s notifications Sent on April 7, 2025 1:24:04 PM MDT
Comment by Sujay Ramesh [ 15/Apr/25 ]
Hi Stephen,
Could you please provide an update here?
Thanks, Sujay
Comment by Stephen Steele [ 15/Apr/25 ]
Hi Ramesh, what is it that you are requesting? Thanks Stephen Steele Steelemixer.com 415-845-3127
On Apr 15, 2025, at 1:54 PM, Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
———-— Reply above this line.
Sujay Ramesh commented:
Hi Stephen,
Could you please provide an update here?
Thanks, Sujay
View request · Turn off this request&#39;s notifications Sent on April 15, 2025 2:54:14 PM MDT
Comment by Sujay Ramesh [ 16/Apr/25 ]
Hi Stephen,
I was asking whether you are receiving email notification for the new POs. But I can see that no new POs were received. I will keep this ticket open until you recieve a new PO.
Thanks, Sujay
Comment by Stephen Steele [ 16/Apr/25 ]
Ye I haven’t received any new pos. Thanks Stephen Steele Steelemixer.com 415-845-3127
On Apr 16, 2025, at 8:55 AM, Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
———-— Reply above this line.
Sujay Ramesh commented:
Hi Stephen,
I was asking whether you are receiving email notification for the new POs. But I can see that no new POs were received. I will keep this ticket open until you recieve a new PO.
Thanks, Sujay
View request · Turn off this request&#39;s notifications Sent on April 16, 2025 9:55:55 AM MDT
Comment by Sujay Ramesh [ 28/Apr/25 ]
Hi Stephen,
Could you confirm if you you had received notification for PO# 9016861044 which was received on 4/22?
Thanks, Sujay
Comment by Stephen Steele [ 28/Apr/25 ]
Yes I did thanks. Stephen Steele Steelemixer.com 415-845-3127
On Apr 28, 2025, at 12:24 PM, Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
———-— Reply above this line.
Sujay Ramesh commented:
Hi Stephen,
Could you confirm if you you had received notification for PO# 9016861044 which was received on 4/22?
Thanks, Sujay
View request · Turn off this request&#39;s notifications Sent on April 28, 2025 1:24:02 PM MDT
Comment by Sujay Ramesh [ 28/Apr/25 ]
Thanks for the confirmation. I will go ahead and close the ticket.
Thanks, Sujay</p>
<h4 id="cs-42716-rite-aid-purchase-order-created-22-apr-25-updated-28-apr-25-resolved-28-apr-25">[CS-42716] Rite Aid Purchase Order Created: 22/Apr/25  Updated: 28/Apr/25  Resolved: 28/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.jpg      image-20250422-212620.png      image001.png      image-20250423-151107.png      image001 (64f86706-8a40-4ff8-b6ff-2c813f878a60).png     image-20250423-160049.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Update WebEDI user
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Good morning.
One of our distributors, Rite Aid, sent a Purchase Order via EDI, but we didn’t receive it on our end. From Rite Aid’s end, here is the acknowledgement that was returned to SPS:
ISA<em>00</em> 00 ZZ<em>DTS5826 <em>ZZ</em>RITEAIDSPS <em>250320</em>1043|00501</em>000000013<em>0</em>P&gt;~
GS<em>FA</em>DTS5826<em>RITEAIDSPS</em>20250320<em>1043</em>13<em>X</em>005010~
ST<em>997</em>00013~
AK1<em>PO</em>1000~
AK2<em>850</em>1000~
AK5<em>A~
AK9</em>A<em>1</em>1<em>1~
SE</em>6<em>00013~
GE</em>1<em>13~
IEA</em>1*000000013~
When you have a chance, can you please investigate?
Thank you. Jayne
Jayne Byrum | Customer Service Support Specialist
Aidance Scientific, Inc.
184 Burnside Ave
Woonsocket, RI 02895
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.aidanceproducts.com">www.aidanceproducts.com</a>
Work: (401) 671-6189 | Ext. 719
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/Apr/25 ]
Comment by Sujay Ramesh [ 22/Apr/25 ]
Hi Jayne,
We have manually pushed the PO## 8238564 and the same can be seen in the Portal.
The issue was that SPS had sent the PO with duplicate Interchange Control Number 100000000 which was already sent before. We had to manually modify this ICN# to push the PO to the web portal. Could you please request with 100000012 or higher values.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/Apr/25 ]
Hi Sujay.
When I type in 8238564 as a Reference Number, there aren’t any results still.
Can we meet via Teams so that I can share my screen?
Thank you. Jayne
Jayne Byrum | Customer Service Support Specialist
Aidance Scientific, Inc.
184 Burnside Ave
Woonsocket, RI 02895
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.aidanceproducts.com">www.aidanceproducts.com</a>
Work: (401) 671-6189 | Ext. 719
Comment by Sujay Ramesh [ 23/Apr/25 ]
Hi Jayne,
Please filter by all messages. It should show up.
If you still cannot see the PO, we can connect via Teams.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/Apr/25 ]
Hi Sujay. When I filter by All Messages, it still isn’t appearing.
Please let me know what your availability looks like.
Thank you. Jayne
Comment by Sujay Ramesh [ 23/Apr/25 ]
Hi Jayne,
Can you join zoom now? <a href="https://cleo.zoom.us/j/91323708680">https://cleo.zoom.us/j/91323708680</a>
Thanks, Sujay
Comment by Sujay Ramesh [ 23/Apr/25 ]
Hi Jayne,
Thanks for joining in. This appears to be an access issue. Please request Nora (admin) to provide required access for Rite Aid SPS TP.
Thanks, Sujay
Comment by Sujay Ramesh [ 28/Apr/25 ]
Hi Jayne,
Do you require any further assistance here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/Apr/25 ]
Hi Sujay. You may close the ticket. Thank you for reaching out and asking if I needed further assistance. Have a great night!
Jayne Byrum | Customer Service Support Specialist
Aidance Scientific, Inc.
184 Burnside Ave
Woonsocket, RI 02895
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.aidanceproducts.com">www.aidanceproducts.com</a>
Work: (401) 671-6189 | Ext. 719</li>
</ul>
<h4 id="cs-42634-new-password-created-19-apr-25-updated-25-apr-25-resolved-25-apr-25">[CS-42634] new password Created: 19/Apr/25  Updated: 25/Apr/25  Resolved: 25/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Support
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
My new password meets the requirements but your system tells me it is too weak.
Comments
Comment by Sujay Ramesh [ 21/Apr/25 ]
Hello,
Even if a password meets the basic requirements, our system may still flag it as weak if it’s commonly used or easy to guess. You could try including a mix of uppercase &amp; lowercase letters, use more number of special characters such as !,@,# and so on. You could also try to make the password longer.
Thanks, Sujay
Comment by Sujay Ramesh [ 24/Apr/25 ]
Hello,
Do you require any further assistance here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 25/Apr/25 ]
No the problem is resolved.
Markay Boros CFO/Owner Alamarra Inc ************ (working from home)</p>
<h4 id="cs-42389-fw-external-re-edi-asn-rejected-created-14-apr-25-updated-25-apr-25-resolved-25-apr-25">[CS-42389] Fw: [EXTERNAL] Re: EDI ASN rejected Created: 14/Apr/25  Updated: 25/Apr/25  Resolved: 25/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      RE__Arrival_of_data_from_UNFI_Conventional__SuperValu__824___2nd <em>Attempt</em>_.eml      image-20250414-194418.png     RE__Arrival_of_data_from_UNFI_Conventional__SuperValu__824___2nd <em>Attempt</em>_ (13e4e108-2eb6-4753-ab48-68d2f87fa9ea).eml      image-20250414-203625.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Missing documents WebEDI
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Good afternoon, Cleo EDI Team.
I searched and couldn&#39;t find how to get to 997&#39;s. Could you please forward the Message ID for the 997/response to our Message ID 43555437 (ASN/856 to trading partner SpartanNash)?
From: Kim Benson Sent: Monday, April 14, 2025 6:59 AM To: admin; Alex Cc: ASN Subject: RE: [EXTERNAL] Re: EDI ASN rejected
Did you receive a 997 when you resent the ASN? It is the vendors responsibility to track to confirm data was received/rejected/accepted with errors via the returned 997.
Kim Benson
Analyst, EDI Integrations
Phone: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
From: admin <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Friday, April 11, 2025 2:50 PM To: Kim Benson <a href="mailto:<EMAIL>"><EMAIL></a>; Alex <a href="mailto:<EMAIL>"><EMAIL></a> Cc: ASN <a href="mailto:<EMAIL>"><EMAIL></a> Subject: [EXTERNAL] Re: EDI ASN rejected
--- Use caution with links and attachments ---
Good afternoon, Kim.
We never heard anything back on whether this resubmission was received and acceptable, and we have now received a $150 fine from SpartanNash. Could you please look into this for us?
As a refresher, this was a parcel shipment, so there were no pallet tiers (or pallets for that matter), but as a courtesy, we retransmitted as soon as possible (the same day as your notification) with a pallet tier of &#34;1&#34;.
Any assistance you can offer is very much appreciated,
Nick Thipsingh
Oleander Brands International, LLC.
+****************
<a href="http://www.lander-int.com">www.lander-int.com</a>
From: admin Sent: Thursday, March 13, 2025 12:35 PM To: Kim Benson; Alex Subject: RE: EDI ASN rejected
Good afternoon, Kim, and thank you for letting us know.
This shipment was parcel sized, but the ASN has been resent with Tiers as “1” and number of Blocks (cases) as “28” – Message ID 43555437. Please let us know if you don’t receive it or if there’s more information needed.
Enjoy the rest of the day,
Nick Thipsingh
Oleander Brands International, LLC.
+****************
<a href="http://www.lander-int.com">www.lander-int.com</a>
From: Kim Benson &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Sent: Thursday, March 13, 2025 5:05 AM To: Alex &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt;; admin &lt; <a href="mailto:<EMAIL>"><EMAIL></a> &gt; Subject: EDI ASN rejected
Invalid data format in PAL02 and 03 elements, please correct and resend.
ISA<em>00</em> 00 ZZ<em>DTS6373 <em>08</em>9255350010 <em>250312</em>1639</em>U<em>00403</em>000000024<em>0</em>P&gt;~
GS<em>SH</em>DTS6373<em>9528306682</em>20250312<em>1639</em>24<em>X</em>004030UCS~
ST<em>856</em>0024~
BSN<em>00</em>09-1612<em>20250312</em>1730<em>0001~
HL</em>1<strong>S~
TD5<em>B</em>2<em>UPSG~
TD3</em>TL~
REF<em>BM</em>1Z2R0E700396954556~
DTM<em>067</em>20250311~
FOB<em>PP~
N1</em>ST<em>SPARTANNASH, INC.<em>9</em>0069622940054~
N3</em>1300 WEST ELKHORN AVENU~
N4<em>SIOUX FALLS</em>SD<em>57104~
N1</em>SF<em>Oleander Brands</em>9<em>1044257810000~
N3</em>194 Tamarack Circle~
N4<em>Skillman</em>NJ<em>08558~
HL</em>2<em>1</em>O~
PRF*726485~
TD1</strong>1~
HL<em>3</em>2<em>T~
MAN</em>GM<em>00008138220100108125~
PAL**N/A</em>N/A<em>6~
HL</em>4<em>3</em>P~
LIN<strong>UA*081382201609~
SN1</strong>28<em>CA~
DTM</em>036<em>20250312~
HL</em>5<em>4</em>I~
LIN<strong>UP*081382201609~
SN1</strong>28<em>CT~
CTT</em>5~
SE<em>29</em>0024~
GE<em>1</em>24~
IEA<em>1</em>000000024~
Kim Benson
Analyst, EDI Integrations
Phone: (*************
<a href="mailto:<EMAIL>"><EMAIL></a>
CONFIDENTIALITY NOTICE: <em>The contents of this email message and any attachments are intended solely for the addressee(s) and may contain confidential and/or privileged information and may be legally protected from disclosure. If you are not the intended recipient of this message or their agent, or if this message has been addressed to you in error, please immediately alert the sender by reply email and then delete this message and any attachments. If you are not the intended recipient, you are hereby notified that any use, dissemination, copying, or storage of this message or its attachments is strictly prohibited.</em>
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 14/Apr/25 ]
<em>[^RE_Arrival_of_data_from_UNFI_ConventionalSuperValu824__2nd Attempt.eml] _(27 kB)</em>
Comment by Sujay Ramesh [ 14/Apr/25 ]
Hi Nick,
I cannot see a 997 being sent for ASN Shipment ID: 09-1612. I had confirmed the same from server end as well. I can see 997s for other ASNs where it was accepted.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 14/Apr/25 ]
Thank you for the quick response, Sujay.
Do you see any notes or updates to the ticket I submitted March 18th (email attached) inquiring about the status of this 856 (ASN)?
And tied to that, can you see a reason why SpartanNash wouldn’t have responded with a 997 or didn’t receive this 856 (ASN) in the first place?
Nick Thipsingh
Oleander Brands International, LLC.
+****************
<a href="http://www.lander-int.com">www.lander-int.com</a>
<em>[^RE_Arrival_of_data_from_UNFI_ConventionalSuperValu824__2nd Attempt_ (13e4e108-2eb6-4753-ab48-68d2f87fa9ea).eml] (28 kB)</em>
Comment by Sujay Ramesh [ 14/Apr/25 ]
Hi Nick,
The 856 was sent to their connection partner Opentext. Below is the snapshot of the same:
Could you ask them to track the above document at their end?
Thanks, Sujay
Comment by Sujay Ramesh [ 17/Apr/25 ]
Hi Nick,
Do you require any further assistance here?
Thanks, Sujay
Comment by Sujay Ramesh [ 21/Apr/25 ]
Hi Nick,
Following up here. Do you require any further assistance?
Thanks, Sujay</li>
</ul>
<h4 id="cs-42555-fw-arrival-of-data-from-delhaize-ahold-850-created-17-apr-25-updated-24-apr-25-resolved-17-apr-25">[CS-42555] Fw: Arrival of data from Delhaize / Ahold-850 Created: 17/Apr/25  Updated: 24/Apr/25  Resolved: 17/Apr/25</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
We are unable to open this PO on the Website. I believe this may be the customer we have been working on getting set up. Can you please help with this?
Sarah Winfrey
Fulcher&#39;s Seafood, LLC ************ phone ************ fax 114 Gatlin Road PO Box 40 Alliance NC 28509 <a href="mailto:<EMAIL>"><EMAIL></a>
The information, including any attachments, contained in this electronic message is intended for the exclusive use of the addressee(s) and may contain confidential or privileged information. Any use of the material by any unintended recipient is strictly prohibited. If you are not the intended recipient, please notify the sender immediately at ************ and destroy all copies of this message and any attachments. Thank you Appointments required. Please contact Andrea Mumper to schedule ************  <a href="mailto:<EMAIL>"><EMAIL></a>
________________________________________ From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, April 17, 2025 11:58 AM To: orders Subject: Arrival of data from Delhaize / Ahold-850
Arrival of data from Delhaize / Ahold to Fulcher&#39;s Seafood This email is to inform you that the following documents have arrived from Delhaize / Ahold. If you are a WebEDI Account holder you may log into WebEDI at <a href="http://www.datatrans-inc.com/login">http://www.datatrans-inc.com/login</a>. Note WebEDI Users: For reports that contain 997 Functional Acknowledgements (FA) please look at the Status column in your Sent Folder to see which messages have been acknowledged.
Date Time Document Interchange Control Reference Message Batch Id 04/17/2025 11:58 am ET 850 ********* 0001 994001 ******** *********
Comments
Comment by Krista Johnson [ 17/Apr/25 ]
Removing support
Hi...
It appears that this is a test for Giant Foods. I&#39;ll take a look in a bit and will update your project over to Delhaize/Ahold.
Thanks,
Krista
Comment by Sujay Ramesh [ 17/Apr/25 ]
Krista acknowledged that this appears to be a Test. Changing ticket status to resolve.
Thanks, Sujay
Comment by Sujay Ramesh [ 24/Apr/25 ]
Hello Sarah,
Do you require any assistance from support team here. I can see that Krista mentioned that this would be a Test order.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 24/Apr/25 ]
I do not believe so. Thank you for checking.
Sarah Winfrey <a href="mailto:<EMAIL>"><EMAIL></a> 114 Gatlin Rd., PO Box 40 Alliance, NC 28509 ************
Please Note: Appointments are required for BOTH pickup and delivery. Please contact Andrea Mumper to schedule ************ <a href="mailto:<EMAIL>"><EMAIL></a>
The information, including any attachments, contained in this electronic message is intended for the exclusive use of the addressee(s) and may contain confidential or privileged information. Any use of the material by any unintended recipient is strictly prohibited. If you are not the intended recipient, please notify the sender immediately at ************ and destroy all copies of this message and any attachments. Thank you</p>
<hr>
<h4 id="cs-42671-invoice-90019381-rejected-created-21-apr-25-updated-24-apr-25-resolved-24-apr-25">[CS-42671] Invoice 90019381 Rejected Created: 21/Apr/25  Updated: 24/Apr/25  Resolved: 24/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Nutkao_90019381_000000212.txt      Screenshot 2025-04-22 at 8.02.54 AM.png      image-20250422-171447.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Document Rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good afternoon.
Please send me the raw data for invoice 90019381 sent to TP Lidl.
...prima di stampare pensa all&#39;ambiente...think before you print...
Nutkao srl, in ottemperanza alla normativa ex D.Lgs 231/01 e successive modifiche, si è dotata di un proprio Codice Etico, di un Modello di organizzazione e ha nominato un Organismo di Vigilanza. Il Codice Etico è a disposizione sul sito Internet <a href="http://www.nutkao.com">www.nutkao.com</a>. Chiunque intrattiene rapporti con Nutkao srl si impegna a rispettare i principi contenuti nel Codice Etico. La violazione di tali principi comporta cessazione del rapporto fiduciario: Nutkao srl si riserva di interrompere ogni rapporto con chiunque violi tali principi e di agire legalmente per il ristoro di eventuali danni.
In accordance to the Italian laws, Decree N. 231/01 and subsequent amendments, Nutkao has decided to adopt a Code of Ethics, such as an organization and management Model. Therefore Nutkao has also designate a Supervisory Board in order to watch on this Model. The Code of Ethics is available on the Company website <a href="http://www.nutkao.com">www.nutkao.com</a>. Anyone who has dealings with Nutkao srl is request to be engaged towards the respect of Nutkao’s Code of Ethics. Violation of its rules imposes the end of the partnership. Nutkao srl has then the right to break down any relationship with anyone who violates these principles and to act under Laws regulations to solve any contingent damage.
Comments
Comment by Sujay Ramesh [ 21/Apr/25 ]
Hello Manuel,
Please find the attached raw data for Invoice# 90019381.
Thanks, Sujay
Nutkao_90019381_000000212.txt
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/Apr/25 ]
Good morning, Sujay.
I cannot open the attachment. When I click on it, a new window opens up asking me to sign in. !Screenshot 2025-04-22 at 8.02.54 AM.png|thumbnail!
Comment by Sujay Ramesh [ 22/Apr/25 ]
Hi Manuel,
We are using Jira portal to handle support tickets. So you would have to sign up to retrieve the attachment. Meanwhile, I have sent you a separate mail with the requested file.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/Apr/25 ]
Hi Sujay,
Can you please resend the other email?
Comment by Sujay Ramesh [ 22/Apr/25 ]
Hi Manuel,
I have resent it. Please do check your spam/junk folder too.
Thanks, Sujay Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 22/Apr/25 ]
I received it. Thanks!
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 23/Apr/25 ]
Good morning, Sujay.
I&#39;ve got a response from the TP&#39;s EDI team. I edited and resent the invoice. They confirmed they received it. You can close this ticket.
The N1 SU segment should be your supplier GLN – 0863734000100.
You sent N1<em>SU</em>Nutkao USA Inc<em>9</em>079561900
ISA<em>00</em> <em>00</em> <em>ZZ</em>DTS3677 <em>ZZ</em>4058119000004 <em>250417</em>1523<em>U</em>00401<em>000000212</em>0<em>P</em>~ GS<em>IN</em>DTS3677<em>4058119000004</em>20250417<em>1523</em>212<em>X</em>004010 ST<em>810</em>0097 BIG<em>20250417</em>90019381<em>20250414</em>152023042560 CUR<em>BY</em>USD REF<em>SI</em>152023042560 N1<em>BY</em>Lidl US Operations<em>UL</em>4059591000001 N3<em>3500 S Clark St N4</em>Arlington<em>VA</em>22202 N1<em>ST</em>Lidl US Operations Perryville<em>UL</em>4059591000070 N3<em>81 Belvidere RD N4</em>Perryville<em>MD</em>21903<em>US N1</em>SU<em>Nutkao USA Inc</em>9<em>079561900 N3</em>7044 NC 48 N4<em>Battleboro</em>NC<em>27809 REF</em>IA<em>21528 DTM</em>011<em>20250417 DTM</em>670<em>20250417 IT1</em>1<em>960</em>CA<em>22.75**UK</em>4334035233826<em>VP</em>02202480012 QTY<em>QD</em>960<em>CA CTP********21840 PID</em>F<em><em><em><em>Hazelnut Spread with Cocoa TDS</em>2184000 CTT</em>1</em>960 SE</em>23<em>0097 GE</em>1<em>212 IEA</em>1*000000212</p>
<h4 id="cs-42502-request-regarding-trading-partner-sur-la-table-created-16-apr-25-updated-21-apr-25-resolved-21-apr-25">[CS-42502] Request Regarding Trading Partner: Sur La Table Created: 16/Apr/25  Updated: 21/Apr/25  Resolved: 21/Apr/25</h4>
<p>Status: Resolved Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Bob Ballengee Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      000000000045068078.0000.000279919.850      Sur_La_Table_846_000001350.txt      image (9ce9a0fd-8ec2-48b3-853e-0b12c7ecd065).png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Inventory/846 document
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
You informed me a few weeks ago that Sur La Table was not equipped to receive automatic 846 (Inventory Advice) reports.
We&#39;ve been encouraging them to get set up with that capability. They have requested the &#34;raw data&#34; for their 846. I manually transmitted one for them yesterday (see the image below).
I don&#39;t really know what they mean by &#39;raw data&#39;, but I am hoping that you do.
Please let me know your thoughts and if that is something you can provide.
Thanks!
Comments
Comment by Bob Ballengee [ 16/Apr/25 ]
My apologies! I forgot to include the screenshot of the 846 that I referenced in the previous email.
!image.png|thumbnail!
Comment by Sujay Ramesh [ 17/Apr/25 ]
Hello Bob,
Please find the attached EDI 846 raw data which was sent to Sur La Table. Please let us know if you require any further assistance.
Thanks, Sujay
000000000045068078.0000.000279919.850
Comment by Bob Ballengee [ 17/Apr/25 ]
Hi Sujay,
That link is password protected. I tried using the credentials I use for signing in to <a href="https://datatrans-inc.com/login/">https://datatrans-inc.com/login/</a>, but it doesn&#39;t work.
How can I access this information?
Comment by Sujay Ramesh [ 17/Apr/25 ]
Hi Bob,
Please ignore the previous attachment. Could you confirm if you can access the attachment in this current mail.
Thanks, Sujay
Sur_La_Table_846_000001350.txt
Comment by Bob Ballengee [ 17/Apr/25 ]
No, I cannot access it. Just like the previous link, when I click on the link that you provided, it takes me to this web page: !image (9ce9a0fd-8ec2-48b3-853e-0b12c7ecd065).png|thumbnail!
Comment by Sujay Ramesh [ 17/Apr/25 ]
Hi Bob,
I have sent you a separate mail with the attachment link for reference.
Please note that you would have to sign up the Jira portal in the snapshot you had provided. After you have signed up, you should be able to view the attachment via the ticket link.
Thanks, Sujay
Comment by Bob Ballengee [ 17/Apr/25 ]
Ok, I was able to get my sign in set up and retrieve the data.
Thank you!</p>
<h4 id="cs-42483-dollar-general-orders-created-16-apr-25-updated-21-apr-25-resolved-21-apr-25">[CS-42483] DOLLAR GENERAL ORDERS Created: 16/Apr/25  Updated: 21/Apr/25  Resolved: 21/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello,
I wanted to see if there was a problem in the transmitting the Dollar General Orders.
I know that the new skus were used on these orders if that maybe causing the issue.
I have yet to receive them in our system and they were accepted and processed this morning around 10:00AM.
Here is a couple POs to look at:
257KS8
257KS4
Thank you,
Noor Amer
Allrite Logistics
614-568-7447
All BOL’s must read:
Your company name c/o Allrite Logistics
6969 Alum Creek Dr – Dock 15-18
Columbus, OH 43217
Please note if sending via USPS use:
6969 Alum Creek Dr – Suite 3
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/Apr/25 ]
I will take a look Noor. Thank you for the info.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/Apr/25 ]
The issue has been resolved and the messages have been restaged. They should arrive within the next 1-2 hours.
Thank you.
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/Apr/25 ]
Thank you for your help, Brantley. I appreciate it.
Thank you,
Noor Amer
Allrite Logistics
614-568-7447
All BOL’s must read:
Your company name c/o Allrite Logistics
6969 Alum Creek Dr – Dock 15-18
Columbus, OH 43217
Please note if sending via USPS use:
6969 Alum Creek Dr – Suite 3</p>
<h4 id="cs-42056-missing-amazon-invoices-created-08-apr-25-updated-21-apr-25-resolved-21-apr-25">[CS-42056] Missing Amazon Invoices Created: 08/Apr/25  Updated: 21/Apr/25  Resolved: 21/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Candie Rogers Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Document not delivered to TP
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good afternoon,
The following invoices are showing in DataTrans as accepted (sent) but they are not showing in the Amazon portal as received. I tried to delete the first one and re do it…it still isn’t showing in Amazon. Are you able to check to see if they were sent on your end? Thank you!
1/2/2025 INV195528 BH0v1NB4B 3/4/2025 61 $11.96
1/2/2025 INV195507 BxTg3wflB 3/4/2025 61 $47.19
1/6/2025 INV196246 BfdKWrl1w 3/8/2025 57 $47.19
1/6/2025 INV196430 BHcGd4Drw 3/8/2025 57 $47.19
1/6/2025 INV196474 Bxhqqw1gw 3/8/2025 57 $47.19
1/6/2025 INV196480 B7FhzCmKw 3/8/2025 57 $47.19
1/6/2025 INV196458 BkKwTrjLw 3/8/2025 57 $223.20
1/6/2025 INV196477 Bkfm5gjGw 3/8/2025 57 $12.76
1/13/2025 INV197515 Bt3lxlgvy 3/15/2025 50 $47.19
1/13/2025 INV197420 BfTz451Sy 3/15/2025 50 $13.27
1/13/2025 INV197580 BWJntH7sy 3/15/2025 50 $94.38
1/22/2025 INV199506 B7RsxDwPD 3/24/2025 41 $25.52
1/24/2025 INV199858 BWW0kx8YD 3/26/2025 39 $11.16
1/24/2025 INV199869 BqcHFx7HD 3/26/2025 39 $25.52
1/24/2025 INV199913 Bszf03D9D 3/26/2025 39 $13.56
1/24/2025 INV199920 Bxf7rw8tD 3/26/2025 39 $11.16
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comments
Comment by Candie Rogers [ 08/Apr/25 ]
Comment by Sujay Ramesh [ 08/Apr/25 ]
Hi Candie,
I spot checked few of the invoice files and I could see that they were accepted. I double confirmed on the server end and could see that we had sent the file to amazon and we have also received Accepted Functional Acknowledgment for the Invoices sent.
Could you try to reach out to Amazon to check if there are any issues?
Thanks, Sujay
Comment by Candie Rogers [ 08/Apr/25 ]
I sure can, thank you for the update!
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |
Comment by Sujay Ramesh [ 17/Apr/25 ]
Hi Candie,
Just following up to check if you need any further assistance here?
Thanks, Sujay
Comment by Candie Rogers [ 17/Apr/25 ]
Thank you for the follow up…this ended up being something on Amazon’s end.
| Candie Rogers Retail Sales Assistant LUBRICATION SPECIALTIES, LLC wholly owned subsidiary of Gold Eagle Co 255 Neal Ave. Mt. Gilead, OH 43338 (O) ************ ext. 122 <a href="http://www.LubricationSpecialties.com">www.LubricationSpecialties.com</a> |</p>
<h4 id="cs-42426-invoice-submission-edi-created-15-apr-25-updated-17-apr-25-resolved-17-apr-25">[CS-42426] Invoice Submission- EDI Created: 15/Apr/25  Updated: 17/Apr/25  Resolved: 17/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.jpg      image002.jpg      image001 (59320f6d-d72d-4ea1-973a-15550decee40).jpg      image001 (0c35b174-acc3-4992-a9b4-bd67de209d6b).jpg
Request Type: Emailed request
Request language: English
Request participants: None
Organizations:
Label: Missing 997 CSAT Comment: {{webhookResponse.body.value.comment}}
Description
[][To Whom It May Concern:|]
Please read below, the following invoices are still under “Sent” on status. Please review and advice. Thanks in advance.
Submitted on 4/01/2025-
Invoice 044067
Amount $1,650.75
Shipment 3/27/2025
Submitted on 4/07/2025-
Invoice 044192 for $1,044.84
Invoice 044194 for $1,733.34
Shipment 4/01/2025
Thank you,
Carmen Avalos
Accounting Clerk
Shamrock Precision
14850 Venture Drive
Farmers Branch, TX 75234
972-241-4226 Ext 522
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.shamrockprecision.com">www.shamrockprecision.com</a>
AS9100 &amp; ISO9001 Certified/ ITAR Registered
Notice: Shamrock Precision’s Terms and Conditions have been recently updated. You can find the current revision on our website at: shamrockprecision.com/purchase-order-terms-and-conditions
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 15/Apr/25 ]
Comment by Sujay Ramesh [ 15/Apr/25 ]
Hello Carmen,
I checked our server and I could see the mentioned invoices were sent to Halliburton. I don’t see an acknowledgment being sent back for these invoices though. Do you want me to repush the files?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 15/Apr/25 ]
[][Please, thanks you….|]
Thank you,
Carmen Avalos
Accounting Clerk
Shamrock Precision
14850 Venture Drive
Farmers Branch, TX 75234
972-241-4226 Ext 522
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.shamrockprecision.com">www.shamrockprecision.com</a>
AS9100 &amp; ISO9001 Certified/ ITAR Registered
Notice: Shamrock Precision’s Terms and Conditions have been recently updated. You can find the current revision on our website at: shamrockprecision.com/purchase-order-terms-and-conditions
Comment by Sujay Ramesh [ 16/Apr/25 ]
Hi Carmen,
The three invoices are currently in Acknowledged status on Web Portal.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/Apr/25 ]
[][Good morning Sujay and thanks for your support, good day.|]
Thank you,
Carmen Avalos
Accounting Clerk
Shamrock Precision
14850 Venture Drive
Farmers Branch, TX 75234
972-241-4226 Ext 522
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="http://www.shamrockprecision.com">www.shamrockprecision.com</a>
AS9100 &amp; ISO9001 Certified/ ITAR Registered
Notice: Shamrock Precision’s Terms and Conditions have been recently updated. You can find the current revision on our website at: shamrockprecision.com/purchase-order-terms-and-conditions</p>
<h4 id="cs-42415-r-w-sauder-inbound-purchase-order-not-received-reference-**********-kwik-trip-inc-created-15-apr-25-updated-17-apr-25-resolved-17-apr-25">[CS-42415] R.W. Sauder - Inbound purchase order not received - Reference ********** - Kwik Trip, Inc Created: 15/Apr/25  Updated: 17/Apr/25  Resolved: 17/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English Request participants: Organizations: Label: Delayed document delivery
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good morning,
Kwik Trip sent a purchase order (reference **********) yesterday. We have not received this purchase order in our system and it does not appear in the web portal.
Thank you
Comments
Comment by Sujay Ramesh [ 15/Apr/25 ]
Hi Michael,
I checked our server. I cannot see the PO being received on our end. Could you request it to be resent?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 16/Apr/25 ]
Good morning, Sujay. A member of our customer service department contacted me and informed me that this order was successfully received. I checked the portal as well and can confirm that order ********** was received.
Thank you for your assistance</p>
<h4 id="cs-42048-john-deere-rejected-asn-t161853-created-08-apr-25-updated-17-apr-25-resolved-17-apr-25">[CS-42048] John Deere Rejected ASN T161853 Created: 08/Apr/25  Updated: 17/Apr/25  Resolved: 17/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0 Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      edi_D20250408T120322.edi      RE__John_Deere_Goods_Receipt_Issue__1018102___Deere_Status_ _In_Progress___No_ASN__Part_Number__T161853.eml      image-20250408-194018.png      image-20250408-194251.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Document Rejection
CSAT Comment: {{webhookResponse.body.value.comment}}
Description  * CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello,
Can you please look into this and let me know why it was rejected?
Thanks</p>
<hr>
<p>Anne Stanton | Customer Service Representative II
2100 Commerce Drive, Carver, MN 55315
Tel: ******.368.5242
<a href="mailto:<EMAIL>"><EMAIL></a> | <a href="http://www.lakeviewindustries.com">www.lakeviewindustries.com</a>
<em><strong>Reminder that the Freight Surcharge does go into effect on October 21st 2024</strong></em>
Office Hours
Monday-Thursday 7:00AM-4:00PM
Friday 7:00AM-2:00PM
LAKEVIEW INDUSTRIES WILL BE CLOSED THE FOLLOWING DAYS:
Monday May 26th
Friday July 4th
Monday September 1st
Thursday November 27th
Friday November 28th
Thursday December 25th
Friday December 26th
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 08/Apr/25 ]
<em>edi_D20250408T120322.edi  (0.5 kB)</em>
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 08/Apr/25 ]</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hello,
Follow up to the below request, please see the attached email from our customer that is saying that there is an incorrect line.
Thanks</li>
</ul>
<hr>
<p>Anne Stanton | Customer Service Representative II
2100 Commerce Drive, Carver, MN 55315
Tel: ******.368.5242
<a href="mailto:<EMAIL>"><EMAIL></a> | <a href="http://www.lakeviewindustries.com">www.lakeviewindustries.com</a>
<em><strong>Reminder that the Freight Surcharge does go into effect on October 21st 2024</strong></em>
Office Hours
Monday-Thursday 7:00AM-4:00PM
Friday 7:00AM-2:00PM
LAKEVIEW INDUSTRIES WILL BE CLOSED THE FOLLOWING DAYS:
Monday May 26th
Friday July 4th
Monday September 1st
Thursday November 27th
Friday November 28th
Thursday December 25th
Friday December 26th
From: Anne Stanton
<em>[^RE_John_Deere_Goods_Receipt_Issue1018102_Deere_Status In_Progress_No_ASNPart_NumberT161853.eml] _(121 kB)</em>
Comment by Sujay Ramesh [ 08/Apr/25 ]
Hi Anne,
This is due insufficient information in the SubLine Item details:
Thanks, Sujay
Comment by Sujay Ramesh [ 08/Apr/25 ]
Hi Anne,
Apologies for the earlier mail. I had by mistakenly clicked send.
The ASN was rejected due to insufficient information in the Subline Item Details:
It is missing the Assigned Identification Number and the Quantity both of which are mandatory as per EDI specification.
You can also ignore to fill the details in Subline Item section since I could see in earlier documents you had not had these information and it was accepted by JD.
Thanks, Sujay
Comment by Sujay Ramesh [ 15/Apr/25 ]
Hi Anne,
Could you please let us know if you require any further assistance here?
Thanks, Sujay</p>
<h4 id="cs-41670-810-documents-created-01-apr-25-updated-17-apr-25-resolved-17-apr-25">[CS-41670] 810 Documents Created: 01/Apr/25  Updated: 17/Apr/25  Resolved: 17/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250401-211221.png      image-20250401-211455.png      image-20250401-211136.png      image-20250401-211825.png      image-20250401-211640.png     image-20250402-191006.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Missing documents WebEDI
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi, Data Trans Team.
Good day!
I am reaching out to see if you can help us look into invoices that was sent 2/23/2025 on the below list of Purchase orders, however, upon checking today, it looks liek there was no invoices submitted.
By any chance, will you be able to check if any invoices were transmitted or any problem that may occur why its showing no invoice was sent?
Invoice Numbers
300452615
300452642
300452753
300452921
300453245
300453254
300453302
300455913
300456049
300456399
300456411
300456523
300456525
300456678
300456681
300459054
300459134
300459135
300459226
300459233
300459235
300459369
300459460
– Looking forward to your reply.
Comments
Comment by Sujay Ramesh [ 01/Apr/25 ]
Checking in ECS 03
Cannot see anything being sent on 2/23.
Randomly checked few of the Invoices mentioned in the list and in the EDI portal, they were sent on 3/31 and also I could see them in accepted status
Same can also been seen in server where the 810s were sent on 3/31
Thanks, Sujay
Comment by Sujay Ramesh [ 01/Apr/25 ]
Hi Kathleen,
I randomly checked the invoices from the list and I could see them being sent successfully to Trading Partner with Accepted status on 3/31.
Could you please confirm and let us know if you require any further help?
Thanks, Sujay Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Apr/25 ]
Hi, Sujay.
Thanks for your response.
Yes, that&#39;s correct because when I get into the portal, I was surprised that the status of the 810 documents was unfulfilled when we sent these documents on 2/28. Was there a way to check if there&#39;s a history of 2/28 -810 docum
The reason for asking is that we are expecting a payment 1st-2nd week of March but only to find out that invoices were ont transmitted to Sephora.
Looking forward to hearing from you.
Best, Kathleen
<a href="https://www.avast.com/sig-email?utm_medium=email&amp;utm_source=link&amp;utm_campaign=sig-email&amp;utm_content=webmail">https://www.avast.com/sig-email?utm_medium=email&amp;utm_source=link&amp;utm_campaign=sig-email&amp;utm_content=webmail</a>
Virus-free.www.avast.com
DAB4FAD8-2DD7-40BB-A1B8-4E2AA1F9FDF2
Comment by Sujay Ramesh [ 02/Apr/25 ]
Hi Kathleen,
I filtered the dates between 2/23 &amp; 3/1 on our server and I could only see few 810s being sent on 26th to Sephora which I can see in the portal as well.
I can confirm that no 810s were sent rest of the days.
Thanks, Sujay
Comment by Sujay Ramesh [ 07/Apr/25 ]
Hi Kathleen,
Could you please let us know if you require any further assistance here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 07/Apr/25 ]
Hi, Sujay.
Thank you for your reply.
However, we would like to request to investigate further as we have submitted batches of invoices dated 2/28.
Is there anyway you can check it for us?
Thank you
Comment by Sujay Ramesh [ 09/Apr/25 ]
Hi Kathleen,
Normally once the document passes the EDI portal, we will see it on our server. I had checked the server for any documents being sent on 2/28 but I didn’t find anything.
Thanks, Sujay
Comment by Sujay Ramesh [ 15/Apr/25 ]
Hi Kathleen,
Do you require any further assistance here?
Thanks, Sujay</p>
<h4 id="cs-42363-franklin-international-unacknowledged-810-invoices-from-04-11-2025-created-14-apr-25-updated-17-apr-25-resolved-17-apr-25">[CS-42363] Franklin International - Unacknowledged 810 Invoices from 04/11/2025 Created: 14/Apr/25  Updated: 17/Apr/25  Resolved: 17/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Joe McMahan Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: Label: Missing 997
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello DTS support,
From Friday 04/11/2025 there are seven Invoices that show as “Sent” on WebEDI.
For LBMX – Nemeon
2751176 2751365
For LBMX – Sphere1
2750966 2751205 2751210 2751335 2751348
Can you please confirm that these Invoices were truly sent and were 997’s received from LBMX?
Thanks,
Franklin International
Joe McMahan
Comments
Comment by Sujay Ramesh [ 14/Apr/25 ]
Hi Franklin,
I can confirm that the invoices were sent to LBMX. We have not yet received the 997s though for the 810s.
Thanks, Sujay
Comment by Joe McMahan [ 14/Apr/25 ]
Hello Sujay,
I contacted LBMX regarding the missing 997’s and they indicated they had not received the 810’s (see their response below)
Please resend the six other Invoices.
Thank you,
Franklin International
Joe McMahan
Hi Joe, Out of all those invoices, we were only able to receive 2750966. I also have to mention that said invoice errored out because the member is no longer part of Sphere1. Please check with your VAN regarding the transmission of the other documents. Regards,
Leandro Santos
Customer Support Specialist Comment by Sujay Ramesh [ 14/Apr/25 ]
Hi Joe,
I have restaged the six invoices.
Thanks, Sujay
Comment by Joe McMahan [ 15/Apr/25 ]
Hello Sujay,
The six Invoice are now showing on WebEDI as “accepted”.
Thank you for your help
Franklin International
Joe McMahan</p>
<h4 id="cs-42101-i-would-like-to-add-two-email-addresses-to-receive-notifications-created-09-apr-25-updated-17-apr-25-resolved-17-apr-25">[CS-42101] I would like to add two email addresses to receive notifications. Created: 09/Apr/25  Updated: 17/Apr/25  Resolved: 17/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Request Type: Support
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
I am trying to add another email in the notifications. I have separated the two addresses with a comma, but it tells me that it is an invalid address.
Comments
Comment by Sujay Ramesh [ 09/Apr/25 ]
Hi,
Can you try to add the mail without any spaces and just comma, eg:
<a href="mailto:<EMAIL>"><EMAIL></a>,<a href="mailto:<EMAIL>"><EMAIL></a>
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 09/Apr/25 ]
That seems to have worked. Thank you!</p>
<h4 id="cs-41888-arrow-chemical-co-e-mail-confirmation-of-orders-created-03-apr-25-updated-15-apr-25-resolved-15-apr-25">[CS-41888] Arrow Chemical Co. - E mail confirmation of orders Created: 03/Apr/25  Updated: 15/Apr/25  Resolved: 15/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  Outlook-A blue and.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: Notification
CSAT Comment: {{webhookResponse.body.value.comment}}
Description</p>
<ul>
<li>CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.
Hi Team,
I would like to change the email address that the order confirmations are sent to please. Can you change it to this email?
<a href="mailto:<EMAIL>"><EMAIL></a>
And you can take it off of <a href="mailto:<EMAIL>"><EMAIL></a>
Please let me know when this is completed.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 03/Apr/25 ]
Comment by Sujay Ramesh [ 04/Apr/25 ]
Hi Matt,
Please refer to below step for adding/changing email notifications:
To add and/or remove contacts from the email notifications list. Click on Settings in the My WebEDI dropdown.
Click on Notifications
Select the Trading Partner or all from the dropdown
Enter email address(es) separated by a comma
Select the reports or all for which to receive the notification.
Click Save.
Thanks, Sujay
Comment by Sujay Ramesh [ 09/Apr/25 ]
Hi Matt,
Do you require any further assistance here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 10/Apr/25 ]
I will let you know if i do thanks</li>
</ul>
<h4 id="cs-42035-fw-shipment-id-with-missing-invoices-3-days-created-08-apr-25-updated-15-apr-25-resolved-15-apr-25">[CS-42035] FW: Shipment ID With Missing Invoices &gt; 3 Days Created: 08/Apr/25  Updated: 15/Apr/25  Resolved: 15/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  ASN &amp; Invoice Numbers.xlsx
Request Type: Emailed request Request language: English
Request participants:
Organizations: Label: Missing documents WebEDI
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good morning Holly,
I am receiving emails from Toyota like the one below saying that they have received our ASN&#39;s but not some invoices. I have attached a list of the ASN &amp; Invoice numbers that they are asking about. When I search for them in DataTrans they are complete and accepted. We&#39;re just wondering why Toyota can not see them. Can you help me with this please? This will cause delays in our payment process if not cleared up.
Thanks, Barbara
----Original Message----From: PARTS HOST <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, April 8, 2025 1:30 AM To: Barbara Thompson <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Shipment ID With Missing Invoices &gt; 3 Days
Hi All,
We have received the EDI856 ASN however we have not received the corresponding EDI810 invoice for the below mentioned ship ID&#39;s
TMS 0929A 057462 TMS 0929A 062405 TMS 0929A 61522 TMS 0929A 61526 TMS 0929A 61528 TMS 0929A 62003 TMS 0929A 62009 TMS 0929A 62102 TMS 0929A 62154 TMS 0929A 62157 TMS 0929A 62301 TMS 0929A 62405
So please transmit us the invoice soon to avoid delay in making Payments
Thanks, PARTS HOST Support Team.
Note: This is an automated email. Any replies to this ID are not monitored.
-------------------------- 5.68 --------------------------- * E-Mail originated from: * Jobname: YP16DX Job Number: JOB13976 * Userid: YP16DX User Name: PRODUCTION.JOB * System: ASYS Node: TMSN1 * Date: April 7, 2025 22:29:48 (Monday) * ----------------------------------------------------------- *
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 08/Apr/25 ]
<em>ASN &amp; Invoice Numbers.xlsx  (11 kB)</em></p>
<h4 id="cs-41698-five-river-outbound-loads-from-03-31-25-created-02-apr-25-updated-15-apr-25-resolved-15-apr-25">[CS-41698] Five River Outbound Loads from 03/31/25 Created: 02/Apr/25  Updated: 15/Apr/25  Resolved: 15/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image008.png      image009.png      image010.png      image011.png      image012.png      image001.png      image002.png
Request Type: Emailed request
Request language: English Request participants: Organizations: Label: Missing documents WebEDI
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
OT,
Five Rivers is stating they have sent these over through EDI-However-I am not showing them on my side. Can we check on these.
Thanks,
Angela Spurlock
Inside Sales Representative
7301 East County Rd.142 • Blytheville, AR 72315
Phone: **************
Sales: **************
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.linkedin.com/company/nucor-corporation/">https://www.linkedin.com/company/nucor-corporation/</a>  <a href="https://www.facebook.com/NucorCorp">https://www.facebook.com/NucorCorp</a>  <a href="https://www.instagram.com/nucorcorporation/">https://www.instagram.com/nucorcorporation/</a>  <a href="https://www.youtube.com/user/thenucorchannel">https://www.youtube.com/user/thenucorchannel</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, April 2, 2025 8:05 AM To: Spurlock, Angela (NSAR) <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Nucor Outbound Loads from 03/31/25
Yes, I sent it through yesterday.
Thank you,
Shanoe’ Teague
Five Rivers Distribution, LLC
2020 Riverfront Road
P.O. Box 5606
Van Buren, AR 72957
PH: (*************
Shipping/Receiving Hours are M-F
7:30 AM to 3:00 PM
From: Spurlock, Angela (NSAR) <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, April 2, 2025 8:00 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Nucor Outbound Loads from 03/31/25
Hey Shanoe’,
Has this one been sent through EDI yet?
Thanks,
Angela Spurlock
Inside Sales Representative
7301 East County Rd.142 • Blytheville, AR 72315
Phone: **************
Sales: **************
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.linkedin.com/company/nucor-corporation/">https://www.linkedin.com/company/nucor-corporation/</a>  <a href="https://www.facebook.com/NucorCorp">https://www.facebook.com/NucorCorp</a>  <a href="https://www.instagram.com/nucorcorporation/">https://www.instagram.com/nucorcorporation/</a>  <a href="https://www.youtube.com/user/thenucorchannel">https://www.youtube.com/user/thenucorchannel</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, April 1, 2025 9:15 AM To: Spurlock, Angela (NSAR) <a href="mailto:<EMAIL>"><EMAIL></a>; Schultz, Lauren (NSAR) <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Melissa Salmon Acc <a href="mailto:<EMAIL>"><EMAIL></a> Subject: [EXT] Nucor Outbound Loads from 03/31/25
This message came from outside of Nucor.
Thank you,
Shanoe’ Teague
Five Rivers Distribution, LLC
2020 Riverfront Road
P.O. Box 5606
Van Buren, AR 72957
PH: (*************
Shipping/Receiving Hours are M-F
7:30 AM to 3:00 PM
CONFIDENTIALITY NOTICE
This e-mail contains privileged and confidential information, which is the property of Nucor, intended only for the use of the intended recipient(s). Unauthorized use or disclosure of this information is prohibited. If you are not an intended recipient, please immediately notify Nucor and destroy any copies of this email. Receipt of this e-mail shall not be deemed a waiver by Nucor of any privilege or the confidential nature of the information.
CONFIDENTIALITY NOTICE
This e-mail contains privileged and confidential information, which is the property of Nucor, intended only for the use of the intended recipient(s). Unauthorized use or disclosure of this information is prohibited. If you are not an intended recipient, please immediately notify Nucor and destroy any copies of this email. Receipt of this e-mail shall not be deemed a waiver by Nucor of any privilege or the confidential nature of the information.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Apr/25 ]
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Apr/25 ]
Last EDI from Five Rivers was on 3/31 for BOL 211508. Comment by Sujay Ramesh [ 02/Apr/25 ]
Hello,
We do not see any files being received post 3/31 from Nucor.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 03/Apr/25 ]
Hey Shanoe’,
Can you check on your side. OT EDI is saying they have not been received.
Thanks,
Angela Spurlock
Inside Sales Representative
7301 East County Rd.142 • Blytheville, AR 72315
Phone: **************
Sales: **************
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.linkedin.com/company/nucor-corporation/">https://www.linkedin.com/company/nucor-corporation/</a>  <a href="https://www.facebook.com/NucorCorp">https://www.facebook.com/NucorCorp</a>  <a href="https://www.instagram.com/nucorcorporation/">https://www.instagram.com/nucorcorporation/</a>  <a href="https://www.youtube.com/user/thenucorchannel">https://www.youtube.com/user/thenucorchannel</a>
From: Spurlock, Angela (NSAR) Sent: Wednesday, April 2, 2025 8:14 AM To: EDI_help <a href="mailto:<EMAIL>"><EMAIL></a>; &#39;DataTrans Support&#39; <a href="mailto:<EMAIL>"><EMAIL></a> Cc: <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Five River Outbound Loads from 03/31/25
OT,
Five Rivers is stating they have sent these over through EDI-However-I am not showing them on my side. Can we check on these.
Thanks,
Angela Spurlock
Inside Sales Representative
7301 East County Rd.142 • Blytheville, AR 72315
Phone: **************
Sales: **************
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.linkedin.com/company/nucor-corporation/">https://www.linkedin.com/company/nucor-corporation/</a>  <a href="https://www.facebook.com/NucorCorp">https://www.facebook.com/NucorCorp</a>  <a href="https://www.instagram.com/nucorcorporation/">https://www.instagram.com/nucorcorporation/</a>  <a href="https://www.youtube.com/user/thenucorchannel">https://www.youtube.com/user/thenucorchannel</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, April 2, 2025 8:05 AM To: Spurlock, Angela (NSAR) <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Nucor Outbound Loads from 03/31/25
Yes, I sent it through yesterday.
Thank you,
Shanoe’ Teague
Five Rivers Distribution, LLC
2020 Riverfront Road
P.O. Box 5606
Van Buren, AR 72957
PH: (*************
Shipping/Receiving Hours are M-F
7:30 AM to 3:00 PM
From: Spurlock, Angela (NSAR) <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, April 2, 2025 8:00 AM To: <a href="mailto:<EMAIL>"><EMAIL></a> Subject: Nucor Outbound Loads from 03/31/25
Hey Shanoe’,
Has this one been sent through EDI yet?
Thanks,
Angela Spurlock
Inside Sales Representative
7301 East County Rd.142 • Blytheville, AR 72315
Phone: **************
Sales: **************
<a href="mailto:<EMAIL>"><EMAIL></a>
<a href="https://www.linkedin.com/company/nucor-corporation/">https://www.linkedin.com/company/nucor-corporation/</a>  <a href="https://www.facebook.com/NucorCorp">https://www.facebook.com/NucorCorp</a>  <a href="https://www.instagram.com/nucorcorporation/">https://www.instagram.com/nucorcorporation/</a>  <a href="https://www.youtube.com/user/thenucorchannel">https://www.youtube.com/user/thenucorchannel</a>
From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, April 1, 2025 9:15 AM To: Spurlock, Angela (NSAR) <a href="mailto:<EMAIL>"><EMAIL></a>; Schultz, Lauren (NSAR) <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Melissa Salmon Acc <a href="mailto:<EMAIL>"><EMAIL></a> Subject: [EXT] Nucor Outbound Loads from 03/31/25
This message came from outside of Nucor.
Thank you,
Shanoe’ Teague
Five Rivers Distribution, LLC
2020 Riverfront Road
P.O. Box 5606
Van Buren, AR 72957
PH: (*************
Shipping/Receiving Hours are M-F
7:30 AM to 3:00 PM
CONFIDENTIALITY NOTICE
This e-mail contains privileged and confidential information, which is the property of Nucor, intended only for the use of the intended recipient(s). Unauthorized use or disclosure of this information is prohibited. If you are not an intended recipient, please immediately notify Nucor and destroy any copies of this email. Receipt of this e-mail shall not be deemed a waiver by Nucor of any privilege or the confidential nature of the information.
CONFIDENTIALITY NOTICE
This e-mail contains privileged and confidential information, which is the property of Nucor, intended only for the use of the intended recipient(s). Unauthorized use or disclosure of this information is prohibited. If you are not an intended recipient, please immediately notify Nucor and destroy any copies of this email. Receipt of this e-mail shall not be deemed a waiver by Nucor of any privilege or the confidential nature of the information.
Comment by Sujay Ramesh [ 07/Apr/25 ]
Hello Team,
Could you please let us know if you require any further assistance from DTS end?
Thanks, Sujay
Comment by Sujay Ramesh [ 09/Apr/25 ]
Hello Team,
Following up here. Could you please let us know if you require any further assistance from DTS end?
Thanks, Sujay</p>
<h4 id="cs-41988-kroger-invoices-not-sent-created-07-apr-25-updated-09-apr-25-resolved-09-apr-25">[CS-41988] Kroger Invoices not sent Created: 07/Apr/25  Updated: 09/Apr/25  Resolved: 09/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Ashley Rivera Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250407-190852.png      image-20250407-191117.png      image-20250407-190614.png      image-20250407-190758.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
The following invoices did not get sent to Kroger
04/03/25 The Kroger Company AR-502044 908626 0001 11525 SO525347 5,562 1
04/03/25 The Kroger Company AR-502045 908626 0001 11616 SO525361 8,748 1
Comments
Comment by Sujay Ramesh [ 07/Apr/25 ]
Found the files in ECS 03
Event rule is missing as per the logs.
Below is the evet rule conditions
The failed file doesn’t match EANEdiCust ID
Below is a successful file from 21st March where the values are coming as correct
Thanks, Sujay
Comment by Sujay Ramesh [ 07/Apr/25 ]
Hi Ashely,
Currently we have below condition in the rules to process the XML files for Kroger The files should have 
The mentioned invoices had below in the XML input file 
Since it didn’t match the file had failed to process. Please let us know how to proceed here.
Thanks, Sujay
Comment by Ashley Rivera [ 07/Apr/25 ]
We have other Invoices that went out with that information and it was working. It looked like they started failing on 3/13/25
Comment by Sujay Ramesh [ 07/Apr/25 ]
Hi Ashley,
I could see the last successful invoice #AR-501267 was sent on 3/21. It had the correct value: 
Thanks, Sujay
Comment by Ashley Rivera [ 08/Apr/25 ]
These have been resent and updated.
Thank you!
Comment by Sujay Ramesh [ 09/Apr/25 ]
Thanks for the update, Ashley. I will go ahead and close the ticket.
Thanks, Sujay</p>
<h4 id="cs-41438-re-external-your-856-advanced-shipment-notice-was-rejected-by-fastenal-co-created-27-mar-25-updated-09-apr-25-resolved-09-apr-25">[CS-41438] Re: [EXTERNAL] Your 856 (Advanced Shipment Notice) was rejected by Fastenal Co Created: 27/Mar/25  Updated: 09/Apr/25  Resolved: 09/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Vinothkumar Narayanamoorthy Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image-20250327-200217.png      image-20250327-200441.png      image-20250327-200358.png      image-20250327-200449.png      image-20250327-201717.png     image-20250327-201641.png      image-20250327-202834.png      image-20250327-202616.png      image-20250327-202718.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: Label: WebEDI Questions
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello Team,
Could you please check the below email and investigate &amp; respond back why the ASN got rejected.
Thank you Vinoth
----Original Message----From: <a href="mailto:<EMAIL>"><EMAIL></a> <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, March 27, 2025 8:01 AM To: Cesar Acevedo <a href="mailto:<EMAIL>"><EMAIL></a> Subject: [EXTERNAL] Your 856 (Advanced Shipment Notice) was rejected by Fastenal Co
F A S T E N A L C O M P A N Y ASN REJECTION NOTICE
Fastenal received a non-compliant EDI 856 - ASN transmission from your organization and has rejected that document and its contents. Please review the following details and work to resolve the problems for all future transmissions as quickly as possible. Please do not attempt to resubmit this ASN.</p>
<hr>
<p>The following ASN (Advanced Shipping Notice) was rejected by Fastenal:
VENDOR # : 140755
VENDOR NAME : WORLD DRYER - 140755 SHIPMENT # : 789367 PO # : HTWV37005 HL<del>4</del>3<del>I}LIN</del>1<del>BP</del>922490096<del>VP</del>Q-974A2~~~CH<del>usa} REJECT REASON: The Country of Origin sent is not a valid 3 character Country Code.
HL</del>1~~S}N1<del>SF</del>worl dryer<del>92</del>0000140755} REJECT REASON: Vendor Location sent does not match the Fastenal assigned Vendor Location.</p>
<hr>
<p>For technical questions, please contact Fastenal ECommerce Support at: <a href="mailto:<EMAIL>"><EMAIL></a>
For business-related questions, please contact Fastenal Supply Chain at: <a href="mailto:<EMAIL>"><EMAIL></a>
Thank you for your immediate attention.
Comments
Comment by Sujay Ramesh [ 27/Mar/25 ]
Issue appears to be for LIN1 09 fields i.e. the country of origin
Seems the issue is because usa is not in CAPs?
Few of the earlier ASNs sent where USA is in caps
Thanks, Sujay
Comment by Sujay Ramesh [ 27/Mar/25 ]
Just now noticed that there is an additional error HL<del>1S}N1</del>SF<del>worl dryer</del>92~0000140755}
REJECT REASON:
Vendor Location sent does not match the Fastenal assigned Vendor Location.
Below is the details in PO
Below is the entered ASN details:
The vendor# seems to be fine. Should rest of the details be matching?
Thanks, Sujay
Comment by Sujay Ramesh [ 27/Mar/25 ]
Hi Vinoth,
There are two issues here.
The first issue appears to be in the pack level, the country of origin should be in Uppercase i.e. USA.
The second issue is in the Ship From details. There seem to be typo in the company name and in lowercase.
Could you try matching the details from the PO as below and try sending the file:
Please let me know if you require any further help here.
Thanks, Sujay
Comment by Sujay Ramesh [ 03/Apr/25 ]
Hi Vinoth,
Do you require any further assistance here?
Thanks, Sujay
Comment by Sujay Ramesh [ 07/Apr/25 ]
Hi Vinoth,
Following up here. Do you require ay further assistance here?
Thanks, Sujay
Comment by Vinothkumar Narayanamoorthy [ 08/Apr/25 ]
Hi Sujay,
Thank you for the information kindly close this issue please.
Vinoth</p>
<h4 id="cs-41733-asn-created-02-apr-25-updated-07-apr-25-resolved-07-apr-25">[CS-41733] ASN Created: 02/Apr/25  Updated: 07/Apr/25  Resolved: 07/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Question Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  IMG_0899.jpeg      IMG_0898.jpeg      image-20250402-212049.png
Request Type: Support
Request language: English
Request participants: None
Organizations: None
CSAT Comment: {{webhookResponse.body.value.comment}}
Description
I need help (training) creating an ASN.
Comments
Comment by Sujay Ramesh [ 02/Apr/25 ]
Hello Tony,
We have few tutorial and documentation on how to create the same.
Below is the tutorial video link: <a href="https://datatrans-inc.com/webedi-video-tutorials-resource-page/">https://datatrans-inc.com/webedi-video-tutorials-resource-page/</a>
Below is the documentation link of the same: <a href="https://help.desk.datatrans-inc.com/servicedesk/customer/kb/view/13697034">https://help.desk.datatrans-inc.com/servicedesk/customer/kb/view/13697034</a>
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Apr/25 ]
Sujay -
These support docs have been helpful, thank you.
In the ASN label, there is no ship from info.
When I print out the packing slip, the information is very small and not eligible.
Let me know how to correct this.
Regards,
Tony Zentil
Toolmaker and Founder
Fix Manufacturing LLC
m. 9498872045
e. <a href="mailto:<EMAIL>"><EMAIL></a>
w. fixmfg.com
<a href="https://www.instagram.com/fixmfg/">https://www.instagram.com/fixmfg/</a> <a href="https://www.facebook.com/fixmfg">https://www.facebook.com/fixmfg</a> &lt;[skype:%20zentiliano]&gt; If you have received this email in error please notify the system manager. This message contains confidential information and is intended only for the individual named. If you are not the named addressee you should not disseminate, distribute or copy this e-mail. Please notify the sender immediately by e-mail if you have received this e-mail by mistake and delete this e-mail from your system. If you are not the intended recipient you are notified that disclosing, copying, distributing or taking any action in reliance on the contents of this information is strictly prohibited. Read more
On Apr 2, 2025, at 11:42 AM, Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
———-— Reply above this line.
Sujay Ramesh commented:
Hello Tony,
We have few tutorial and documentation on how to create the same.
Below is the tutorial video link: <a href="https://datatrans-inc.com/webedi-video-tutorials-resource-page/">https://datatrans-inc.com/webedi-video-tutorials-resource-page/</a>
Below is the documentation link of the same: <a href="https://help.desk.datatrans-inc.com/servicedesk/customer/kb/view/13697034">https://help.desk.datatrans-inc.com/servicedesk/customer/kb/view/13697034</a>
Thanks, Sujay
View request · Turn off this request&#39;s notifications Sent on April 2, 2025 12:42:39 PM MDT
Comment by Sujay Ramesh [ 02/Apr/25 ]
Hi Tony,
I was checking on this internally and it seems that this project is still in testing phase. I guess that’s why you didn’t see Ship From at the time since it was in development. If you check now, it must have populated.
For the packing slip, could you try printing it by changing the orientation and maybe check the fonts.
Once this project is moved to production, you will receive an invitation for Training Class.
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 02/Apr/25 ]
Ok thank you Sujay.
Regards,
Tony Zentil
Toolmaker and Founder
Fix Manufacturing LLC
m. 9498872045
e. <a href="mailto:<EMAIL>"><EMAIL></a>
w. fixmfg.com
<a href="https://www.instagram.com/fixmfg/">https://www.instagram.com/fixmfg/</a> <a href="https://www.facebook.com/fixmfg">https://www.facebook.com/fixmfg</a> &lt;[skype:%20zentiliano]&gt; If you have received this email in error please notify the system manager. This message contains confidential information and is intended only for the individual named. If you are not the named addressee you should not disseminate, distribute or copy this e-mail. Please notify the sender immediately by e-mail if you have received this e-mail by mistake and delete this e-mail from your system. If you are not the intended recipient you are notified that disclosing, copying, distributing or taking any action in reliance on the contents of this information is strictly prohibited. Read more
On Apr 2, 2025, at 2:25 PM, Sujay Ramesh <a href="mailto:<EMAIL>"><EMAIL></a> wrote:
———-— Reply above this line.
Sujay Ramesh commented:
Hi Tony,
I was checking on this internally and it seems that this project is still in testing phase. I guess that’s why you didn’t see Ship From at the time since it was in development. If you check now, it must have populated.
&lt;jira-generated-image-static-5e6ee74b-ad4e-48a8-8d8d-507959d075a2.png&gt;
For the packing slip, could you try printing it by changing the orientation and maybe check the fonts.
Once this project is moved to production, you will receive an invitation for Training Class.
Thanks, Sujay
View request · Turn off this request&#39;s notifications Sent on April 2, 2025 3:25:07 PM MDT</p>
<h4 id="cs-41338-re-puma-biotechnology-missing-edi-867-data-created-26-mar-25-updated-07-apr-25-resolved-07-apr-25">[CS-41338] RE: Puma biotechnology Missing EDI 867 Data Created: 26/Mar/25  Updated: 07/Apr/25  Resolved: 07/Apr/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Ambure Vasanth Kumar Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image001.png      image002.gif      image003.png      image004.png
Request Type: Emailed request
Request language: English
Request participants: Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hi DataTrans team,
The Cardinal team has confirmed that they resent the file edi_D20241209T044958.edi with Correlation ID: e6e5b96d-32eb-4220-b0c4-da4308fd737e.
However, we still have not received the transaction on our CG end yet. Could you please verify and confirm on this?
Thanks &amp; Regards
VK
VASANTH KUMAR, AMBURE
Consultant, Connected Customer Journey Platform
Capgemini Technology Services India Limited | Hyderabad
Mob: + 91 77 31 82 82 86
PTO Alert:
From: Lindsay Lippert <a href="mailto:<EMAIL>"><EMAIL></a> Sent: 25 March 2025 19:55 To: Ambure, Vasanth Kumar <a href="mailto:<EMAIL>"><EMAIL></a>; Dheenadayalan, Baraneedaran <a href="mailto:<EMAIL>"><EMAIL></a>; 159 Solutions Support - Puma <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Jennifer Hotter <a href="mailto:<EMAIL>"><EMAIL></a>; Lindsay Lippert <a href="mailto:<EMAIL>"><EMAIL></a> Subject: FW: Puma biotechnology Missing EDI 867 Data Importance: High
This mail has been sent from an external source. Do not reply to it, or open any links/attachments unless you are sure of the sender&#39;s identity.
Hi Team,
Can you confirm that we have received this file with the Cardinal transaction from November?
Thanks,
Lindsay
From: GMB-DUB-DCOE <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, March 25, 2025 5:20 AM To: Lindsay Lippert <a href="mailto:<EMAIL>"><EMAIL></a>; GMB-DUB-DCOE <a href="mailto:<EMAIL>"><EMAIL></a>; Salerno, Craig <a href="mailto:<EMAIL>"><EMAIL></a>; Hicks, Jonathan <a href="mailto:<EMAIL>"><EMAIL></a>; Jennifer Hotter <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Payton, Jeffrey <a href="mailto:<EMAIL>"><EMAIL></a>; Michael Flinn <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Puma biotechnology Missing EDI 867 Data
***EXTERNAL EMAIL: Do not click any links or open any attachments unless you trust the sender and know the content is safe. ***
Hello,
IT updated me with the following information that they have successfully sent filename edi_D20241209T044958.edi to PUMA:
Correlation ID: e6e5b96d-32eb-4220-b0c4-da4308fd737e
Thank you,
| Jonathan Hicks Consultant, Strategic Planning and Execution Sourcing Solutions and Commercial Intelligence 7000 Cardinal Place, Dublin, OH 43017 |
From: Lindsay Lippert <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Thursday, March 20, 2025 10:03 AM To: GMB-DUB-DCOE <a href="mailto:<EMAIL>"><EMAIL></a>; Salerno, Craig <a href="mailto:<EMAIL>"><EMAIL></a>; Hicks, Jonathan <a href="mailto:<EMAIL>"><EMAIL></a>; Jennifer Hotter <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Payton, Jeffrey <a href="mailto:<EMAIL>"><EMAIL></a>; Michael Flinn <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Puma biotechnology Missing EDI 867 Data
External Email – Please use caution before opening attachments or clicking links
Hi Jonathan,
Please resend the file. That would be helpful.
Thank you,
Lindsay
From: GMB-DUB-DCOE <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, March 18, 2025 8:11 AM To: Salerno, Craig <a href="mailto:<EMAIL>"><EMAIL></a>; Lindsay Lippert <a href="mailto:<EMAIL>"><EMAIL></a>; Hicks, Jonathan <a href="mailto:<EMAIL>"><EMAIL></a>; GMB-DUB-DCOE <a href="mailto:<EMAIL>"><EMAIL></a>; Jennifer Hotter <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Payton, Jeffrey <a href="mailto:<EMAIL>"><EMAIL></a>; Michael Flinn <a href="mailto:<EMAIL>"><EMAIL></a> Subject: RE: Puma biotechnology Missing EDI 867 Data
***EXTERNAL EMAIL: Do not click any links or open any attachments unless you trust the sender and know the content is safe. ***
Good Morning,
Our technical team is telling me that the file that we sent and the one attached previously are exactly same just that ISA date and the segment terminator is different. This tells them that our file is forwarded which changes the ISA date and the segment terminator but they see no missing DEA/HIN information in the transmission.
They are willing to resend the file, though, if that would help.
Thanks,
| Jonathan Hicks Consultant, Strategic Planning and Execution Sourcing Solutions and Commercial Intelligence 7000 Cardinal Place, Dublin, OH 43017 |
This message contains information that may be privileged or confidential and is the property of the Capgemini Group. It is intended only for the person to whom it is addressed. If you are not the intended recipient, you are not authorized to read, print, retain, copy, disseminate, distribute, or use this message or any part thereof. If you receive this message in error, please notify the sender immediately and delete all copies of this message.
Comments  Comment by Ambure Vasanth Kumar [ 26/Mar/25 ]
Comment by Sujay Ramesh [ 26/Mar/25 ]
Hi Vasanth,
I checked and I can confirm that we have not received the file edi_D20241209T044958.edi with Correlation ID: e6e5b96d-32eb-4220-b0c4-da4308fd737e on our end.
Please let me know if you require any further assistance.
Thanks,
Sujay
Comment by Sujay Ramesh [ 26/Mar/25 ]
Tried checking for the file. Could not find it both in ECS 02 and 03.
Thanks,
Sujay Comment by Sujay Ramesh [ 07/Apr/25 ]
There is another simlilar ticket
<a href="https://datatrans-inc.atlassian.net/browse/CS-41650">https://datatrans-inc.atlassian.net/browse/CS-41650</a>
Thanks, Sujay</p>
<h4 id="cs-41425-tp-meijer-retransmitted-850s-created-27-mar-25-updated-28-mar-25-resolved-28-mar-25">[CS-41425] TP Meijer Retransmitted 850s Created: 27/Mar/25  Updated: 28/Mar/25  Resolved: 28/Mar/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      image-20250327-175714.png      image-20250327-175531.png      image-20250327-175424.png      image-20250327-175616.png      image-20250327-175753.png
Request Type: Emailed request
Request language: English
Request participants: None Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Good morning.
Our TP Meijer sent us an email stating they retransmitted the two 850s listed below. Can you please check if that is the case?
...prima di stampare pensa all&#39;ambiente...think before you print...
Nutkao srl, in ottemperanza alla normativa ex D.Lgs 231/01 e successive modifiche, si è dotata di un proprio Codice Etico, di un Modello di organizzazione e ha nominato un Organismo di Vigilanza. Il Codice Etico è a disposizione sul sito Internet <a href="http://www.nutkao.com">www.nutkao.com</a>. Chiunque intrattiene rapporti con Nutkao srl si impegna a rispettare i principi contenuti nel Codice Etico. La violazione di tali principi comporta cessazione del rapporto fiduciario: Nutkao srl si riserva di interrompere ogni rapporto con chiunque violi tali principi e di agire legalmente per il ristoro di eventuali danni.
In accordance to the Italian laws, Decree N. 231/01 and subsequent amendments, Nutkao has decided to adopt a Code of Ethics, such as an organization and management Model. Therefore Nutkao has also designate a Supervisory Board in order to watch on this Model. The Code of Ethics is available on the Company website <a href="http://www.nutkao.com">www.nutkao.com</a>. Anyone who has dealings with Nutkao srl is request to be engaged towards the respect of Nutkao’s Code of Ethics. Violation of its rules imposes the end of the partnership. Nutkao srl has then the right to break down any relationship with anyone who violates these principles and to act under Laws regulations to solve any contingent damage.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Mar/25 ]
Comment by Tyler Daughrity (Inactive) [ 27/Mar/25 ]
Hi Manny,
The requested orders have been resent to RJW, please let us know if you need anything else.
Thank you,
Tyler Daughrity
DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a>
************
Comment by Tyler Daughrity (Inactive) [ 27/Mar/25 ]
Customer called into support and requested PO be re pushed to RJW
Tyler Daughrity
DataTrans Solutions
<a href="mailto:<EMAIL>"><EMAIL></a>
************
Comment by Sujay Ramesh [ 27/Mar/25 ]
The file was failing in map Nutkao_850_005010 to RJW_940_4010 due to error Rule #45: Data value not included in target code list (expression: G6201=Right(DTM01, 2), data: 15)
This was due to the presence of extra DTM*015 segment which usually is not sent by Meijer
It was not part of the accepted values
Sandy had checked the below setting and installed the map.
The file was restaged and it went through.
Thanks,
Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 28/Mar/25 ]
Thanks Tyler for restaging the orders.
Comment by Sujay Ramesh [ 28/Mar/25 ]
Hello Manny,
Since the issue has been resolved, we will go ahead and close the ticket. Please do reach out to us if you require any further assistance.
Thanks, Sujay</p>
<h4 id="cs-41421-fwd-brinton-group-50314-winco-invoice-compliance-errors-created-27-mar-25-updated-28-mar-25-resolved-28-mar-25">[CS-41421] Fwd: BRINTON GROUP-50314- WinCo Invoice compliance errors Created: 27/Mar/25  Updated: 28/Mar/25  Resolved: 28/Mar/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: <a href="mailto:<EMAIL>"><EMAIL></a> Assignee: Sujay Ramesh Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image.png      image (be557d1f-a478-4f27-b604-ee011db58bd0).png      95122.pdf      95109.pdf      95110.pdf      image-20250327-154331.png      image-20250327-154629.png      image-20250327-154847.png      image-20250327-154712.png      image-20250327-180425.png      image-20250327-180044.png      image-20250327-180858.png      image-20250327-180814.png
Request Type: Emailed request
Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello!
Received the below from our customer WinCo Foods re compliance errors for 3 invoices sent yesterday (attached). System shows these were accepted so I&#39;m not sure what the issue is. Help to get this resolved is appreciated.
---------- Forwarded message ---------From: EDI Admin <a href="mailto:<EMAIL>"><EMAIL></a> Date: Thu, Mar 27, 2025 at 7:16 AM Subject: BRINTON GROUP-50314-Invoice compliance errors To: Kat Klamerus (<a href="mailto:<EMAIL>"><EMAIL></a>) <a href="mailto:<EMAIL>"><EMAIL></a>, Michelle Wolfe (<a href="mailto:<EMAIL>"><EMAIL></a>) <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Marcia Brinton (<a href="mailto:<EMAIL>"><EMAIL></a>) <a href="mailto:<EMAIL>"><EMAIL></a>
Good Morning,
We are receiving invoices with compliance errors.
BRINTON GROUP-50314 03/26/2025 880 95122 E
BRINTON GROUP-50314 03/26/2025 880 95110 E
BRINTON GROUP-50314 03/26/2025 880 95109 E
ST<em>880</em>0149~
G01<em>20250326</em>95122<em>20241125</em>717460700~
N4<em>BOISE</em>ID<em>83707~
G72********1636~ ç(Missing mandatory G7201 should be “62”, G7202 should be “2”)
G17</em>71<em>CA</em>23.04<em>085577907069</em>IN*976631~
You should have received a 997 indicating this invoice was received with error. Please confirm this is being looked into as there may be issues with payment processing, and let me know if there are any questions.
Thank you,
Ashley Jenkins | Associate EDI Admin | WinCo Foods, LLC. | Office (208) 672-2329 | [ <a href="mailto:<EMAIL>"><EMAIL></a>|<a href="mailto:<EMAIL>">mailto:<EMAIL></a>]
Go to [ <em>http://partners.wincofoods.com</em>|http://partners.wincofoods.com/] for the latest EDI documents or how to become an EDI trading partner.
Comments
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Mar/25 ]
<em>95109.pdf  (224 kB)</em>
<em>95110.pdf  (225 kB)</em>
<em>95122.pdf  (224 kB)</em>
Comment by Sujay Ramesh [ 27/Mar/25 ]
The three invoices mentioned are 95110, 95109, 95122.
95122 is missing allowance charge code details
This is mostly due to missing allowance charge code and handling charge code
Below is the data from another correct invoice
95110 also has the same issue. But 95109 looks fine. EDI notepad also doesn’t show anything. The 997s for all the documents were sent back as accepted.
Thanks,
Sujay
Comment by Sujay Ramesh [ 27/Mar/25 ]
The customer has apparently resent them after correction.
Thanks,
Sujay
Comment by Sujay Ramesh [ 27/Mar/25 ]
Hi Kat,
The file was earlier missing the allowance charge. But it looks like the files was resent after correcting the same. Could you please let me know if you require any further assistance here?
Thanks, Sujay
Comment by <a href="mailto:<EMAIL>"><EMAIL></a> [ 27/Mar/25 ]
All good! Thanks, Sujay!
Comment by Sujay Ramesh [ 28/Mar/25 ]
Thanks for the confirmation, Kat. I will go ahead and close the ticket.
Thanks, Sujay</p>
<h4 id="cs-41432-fw-l-2-db90107-20-created-27-mar-25-updated-28-mar-25-resolved-28-mar-25">[CS-41432] FW: L-2.DB90107-20 Created: 27/Mar/25  Updated: 28/Mar/25  Resolved: 28/Mar/25</h4>
<p>Status: Resolved
Project: Customer Support
Components: None
Affects versions: None
Fix versions: None
Type: Support Priority: Medium
Reporter: Susan Vasser Assignee: Sujay Ramesh
Resolution: Done Votes: 0
Labels: None
Remaining Estimate: Not Specified
Time Spent: Not Specified
Original estimate: Not Specified
Attachments:  image003.png      image004.png      image005.png      image006.png      image459518.png      ZL305297.html      image-20250327-195127.png
Request Type: Emailed request Request language: English
Request participants: None
Organizations: CSAT Comment: {{webhookResponse.body.value.comment}}
Description
Hello,
My trading partner entered a PO below and attached on 3/25 that we are not showing as received from you.
Would you be able to check to see if you can see why we have not received this?
Thank you,
Susan
CAUTION EXTERNAL EMAIL: This email originated from outside of the corporate network. Do not click links or open attachments unless you recognize the sender and know the content is safe. If you have any questions about the legitimacy of this email, please reach out to IT help desk. While mismatches between the actual SENDER field and the FROM field are common, it is also a common spoofing tactic. For additional reference, Please verify the sender of this email
Good morning Susan. I went back in and confirmed it. Maybe that’s why not sure.
Thanks,
Chuck Hanvey MCC / CEP - Customer Service/Inventory Control
Cell ************
Thanks,
<a href="http://sg360.com/">http://sg360.com/</a>|
Susan Vasser
Lead Project Manager
Phone: ************ <a href="about:invalid#zCSafez">tel:************</a>
Mobile: ************ <a href="about:invalid#zCSafez">tel:************</a> Email: <a href="mailto:<EMAIL>"><EMAIL></a>
sg360.com
From: Susan Vasser <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Wednesday, March 26, 2025 3:26 PM To: Charles Hanvey <a href="mailto:<EMAIL>"><EMAIL></a>; Jack B George <a href="mailto:<EMAIL>"><EMAIL></a>; Evan D Jones <a href="mailto:<EMAIL>"><EMAIL></a>; Alejandra Hernandez <a href="mailto:<EMAIL>"><EMAIL></a> Cc: Laura Gentry <a href="mailto:<EMAIL>"><EMAIL></a> Subject: [EXTERNAL] RE: L-2.DB90107-20
[Caution] External email. Be sure you trust or verify the sender before entering usernames or passwords when prompted by a link.
Hi Chuck,
Not seeing this PO in our EDI portal. Can you send me a copy of the PO so I can investigate?
Thank you,
Susan
<a href="https://urldefense.com/v3/__http:/sg360.com/__;!!Cg_6rE7FVGHU6vd7!9OZsnmYmD7X7U49b3jFcHlOBa8AVQIzX3omyCfpYi8rhnCXLGmylEWEr6gGu98UXb3usriq-gfvniLuRuwwf$">https://urldefense.com/v3/__http:/sg360.com/__;!!Cg_6rE7FVGHU6vd7!9OZsnmYmD7X7U49b3jFcHlOBa8AVQIzX3omyCfpYi8rhnCXLGmylEWEr6gGu98UXb3usriq-gfvniLuRuwwf$</a> |
Susan Vasser
Lead Project Manager
Phone: ************ <a href="about:invalid#zCSafez">tel:************</a>
Mobile: ************ <a href="about:invalid#zCSafez">tel:************</a> Email: <a href="mailto:<EMAIL>"><EMAIL></a> sg360.com
From: Charles Hanvey <a href="mailto:<EMAIL>"><EMAIL></a> Sent: Tuesday, March 25, 2025 8:21 AM To: Susan Vasser <a href="mailto:<EMAIL>"><EMAIL></a>; Jack B. George <a href="mailto:<EMAIL>"><EMAIL></a>; Evan D Jones <a href="mailto:<EMAIL>"><EMAIL></a>; Alejandra Hernandez <a href="mailto:<EMAIL>"><EMAIL></a> Subject: L-2.DB90107-20
CAUTION EXTERNAL EMAIL: This email originated from outside of the corporate network. Do not click links or open attachments unless you recognize the sender and know the content is safe. If you have any questions about the legitimacy of this email, please reach out to IT help desk. While mismatches between the actual SENDER field and the FROM field are common, it is also a common spoofing tactic.
For additional reference, Please verify the sender of this email
Good morning, Susan, I hope all is well.
I just entered this PO. I did not confirm it.
Can you please provide a ship date when available?
Thanks,
Chuck Hanvey MCC / CEP - Customer Service/Inventory Control
Cell ************
Thanks,
Comments
Comment by Susan Vasser [ 27/Mar/25 ]
<em>ZL305297.html  (164 kB)</em>
Comment by Sujay Ramesh [ 27/Mar/25 ]
Hi Susan,
I do not see PO# ZL305297 being received on our end. Could you please request Trading Partner to resend it?
Thanks, Sujay
Comment by Sujay Ramesh [ 27/Mar/25 ]
Can’t see the PO
Thanks, Sujay Comment by Susan Vasser [ 27/Mar/25 ]
Ok thanks for confirming. I have already asked them to resend this item on a new PO#.
Thank you,
Susan
<a href="http://sg360.com/">http://sg360.com/</a>|
Susan Vasser
Lead Project Manager
Phone: ************ <a href="about:invalid#zCSafez">tel:************</a>
Mobile: ************ <a href="about:invalid#zCSafez">tel:************</a> Email: <a href="mailto:<EMAIL>"><EMAIL></a>
sg360.com
Comment by Sujay Ramesh [ 28/Mar/25 ]
Thanks for the update, Susan. I will go ahead and close the ticket.
Thanks, Sujay
Generated at Tue Jun 03 12:56:22 UTC 2025 by Michael Hoang using Jira 1001.0.0-SNAPSHOT#100283-rev:905ed9523971d96e575e35bcc09bdcdec39be6f6.</p>
