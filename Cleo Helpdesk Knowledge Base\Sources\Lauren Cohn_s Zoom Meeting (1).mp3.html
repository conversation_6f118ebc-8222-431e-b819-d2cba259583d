<p>for <PERSON> rejected rejected invoice. These are going to be the easy ones. Um those are usually a good start. Um let me grab control number. So that is coming from here. there. So, grabbing Oh, man. I can&#39;t do both lines. Okay. So, I&#39;ll just grab this and then put a date here and then this way I have something to go off later. Um, I could not locate that. I may just need to reconnect my servers and just refresh it. Maybe that&#39;s why I couldn&#39;t search for it. No clue. But basically, I just told him I was looking into it. I would get back to him. So, starting a ticket, what the issue is, it&#39;s a rejected invoice. And I&#39;m going to put invoice number. Who I spoke to, rejected invoice status, attempted to locate 997 while on call, could not locate. Let customer know that I would review and get back. Okay. So, what the issue was, who I spoke to, the document. Um, I usually just copy the document details from the actual document at the top. So, these guys, it tells you what it is, the exact message ID, and the reference number if there is one. So, those make it way easier to go back and retrace your steps. Um, and then I put the control number down here. So I don&#39;t necessarily have to open it if I just want to try to search that again later. So creating that ticket view moving to in progress since he will get a status update here. Santa Fe and label rejected invoice provided. Okay. So I am done with that. Let&#39;s go look at yours for him. Okay. Oh, why did he address me? Okay. Okay. Interesting. Yeah, they do that. We had a few people that like refused to talk to anybody but the person they were addressing. And Sandy&#39;s like, &#34;You can&#39;t do that because people come and go.&#34; So, I don&#39;t know. Okay. So, let&#39;s look at yours. Um Oh, I can record. No, we&#39;re just Okay. So, I&#39;m recording it now. Um, we&#39;re looking at your ticket with the air. So, the ASN Oh my goodness. error. So, due to the large quantity, please contact support. We need to update that. Yes. And then we also have not that screenshot, the screenshot provided by the customer. In the screenshot, you&#39;ll have their username. So, their account number, what it is, so the label, the status, and the message ID. This is the most important thing as this will be what you use to locate the actual message. So, let&#39;s go ahead and close out of here. Um, do you want me to go ahead and just open the portal and everything? Okay. Sorry, the recording is going to have questions. back and forth. All righty. So, if that happens, just refresh and do it again. Okay. So, we have it&#39;s A1. Log into theirs. This will take you to their page. Hit the little batches up here, the little bell, and we&#39;re locating that message ID. here. So, count label failed status error that we&#39;re looking for. Um, it does look like they have another ASN with that same issue, but we&#39;ll get there. So, let&#39;s work on just this one. So, we&#39;re going to copy that message ID, put it here, hit this dropown, select message, and then hit go. This will bring you to the ASN in question. Double click to open. All righty. So, we are in the ASN. Currently, there isn&#39;t a reference number because they did not input a shipment ID or they haven&#39;t hit save on their side. Um, with it being labels, always always before we try to generate or mess with anything, have them make sure they hit save or redirect them and make sure they&#39;re actually filling out anything in blue. A lot of this stuff is required for the label. So, they&#39;ll just have to redo it. Um, so this saves a little bit of trouble. So, I always recommend scroll through, look down, anything in blue, make sure it is there, and then below that you&#39;ll have the actual items. So, here we show that pack one has 1662 um each within it. However, when we scroll down, pack two on these don&#39;t look right. So, we can continue to scroll or Or we can hit this edit packing button, which may take a minute since there&#39;s quite a bit. Quite a few minutes. We&#39;ll get there. So, the bigger the ASN, the longer this takes. If there were only like five to 10 packs, it would have already loaded. Um, but because there&#39;s well over a thousand. Yeah. Um, even like 50, that&#39;s not terrible, but this shows that there should be Yeah. So, that might take a moment. But yeah, see, even under the item details, this may be required on the actual label. So, they definitely need to make sure to fill that in. So, anything in blue all over the document, they have to go through and make sure that&#39;s there and hit save. So, that way we can see the current state. Um, they should um sometimes they&#39;ll have, you know, new people or someone that&#39;s filling in for somebody else doing the process. So, that&#39;s usually part of the problem or the document generated and something&#39;s wrong with the actual file on the back end and they may need to recreate it. Oh my goodness. There we go. Okay. So, once you hit open or edit packing, this will open to your screen. You can see everything available. This is way easier to scroll through. Um, so you&#39;ll be able to go all the way down if you want to look. Um, here we&#39;ll see that literally everything is empty. There&#39;s not a single item within each pack. except for this at the top. I&#39;ll zoom in maybe trying to make it bigger but it did not want to. You&#39;ll have the PO details and how many packs are below. So for this one 66 600 1662 packs are there but only one as items. So one they did meet the threshold for the batch label which is where they have to come to us and we help them generate it. And two their labels are failing and having issues because it doesn&#39;t know what to do. It&#39;s an internal panic because it it has nothing to pack. There&#39;s nothing to print. Yes. So, right now, this state, the customer needs to go back through and just make sure everything is correct. Um, if they do in fact want to pack everything in pack one, they can either choose to go through and remove all those other packs or they can start fresh on a new um 856 if they&#39;re not wanting to go one by one to delete those guys. Um the other thing they could have done I guess I could have left edit hacking open. Let&#39;s go back. Maybe they could go ahead and I believe they can just hit the delete button at the top level and it could clear everything out. But we really just need them to confirm what they&#39;re expecting. How many boxes they&#39;re actually filling and how many items of or the quantity of each item. So for this, I only show that there&#39;s this one item. If we want to verify this blue hyperlink here is the original purchase order. We can click onto that and it will take us I see there&#39;s two 856s. Um it&#39;ll take us to the actual order itself and where we can verify wow um the actual quantity. ordered and what the details will be. So, right here it&#39;s saying that there should be six. H. Okay. So, they definitely need to confirm for us what they&#39;re expecting their packaging to look like. But yes, this just shows that it is just that one item. So, they&#39;re good there. It&#39;s just a matter of confirming how many needs to be in pack one because right now it shows 1662 within pack one. Um, if they&#39;re expecting to just have one label for one big box, then they would put the full amount that they&#39;re wanting to ship on that ASN. Um, the customer is able to ship the full order. Or, oh my goodness. Or if they want to do multiple ASN&#39;s and say they&#39;re only supplying this much for right now, they can go ahead and build the ASN, just put that in that singular pack or however many packs they&#39;re wanting and submit the ASN. So, they don&#39;t have to fulfill the full 9,972. That is completely up to them and between them and their trading partner. So, right now, they just need to either fill these or confirm with us so we can help them guide them on this. Um, yeah, I don&#39;t want to delete order. Yeah, and there&#39;s no mass delete button. So, yes, um, they can either go through and pack all of these or they can just create a new ASN Okay. Do you have any questions?</p>
<p>Yes. Um, so if all of those were made an error, I would just make a new ASN. Um, if you look over here on the actual PO, there&#39;s two that were generated. So if you click on one, Aha, they&#39;re in something else. I wonder if this is the other one that failed. 157. Nope, that is a completely different one. Okay. Um, this is another ASN that they tried to build for the same order. Same issue. They put 1662 each in one pack and everything below it is blank. So, whoever&#39;s building the ASN, I don&#39;t think they&#39;re necessarily doing it correctly. So, from the PO, I&#39;ll go ahead and generate one. That&#39;s totally okay. 856 It sounds like they&#39;re doing pick and pack. So, number of cartons. This is where they&#39;ll tell the system how many cartons they&#39;re needing, how many boxes. So, if it&#39;s a one one, if that one item, cuz it&#39;s a 12-pack, fills up one box, then yes, they would go ahead and put however many they&#39;re needing in total. So, if it&#39;s that 1,662 that they&#39;re fulfilling or whatever, um, they would put that here. So, for this, I&#39;ll go ahead and put five just so you can see. So, I&#39;m creating an ASN that has five cartons on it. Next, and it may take a moment. Once that generates, you&#39;ll see that I have my ASN, I have my five packs, and I have my quantity. Since they haven&#39;t sent an ASN, the quantity left will stay um the full quantity. Once they actually do send the ASN, this will actually have a deduction from it. But since nothing has been submitted, nothing&#39;s solid in stone. It&#39;s just there. Um, so for this example, I will do one. Okay. So, what I did was I packed item one. I put a quantity of one in this pack. And you can do that line by line or you can hit this little copy button, copy pack, and you can copy it to the next four. It does take a moment, but it&#39;ll go ahead and put that over there for you. So, you&#39;re not drag and dropping for each one. Um, you can do it up to 50 packs per. So, if they have a 100 packs that they&#39;re filling, you can do, you know, that two different things. All righty. So, now that our packing editor shows that we have five packs, each one has a quantity of one, they&#39;re all packed. We can hit finish. with this being a test on our side. Always make sure you put test or test delete something for us. All righty. And then I don&#39;t fill any of this in because I don&#39;t want them to actually hit send. But a reminder, if you&#39;re helping them, tell them about the blue, especially their vendor number. And when you scroll down, you will actually see our packs. Now you&#39;re showing filled lines. Now everything has an item within it. Um anything in blue they would just need to go ahead and fill out. If they&#39;re not wanting to fill this each time, they can set up a document default. You can discuss that with them if they do ask that question. Um or they can between default and the catalog. They&#39;ll have two options. But yes, this is how it should look. So if they are only needing one one in each pack. It should go one one all the way down for as many packs as they&#39;re needing. Um but once they&#39;re good and ready to go, have them hit save. Depending on how many um labels they&#39;ll need, they can hit the print label and if it generates, perfect. It&#39;ll give them whether they needed to email it to themselves or there will actually be a button here on the right that does say label. Yes. So, view label. And I can click one so you can see what it looks like. So, this is their actual labeled. Oh my goodness up here. So, it shows um every spec will be different, but for this one, it shows where it&#39;s from, where it&#39;s going to, the postal code, any details, and any specs that were set up, like whether it&#39;s an item description, skew, UPC, all of that. And then the big barcode at the bottom. This is pretty generic. across the board for most trading partners. So, it should look mostly like this. Um, as far as like the contents here, this may vary, though. So, it may only have a PO number. It may just have a skew. So, don&#39;t freak out if it&#39;s not all the same. They&#39;re not all um one sizefits-all. It is a little different, but they can always access that label. They don&#39;t have to keep printing it. They can access it up here with the little bell for their batches. Um, if they have those failures, those will be here. It&#39;ll also confirm if they&#39;re doing the email where it&#39;s going. Um, I do believe that it will go directly to whoever is set up as that user. Um, okay. So, let me hit save here so we can kind of cover all the bases here. Save. We&#39;ll go to settings. Yeah, I&#39;m pretty sure that&#39;s how it goes. So, he would have to update it there. If they&#39;re wanting to do the email to me button, it&#39;ll go directly to whoever is set up on the admin or if Tommy&#39;s doing it, it&#39;ll go to Tommy&#39;s email. Um, what was the name on there? Okay, that&#39;s a completely different email. Okay, so it won&#39;t go to that. He could add that as another user or if he&#39;s I would do that. I would recommend adding as another user. We can cross that bridge in a minute. But if he adds another user, he can give them um all the capabilities and then that user can hit email to me and it&#39;ll email over to that person. Um unless he&#39;s wanting to go ahead and give permission to that email address for the admin. So, we&#39;ll really have to cross that bridge with him in a minute, but we definitely need him to confirm his ASN details, his quantity packing, all of that, and then we can get to the email part. So, from what I know, it would either need to replace this one or add a user. But, yep. So, right now, Ball is in the customer&#39;s court. We just need him to let us know. Trying Good. Good. I There&#39;s no structure. Um there&#39;s no method to my madness. It&#39;s just kind of there. So So I hope it makes sense. Um Okay, good. Okay, so that&#39;s really it. It&#39;s just we&#39;re waiting on him now. Um but yeah, let him confirm and And then once he does, you can let me know. We can look back at it if he hasn&#39;t already answered. Okay. Not yet. That one. Mhm. Okay. I remembered seeing that one the other day. Okay. Yeah. So that&#39;s the right process. Um dis disabling the auto send for that. Wow. Um Okay. Yes. So, he just needs to disable the auto send. Let me see. Oh, who messaged? It was Raf. Okay, she&#39;s fine. Um, this is villain. I wonder. Let me grab something real quick. Cool. Hold on. If it&#39;s the right path, I will show you. Otherwise, is I&#39;m not going to show you so I don&#39;t confuse you. Okay, this is 7175. Okay. So, not that. Cool. Glad I did not show you. That is not what I was looking for at all. That&#39;s okay. Let&#39;s go over here. Um, do you know what server they&#39;re on by chance? Okay, perfect. They&#39;re going to have to wait it. shouldn&#39;t. Mine doesn&#39;t normally do that. Okay, maybe Maria will catch that. Perfect. She did. Awesome. Okay, let me check. Go ahead. Go to his account for me real quick. Uh, no. Go on webdi on the portal. And then once you sign in his draft folder, I want to see something in his draft folder. Is there anything for DSG?</p>
<p>Mhm. No. think they auto send. It wouldn&#39;t go to his draft folder. Um, okay. Let me look at that. May be a manually created one. Okay, let me go look at something else. So if it&#39;s in draft now, we&#39;ll go on the actual server and we&#39;ll see if it&#39;s something that he sent. If it is a file that he sent from the server and it&#39;s in his draft, it does not auto send. So who is he? Oh, no. Do you know what his channel was? Hold on. It&#39;s okay. Let me check with his account number first. 7175. 7176. Okay. Okay. I&#39;m going to locate his details. Hold on. Okay. Input. Oh my goodness. Cannot make it easy. That&#39;s okay. giant bicycle. Let me look at anything he sent for this month. Let&#39;s see if it gives me a source. He has not sent anything this month. Okay. Or his inbound might actually be on the other one. I know sometimes they um bounce between. So, okay. So, his O2 for some reason I don&#39;t see anything processed there, which is fine. I&#39;m going to open the other one and home. Okay, here we go. So, he does have stuff on O2, but it looks like 03 is actually where he is coming into. Let&#39;s grab that and check his step one. Nothing. Cool. Um March. Okay, there we go. Okay. Yeah. So, he is on 03 for his inbound. The D drive is only located on that server. So, if it&#39;s T, it can be located on either one. B is specifically to where you&#39;re at. I show on 317 there&#39;s one that says test. Um, trying to think. think T is anywhere. So T is universal. Um D is specific to the server that you&#39;re on. So they both have a D drive. So it&#39;s kind of like your C, but the D drive is um hard drive specific. So in this case, you know, the server has its own D drive and ECSO2 has its own D drive. But they share tea. Okay. Um I was hoping it was just a partner config. I don&#39;t see anything for that. Let me do something else. Configs config name last I don&#39;t show this customer under for auto sun. Okay, then unless he hits send in the portal, it&#39;s not going to go. Nope, that&#39;s my customer. Yours is this one, 7175. So, what I did is I went to partner configs in admin and I hit config name list. And this will show you every configuration. Um, we don&#39;t need all that. You can search I did auto and there&#39;s an actual auto send feature. You can hit show usage and under search, put your customer&#39;s number. He&#39;s not in here. These are the only two with 71 in their number. number that shows auto send as configured. So his shouldn&#39;t be man automatically going out unless he&#39;s manually hitting send on it. That&#39;s what I thought but I wasn&#39;t sure. So when I hit partner config I can pull him right here. 7175 this guy. So web ID hit edit. within that you can see anything if it&#39;s enabled. Um, if it has a one next to it, that means it&#39;s on. If there&#39;s nothing next to it, it&#39;s most likely not enabled. So, for him, I did auto. I don&#39;t have anything. Nothing is set up automatically for him. I mean, I can Yeah, I just don&#39;t know if we need to add in the auto send and set it to zero if that&#39;ll um anything that says zero is turned off. Let&#39;s do that here. Add config, of course. Okay, let me go back and pull it. I was hoping I could just search it. It does not work that way. No, I I&#39;m not that magical, unfortunately. Okay, so here is the list. We&#39;ll do auto. Um, I wonder what those do, but I think it&#39;s just literally auto sun. Who does this? Let me look up. Oh goodness, I can&#39;t think of her coming. name. I don&#39;t know most of these people. I&#39;m terrible. I know customer names. Okay, hold on. Oh, yes, I do remember hers, actually. Um Oh, lovely. That&#39;s why hers doesn&#39;t work. I&#39;m scrolling through hers. Hers should be set up for that. Okay, let&#39;s check anything that says send uh send notification inbox. That is literally notifications in the inbox. They get emailed for it. That&#39;s what that one means. Um, so auto send is actually disabled for this customer and it should not be, but I&#39;m not changing that. So, let&#39;s do auto send back here. We&#39;ll go ahead and add it. Leave it as zero. So, we&#39;re adding it to not work. Um, so email him back. Let him know that you disabled. Um, I think that does it for everybody though. That&#39;s what makes me nervous. Are those the only two partners he&#39;s using that for? Uh, using the portal for, do you know? Cuz I&#39;d hate to turn that on if he&#39;s using the portal for anyone else. Okay, so just those two. Oh, perfect. Um, so if he had like five and he&#39;s only disabling for two, then that would be a little more tricky. Okay, so AI sports. Perfect. Um, so we can try that. See if that helps anything. Um, So screenshot. Let me go to this one added config to disable auto send. His concern is auto sending to the partner, right? Mhm. Okay. So that should disable his account altogether will not send anywhere. Um, so just let him know that we&#39;ve configured the account to no longer auto send to either. Um, and just tag us in once he&#39;s ready or needs to make any further changes. But as of now, you do show that as disabled. And I put that note there so they&#39;ll see that I did that. That way they&#39;re not guessing who did it. It was me. If it&#39;s wrong, it&#39;s on me. Um, what I would say, where are you? Let me not send that to the whole group. ESG. Is that how we labeled it? ESG and ASG. Um, please let us know if you You can send him that. Um, so since they&#39;re not going out, they will create a draft. So that should cover both. Um, here Allow for these files to show. There you go. Okay. That way it just touches that. You&#39;re good. You&#39;re good. Okay. So, copy that. Shoot it over his way. And then if he encounters any issues, we did put, you know, let us know if you experience any hiccups. And then That way he knows like, okay, this is a maybe. So, um there&#39;s no way for me to check until he does try Um, I usually leave it open um to see if they do respond, but if he doesn&#39;t respond by the end of, you know, today or tomorrow morning, you can close it. But no, go away. I don&#39;t feel like answering. Mainly because I go in 10 minutes, but if she doesn&#39;t want it, I&#39;ll take it. Um, Okay. So, that should be that for that one. Do you have any others? Oh, yeah. Um, that one is definitely a fun one. Honestly, I&#39;ve done the same thing. I used to take Glenn&#39;s tickets a lot. Plus, when I started, I was the 7 to 4 shift. Um, and that&#39;s when Glenn calls at 7:00 in the morning. So, yeah, he kept calling and I kept getting him. So, it was fun. But I kind of learned to, you know, if he does call, I can help him. But for the most part, if I see his tickets, I already know that that&#39;s going to somebody else. Yeah, she just said she&#39;s like, &#34;I&#39;m on the I just finished a call with Milkut.&#34; I said, &#34;Uh, he has his ticket.&#34; Okay. Have the Glen. Sorry, I had an SFTP call. I&#39;m free. Okay. So, Nico&#39;s back and Maria has she said you can give the ticket to her actually. Um Yes. So, open your ticket. Click here. I can show you online. So, assigne just change that to somebody else. So, you can click Maria. Oh, I think it&#39;s because you haven&#39;t searched it yet. I think if you search for her, she should come up. Should All right. Um, so I guess she&#39;s just going to take over that one, which is great. That works out for you cuz that, like I said, that one is just a lot. Anything with global shop is just It&#39;s a lot. There&#39;s a lot going on in their files. Um, for mine, I have no idea. Yes. Anything that says like, oh, I can&#39;t log in. Um, this is showing rejected in the portal. This or that. If they&#39;re saying anything that says portal, the website, any of that is usually a key indicator that it&#39;s going to the website, our website. Um, that should be a little bit easier to navigate. Um, I know secondary users after a project finishes, they may not have the permissions or access to a trading partner. So, they&#39;ll say like, &#34;Hey, my inbox says I have messages, but when I go to my inbox, it&#39;s not there.&#34; That&#39;s usually a permissions issue. So, that one&#39;s also really easy. Um, all you have to do is log in as the admin and double check to make sure they have visibility. So theirs is and they&#39;ll usually put pictures. Theirs is pretty lengthy. I would leave that one. Banner is a very hefty one. Usually Maria and Sandy handled him. Um let&#39;s see. Invoices adding extra lines. Oh. Um seems like the customer ization was reversed. And now, if you want, you can take that one and we can look at that one together. Um, that one is going to be an integrator and QuickBooks one. Um, if you want to grab this one, let Kendall know that you&#39;re reviewing and you&#39;re looking into it. We can go over that one in a little bit. Um, that one that one would follow underneath us technically because it&#39;s just QuickBooks. I think I know a way to do that possibly. But yeah, go ahead, grab that one. We can work on that one together. Um, this guy, maybe for now, don&#39;t grab Cro. Cro is not a fun one. He will blow your phone up. Well, your phone&#39;s not on yet. But still, um, if you see Devon, go ahead and leave him there. He can talk to Nico. That&#39;s terrible. But he&#39;s not very nice. Um, okay. So, the one at the very bottom is a failed 856. So okay. Um without assigning this anywhere. Who is this? Hover. Nope. It does not show me her email. Okay. Um I have a few minutes. I&#39;ll do it. Let&#39;s see. Okay. So, I usually try to shuffle through these a little bit. Um, especially for you, I would go through and open them up in admin or I guess hold on, the website now has a so we don&#39;t have to do all of that. Yeah. Um, okay. So, medical. Okay. So, login. We&#39;ll see if we can locate an account. Um, if it&#39;s just a little injection. You may be able to Yep. Here we So, Medical Pharma, there&#39;s no rejection in here. So, they must have reached out to her directly. Um, okay. So, this ASN, let me copy. Oh, she even gave us the original ticket. So, let&#39;s go look there. Oh, boy. I have a notification. Okay, cool. Um, who is Nicole? Oh, I know. Okay, that&#39;s someone that&#39;s helping us. Oh, no. Um, okay. So, somebody on Cleo&#39;s side. Oh, no. Marie or Sandy was helping. Okay. Hello. Since the original email is not included in this thread, we&#39;re going to need some additional information. Please provide the Okay, here&#39;s the ASN. Lindsay, please provide. Oh, this is a work in progress. I don&#39;t know why she opened a Second ticket. That is not her vendor number either. That is incorrect. Um, okay. So, that goes with Nope. I&#39;m giving that to Sandy. So since some somebody&#39;s already working on that that is not us. Okay. Save assign not to me far away from me. Go to Sandy and always mark it as in progress. Okay. back here. Let&#39;s see if there&#39;s anything else. EDI maps, no, not tier one. IP changes, also not us. Certificate change, not us. We can look at CPRO. Our invoices are still calculating GST off of pre-disounted totals. Where should it be based off of post discounted totals? Yep. No, we&#39;re going to leave him there. Devon is just very needy and the moment you start helping him, he will be up your butt. So, full disclosure. So, sincere apologies if you do end up getting him. Um, yeah, you can grab So, that one if you didn&#39;t already grab the 44058 date. We can look at that. Um, oh, I think your customer earlier responded maybe. Um, this is your packing guy. Um, this one&#39;s the five below and he didn&#39;t fill all of his packs. Goodness. Please confirm. We&#39;ve been pushing him for a while, so please note. Okay. Um, Who is what? Oh, they need to reissue the PO. That was on the 15th. Okay. I don&#39;t understand who that is. I don&#39;t I don&#39;t get why people have signatures and they don&#39;t put their company name. If you&#39;re responding from a Gmail, who are you? I have no idea. I don&#39;t know who that is. Um, hi Raina. Okay. So, I&#39;m assuming Raina is with Five Below. Five Below doesn&#39;t have a domain. Interesting. Okay. So, for this one, this goes with your slime guy. Um, allegedly they were waiting on POS to be issued with a split that actually showed where the DCs were. That was two hours ago. I don&#39;t know. Mhm. So, I don&#39;t know if you want to grab that and work that with him, too, since it&#39;s the same person. I don&#39;t know. That is your guy, right? A1 Toys. Okay. Okay. Weird. Um um you can respond on there and just let them know. Just let just say, &#34;Hi, Chris. Let me know how I can help or if you need anything from our side.&#34; That&#39;s what I would say on that one. It may not need us. They may be just having us there for visibility. Um That&#39;s what I would do. And if they do, then we&#39;ll just ask for more details. But yeah, that was your guy with the labels. Um, okay. Miss Candy Rogers. I know she&#39;s in a greeted. That was yesterday. Okay. So, this one is one in the portal that was sent. Do believe that was in the middle of the restart yesterday. Oh, no. That&#39;s have to work. That would have been at 4. Um, this one, the way I would work it, I would respond, say, &#34;Hi, Miss Candy. Um, we&#39;re looking into this. I will get back to you. And then we would go through on the server, see if it did send, see if there&#39;s a 997. If not, we can respond back and give her options. So option one is we can go ahead and resend it if her partner hasn&#39;t confirmed with her if it&#39;s been received. Or option two, we can reach out to the trading partner. Um, this one&#39;s pretty straightforward. Did you want to try that one? Perfect. So grab it. I can put a note on here. And I&#39;m trying to put it in quotes. Um, Candy. So that&#39;s what I would say to her. Next album. Um, Sorry, I am like super quiet when I&#39;m typing. Option one. Okay, I&#39;m going to actually label that as option one. Option one, we can reach out copy. So you can do this the reach out. I&#39;ll do instructions in bold so that way it makes a little more sense and it&#39;ll stand out. So if you accidentally copy it, it&#39;s okay, you know, to take it off. Okay. Um, so first is going to be just reaching out, letting them know that you&#39;re looking into it. Then we can go on the servers, check to see if it did send, and then we can search for any inbound 997s yesterday that she would have received. Um, from there, if it wasn&#39;t, you can send this back. Um, I put, &#34;Unfortunately, I&#39;m not showing a positive 997 was returned.&#34; We have two options for this. Option one, we can reach out to the partner on your behalf to confirm if it was received or not, and retransmit if not. Um, option two, we can go ahead and always put with your permission with rescending the delivery. I&#39;ll just put the document to trigger the 997. If the partner has already confirmed with you that the document was not received, please let us know how you&#39;d like to proceed. So that&#39;s what I would do for that. And make sure that&#39;s internal. Yes. All righty. So that should go there. So once you assign it to yourself, if you haven&#39;t already, you might have, it may not have updated. Um, did you pull it already? Okay. Okay. Yeah. Sometimes mine is slow. So that&#39;s why I&#39;m like I don&#39;t know. Okay. Unassign. I do show that the numbers are going down. Yes. Okay. Perfect. So yeah, there&#39;s your note. Um you can respond to her if you want right now and just let her know you&#39;re looking into it. She&#39;s not super pushy, so even if we don&#39;t get a response back to her tonight, she&#39;ll be okay. Miss Candy is also really nice. Um I try not to throw anybody that&#39;s going to, you know, be on your on your neck over anything your way. Um of course. All righty. So, it is 3. So, I am about to head to my break. Um, perfect. Yeah, go for it. Um, play with it. See if you can locate even from his candy. See if you can find the stuff. on the server just so you get practice. Um, but if you have any questions, let me know. Let us know. We&#39;re here, of course. All righty. Well, you have a good day. You, too. Oh, you already had yours. You have a great rest of your day. All right. Anytime. Bye.</p>
