# CS-44924: Fw: Adding User

## Ticket Information
- **Key**: CS-44924
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-06-04T08:59:13.379-0600
- **Updated**: 2025-06-04T10:19:23.137-0600
- **Customer**: <EMAIL>

## Description
{color:#172B4D}For Maria 🙂{color}{color:#172B4D}{color}  
  
{color:#172B4D} Thank you!{color} 
|
|
|
|{color:#000001}{color}
|{color:#000001}Macy{color}{color:#000001}{color}|{color:#000001}  \\{color}{color:#000001}{color}|{color:#000001}Thurler{color}{color:#000001}{color}|{color:#000001}{color}
|{color:#000001}{color}
|{color:#000001}Cleo{color}{color:#000001}{color}|{color:#000001} :  \\{color}{color:#000001}{color}|{color:#000001}Account Manager{color}{color:#000001}{color}|{color:#000001}{color}
|
|{color:#000001}{color}
|{color:#000001}{color}{color:#000001}
{color}{color:#000001}{color}
|{color:#000001}Mobile: {color}{color:#000001}{color}|{color:#000001}262‑391‑7411{color}{color:#000001}{color}|{color:#000001}{color}
{color:#000001}{color}{color:#000001}{color}{color:#000001} {color}{color:#000001}{color}{color:#000001}{color}
|
| | | {color:#000001}{color}
|{color:#000001}{color}{color:#000001}
{color}{color:#000001}{color}
|{color:#000001}Email: {color}{color:#000001}{color}|{color:#000001}{color}[{color:#000001}{color}{color:#000001}<EMAIL>{color}|mailto:<EMAIL>]{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}|{color:#000001}{color}
{color:#000001}{color}{color:#000001}{color}{color:#000001} | {color}{color:#000001}{color}{color:#000001}
Web: {color}{color:#000001}{color}{color:#000001}{color}[{color:#000001}{color}{color:#000001}www.cleo.com{color}|https://www.cleo.com/]{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}{color:#000001}{color}
| | | {color:#0083CA}{color}
|{color:#0083CA}{color}{color:#0083CA}{color}
|{color:#0083CA}{color}[{color:#0083CA}{color}{color:#0083CA}Join us at one of our upcoming events. Check out the list!{color}|https://www.cleo.com/events]{color:#0083CA}{color}{color:#0083CA} \\{color}{color:#0083CA}{color}|{color:#0083CA}{color} 
----
 
 {color:#172B4D}*From:* Michael Sugerman <<EMAIL>>
  *Sent:* Wednesday, June 4, 2025 10:53 AM
  *To:* Thurler, Macy <<EMAIL>>
  *Cc:* Abdalla Al Ramahi <<EMAIL>>
  *Subject:* RE: Adding User{color}     
 *{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}
 
 
 
  

Perhaps this may be simpler that we think. 

My goal is to replace user Brian Schrieber who has retired [<EMAIL>|mailto:<EMAIL>] 

with his replacement Abdalla Al Ramahi [<EMAIL>|mailto:<EMAIL>] 

same log in same permissions just a different person . 

  

I want Abdalla to receive the Data-Trans emails every day and be able to at a minimum open & view the PO’s and print them. 

I can certainly do everything you are directing me to do if this is what’s needed but I don’t recall doing any pf this when we set up Brian a few years ago. 
| {color:#696969}
{color}|{color:#696969}  \\ {color}{color:#696969} {color}| 
| {color:#696969} {color} 
|{color:#696969} Best Regards, \\ {color}{color:#696969} {color}| {color:#696969}{color} 
| {color:#696969} {color} 
|{color:#696969} {color}{color:#696969} 
{color}{color:#696969} {color} 
|{color:#696969} Michael Sugerman \\ {color}{color:#696969} {color}| {color:#696969}{color}
{color:#696969} {color}{color:#696969} {color} {color:#696969} Office Manager{color}{color:#696969} {color} {color:#696969}{color} {color:#696969}{color}{color:#696969} Please note my new email <NAME_EMAIL>. All emails will be forwarded to this new address. {color}{color:#696969} 
{color} 
| 
| 
| {color:#696969} {color} 
|{color:#696969} {color}{color:#696969} 
{color} 
|{color:#696969}
{color}!image993399.png|thumbnail!{color:#696969}{color}{color:#696969} {color}| {color:#696969} {color}{color:#696969} {color}{color:#696969}
 +1 401 431 6101{color}{color:#696969} {color} {color:#696969}{color} 
| {color:#696969} {color} 
|{color:#696969} {color}{color:#696969} 
{color} 
|{color:#696969}
{color}!image135049.png|thumbnail!{color:#696969}{color}{color:#696969} {color}| {color:#696969} {color}{color:#696969} {color}{color:#696969}
 +1 401 431 6104{color}{color:#696969} {color} {color:#696969}{color} 
| {color:#696969} {color} 
|{color:#696969} {color}{color:#696969} 
{color} 
|{color:#696969}
{color}!image260404.png|thumbnail!{color:#696969}{color}{color:#696969} {color}| {color:#696969} {color}{color:#696969} {color}{color:#696969}{color}
[{color:#696969}{color}{color:#005AAA}rrmechatronics.com{color}|https://www.rrmechatronics.com/]{color:#005AAA}{color}{color:#696969}{color}{color:#696969} {color} {color:#696969}{color} 
| {color:#005AAA} {color} 
|{color:#005AAA} {color}{color:#005AAA} 
{color} 
|{color:#005AAA}
{color}!image821525.png|thumbnail!{color:#005AAA}{color}{color:#005AAA} {color}| {color:#005AAA} {color}{color:#005AAA} {color}{color:#005AAA}{color}
 [{color:#005AAA}{color}{color:#005AAA}lorrca.com{color}|https://lorrca.com/]{color:#005AAA}{color}{color:#005AAA}{color}{color:#005AAA} {color} {color:#005AAA}{color} 
| 
| 
| 
|
!image943583.png|thumbnail! <[https://rrmechatronics.com/]> | 
|
!image866458.png|thumbnail! <[https://lorrca.com/]> | {color:#696969}{color}{color:#696969}{color} [{color:#696969} {color}{color:#000001}{color}{color:#696969}R&R Mechatronics International B.V.{color}{color:#000001} {color}{color:#696969}The information contained in this communication is confidential and is intended solely for the use of the individual or entity to whom it is addressed. You should not copy, disclose or distribute this communication without the authorization of R&R Mechatronics International B.V.{color} {color:#000001}{color}{color:#0096D7}Disclaimer - Privacy Policy{color} {color:#000001} {color}|https://rrmechatronics.com/disclaimer]{color:#000001}{color}{color:#696969}{color}{color:#696969} {color} 
       

 *From:* Thurler, Macy <<EMAIL>> 
  *Sent:* Wednesday, June 4, 2025 10:43 AM
  *To:* Michael Sugerman <<EMAIL>>
  *Subject:* Re: Adding User 
| | {color:revert!important} {color}{color:#212121}You don't often get email from {color}[{color:#212121}<EMAIL>{color}|mailto:<EMAIL>]{color:#212121}. {color}[{color:#212121} Learn why this is important{color}|https://aka.ms/LearnAboutSenderIdentification]{color:#212121}{color} {color:revert!important} {color}{color:revert!important}{color} | {color:revert!important}{color}{color:revert!important}{color} | 
  

{color:#333333}Good morning, Michael!{color}  

  

{color:#333333}You will need to allow some permissions for the newly added user to see and access everything you can. No worries though- I have some steps detailed below for you!{color}  

  

{color:#333333}At first, newly added user will  *not* automatically be able to see and do everything an administrator can. The administrator account holds the master key, with full access to all documents and permission levels by default. Any secondary users, including those newly added, need to have their specific access and permissions configured by the account administrator. This allows the administrator to control exactly what documents a user can view and what actions they can perform within the system, even down to specific trading partners.{color} 

{color:#333333}Here are the precise steps you will need to follow to grant the necessary permissions to the new user:{color} 
*  {color:#333333}*Sign onto the WebEDI site*.{color}
* {color:#333333}From the  *'My WebEDI' dropdown* in the top right corner, click on  *settings*.{color}
* {color:#333333}Click  *'Users'* on the left-hand menu.{color}
* {color:#333333}If the user hasn't been added yet, click the  *"Add User"* button. Remember, accounts typically start with one admin and two secondary users, so if you're adding a fourth user, you may need to ensure you have enough user licenses.{color}
* {color:#333333}Once the user is listed, find and select their account.{color}
* {color:#333333}Scroll down to the  *Permissions* section.{color}
* {color:#333333}Here, the administrator can set limits. Select the desired  *trading partner* from the dropdown menu to configure permissions for that specific partner.{color}
* {color:#333333}Check the boxes for the  *document types* and  *actions* (like viewing 850s, creating 810s, sending 856s) that you want this user to have access to for the selected trading partner.{color}
* {color:#333333}If the user needs to see pricing information, make sure to click on the  *View Pricing and Totals* box.{color}
* {color:#333333}After configuring the permissions for the desired trading partners, click on the  *Save User Button* at the bottom of the page. You should see a confirmation message.{color}
* {color:#333333}Have the new user  *refresh their browser* or log out and log back in for the changes to take effect.{color}
 

{color:#333333}Sometimes, permissions might appear set, but the user still can't see documents. A helpful troubleshooting step for the administrator is to log in, go to settings > users > select the impacted user > scroll down to permissions >  *disable view for all documents* for a moment > scroll to the bottom and hit  *save* > then  *re-enable* the correct document permissions > and  *save* again. Then have the user refresh. This often helps reset the permissions correctly.{color} 

  

{color:#333333}Once completed, can you let me know if the new user is now able to access the documents and perform the actions they need? If you need anything else let me know!! I'm happy to help{color} {color:#333333}🙂{color} 

  

{color:#333333}Best,{color}{color:#172B4D}{color} 
| 
| 
| 
| 
|  *{color:#000001} *Macy*{color}* | {color:#000001}{color} |  *{color:#000001} *Thurler*{color}* | 
| 
|  *{color:#000001} *Cleo*{color}* |  *{color:#000001} *:*{color}* | {color:#000001}Account Manager{color} | 
| 
| 
| 
| {color:#000001}Mobile:{color} | {color:#000001}262‑391‑7411{color} |{color:#000001}{color} 
| 
| | | 
| 
| {color:#000001}Email:{color} | {color:#000001}{color}[{color:#000001}{color}{color:#000001}<EMAIL>{color}{color:#000001}{color}|mailto:<EMAIL>]{color:#000001}{color} |  {color:#000001} |{color}  {color:#000001}Web:{color}{color:#000001}{color}  [{color:#000001}{color}{color:#000001}www.cleo.com{color}{color:#000001}{color}|https://www.cleo.com/]{color:#000001}{color} 
| | | 
| | {color:#0083CA} *{color}[{color:#0083CA} {color}{color:#0083CA} *Join us at one of our upcoming events. Check out the list!*{color}{color:#0083CA} {color}|https://www.cleo.com/events]{color:#0083CA} {color}*{color:#0083CA}{color} | 
----
   

 *{color:#172B4D} *From:*{color}*{color:#172B4D} Michael Sugerman <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Sent:* Tuesday, June 3, 2025 1:58 PM
  *To:* Thurler, Macy <{color}[{color:#172B4D}<EMAIL>{color}|mailto:<EMAIL>]{color:#172B4D}>
  *Subject:* RE: Adding User{color}   

     

 *{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}*
 
  
  

Yes – I looked at that and I had started to add the new user but please clarify 

I want the new user to see & access everything I can currently see & access as user 1640 

Can I pick a different user name and give him access to what I see as user 1640? 
| 
| {color:dimgray}{color} | 
| 
| {color:dimgray}Best Regards,{color} | 
| 
| 
|  *{color:dimgray} *Michael Sugerman*{color}* |  {color:dimgray}Office Manager{color}   _{color:dimgray} _Please note my new email address is_{color} [{color:dimgray} _michael.sugerman@rrmechatronics.com_{color}|mailto:<EMAIL>]{color:dimgray} _. All emails will be forwarded to this new address._
 {color}_ 
| 
| 
| 
| 
| 
!image001.png|thumbnail! |  {color:dimgray}+1 401 431 6101{color} 
| 
| 
| 
!image002.png|thumbnail! |  {color:dimgray}+1 401 431 6104{color} 
| 
| 
| 
!image003.png|thumbnail! |{color:dimgray}{color}  [{color:dimgray}{color}{color:#005AAA}rrmechatronics.com{color}{color:dimgray}{color}|https://www.rrmechatronics.com/]{color:dimgray}{color} 
| 
| 
| 
!image003.png|thumbnail! |{color:#005AAA}{color}  [{color:#005AAA}{color}{color:#005AAA}lorrca.com{color}{color:#005AAA}{color}|https://lorrca.com/]{color:#005AAA}{color} 
| 
| 
| 
| 
!image004.png|thumbnail! <[https://rrmechatronics.com/]> | 
| 
!image005.png|thumbnail! <[https://lorrca.com/]> |{color:dimgray}{color}  [{color:dimgray} {color}{color:#000001}{color}{color:dimgray} {color}{color:dimgray} *R&R Mechatronics International B.V.*{color}{color:dimgray} {color}{color:#000001}{color}{color:dimgray} {color}{color:dimgray}The information contained in this communication is confidential and is intended solely for the use of the individual or entity to whom it is addressed. You should not copy, disclose or distribute this communication without the authorization of R&R Mechatronics International B.V.{color} {color:dimgray}{color}{color:#0096D7}Disclaimer - Privacy Policy{color}{color:dimgray} {color}{color:#0096D7}{color}{color:dimgray} {color}{color:#000001}{color}{color:dimgray}{color}|https://rrmechatronics.com/disclaimer]{color:dimgray}{color} 
 

       

 *From:* Thurler, Macy <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Tuesday, June 3, 2025 1:14 PM
  *To:* Michael Sugerman <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* Adding User 
| | {color:revert!important} {color}{color:#212121}You don't often get email from {color}[{color:#212121}<EMAIL>{color}|mailto:<EMAIL>]{color:#212121}. {color}[{color:#212121} Learn why this is important{color}|https://aka.ms/LearnAboutSenderIdentification]{color:#212121}{color} {color:revert!important} {color}{color:revert!important}{color} | {color:revert!important}{color}{color:revert!important}{color} | 
 
  

{color:#172B4D}Hi Michael,{color}{color:#172B4D}{color}   

    

{color:#172B4D}This is Macy, your Account Manager with Cleo!{color}{color:#172B4D}{color}   

    

{color:#172B4D}I saw you were inquiring about adding a user, is this to your WebEDI portal for access? If so, you should be able to add him right through the portal itself! I attached an image to assist.{color}{color:#172B4D}{color}   

    

{color:#172B4D}Under settings, go to user, add user, and you can add the person you wish to grant access to the portal.{color}{color:#172B4D}{color}    

    

{color:#172B4D}I hope this helps, please let me know if you have any questions.{color} {color:#172B4D}🙂{color} 
| 
| 
| 
| 
|  *{color:#000001} *Macy*{color}* | {color:#000001}{color} |  *{color:#000001} *Thurler*{color}* | 
| 
|  *{color:#000001} *Cleo*{color}* |  *{color:#000001} *:*{color}* | {color:#000001}Account Manager{color} | 
| 
| 
| 
| {color:#000001}Mobile:{color} | {color:#000001}262‑391‑7411{color} |{color:#000001}{color} 
| 
| | | 
| 
| {color:#000001}Email:{color} | {color:#000001}{color}[{color:#000001}{color}{color:#000001}<EMAIL>{color}{color:#000001}{color}|mailto:<EMAIL>]{color:#000001}{color} |  {color:#000001} |{color}  {color:#000001}Web:{color}{color:#000001}{color}  [{color:#000001}{color}{color:#000001}www.cleo.com{color}{color:#000001}{color}|https://www.cleo.com/]{color:#000001}{color} 
| | | 
| | {color:#0083CA} *{color}[{color:#0083CA} {color}{color:#0083CA} *Join us at one of our upcoming events. Check out the list!*{color}{color:#0083CA} {color}|https://www.cleo.com/events]{color:#0083CA} {color}* |

## Components


## Labels

