{"summary": {"total_tickets_analyzed": 360, "total_edi_tickets": 319, "edi_percentage": 88.61111111111111, "complete_resolutions": 164, "resolution_rate": 51.410658307210035, "high_value_tickets": 60}, "file_breakdown": {"0001-1000.pdf(1).html": {"total_tickets": 155, "edi_tickets": 141, "complete_resolutions": 66, "edi_percentage": 90.96774193548387}, "1001-2000.pdf(1).html": {"total_tickets": 112, "edi_tickets": 98, "complete_resolutions": 45, "edi_percentage": 87.5}, "2001-3000.pdf(1).html": {"total_tickets": 77, "edi_tickets": 65, "complete_resolutions": 44, "edi_percentage": 84.4155844155844}, "3001-3498.pdf.html": {"total_tickets": 16, "edi_tickets": 15, "complete_resolutions": 9, "edi_percentage": 93.75}}, "edi_type_distribution": {"EDI": 178, "810": 123, "Communication": 110, "856": 96, "850": 58, "SFTP": 39, "Mapping": 38, "FTP": 34, "997": 34, "AS2": 33, "X12": 29}, "common_issue_keywords": {"format": 87, "error": 85, "missing": 40, "connection": 30, "failed": 29, "validation": 15, "mapping": 12, "duplicate": 9, "certificate": 7, "stuck": 4}, "high_value_tickets": [{"id": "CS-43963", "title": "Fw: Invoice# 254784. VID# 168595 , EZ GROOM.", "edi_types": ["810", "FTP", "EDI"], "root_cause": "replying to a previous email thread.", "resolution": "and the ticket will be automatically closed after 5 days.", "troubleshooting_steps": 2}, {"id": "CS-43611", "title": "Boscov&#39;s UCC-128", "edi_types": ["EDI"], "root_cause": "", "resolution": "specs:\n<a href=\"https://datatrans-inc.", "troubleshooting_steps": 4}, {"id": "CS-43947", "title": "Global Shop Connection", "edi_types": ["SFTP", "Communication"], "root_cause": "", "resolution": "this issue.", "troubleshooting_steps": 2}, {"id": "CS-43291", "title": "FW: Cooper Standard: EDI Supplier Notification - Livonia / COOPER FAIRVIEW", "edi_types": ["856", "X12", "EDI"], "root_cause": "", "resolution": "ative\ngreystone of lincoln|w (401)-333-0444 ext.", "troubleshooting_steps": 4}, {"id": "CS-42177", "title": "Off invoice (DTS-620)", "edi_types": ["856", "810", "Mapping"], "root_cause": "supposed to be resolved but today i am having the same problem with the same customer.", "resolution": "the error causing the allowance information to replicate on all line items.", "troubleshooting_steps": 2}, {"id": "CS-44150", "title": "Fwd: Document Processing Error: Cabelas Canada 2025-05-21 17:43:10", "edi_types": ["997", "AS2", "EDI"], "root_cause": "detected at: segment count: 320 element count: 2 characters: 12067 through 12068 if either of ack02, ack03 is present, then all must be present.", "resolution": "the error or need additional information, please visit our support center and choose one of our convenient contact channels to connect with a support representative.", "troubleshooting_steps": 7}, {"id": "CS-44069", "title": "Fwd: Document Processing Error: Cabelas Canada 2025-05-20 21:19:42", "edi_types": ["856", "997", "AS2", "EDI"], "root_cause": "detected at: segment count: 227 element count: 2 characters: 7214 through 7217\nan error has occurred while processing the document referenced above.", "resolution": "the error or need additional information, please visit our support center and choose one of our convenient contact channels to connect with a support representative.", "troubleshooting_steps": 2}, {"id": "CS-43231", "title": "KeHE Distributors - 856 Document Default (DTS-653)", "edi_types": ["850", "856"], "root_cause": "", "resolution": "the document default page.", "troubleshooting_steps": 2}, {"id": "CS-43282", "title": "Re: FW: FW: EDI with Milcut - AM General", "edi_types": ["850", "856", "EDI", "Mapping", "Communication"], "root_cause": "", "resolution": "to the issue.", "troubleshooting_steps": 5}, {"id": "CS-43214", "title": "FW: POPTIME SNACK BRANDS - 850 Failure", "edi_types": ["850", "EDI", "Mapping", "Communication"], "root_cause": "root cause: translation failed due to value in n1_02 exceeded the allowable maximum length of 35 characters, per map specs.", "resolution": "the ship to info but i don’t see that the order was entered.", "troubleshooting_steps": 6}, {"id": "CS-43294", "title": "Re: Target POs Not Syncing into ShipStation Integration", "edi_types": ["850", "856", "810", "EDI", "Mapping", "Communication"], "root_cause": "that the integrator could not find item 889168293451 in your catalog.", "resolution": "", "troubleshooting_steps": 3}, {"id": "CS-43137", "title": "Missing 810 for WESCO PO# 1768-799085", "edi_types": ["810", "Mapping", "Communication"], "root_cause": "", "resolution": "", "troubleshooting_steps": 3}, {"id": "CS-43096", "title": "See message 44112193.", "edi_types": ["810"], "root_cause": "", "resolution": "", "troubleshooting_steps": 3}, {"id": "CS-43036", "title": "Re: FW: Arrival of data from Omnimax International-850", "edi_types": ["850", "Communication"], "root_cause": "", "resolution": "", "troubleshooting_steps": 2}, {"id": "CS-43021", "title": "RE: [EXTERNAL] Re: AAFES/Exchange x Radial System to System Integration - A Better Tomorrow LLC (07847402)", "edi_types": ["850", "856", "810", "997", "EDI"], "root_cause": "", "resolution": "files have been sent.", "troubleshooting_steps": 3}, {"id": "CS-42990", "title": "Re: [EXTERNAL] Re: AAFES/Exchange x Radial System to System Integration - A Better Tomorrow LLC (07847402)", "edi_types": ["850", "856", "810", "997", "AS2", "EDI", "Communication"], "root_cause": "", "resolution": "files have been sent.", "troubleshooting_steps": 3}, {"id": "CS-43107", "title": "RE: CS-43050 Parent Pointer not Found", "edi_types": ["856"], "root_cause": "viruses being passed via e-mail.", "resolution": "by our project manager.", "troubleshooting_steps": 2}, {"id": "CS-43024", "title": "RE: [EXTERNAL] CS-42354 Data Trans missing 855 for Grainger and 810 unread in Draft folder", "edi_types": ["850", "810", "SFTP", "Communication"], "root_cause": "", "resolution": "data trans missing 855 for grainger and 810 unread in draft folder\n———-—\nreply above this line.", "troubleshooting_steps": 2}, {"id": "CS-42902", "title": "FW: EDI Invoice rejected", "edi_types": ["810", "EDI"], "root_cause": "", "resolution": "the invoice issue.", "troubleshooting_steps": 2}, {"id": "CS-42796", "title": "FW: Document Processing Error: True Value - Retail 2025-04-22 16:09:08", "edi_types": ["850", "810", "AS2", "EDI"], "root_cause": "", "resolution": "the error or need additional information, please visit our support center and choose one of our convenient contact channels to connect with a support representative.", "troubleshooting_steps": 4}]}