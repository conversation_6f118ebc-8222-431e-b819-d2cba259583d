# CS-45186: KeHE 855 Purchase Order 3100814

## Ticket Information
- **Key**: CS-45186
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON>
- **Created**: 2025-06-10T08:11:05.294-0600
- **Updated**: 2025-06-10T09:47:19.834-0600
- **Customer**: <PERSON> Extracts

## Description
Customer: Adams Extracts 

ID: 6061 

Trading Partner: KeHE Distributors 

Document: 855 PO Ack 

Number: 3100814 

  

I received the email below from Genius Central regarding PO Acknowledgement 3100814 that was sent to KeHE on 6/5/25. Can you please investigate? 

  

!image001.png|thumbnail! 

  

  
  

 *From:* <EMAIL> <<EMAIL>> 
  *Sent:* Thursday, June 5, 2025 3:26 PM
  *To:* <PERSON>, <PERSON> <<EMAIL>>
  *Subject:* Document error - 855 Purchase Order - 3100814   

   

 {color:#172B4D}EDI Document Error Notification: Action Required{color} 
| 
| !http://geniuscentral.com/files/email/2017west/gc-logo_b.png! <[http://url6300.geniuscentral.com/ls/click?upn=u001.7yP8kVZ7a3dCLkCt3iS4edgJ7-2BUQ-2BXqMf3KSCvtptjHycN8jDrqNeC7KxRcwc5BNkW1G_6u6vDCkK85wo1bmjII8yQu3W-2FBReWt9HnB5WjuZXDORL36i7yyFW6cNRNyQ-2BGKPlWqtO6gootaWUq3c899HkaQ-2FIgkLOfIiVTuvTIkoiNmWGt9qFxDFVn-2BbcevOu4YBwQx-2BvyTC-2BQWx5-2FTdfuHlemH3WZHuJXnPQMMHmv1ApeKSbJnBKiy97Ec86IfhIRpFokPTqigjFmhicJuTCu0SRfw-3D-3D]> | 
| {color:#555555} \\  *EDI Document Error Notification: Action Required*{color} | 
| 
 *{color:#555555} *Date:*{color}* {color:#555555}2025-06-05T19:06:11+00:00{color} 
 *{color:#555555} *Document Type:*{color}* {color:#555555}855{color} 
 *{color:#555555} *Sender:*{color}* {color:#555555}ADAMS EXTRACT - DTS6061{color} 
 *{color:#555555} *Receiver:*{color}* {color:#555555}KeHE Buyer - 0569813430000{color} {color:#555555}{color} 
 *{color:#555555} *Group Control Number:*{color}* {color:#555555}249{color} 
 *{color:#555555} *Transaction Control Number:*{color}* {color:#555555}249{color} {color:#555555}{color} 
 *{color:#555555} *PO #:*{color}* {color:#555555}3100814{color} 
 *{color:#555555} *GC Order ID:*{color}* {color:#555555}0{color} 
 *{color:#555555} *Transaction Key:*{color}* {color:#555555}6b6ab54b-aa90-4178-81a0-3590ebefe860{color} {color:#555555}{color} 
 *{color:#555555} *Error:*{color}* {color:#555555}Message was not sent successfully or completely. Call to remote http server failed (Remote server answered with http return code 400 (Bad Request).)..{color} | 
| 
| 
{color:white}Please correct the error listed above.{color} 
{color:white}If you need additional help, please contact the GC Support Team.{color} {color:white}{color}[{color:white}{color}{color:white}geniuscentral.com{color}{color:white}{color}|http://url6300.geniuscentral.com/ls/click?upn=u001.7yP8kVZ7a3dCLkCt3iS4edgJ7-2BUQ-2BXqMf3KSCvtptjH6YoUnlnAexZqvX6BI-2FAkn4xSP_6u6vDCkK85wo1bmjII8yQu3W-2FBReWt9HnB5WjuZXDORL36i7yyFW6cNRNyQ-2BGKPl2TsIJxMjxpLqxcL6NECzMi2uwzYG7e7H3sm-2BCH-2BCay4DDKMpwmjtC0LIPM9hukTi8x5kQtkziziu-2FiWNNCbLooQWW6L9DKFKQy2VAocoIQIL4yiVsPax1SauxKJyNneqAUjfGrHbzhoa4Gn6wVMK-2Fg-3D-3D]{color:white} : {color}{color:white}************{color}{color:white} : {color}[{color:white}{color}{color:white}<EMAIL>{color}{color:white}{color}|mailto:<EMAIL>]{color:white}{color} | 
| | 
   

!http://url6300.geniuscentral.com/wf/open?upn=u001.o53znndtEotsMzGQnRM-2FBi1JYSm83CAFahXLxZL-2FbRGIszQRwHDB14lnam3eQi2SlZRBEtL6eDh1epc6tztD6MWIX88tyRHsR8kDE7tq4OzcZ5U0kxc8uEIMN-2B-2BZ3I-2BOnRBYiaEyHHSuwyph-2BDftfi5a-2B-2FMDpn-2FD6YIQRB-2FJo6d2Fn-2FiQgSbHsngi-2FezxBqOD7f3ejhIsnuirFIQ-2FxAgAfRH-2F50SCepdap-2BRPqwW8AI-3D!

## Components


## Labels

