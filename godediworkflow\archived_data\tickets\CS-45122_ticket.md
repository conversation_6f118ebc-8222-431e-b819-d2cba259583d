# CS-45122: 830 re-stage request

## Ticket Information
- **Key**: CS-45122
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: Unassigned
- **Reporter**: <PERSON>
- **Created**: 2025-06-09T08:43:04.159-0600
- **Updated**: 2025-06-09T11:40:16.513-0600
- **Customer**: <PERSON>

## Description
Good morning, 

  

I’m reaching about 2 EDI reports that failed to populate a csv file this week…. 

 *+Summary:+* 
* Back in 2020 we worked with <PERSON> at DataTrans and <PERSON> (former IT Director) to get 2 reports generated that merge data into csv files
* These reports somehow get output to T(sales):\DTFTP\ 
* Looks like the daily report (862) stopped populating after 6/5, and we also didn’t receive the weekly report (830) that we normally would’ve received on 6/8
 

 *+Questions:+* 

Would you be able to help correct this issue, and also re-stage/send a current 830 report? 

  

Thanks, 

 *{color:#049099} *<PERSON>*{color}* 

{color:#00616F}Sales Operations Specialist
 Mobile ******-346-6726
 Hexagon Agility{color} 

   Hexagon Agility Privacy Policy and Email Disclaimer
  | [Hexagon Agility Privacy Policy|https://hexagonagility.com/privacy-policy/]  | [Hexagon Agility Email Disclaimer|https://hexagonagility.com/email-disclaimer/] |

## Components


## Labels

