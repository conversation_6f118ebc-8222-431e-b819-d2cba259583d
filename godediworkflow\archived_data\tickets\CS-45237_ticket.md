# CS-45237: Guidance Request on AutoZone Integration & Training Class Access

## Ticket Information
- **Key**: CS-45237
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-06-10T21:28:06.686-0600
- **Updated**: 2025-06-16T07:43:41.889-0600
- **Customer**: <EMAIL>

## Description
Hi Team,

I hope this message finds you well. 

This is <PERSON> from AstroAI, and we recently completed our account integration with AutoZone. Could you kindly provide guidance on navigating WebEDI for this specific partnership?

I’ve reviewed the generic tutorial videos, but as practices vary across trading partners, I’d appreciate tailored instructions for AutoZone’s workflows—particularly regarding the following file types:


{quote}
ASN (856)
INVENTORY ADVICE (846)
INVOICE (810)
PO (850){quote}



Currently, my Inbox only contains test POs, and I’m unsure which fields auto-populate or require manual input. For example, the blue fields in the attached screenshot appear blank.  *Would direct support (e.g., via quick call) be possible to clarify these details?*

Additionally, I attempted to join Monday’s Training Class but was unable to access it. Could you confirm if my account requires an invitation? I’d welcome the opportunity to attend future sessions.

Thanks,{adf}{"type":"expand","content":[{"type":"paragraph","content":[{"type":"text","text":"Andrea L","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":"Business Development","marks":[{"type":"textColor","attrs":{"color":"#808080"}}]},{"type":"text","text":" "},{"type":"text","text":"▂▂","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#ff6600"}}]},{"type":"text","text":"  "},{"type":"text","text":"AstroAI Corporation","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":"  ","marks":[{"type":"textColor","attrs":{"color":"#172b4d"}}]},{"type":"text","text":"7423 Doig Dr, Garden Grove, CA 92841Tel (WhatsApp): +86 *********** Web:","marks":[{"type":"textColor","attrs":{"color":"#808080"}}]},{"type":"text","text":" "},{"type":"text","text":"www.astroai.com","marks":[{"type":"textColor","attrs":{"color":"#808080"}},{"type":"link","attrs":{"href":"http://www.astroai.com"}}]},{"type":"text","text":"  "},{"type":"text","text":"E-mail:","marks":[{"type":"textColor","attrs":{"color":"#808080"}}]},{"type":"text","text":" "},{"type":"text","text":"<EMAIL>","marks":[{"type":"textColor","attrs":{"color":"#808080"}},{"type":"link","attrs":{"href":"mailto:<EMAIL>"}}]},{"type":"text","text":"  "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"http://exmail.qq.com/cgi-bin/viewfile?type=signature&picid=ZX2509-7rWbrydOL_%7EUIQxvo1RjScn&uin=768641115","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":" "}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

