#!/usr/bin/env python3
import PyPDF2
import os

def extract_full_text(pdf_path):
    """Extract complete text from PDF."""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text
    except Exception as e:
        return f"Error: {str(e)}"

# Compare the two wisebatch files
file1 = '/home/<USER>/edi_knowledge_base/trading_partners/wisebatch.pdf'
file2 = '/home/<USER>/edi_knowledge_base/trading_partners/wisebatch 2.pdf'

print("WISEBATCH FILE COMPARISON")
print("=" * 60)

text1 = extract_full_text(file1)
text2 = extract_full_text(file2)

print(f"\nwisebatch.pdf:")
print(f"- Length: {len(text1)} characters")
print(f"- First 200 chars: {text1[:200]}...")

print(f"\nwisebatch 2.pdf:")
print(f"- Length: {len(text2)} characters")
print(f"- First 200 chars: {text2[:200]}...")

# Check if they're the same
if text1 == text2:
    print("\n✅ Files contain IDENTICAL text content")
else:
    print("\n❌ Files contain DIFFERENT content")
    
    # Find what's different
    if "810" in text2 and "810" not in text1:
        print("- wisebatch 2.pdf contains EDI 810 transaction data")
    if "ISA*" in text2 and "ISA*" not in text1:
        print("- wisebatch 2.pdf contains actual EDI segments")
    if len(text1) > len(text2):
        print("- wisebatch.pdf has more content")
    elif len(text2) > len(text1):
        print("- wisebatch 2.pdf has more content")