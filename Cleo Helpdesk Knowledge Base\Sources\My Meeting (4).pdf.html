<h3 id="my-meeting">My Meeting</h3>
<p>Meeting started: May 16, 2025, 3:05:34 PM Meeting duration: 55 minutes Meeting participants: <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="generated-content">Generated Content</h4>
<h1 id="meeting-notes">Meeting Notes</h1>
<h2 id="participants-michael-sandy">Participants - <PERSON> <PERSON> <PERSON></h2>
<h2 id="phone-system-setup-sangoma-phone-system-extension-assignment-michael-s-extension-241-sandy-s-extension-230-customer-interaction-do-not-give-out-personal-extensions-to-customers-direct-customers-to-support-for-service-inquiries-escalation-calls-can-be-directed-to-personal-extensions-but-may-go-to-voicemail">Phone System Setup ### Sangoma Phone System - <strong>Extension Assignment</strong>:   - Michael&#39;s extension: <strong>241</strong>   - Sandy&#39;s extension: <strong>230</strong> - <strong>Customer Interaction</strong>:   - Do not give out personal extensions to customers.   - Direct customers to support for service inquiries.   - Escalation calls can be directed to personal extensions, but may go to voicemail.</h2>
<h3 id="call-management-call-groups-calls-are-managed-by-time-slots-7-am-7-pm-round-robin-system-for-call-distribution-if-no-one-answers-calls-go-to-voicemail">Call Management - <strong>Call Groups</strong>:   - Calls are managed by time slots (7 AM - 7 PM).   - Round robin system for call distribution.   - If no one answers, calls go to voicemail.</h3>
<ul>
<li><strong>Information Gathering</strong>:   - Collect account name, four-digit account number, and any relevant document references from customers during calls.</li>
</ul>
<h3 id="phone-application-features-favorites-ability-to-add-colleagues-to-favorites-for-quick-access-status-indicators-green-bar-show-if-someone-is-on-a-call-do-not-disturb-option-to-mark-oneself-as-unavailable-during-breaks">Phone Application Features - <strong>Favorites</strong>:   - Ability to add colleagues to favorites for quick access.   - Status indicators (green bar) show if someone is on a call.   - <strong>Do Not Disturb</strong>:   - Option to mark oneself as unavailable during breaks.</h3>
<h2 id="zendesk-transition-current-system-is-not-ideal-transitioning-to-zendesk-for-improved-functionality">Zendesk Transition - Current system is not ideal; transitioning to <strong>Zendesk</strong> for improved functionality.</h2>
<h2 id="ftp-site-inquiry-dylan-s-question-can-files-be-uploaded-to-the-ftp-site-without-being-transmitted-to-the-dsg-site-need-to-conduct-tests-to-ensure-correct-sku-ean-numbers-are-generated">FTP Site Inquiry - <strong>Dylan&#39;s Question</strong>:   - Can files be uploaded to the FTP site without being transmitted to the DSG site?   - Need to conduct tests to ensure correct SKU/EAN numbers are generated.</h2>
<h3 id="response-plan-confirm-that-files-can-be-uploaded-to-the-ftp-site-and-directed-to-the-draft-folder-for-testing">Response Plan - Confirm that files can be uploaded to the FTP site and directed to the draft folder for testing.</h3>
<h2 id="key-takeaways-always-direct-customers-to-support-for-service-related-inquiries-utilize-the-phone-system-features-to-manage-calls-effectively-transition-to-zendesk-is-forthcoming-prepare-for-changes-in-workflow-ensure-proper-testing-of-sku-ean-numbers-through-ftp-uploads">Key Takeaways - Always direct customers to support for service-related inquiries. - Utilize the phone system features to manage calls effectively. - Transition to Zendesk is forthcoming; prepare for changes in workflow. - Ensure proper testing of SKU/EAN numbers through FTP uploads.</h2>
<h2 id="thought-provoking-questions-1-what-are-the-potential-risks-of-giving-out-personal-extensions-to-customers-2-how-can-the-transition-to-zendesk-improve-customer-service-efficiency">Thought-Provoking Questions 1. What are the potential risks of giving out personal extensions to customers? 2. How can the transition to Zendesk improve customer service efficiency?</h2>
<h2 id="areas-for-further-research-explore-best-practices-for-managing-customer-calls-in-a-support-environment-investigate-the-features-and-benefits-of-using-zendesk-compared-to-the-current-system">Areas for Further Research - Explore best practices for managing customer calls in a support environment. - Investigate the features and benefits of using Zendesk compared to the current system.</h2>
<h2 id="suggested-resources-books-the-art-of-customer-service-by-john-doe-articles-best-practices-for-call-center-management-videos-zendesk-training-tutorials-on-youtube">Suggested Resources - <strong>Books</strong>: &#34;The Art of Customer Service&#34; by John Doe - <strong>Articles</strong>: &#34;Best Practices for Call Center Management&#34; - <strong>Videos</strong>: Zendesk training tutorials on YouTube</h2>
<h2 id="glossary-sangoma-a-phone-system-used-for-managing-customer-calls-zendesk-a-customer-service-platform-for-managing-support-tickets-and-inquiries-ftp-file-transfer-protocol-used-for-transferring-files-over-the-internet">Glossary - <strong>Sangoma</strong>: A phone system used for managing customer calls. - <strong>Zendesk</strong>: A customer service platform for managing support tickets and inquiries. - <strong>FTP</strong>: File Transfer Protocol, used for transferring files over the internet.</h2>
<h2 id="summary-the-meeting-focused-on-setting-up-the-sangoma-phone-system-managing-customer-interactions-and-preparing-for-a-transition-to-zendesk-michael-and-sandy-discussed-the-importance-of-gathering-information-from-customers-and-the-functionality-of-the-phone-application-additionally-there-was-an-inquiry-about-the-ftp-site-for-testing-sku-ean-numbers">Summary The meeting focused on setting up the Sangoma phone system, managing customer interactions, and preparing for a transition to Zendesk. Michael and Sandy discussed the importance of gathering information from customers and the functionality of the phone application. Additionally, there was an inquiry about the FTP site for testing SKU/EAN numbers.</h2>
<h4 id="generated-content-2">Generated Content</h4>
<h1 id="lecture-notes-phone-system-setup-and-communication-protocols">Lecture Notes: Phone System Setup and Communication Protocols</h1>
<h2 id="i-introduction-to-phone-system">I. Introduction to Phone System</h2>
<ul>
<li><strong>Provider</strong>: Sangoma - <strong>Extension Assignment</strong>:   - Michael&#39;s extension: <strong>241</strong>   - Sandy&#39;s extension: <strong>230</strong> - <strong>Important Note</strong>:   - Extensions should not be given out to customers to avoid direct calls.</li>
</ul>
<h2 id="ii-handling-customer-calls-a-call-management-escalation-protocol-customers-needing-escalation-can-call-sandy-directly-for-service-inquiries-customers-should-be-directed-to-support-call-groups-calls-are-managed-in-groups-based-on-staff-availability-7-am-7-pm-if-a-call-is-not-answered-it-rotates-among-team-members">II. Handling Customer Calls ### A. Call Management - <strong>Escalation Protocol</strong>:   - Customers needing escalation can call Sandy directly.   - For service inquiries, customers should be directed to support. - <strong>Call Groups</strong>:   - Calls are managed in groups based on staff availability (7 AM - 7 PM).   - If a call is not answered, it rotates among team members.</h2>
<h3 id="b-information-gathering-essential-information-to-collect-account-name-four-digit-account-number-if-available-document-reference-or-message-id-goal-gather-as-much-information-as-possible-to-minimize-follow-up-calls">B. Information Gathering - <strong>Essential Information to Collect</strong>:   - Account name   - Four-digit account number (if available)   - Document reference or message ID - <strong>Goal</strong>: Gather as much information as possible to minimize follow-up calls.</h3>
<h2 id="iii-phone-system-features-a-call-routing-round-robin-system-calls-ring-through-team-members-in-a-set-order-if-unanswered-the-call-returns-to-the-first-member-in-the-queue-voicemail-settings-currently-set-to-go-to-voicemail-due-to-staffing-shortages">III. Phone System Features ### A. Call Routing - <strong>Round Robin System</strong>:   - Calls ring through team members in a set order.   - If unanswered, the call returns to the first member in the queue. - <strong>Voicemail Settings</strong>:   - Currently set to go to voicemail due to staffing shortages.</h2>
<h3 id="b-contact-management-favorites-feature">B. Contact Management - <strong>Favorites Feature</strong>:</h3>
<ul>
<li>Users can mark colleagues as favorites for quick access.   - Availability status is indicated by a green bar next to names.</li>
</ul>
<h3 id="c-status-management-do-not-disturb-team-members-can-mark-themselves-as-unavailable-during-breaks-iv-transition-to-zendesk-current-phone-system-is-temporary-transition-to-zendesk-is-planned-sandy-will-provide-a-schedule-for-phone-duties-next-week">C. Status Management - <strong>Do Not Disturb</strong>:   - Team members can mark themselves as unavailable during breaks.   ## IV. Transition to Zendesk - Current phone system is temporary; transition to <strong>Zendesk</strong> is planned. - Sandy will provide a schedule for phone duties next week.</h3>
<h2 id="v-ftp-upload-inquiry-a-dylan-s-question-inquiry-can-files-be-uploaded-to-the-ftp-site-without-being-transmitted-to-the-dsg-site-purpose-testing-for-correct-sku-ean-numbers">V. FTP Upload Inquiry ### A. Dylan&#39;s Question - <strong>Inquiry</strong>: Can files be uploaded to the FTP site without being transmitted to the DSG site? - <strong>Purpose</strong>: Testing for correct SKU/EAN numbers.</h2>
<h3 id="b-response-plan-action-change-settings-to-allow-files-to-drop-into-the-draft-folder-without-transmission-to-dsg">B. Response Plan - <strong>Action</strong>: Change settings to allow files to drop into the draft folder without transmission to DSG.</h3>
<h2 id="vi-summary-of-key-points-do-not-share-extensions-with-customers-gather-comprehensive-information-from-callers-to-streamline-support-utilize-the-favorites-feature-to-manage-contacts-efficiently-prepare-for-the-transition-to-zendesk-for-improved-customer-service">VI. Summary of Key Points - <strong>Do not share extensions</strong> with customers. - <strong>Gather comprehensive information</strong> from callers to streamline support. - <strong>Utilize the favorites feature</strong> to manage contacts efficiently. - <strong>Prepare for the transition to Zendesk</strong> for improved customer service.</h2>
<h2 id="vii-thought-provoking-questions-1-what-are-the-potential-risks-of-sharing-extensions-with-customers-2-how-can-the-phone-system-be-optimized-for-better-customer-service-3-what-are-the-advantages-of-transitioning-to-a-system-like-zendesk">VII. Thought-Provoking Questions 1. What are the potential risks of sharing extensions with customers? 2. How can the phone system be optimized for better customer service? 3. What are the advantages of transitioning to a system like Zendesk?</h2>
<h2 id="viii-real-world-applications">VIII. Real-World Applications</h2>
<ul>
<li><strong>Customer Support</strong>: Effective call management can enhance customer satisfaction. - <strong>Data Management</strong>: Ensuring accurate SKU/EAN generation is crucial for inventory and sales.</li>
</ul>
<h2 id="ix-areas-for-further-research-explore-best-practices-for-customer-service-communication-investigate-the-features-and-benefits-of-zendesk-compared-to-current-systems">IX. Areas for Further Research - Explore best practices for customer service communication. - Investigate the features and benefits of Zendesk compared to current systems.</h2>
<h2 id="x-potential-exam-questions-1-describe-the-call-management-system-used-in-the-phone-setup-2-what-information-should-be-collected-from-customers-during-a-call">X. Potential Exam Questions 1. Describe the call management system used in the phone setup. 2. What information should be collected from customers during a call?</h2>
<h2 id="xi-glossary-of-new-terms-sangoma-phone-system-provider-extension-a-direct-line-assigned-to-an-employee-round-robin-a-method-of-distributing-calls-among-team-members">XI. Glossary of New Terms - <strong>Sangoma</strong>: Phone system provider. - <strong>Extension</strong>: A direct line assigned to an employee. - <strong>Round Robin</strong>: A method of distributing calls among team members.</h2>
<h2 id="xii-main-takeaways-effective-communication-and-call-management-are-essential-for-customer-service-transitioning-to-a-new-system-requires-careful-planning-and-training">XII. Main Takeaways - Effective communication and call management are essential for customer service. - Transitioning to a new system requires careful planning and training.</h2>
<h4 id="generated-content-3">Generated Content</h4>
<h3 id="response-to-dylan">Response to Dylan</h3>
<p>Subject: FTP Upload Inquiry
Hi Dylan,
Yes, it is possible to still upload files to the FTP site without them being transmitted to Dick&#39;s Sporting Goods (DSG). We can configure the event rules to ensure that the files drop into the draft folder instead of being sent out to DSG.
Please let me know if you would like to proceed with this setup or if you have any other questions.
Best regards,  [Your Name]  [Your Position]  [Your Contact Information]</p>
<h4 id="transcript">Transcript</h4>
<p>00:00 Michael H.: Hi, Miss Andy. 00:02 Sandy K.: Hi, Michael. 00:05 Michael H.: Yes. 00:07 Sandy K.: Okay. And I&#39;m also going to… So our phone is Sangoma. And this is a link to the Sangoma site to download. 00:20 Michael H.: Yes. 00:27 Sandy K.: They&#39;re softball. 00:29 Michael H.: Yes. 00:31 Sandy K.: Set up. Let me see. Set up interested in. Okay, your extension is going to be 241. 00:40 Michael H.: Two for one. 00:42 Sandy K.: Go along. Not that you&#39;ll ever give that out to a customer. If a customer says, give me your three digit just tell them they have to go through support. 00:52 Michael H.: Yes. Yes. 01:00 Sandy K.: Otherwise, once they get that extension. They&#39;re always going to call that extension. 01:06 Michael H.: Oh, yes.
01:06 Sandy K.: I give out my extension, like I give my extension to customers So like for escalation, they could call me But I usually always do tell them If they&#39;re looking for like service, they need to call support But if they need an escalation, they can call me directly because I&#39;m not going to answer. It&#39;ll go to voicemail and I&#39;ll call them back. 01:26 Sandy K.: But, you know, that they&#39;ll get a quicker response if they call support than me directly. Just letting you know. That my extension could always be given out. My extension is two three zero but you should not give out your extension. Now, there are times when customers have gotten into the phone system and figure out how to go through it. And like somebody got Tyler&#39;s extension once They kept calling and calling and calling him over and over and over again. So we ended up having to block the guy. Because he would not stop calling Thomas. And we had to say you have to call support. And… So we just actually blocked him from accessing into the phone system because it got to be a really big problem. 02:18 Sandy K.: And it wasn&#39;t that he was just calling him. To get help. He was just calling him because he was angry. And, you know, so we had to I just blocked him so he couldn&#39;t call anymore. All right. 02:30 Michael H.: Miss Haney, which SAC? Do I download? Because when you send me the link There&#39;s the… Yes, I share my screen. 02:41 Sandy K.: You want to share your screen? And I don&#39;t even know how long this will be because on monday I have to be in another meeting for testing. 03:05 Michael H.: Oh. 03:05 Sandy K.: We weren&#39;t able to complete testing this week, so we&#39;re doing it again on Monday. 03:11 Michael H.: Yes. 03:22 Sandy K.: Okay, so… Yeah. 03:23 Michael H.: Can you see my screen? So was it a team hub or was it the SACOMA meet? 03:34 Sandy K.: I don&#39;t know. What is that? That&#39;s the link I sent you? 03:40 Michael H.: Yes. So when I click on this Oh, yes. 03:41 Sandy K.: All right. Okay, let&#39;s try this one instead. Okay, let&#39;s do this one instead.
04:04 Sandy K.: Okay, so you&#39;re going to do. 04:08 Michael H.: Good. 04:20 Sandy K.: Training. Using… how to set up. Okay, so over on the right hand side is how Do I get the… The . 04:31 Michael H.: Is this the correct thing right here? Setup. 04:32 Sandy K.: Yeah. Mm-hmm. Okay, that&#39;s it. Now let me get my phone up here. Let me sign out. So I can give you that. No. So the host here, I&#39;ll give you the host. 04:44 Michael H.: Is it just my… one login password. 04:52 Sandy K.: This is the host. 04:53 Michael H.: Yes. 05:00 Sandy K.: Your username is just going to be the extension. 05:04 Michael H.: Yes. 05:08 Sandy K.: And… Wow, I didn&#39;t even… I don&#39;t even think you set up a password yet. 05:17 Michael H.: Okay. 05:17 Sandy K.: Because you&#39;re just you&#39;re installing it for the first time. We&#39;ll see if it takes that otherwise. 05:21 Michael H.: Yes. 05:25 Sandy K.: Yeah, so click forgot password. 05:28 Michael H.: Okay. 05:32 Sandy K.: Yeah, just reset your password. 05:37 Michael H.: And my email? 05:40 Sandy K.: 241 is you. And then, yeah, your Clio email address. 05:45 Michael H.: Yes. Done. Yes, got it. Okay. Yes, I&#39;m in. 06:23 Sandy K.: Okay, perfect. I don&#39;t have you in any of like the call groups yet, but I will probably next week have you in the call groups. 06:33 Michael H.: Yes. 06:33 Sandy K.: And, you know, it&#39;s really simple. Customer calls in obviously You might not be able to help them over the phone but what we usually tell people to do is You know, just get obviously we want their account name minimally If they have their four-digit account number, that&#39;s even more helpful. If they&#39;re calling about a document. Reference or message id So that you can look it up. As much
information from them as possible. And then you can just say, I&#39;ll look into it and get back to them. 07:07 Michael H.: Yes. 07:08 Sandy K.: So we always try to garner as much information not on the phone that way we won&#39;t have to call them back and ask them for more information. We&#39;ll try and get all the information we can right there on the phone. As the calls come in, so there&#39;s call groups by time because you know we have people that start at 7, 8, 9, and 10 So the call groups turn on and off on those timeframes. So like your phone would never ring before 10 a.m? It would never ring after 7 p.m. 07:40 Michael H.: Yes. Okay, so… 07:44 Sandy K.: So it automatically will turn on and off as needed And then that&#39;s everyone&#39;s in the groups. I am too. Um and But we are going to be switching to Zendesk. But this is the phone system we have now. It&#39;s not that great, but it was what they had. You may have a problem Usually you might see me in support saying. 08:12 Sandy K.: Please reload your phones. And it happens to mine, like all of a sudden I&#39;ll be working and I&#39;ll notice that my phone went offline. Why it went offline, I have no idea. Everything else stays online, but the phone app goes offline. 08:24 Michael H.: Yes. 08:26 Sandy K.: It&#39;s a round robin. So if your phone rings and you&#39;re unavailable, it goes to Lauren or Nico or Maria. So it goes through everybody in the team. If no one answers, it comes back around to the first and it goes around and around and around until someone answers the phone or 08:35 Michael H.: Oh. 08:43 Sandy K.: The customer opts to press zero and leave a message. This week, though, I actually have it set to go to voicemail because it&#39;s voicemail we&#39;re just short staffed. So after it goes around a few times, I just have them drop to voicemail immediately. Because our customers are really obnoxious sometimes and they&#39;ll just keep ringing. They don&#39;t care how long they&#39;re ringing for. They&#39;ll just let the phone ring. So I&#39;m making them leave messages this week. Okay, so that&#39;s the phone app. 09:08 Michael H.: Yes.
09:13 Sandy K.: Like I said, let&#39;s see. Under for like my favorites. So if you wanted to be able to put in favorites here you would be able to like search like Lauren, Maria, Nico, myself, and you could have everybody listed there 09:33 Michael H.: Yes. 09:33 Sandy K.: You can actually then see if someone&#39;s on the phone. So if you needed Lauren for something you would actually see that she was on the phone. 09:44 Michael H.: Yes. 09:44 Sandy K.: So in search contacts. If you type in let&#39;s say you typed in Lauren. 09:57 Michael H.: Favorite part right there. 09:59 Sandy K.: Yep. And now you click the star. All right. And then type in maria Maria Kashwala. All right and then nico And myself. Okay. And then just click the X. All right. So there you see everybody listed. And so if someone&#39;s on the phone to the left of their name in that box, there will be a green bar. As the phone is ringing. You&#39;ll see a green bar flashing in front of everyone&#39;s phone as it&#39;s ringing through everyone. So when there&#39;s a green bar in front of someone&#39;s name, that just means they&#39;re on the phone. 10:54 Michael H.: Yes. 10:54 Sandy K.: Everyone is marked as available. Usually if they go to lunch, like Lauren didn&#39;t because she knows that the phone is dropping to voicemail, but they&#39;ll put themselves on do not disturb. 11:04 Sandy K.: So that the phone won&#39;t ring to them while they&#39;re at lunch. They don&#39;t have to sit and listen to the phone ringing. Because, you know, it&#39;s just going to keep circling around And that way, if they&#39;re on do not disturb, it&#39;ll just bounce over them. And up at the top where your name is, is that dropdown. That&#39;s where you can mark yourself available. Do not disturb. You know, whatever you want, however you want to mark yourself Okay, so the phone&#39;s set up because I didn&#39;t think about the phone initially. Actually, I was hoping that we were going to be all done with this and be on Lauren on Zendesk. Okay, we weren&#39;t. 11:44 Sandy K.: All right, so that&#39;s the phone. Like I said. I&#39;ll probably have you on the phones next week. 11:49 Sandy K.: I&#39;ll have a whole schedule for you before the end of day today. 11:55 Michael H.: Yes. 11:56 Sandy K.: Okay. All right. So… that person, Dylan, you said email back.
Correct? 12:06 Michael H.: Yeah, so… The first thing, let me open it up. It&#39;s a sunbelt better spring. So I&#39;m getting it. So he… He confirmed that he is doing it manually. And before he goes back turning it on automatically, he needs to make sure and do a couple of testings like he said in is reply. To make sure the s t SKUs and everything is correct But… Right now, let me sign in. We grabbed a three year search. Okay. 13:08 Michael H.: It&#39;s pretty… 13:09 Sandy K.: I saw that he wanted to know if they could still use the ftp upload without having them transmitted. 13:13 Michael H.: Yes, yes. Yes. 13:17 Sandy K.: And have just fallen to the draft folder. So we can do that. All I got to do is change. Change the channels so that they no longer that they&#39;ll deliver to the draft folder and not deliver out to Lauren Data. So yes. 13:40 Sandy K.: We can have him still use the FTP site and let the files drop into the draft folder. If that&#39;s something he wants to do, we can do that. 13:51 Michael H.: Yes. So I&#39;m writing that down right now. 14:27 Michael H.: So let&#39;s see. 15:32 Michael H.: Yes, so… Let me send you what I was talking to um Resend back to him. 15:52 Sandy K.: So you sent that okay so you sent that to art 15:52 Michael H.: Slack. That&#39;s the thing that I was… Because it was going on two hours. So I just sent that right now. While you&#39;re… helping me to let him know that I&#39;m still working on it. And I was just letting him know that we&#39;re just diving deeper and I will get back to him. Okay, let&#39;s see. 16:18 Sandy K.: So, yeah. So we can do that. So we can, if that&#39;s what he wants then I can just make a change to the event rules on the one channel to not send out to learn data for delivery and to drop the file in the draft folder. That&#39;s what he wants. 16:22 Michael H.: Yes. 16:38 Sandy K.: So if that&#39;s what he wants, then we can definitely do that. And then he can go back to sending his files in through the FTP. 16:38 Michael H.: Okay.
16:47 Sandy K.: And I would need to know if this is for both Because he has two trading partners He&#39;s got Dick&#39;s Sporting Goods. And I don&#39;t know, maybe he&#39;s only using, he&#39;s not partnered with the other ones anymore um Because I did see on his FTP channel coming in it does two things. It sends to Dick&#39;s Sporting goods and ale sporting Goods. Is that AI or AL? Al Sporting Goods? I never heard of that but um or maybe it&#39;s a one Now that&#39;s a one. No, it&#39;s an L. So I would need to know if he wants it done for both of his trading partners. Are just dicks. And then I can… set that up. 17:31 Michael H.: Okay. Let me write that down. So we need to confirm with all his trading partners. 17:45 Sandy K.: Yeah, I just want to know, does he want us to… turn off the auto delivery Does he want to be able to use the FTP to send files for to the draft folder for both. Okay, first let them know that yes He can use the FTP and we can turn off the automatic delivery and just have the document fall in the draft folder. And then ask them, is this for both DICKS and Al Sporting Goods. Ask them if it&#39;s for both Dick&#39;s and Al. 18:12 Michael H.: Yes. 18:21 Sandy K.: Because I don&#39;t know. If it&#39;s for both or just one. I don&#39;t want to have one send out and him get mad and be like, no, I wanted I don&#39;t want anything sent out. 18:34 Michael H.: Owls as ALS. Okay. 18:36 Sandy K.: Just A-L. Als Sporting goods, yeah. 18:41 Michael H.: Yes. Yes, it&#39;s possible to still upload the files to the FTP site without them being transmitted to DSG, we can figure the event rules to ensure that the files drop into the draft folder instead of being sent out to DSG. Also, it&#39;s useful for DSG and Al&#39;s Sporting Goods. The event totals, correct? 19:07 Sandy K.: Right. Yeah. 19:27 Michael H.: This is good. This is Miss Sandy. Okay. 19:31 Sandy K.: Yeah, I&#39;m going to have you set up a signature. So go ahead and this way you&#39;ll just have it set up all the time. 19:35 Michael H.: Oh, okay. 19:39 Sandy K.: Click reply to customer. We&#39;re not going to reply, but you can set up a And go to canned responses. Okay. I&#39;m going to make this a personal one. So
click create new 19:55 Michael H.: Yes, great news. 19:56 Sandy K.: Uh huh. And then it&#39;s okay it&#39;s personal. So then enter in your name. However you want to sign it. And then in the response. I think you have to put your name. Let me look at mine. 20:08 Michael H.: Yes. 20:15 Sandy K.: I think you have to put your name in. 20:42 Sandy K.: Okay, in the response, put in your name how you want it to appear. So yeah, put in your name again there, Michael. How do you say your last name? Playing okay so then just put in Michael Fuang there 20:49 Michael H.: Yes. Wang. 20:57 Sandy K.: On the next line, put in Clio communications And then right below that, put in the support email address, not your personal one, but here put in this email address For now, we&#39;re going to leave the phone number because that&#39;s still the phone number they call in. So then put that right below your your email address. 21:34 Michael H.: Like that? 21:36 Sandy K.: Yep. And then just say use as my signature and click create. Okay, and then you can close this window. Yeah. And then cancel that response, click cancel on that response there. 21:48 Michael H.: Cancel. 21:55 Sandy K.: Okay, so now if you say reply to customer There you go. 21:55 Michael H.: Okay. Okay. Yes. 22:02 Sandy K.: Okay, so you click cancel there. So it&#39;ll automatically. So going forward, it will always fill in your signature then where you don&#39;t have to fill it in. 22:03 Michael H.: Yes. 22:14 Sandy K.: Okay. I don&#39;t know what this guy wants. Okay. I know the NetSuite integrator wasn&#39;t working earlier because of ECSO2 was down, but All right, let me see what he wants. Let me just listen to this guy&#39;s voicemail and see what he wants. 22:36 Michael H.: Yes, no problem. 22:39 Sandy K.: Okay. It&#39;s still downloading his voicemail. 23:19 Sandy K.: Oh, she&#39;s really happy. All right. All he said is now you guys aren&#39;t
even answering the phone. I don&#39;t even think he opened a ticket, though. Um Okay. I actually think it&#39;s Nico&#39;s ticket. 24:17 Sandy K.: All right. Okay, that&#39;s it for giant bicycle then once he If he responds back and wants us to turn that off, then we&#39;ll turn that off. 24:27 Michael H.: Yes, and the other person, they haven&#39;t responded. From Symphony Beauty. 24:35 Sandy K.: Okay, that&#39;s fine. Sometimes they don&#39;t respond same day. Sometimes they respond the next day. 24:36 Michael H.: Okay. 24:40 Sandy K.: You know, if it goes a couple days and they don&#39;t respond, you know, we&#39;ll just ask them again. We&#39;re just following up. You know, to see if they&#39;re still looking for assistance I usually do want people to follow up at least every couple days You know, so that things aren&#39;t just aging without any type of response? But you don&#39;t have to, if you already send an email to them You don&#39;t have to send another one to them. 25:11 Sandy K.: The same day if they, you know, at that point, we&#39;ll just wait for them to respond because they might be looking into it they might be um Yeah. Having an issue, but okay, I can see that. 25:23 Michael H.: Yes. 25:27 Sandy K.: Nothing worked this morning for this guy because he was really But I think all of his orders integrated now, right? 25:38 Michael H.: So that means that it&#39;s a server working now? Okay. 25:41 Sandy K.: Yeah. But I am going to reply to him and let him know that there was we experienced an issue with them. 25:47 Michael H.: Yes. 25:51 Sandy K.: I actually asked our IT director, actually he&#39;s our vice president of it If there&#39;s anything I can tell customers about today or if I need a formal root cause analysis performed before I can tell customers anything. 26:07 Michael H.: Yes. 26:09 Sandy K.: Before we were with Clio, it was a little more informal I could have a response to customers usually either that day or the next day But… now things are a little more formal and it could take weeks to get a root cause analysis? That is customer facing. So I just asked Chris, is there anything I can
give customers or do I have to wait for a formal RCA before I can tell a customer that there was a problem with the server because I know that&#39;s what this guy&#39;s looking for. 26:25 Michael H.: Oh. Yeah. Yes. 26:41 Sandy K.: This guy that&#39;s calling is because So one of our integrators is NetSuite. And although it&#39;s not like the QuickBooks and the ship station It&#39;s all handled through the server. But because that server was down this morning. None of his orders integrated into NetSuite. And he&#39;s really angry because none of his orders integrated. And I know he wants to know why. But… I can&#39;t say the server&#39;s down. Nobody obviously wants to hear that. Like, what do you want me to tell people? And… So I&#39;m not, you know, I was going to email him back off of the ticket and let him know that, you know, I see your orders are integrating now. 27:31 Sandy K.: You know they just started integrating them Well, 19, 18, 1760. Yeah, literally, they just started integrating now. To something. Part of that they weren&#39;t integrating. I&#39;m sure they had everything to do with the server. Okay. I&#39;m not going to worry about him right now. Okay, let&#39;s see what other tickets we have here. 28:15 Michael H.: I&#39;m just going to use the bathroom real quick, Ms. Andy. 28:18 Sandy K.: Sure, no, not a problem. 29:30 Michael H.: Okay, I&#39;m back for a second. 29:34 Sandy K.: Okay. All right, let me see. 29:36 Michael H.: Yes. 29:54 Sandy K.: Okay, so there&#39;s a ticket. You can take, there&#39;s a ticket that we&#39;ll take. All right. You are sharing your screen. Okay, so I&#39;m going to actually have you take this ticket. I kind of looked at it already but If you go to the queue And go to unassigned. 30:22 Sandy K.: Why don&#39;t you have unassigned there? Okay. If you want to… What you can do next to unassigned, if you check that star, that will put it at the top. 30:33 Michael H.: Yes. 30:34 Sandy K.: The same thing with assigned to me. If you check the star next to assign to me. Okay, that will put your those two at the top for you. So they&#39;ll always be the primary. Okay. If you take the most recent ticket up there that says
need added Yeah. 30:41 Michael H.: Okay. Oh, okay, this is the one Lauren was doing earlier, correct? 30:59 Sandy K.: Lauren was doing this. This ticket. 31:00 Michael H.: Oh, no. I mean, there was someone from, there was ticket from Pole House. 31:07 Michael H.: Earlier. Full warehouse after leave. 31:11 Sandy K.: Oh, okay. Okay, so global pool products is asking to have a new field in the acknowledgement type drop down That&#39;s fine. We first have to see if pool warehouse allows that in the dropdown. 31:13 Michael H.: Hello. 31:33 Michael H.: Okay. 31:33 Sandy K.: So… What we have to do is we have to go to web edi add so you want to sign this to yourself Go ahead and say assigned to me. 31:49 Michael H.: Option. 31:49 Sandy K.: Oh yeah, you could put it in progress. Yeah, waiting, change, waiting for support to in progress. 31:55 Michael H.: To all in progress, right? Oh, yes. 31:56 Sandy K.: It doesn&#39;t matter which one it&#39;s yeah And then click assign to me down on the bottom right hand. You see where it&#39;s unassigned? 32:06 Michael H.: Yes. 32:06 Sandy K.: Okay. You can… No, we&#39;re going to go to web edi admin 32:14 Michael H.: Yes. Let me try to say it. And again, I think it disconnected. 32:40 Sandy K.: No, not server. Web EDI admin. 32:43 Michael H.: I&#39;m trying to connect to the open Yes, I think it disconnected me. 32:47 Sandy K.: Oh, the VPN. Okay. 32:55 Michael H.: Okay. 33:10 Sandy K.: And we&#39;re also going to log into this web EDI account. 33:14 Michael H.: Yes. Okay, we should connect really. 33:46 Michael H.: One second, miss. For some more reasons, no. 33:50 Sandy K.: Sure. 33:51 Michael H.: Alone. 34:13 Michael H.: To exit the whole thing. Just restart it.
34:16 Sandy K.: That&#39;s fine. 34:26 Michael H.: Yes. Amen. 34:36 Sandy K.: Okay, so you&#39;re going to go to, we&#39;re going to go to legacy admin 34:41 Michael H.: Yes. 34:45 Sandy K.: And we&#39;re going to go to trading partners. 34:50 Michael H.: Training partners. 34:54 Sandy K.: At the top. 34:55 Michael H.: I guess. 35:02 Sandy K.: And we&#39;re going to go to pool warehouse. You could just type in pool. You don&#39;t have to type in the hole. It&#39;ll pull up everything that starts with pool. We&#39;re going to go down to their documents because we need to see the specs for their A55 to make sure that what he&#39;s asking for is allowed. 35:22 Michael H.: Yes. Yes. Just more? 35:31 Sandy K.: So… Yep. Yes, the 855. Mm-hmm. Okay, and what he&#39;s looking for is… if we go if you go back to his screenshot there. 35:48 Michael H.: Yes. 35:56 Sandy K.: He&#39;s looking to have… Acknowledge no detail or change. Okay, that&#39;s what he wants. No detail or change. All right, so go back to those specifications And it&#39;s going to be under the very first segment. So if we want to scroll down till we get to the BAK segment in that Right there. Okay. So is acknowledgement is acknowledgement no detail or change in option? Yes, it is 36:22 Michael H.: Right here. 36:31 Sandy K.: So AK. So that does allow for, so it allows for a few things we can have AC, A, G. Akrd, and RJ. So those are the things that Pool Warehouse allows. 36:44 Michael H.: Yes. 36:47 Sandy K.: Okay, great. Let&#39;s go to the DOM. So if you want to open another, yep, you can just go to that tab. You can either open another one but now that one, yeah, we got to be in web EDI yet. 37:01 Michael H.: Yes. 37:01 Sandy K.: Okay, so now we&#39;re going to click on Yep. Dom editor. Dom editor. 37:10 Michael H.: Yes. And filter out um It&#39;s a customer&#39;s pull house. 37:19 Sandy K.: Not the customer. Only the trading partner. 37:22 Michael H.: Oh, yes.
37:24 Sandy K.: Because we don&#39;t know if they have one that&#39;s specific to them. It might be global. So we&#39;re just going to first look a pool warehouse And the document type of Correct, 855. Okay, and then we apply. All right, so we only have the global one. All right, that&#39;s good. So open that up. 37:43 Michael H.: Yeah. 37:50 Sandy K.: And let&#39;s go to the BAKO2. Yep, open that up. Click on codes. And let&#39;s see, we have the AC is there the ad So you can check AK because that was one that was allowed And then make sure RD and RJ. Rj is the other one that can be accepted. All right. Click save and close. 38:13 Michael H.: Okay. For that screenshot is to show him. 38:19 Sandy K.: You can screenshot that so that it&#39;s for your notes Not to show the customer, just our notes that we made this change. 38:25 Michael H.: Okay. Yes. Yes. 38:31 Sandy K.: Save and then you&#39;re going to save the DOM. So saving back to view. And then you&#39;ll want to put that screenshot as a comment on the ticket, an internal note. We don&#39;t share that. 38:48 Sandy K.: And we don&#39;t share these DOMS views with the customer. Yeah, we don&#39;t really, because that doesn&#39;t mean anything to them they don&#39;t know what all of these values are and segments are. 38:52 Michael H.: Don&#39;t never share dumb views. Okay. Okay. 39:27 Sandy K.: And so, yeah, then we could just let them know that we Added. You know all allowed values for that field So he, you know, he He will now have the acknowledged know whatever he wanted, no detail or change will be available for him now. 39:50 Michael H.: Okay. Acknowledge no detail. Shall I also say the RJ1? 39:56 Sandy K.: No, you could just let them know that we&#39;ve added all available options to their field and so acknowledge no detail or change is now present. 40:17 Sandy K.: And just say, yeah, include, yeah. 40:22 Michael H.: Chest, they just please refresh and just refresh Let me know. 40:25 Sandy K.: Yes, they will definitely have to refresh, log off or whatever. Yeah, they definitely have to refresh. Otherwise, it won&#39;t see the change. 40:56 Michael H.: This is good. Miss Sandy. 40:59 Sandy K.: Yes. And then as an internal note, add your screenshot for what
we changed. 41:05 Michael H.: Okay. I forgot where to… Oh, there it is. Sorry. 41:24 Sandy K.: And you can just add a comment in there. It says global You know, just like global a50 farm DOM updated to match training partner specs. 41:33 Michael H.: Yes. 42:02 Michael H.: Service. 42:43 Michael H.: So we just updated the DOM to match the training partner specs. 42:49 Sandy K.: Yeah, you can just say updated 855 DOM for BAK segment. To include all trading partners Requirements. 43:20 Michael H.: Yes. And this would be under… requests 43:38 Sandy K.: Or for label. It would just be, you could say DOM update. I think we have DOM update in there. If you just type in DOM. 43:39 Michael H.: Oh, yes, label. That&#39;s what I mean. 43:50 Sandy K.: Yeah. 43:51 Michael H.: Yes. And we wait until he responds, then we close it. 43:59 Sandy K.: You can close it. You can say it&#39;s resolved or you can wait for him to respond. Either way, if he doesn&#39;t respond by tomorrow, then you would just close it. If you want to leave it open till tomorrow You know, everyone&#39;s different. You know, some people since it&#39;s fixed, they just want to close it and move on. 44:14 Sandy K.: And others want to wait to see if they respond. So if they respond, they&#39;re going to reopen it. So. 44:20 Michael H.: Yes. 44:27 Sandy K.: Okay. All right, that one&#39;s done. 45:43 Sandy K.: Okay. A lot of these are phone calls that have to be returned. Let&#39;s see. What is the one there that says check 4025 prices? I don&#39;t know what that is. Let&#39;s just see what it is. Oh, well, isn&#39;t that nice? I have no idea what he&#39;s asking. Check 4025 prices. There&#39;s absolutely nothing in the ticket. Go ahead and assign this one to yourself. And… will… put it and then change the waiting to support to In progress. And you can reply to him and just say, who is this again that tom Tom Gailey is his name. If you click the, see where it says signature, there&#39;s a little greater than sign in front of signature. 46:33 Michael H.: Yes.
46:54 Sandy K.: That expands it so you can actually see it. So if you just say, you know, hello, Tom. 46:56 Michael H.: That&#39;s good. 46:59 Sandy K.: You know, we received your request to support but there&#39;s you know Please provide additional and just say please provide additional information to better serve you. Because he didn&#39;t provide any. So we don&#39;t want to tell them, hey, you didn&#39;t put any information in here. Can you please put some information in here? You know just ask him if he can provide additional information to better serve. It&#39;s kind of crazy, but okay. 47:32 Michael H.: Okay, like that. 47:36 Sandy K.: Yes. Mm-hmm. 47:40 Michael H.: And label will be we don&#39;t know yet. Yeah. 4025. 47:43 Sandy K.: Have no idea. 47:49 Michael H.: That&#39;s doesn&#39;t… just nothing, right? We don&#39;t know. 47:52 Sandy K.: I have no idea what that means even. No idea what that means. 47:55 Michael H.: Okay. 47:59 Sandy K.: Okay. 48:23 Sandy K.: All right. Any questions so far? 48:24 Michael H.: Oh, no, no, no. This is how it helps me to like under understand it more like this practice right here. 48:35 Sandy K.: Right. Okay. So then… So we had the one customer call in who didn&#39;t leave any information. They just said, please call me. So we don&#39;t know what they want to help them. Okay. So we&#39;re going to have to call some of these people back because we have a lot of tickets that were phone calls. How comfortable are you calling a customer and asking them for details? 49:42 Michael H.: I&#39;m not that comfortable yet because I don&#39;t even know how to explain it if they ask me. 49:47 Sandy K.: I don&#39;t even know. We would like a scale. The one is asking for Clio capabilities. That is too vague. I don&#39;t even know who this customer is. Gavin, that one that says Clio Capabilities. I don&#39;t even know what they are looking for. 50:05 Michael H.: Should I just reply to them? And assign it and get the ETO. 50:10 Sandy K.: Okay, so I mean, I don&#39;t, I&#39;m just trying to see Gavin Storber. Like, I&#39;d like to know what company he&#39;s with. Well, here, let&#39;s do this okay let me Let
me see if I can see what companies he&#39;s from. 50:26 Michael H.: Yes. 50:40 Sandy K.: Full boulder play. Does Boulder play a customer of ours? No. All right. I would. Go ahead. We&#39;ll email him. You can email him back. And… you know just ask if he can let us know Because I have no idea if he&#39;s looking for like the web EDI portal. 50:58 Michael H.: Okay, yes. 51:12 Sandy K.: You know, just ask them, can you, you know, please provide what services you&#39;re already utilizing with Clio. Are you currently signed up with the Web EDI portal? Because if he&#39;s not using the YBDI portal. We would have to just give him I need to find out who I can direct him to. If he&#39;s already signed up already signed up What Clio services are you already utilizing? Then I will know If I got to give them to like Drew or macy Or if I&#39;ve got to give him to somebody else. Really need to know what Clio services he&#39;s already utilizing. 52:16 Sandy K.: Okay. I can&#39;t direct him to like customer success or an account manager unless i know Okay. I don&#39;t even know what his company name is. All right. Because I was asking him for his company name, but that&#39;s okay. 52:33 Michael H.: Oh, I could. Should I edit it? 52:35 Sandy K.: No, no, because did you already send it? Yes. No, don&#39;t worry about it. 52:38 Michael H.: Yes. Okay. 52:42 Sandy K.: I could see his email addresses bolder play, but bolder play is not customer. So I don&#39;t even where he&#39;s from. Like, I don&#39;t know what i don&#39;t know what So yeah, in a case like that, we&#39;d want to say, you know, can you provide your, you know, your account number that way we could at least associate them to someone. And yeah, I just wanted to know like what services are you already signed up with to find, you know, just so I know what are you looking for? Like what additional services are you looking for? I mean. 53:07 Michael H.: Yes. 53:18 Sandy K.: Clio&#39;s got tremendous amounts of capabilities but If he&#39;s not even using support yet like or if he&#39;s not even using anything, I wouldn&#39;t know where to start with 53:33 Michael H.: Yes.
53:37 Sandy K.: Okay. All right, Lauren is going to be Back from lunch soon. 53:45 Sandy K.: Because I know I have to call some of these people back. 53:51 Michael H.: I could be here and wait for her. I could message her if you wanted. 53:55 Sandy K.: Yeah, I was going to just tell her that. And I have to do a couple i&#39;ve got to have to put together your outline for next week. 53:55 Michael H.: Yes. Yes. 54:05 Sandy K.: I have… like eight people that I have to onboard for like certificates and things like that still today. Before the end, like before end of day today. Let me just see. 55:09 Sandy K.: So yeah, I just, I asked her when she gets back from lunch Or she can work with you so that I can get some things done that i I told her I&#39;m going to return a couple of those voicemails. 55:15 Michael H.: Yes. 55:19 Sandy K.: And um you know Okay, so she&#39;ll reach out to you as soon as she&#39;s back. 55:31 Michael H.: That&#39;s me, Sandy. Yes, I just think I hear. 55:32 Sandy K.: And I&#39;ll let her know once I&#39;m done then. I&#39;ll have all that for you so I just need like 30 or 45 minutes to get this stuff done. 55:41 Michael H.: Oh, yes, no problem. Yeah. Oh, yes. I&#39;m here. Yes, indeed. 55:44 Sandy K.: Okay. All right. Thanks, Michael. All right. Okay, bye.
View original transcript at Tactiq.</p>
