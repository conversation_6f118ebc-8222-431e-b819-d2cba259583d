<h3 id="lauren-cohn-s-zoom-meeting"><PERSON>&#39;s Zoom Meeting</h3>
<p>Meeting started: May 20, 2025, 3:06:00 PM Meeting duration: 65 minutes Meeting participants: <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="generated-content">Generated Content</h4>
<h3 id="finding-edi-address">Finding EDI Address</h3>
<p><strong>1. Definition of EDI Address</strong>    - An EDI (Electronic Data Interchange) address is a unique identifier used to send and receive electronic documents between trading partners.
<strong>2. Steps to Find EDI Address</strong>    - <strong>Check Internal Documentation:</strong>      - Review any internal manuals or guides that may list EDI addresses for your organization.    - <strong>Contact IT or EDI Support Team:</strong>      - Reach out to your IT department or the team responsible for EDI to obtain the correct address.    - <strong>Access EDI Software:</strong>      - Log into your EDI software or platform where trading partner configurations are stored.      - Navigate to the partner configuration section to find the EDI address associated with specific partners.    - <strong>Review Trading Partner Agreements:</strong>      - Look at any agreements or contracts with trading partners, as they often include EDI address details.    - <strong>Consult EDI Mapping Documents:</strong></p>
<ul>
<li>If available, check EDI mapping documents that outline the communication protocols and addresses for each partner.
<strong>3. Key Considerations</strong>    - Ensure you have the correct EDI address format (e.g., X12, EDIFACT).    - Verify that the EDI address is up-to-date, as changes may occur over time.
<strong>4. Thought-Provoking Questions</strong>    - How do changes in trading partners affect EDI address configurations?    - What are the implications of using incorrect EDI addresses in transactions?
<strong>5. Real-World Application</strong>    - Businesses often need to update their EDI addresses when onboarding new partners or changing service providers.
<strong>6. Further Research Areas</strong>    - Explore the impact of EDI address errors on supply chain efficiency.    - Investigate best practices for maintaining EDI address databases.
<strong>7. Glossary</strong>    - <strong>EDI:</strong> Electronic Data Interchange    - <strong>Trading Partner:</strong> A business entity that exchanges EDI documents with another entity.</li>
</ul>
<h3 id="summary-finding-an-edi-address-involves-checking-internal-documentation-consulting-with-it-or-edi-support-accessing-edi-software-reviewing-trading-partner-agreements-and-examining-edi-mapping-documents-always-ensure-the-address-is-correct-and-up-to-date-to-maintain-efficient-electronic-transactions">Summary Finding an EDI address involves checking internal documentation, consulting with IT or EDI support, accessing EDI software, reviewing trading partner agreements, and examining EDI mapping documents. Always ensure the address is correct and up-to-date to maintain efficient electronic transactions.</h3>
<h4 id="transcript">Transcript</h4>
<p>00:00 Lauren C.: Alrighty, so let&#39;s do… let me get set up first. 00:05 Michael H.: Okay, yes. 00:07 Lauren C.: Okay, so I&#39;m going to… hold on real quick, I was just making sure his stuff Yeah, the phone call I took was for a rejected invoice, um… But I&#39;m very confused on. 00:26 Michael H.: Oh… 00:28 Lauren C.: Well, I exported… I don&#39;t know why everything goes over there. There&#39;s nothing structurally wrong. And I copied the control number because it says it&#39;s rejected. That control number doesn&#39;t exist. And the only thing this customer received on that date I can show you. This view&#39;s better, shows… transaction accepted. 01:01 Michael H.: Oh. 01:02 Lauren C.: I don&#39;t know. Oh, wait, that&#39;s a completely different control number, though. I wonder if it&#39;s because he re-sent it, and now it has a different control. Oh my goodness! Okay Um, his is gonna be fun, so let me just copy this. I didn&#39;t make the ticket yet. I&#39;m terrible. 01:21 Lauren C.: Harry&#39;s great, though. That&#39;s why I was saying, like, this dude is so cool. I love Mr. Harry. Um, locate your tab real quick… So I can keep that there. Create… Um, so yeah, so when you&#39;re creating a ticket. 01:37 Lauren C.: You can see my screen, right? I didn&#39;t share it. Okay. 01:40 Michael H.: Yes. No, no, no, no, I can&#39;t. Um, it was, but then it stopped. 01:46 Lauren C.: What the… hit share. Okay, let me do it again. Sure. Screen 2. 01:55 Michael H.: Okay. 01:56 Lauren C.: Weird. Okay, I should have known this didn&#39;t pop up and stay up here. Um, so, create… Everything at the top is normal. On behalf of will always be the customer, so Santa Fe, Mr. Harry… Rejected link. 02:11 Michael H.: Yes. 02:14 Lauren C.: Objective invoice. These are going to be the easy ones. Um, those are usually a good start. 02:19 Michael H.: Okay. 02:28 Lauren C.: Um, let me grab Control number… So, that is coming from here. So, grabbing… Oh, man, I can&#39;t do both lines. Okay, so I&#39;ll just grab this. 02:41 Michael H.: Okay.
02:47 Lauren C.: And then put a date in here. And this way I have something to go off later. Um, I could not locate that. I may just need to reconnect my servers and… just refresh it, maybe that&#39;s why I couldn&#39;t search for it? No clue. But basically, I just told him I was looking into it, I would get back to him. So, starting a ticket. What the issue is, it&#39;s a rejected invoice, and I&#39;m going to put the invoice number… who I spoke to rejected invoice status. 03:23 Lauren C.: Attempted to locate 997 while on call. Did not locate… My customer know that. I would review and get back. Okay. So, what the issue was, who I spoke to, the document, um, I usually just copy the document details from the actual document at the top. 03:56 Lauren C.: So these guys, it tells you what it is. The exact message ID and the reference number, if there is one. So those make it way easier to go back and retrace your steps. Um… And then I put the control number down here, so I don&#39;t necessarily have to open it if… I just want to try to search that again later. So, creating that ticket… View. 04:25 Lauren C.: Moving to in progress, since he will get a status update here. Santa Fe… And… label… Rejected… invoice. Perfect. Okay, so I am done with that. Let&#39;s go look at yours. For him. Okay. Oh. Why did he address me? Okay. 04:51 Michael H.: Yeah, yeah, yeah, yeah, in this first message. Does that… But Ms. Sandy saw that asked Ms. Sandy, and she was like, don&#39;t worry. 04:55 Lauren C.: Okay. Interesting. Yeah, they do that. We had a few people that, like, refused to talk to anybody but the person they were addressing And Sandy&#39;s like, you can&#39;t do that, because people come and go, so… 05:09 Michael H.: Yeah. 05:18 Lauren C.: Okay, so let&#39;s look at yours. Um… Ooh, I can record that. No, we&#39;re just… Okay, so I&#39;m recording it now. Um, we&#39;re looking at your ticket. 05:46 Michael H.: Yes. 05:47 Lauren C.: With the air, so the ASN… Oh my goodness. Air, so due to the large quantity, please contact support. 05:57 Lauren C.: We need to update that. Yes. And then we also have… Oh, not that screenshot. The screenshot provided by the customer. In this screenshot, you&#39;ll have their username, so their account number. What it is, so the label. The status, and the message ID. This is the most important thing, as this will be what
you use to locate the actual message. 06:23 Lauren C.: So let&#39;s go ahead and close out of here. Um, do you want me to go ahead and just open the portal and everything? 06:33 Michael H.: Oh, okay, yes. 06:36 Lauren C.: Okay, sorry, the recording&#39;s gonna have questions back and forth. Alrighty, so if that happens, just refresh and do it again. 06:47 Michael H.: Yes. 06:50 Lauren C.: Okay, so we have its… A1… Log into theirs. This will take you to their page. Pick little batches up here, the little bell. And we&#39;re locating that message ID here, so… Count, label, failed status error that we&#39;re looking for. Um, it does look like they have another ASN with that same issue. 07:15 Michael H.: Yes. 07:19 Lauren C.: But we&#39;ll get there. So let&#39;s work on just this one. So we&#39;re going to copy that message ID. Put it here, hit this dropdown, select message. And then hit go. This will bring you to the ASN in question. 07:34 Michael H.: Okay. 07:37 Lauren C.: Double-click to open… Alrighty, so we are in the ASN. Currently, there isn&#39;t a reference number because they did not input a shipment ID, or they haven&#39;t hit save on their side. Um, with it being labels. Always, always, before we try to generate or mess with anything. Have them make sure they hit save, or… redirect them and make sure they&#39;re actually filling out anything in blue. A lot of this stuff is required for the labels, so they&#39;ll just have to redo it. Um, so this saves a little bit of trouble. So I always recommend scroll through, look down, anything in blue, make sure it is there. 08:13 Michael H.: Yeah. 08:20 Lauren C.: And then below that, you&#39;ll have the actual items. So here we show that PAC 1 has 1,662. Um, each within it. However, when we scroll down, pack 2 on. These don&#39;t look right. So, we can continue to scroll, or we can hit this edit packing button. Which may take a minute, since there&#39;s quite a bit. 09:03 Lauren C.: Quite a few minutes, we&#39;ll get there. So, the bigger the ASN, the longer this takes. If there were only, like, 5 to 10 packs, it… would have already loaded. Um, but because there&#39;s… well over a thousand. 09:17 Michael H.: Yeah, like, 50 plus.
09:19 Lauren C.: Yeah, um, even, like, 50, that&#39;s not terrible, but this shows that there should be… Yeah, so that might take a moment. 09:24 Michael H.: Oh, yeah, 1600, I mean. 09:31 Lauren C.: But yeah, see, even under the item details. This may be required on the actual labels, so they definitely need to make sure to fill that in. So, anything in blue, all over the document. They have to go through and make sure that&#39;s there, and hit save, so that way we can see the current state Um… 09:49 Michael H.: Yeah, that&#39;s… that&#39;s the crazy thing. They… they should know that. Right? See… 09:53 Lauren C.: They should, um, sometimes they&#39;ll have, you know, new people, or someone that&#39;s filling in for somebody else. Doing the process, so that&#39;s usually part of the problem. Or… The document generated, and something&#39;s wrong with the actual file on the backend, and they may need to recreate it. 10:00 Michael H.: Okay. Oh, okay. 10:13 Lauren C.: Oh my goodness. There we go. Okay, so once you hit open, or edit packing, this will open to your screen. You can see everything available. This is way easier to scroll through. Um, so you&#39;ll be able to go all the way down if you want to look. Um, here we&#39;ll see that literally everything is empty. There&#39;s not a single item within each pack. 10:38 Michael H.: Yes. 10:39 Lauren C.: Except for this. At the top. I&#39;ll zoom in, maybe. I was trying to make it bigger, but it did not want to. You&#39;ll have the PO details, and how many packs are below. So for this one, 66… 600. 1,662 packs are there, but only one As items. So… One, they did meet the threshold for the batch label, which is where they have to come to us, and we help them generate it. 11:07 Lauren C.: And two, their labels are… failing and having issues because It doesn&#39;t know what to do, it&#39;s an internal panic, because it has nothing to pack. There&#39;s nothing to print. Yes. So… Right now, this state, the customer needs to go back through and just make sure everything is correct. Um, if they… Do you, in fact, want to pack everything in pack 1? They can either choose to go through and remove all those other packs. Or they can start fresh on a new, um, 856. If they&#39;re not wanting to go one by one to delete those guys. Um, the other thing they could have done, I guess I could have left edit packing open.
11:50 Lauren C.: Let&#39;s go back. Maybe. They could go ahead, and I believe they can just hit the delete button at the top level, and it could clear everything out. But we really just need them to confirm what they&#39;re expecting. How many boxes they&#39;re actually filling, and how many items of… or the quantity of each item. So for this, I only show that there&#39;s this one item. If we want to verify, this blue hyperlink here is the original purchase order. We can click onto that, and it will take us… ooh, I see there&#39;s two 856s. Um, it&#39;ll take us to the actual order itself, where we can verify Wow. Um, the actual quantity ordered. And what the details will be. 12:42 Lauren C.: So right here, it&#39;s saying that there should be 6. Hmm. Okay, so they definitely need to confirm for us what they&#39;re expecting their packaging to look like. But yes, this just shows that it is just that one item, so they&#39;re good there, it&#39;s just a matter of confirming. How many needs to be in Pack 1? Because right now, it shows 1662 within PAC-1. Um, if they&#39;re expecting to just have one label for one big box, then they would put the full amount that they&#39;re wanting to ship on that ASN. Um, the customer is able to ship the full order. Or… oh my goodness. Or if they want to do multiple ASNs and say they&#39;re only supplying this much for right now. 13:29 Lauren C.: They can go ahead and build the ASN, just put that in that singular pack, or however many packs they&#39;re wanting. And submit the ASN. So they don&#39;t have to fulfill the full $9,972. 13:41 Lauren C.: That is completely up to them, and between them and their trading partner. So, right now, they just need to either fill these or confirm with us so we can help them. Guide them on this? Um… Yeah, I don&#39;t want to delete order. Yeah, and there&#39;s no mass delete button, so yes, um, they can either go through and pack all of these, or they can just create a new ASN. Okay, do you have any questions? Yes. Um… So if all of those were made in error. 14:14 Michael H.: That I got manually delete each one. A lot. 14:21 Lauren C.: I would just make a new ASN. Um, if you look over here on the actual PO, There&#39;s two that were generated, so if you click on one… aha, they&#39;re in something else. I wonder if this is the other one that failed. 157… Nope, that is completely different one. Okay. Um, this is another ASN that they tried to build for the same order. Same issue. They put 1,662 each in one pack And everything
below it is blank. 14:58 Lauren C.: So whoever&#39;s building the ASN, I don&#39;t think they&#39;re necessarily doing it correctly. So, from the PO, I&#39;ll go ahead and generate one, that&#39;s totally okay. 8.56. It sounds like they&#39;re doing pick and pack. So, number of cartons. This is where they&#39;ll tell the system how many cartons they&#39;re needing, how many boxes. So if it&#39;s a one-to-one, if it… that one item, because it&#39;s a 12-pack. Fills up one box, then yes, they would go ahead and put however many they&#39;re needing in total. So if it&#39;s that 1,662 that they&#39;re fulfilling, or whatever. Um, they would put that here. So for this, I&#39;ll go ahead and put 5, just so you can see. So I&#39;m creating an ASN that has 5 cartons on it. Next. 15:49 Lauren C.: And it may take a moment. Once that generates, you&#39;ll see that I have my ASN, I have my 5 packs, and I have My quantity, since they haven&#39;t sent an ASN, The quantity left will stay, um, the full quantity. Once they actually do send the ASN, this will actually have a deduction from it. But since nothing&#39;s been submitted, nothing&#39;s solid in stone. It&#39;s just there. Um, so for this example, I will do 1. 16:25 Michael H.: Okay. 16:26 Lauren C.: Okay, so what I did was I packed item 1, I put a quantity of 1 in this pack. And you can do that line by line, or you can hit this little copy button, copy pack. And you can copy it to the next 4. It does take a moment, but it&#39;ll go ahead and put that over there for you, so you&#39;re not drag and dropping for each one Um, you can do it up to 50 packs. So if they have… 100 packs that they&#39;re filling, you can do. You know, that and two different things. Alrighty. So now that our packing editor shows that we have 5 packs, each one has a quantity of 1, they&#39;re all packed. We can hit finish. 17:15 Lauren C.: With this being a test, on our side, always make sure you put test or test delete, something for us. 17:22 Michael H.: Okay. 17:24 Lauren C.: Alrighty, and then… I don&#39;t fill any of this in, because I don&#39;t want them to actually hit send. But a reminder, if you&#39;re helping them. Tell them about the blue. Especially their vendor number. Now, when you scroll down, you will actually see our packs. Now you&#39;re showing filled lines. Now everything has an item within it. Um, anything in blue, they would just need to go ahead and fill out.
If they&#39;re not wanting to fill this each time, they can set up a document default, you can discuss that with them if they do ask that question. Um, or they can… Between default and the catalog, they&#39;ll have two options. 18:05 Lauren C.: But yes, this is how it should look, so if they&#39;re only needing one in each pack, it should go one-to-one all the way down for as many packs as they&#39;re needing. Um, but once they&#39;re good and ready to go, have them hit save. Depending on how many, um, labels they&#39;ll need, they can hit the print label. And if it generates, perfect. It&#39;ll give them whether they needed to email it to themselves, or There will actually be a button here on the right that does say label. Yes, so view label. 18:37 Michael H.: Let&#39;s see it. 18:38 Lauren C.: And I can click one so you can see what it looks like. So this is their actual label. Oh my goodness. Up here. So, it shows, um… Every spec will be different, but for this one, it shows where it&#39;s from, where it&#39;s going to… The postal code, any details, and any specs that were set up, like whether it&#39;s an item description SKU, UPC, all of that. And then the big barcode at the bottom. This is pretty generic across the board for most trading partners, so it should look mostly like this. Um, as far as, like, the contents here, this may vary, though. So it may only have… a PO number. It may just have a SKU. 19:13 Michael H.: Yes. 19:20 Lauren C.: So, don&#39;t freak out if it&#39;s not all the same, they&#39;re not all, um, one-size-fits-all, it is a little different. But they can always access that label, they don&#39;t have to keep printing it, they can access it up here with the little bell. For their batches. Um, if they have those failures, those will be here. It&#39;ll also confirm if they&#39;re doing the email. Where it&#39;s going. Um, I do believe… that it will go directly to whoever is set up as that user. 19:42 Michael H.: Yes. 19:53 Lauren C.: Um, okay, so let me hit save here. So we can kind of cover all the bases here. Save, we&#39;ll go to Settings. Yeah, I&#39;m pretty sure that&#39;s how it goes. So he would have to update it There… If they&#39;re wanting to do the email to me button, it&#39;ll go directly to whoever is set up on the admin, or if Tommy&#39;s doing it, it&#39;ll go to Tommy&#39;s email. 20:25 Lauren C.: Um, what was the name on there? Okay, that&#39;s a completely
different email. Okay, so it won&#39;t go to that. He could… Add that as another user, or if he&#39;s… I would do that. I would recommend adding as another user. We can cross that bridge in a minute, but… If he adds another user, he can give them… Um, all the capabilities, and then that user can hit email to me, and it&#39;ll email over to that person. Um, unless he&#39;s wanting to go ahead and give permission to that email address. For the admin. So… We&#39;ll really have to cross that bridge with him in a minute, but we definitely need him to confirm his ASN details, his quantity packing, all of that. And then we can get to the email part. So, from what I know. 21:18 Lauren C.: It would either need to replace this one, or add a user. But yep, so right now, Ball is in the customer&#39;s court, we just need him to… Let us know. 21:30 Michael H.: Thank you for making it so clear, like. Completely understand the whole process for this one. Yeah, this is, like, the first ticket that… Um, I completely understand, and that would have been my solution. Yes. 21:36 Lauren C.: Trying… Good, good. I… There&#39;s no structure, um, there&#39;s no method to my madness, it&#39;s just kind of there. So, I hope it makes sense. 21:57 Michael H.: No, it does. 21:57 Lauren C.: Um… Okay, good. Okay, so that&#39;s really it. It&#39;s just… We&#39;re waiting on him now, um, but yeah, let him confirm, and then once he does. You can let me know, we can look back at it. If he hasn&#39;t already answered. Okay, not yet. 22:16 Michael H.: Yes, and then I have one more ticket. Um, I wasn&#39;t sure. This was the one that, um It was ticket… Where is it? 43? 22:32 Michael H.: 81.6. 22:38 Lauren C.: Ooh, yeah, I remember seeing that one. Mm-hmm. Okay, I remembered seeing that one the other day. 22:57 Michael H.: Yes. Oh, he sent a message today at 919. Um, he gave the confirmation about, um. Or he was gonna do, and… I wasn&#39;t sure how to… Do this correctly? 23:23 Lauren C.: Wow. 23:24 Michael H.: This is the ticket for turn off EDI transmission to DSG. Ticket CS43816. 23:50 Lauren C.: Okay, yeah, so that&#39;s the right process. Um, disabling the auto-send for that. Wow… Okay, yes, so he just needs to disable the auto-send. Let me see… oh, who he messaged It was Rafought. Okay, she&#39;s fine. Um… this is… Fill
in… I wonder… Let me grab something real quick, hold on. If it&#39;s the right path, I will show you. Otherwise, I&#39;m not gonna show you, so I don&#39;t confuse you. 24:41 Michael H.: Yes. 24:42 Lauren C.: Okay, this is 7175… 25:10 Lauren C.: Okay, so not that. Cool. Glad I did not show you. That is not what I was looking for at all. That&#39;s okay. Let&#39;s go over here. Um, do you know what server they&#39;re on, by chance? 25:22 Michael H.: I believe, uh, uh, what&#39;s it, O2? 25:26 Lauren C.: Okay, perfect. They&#39;re gonna have to wait. 25:31 Michael H.: Does the OpenVPN connection sometimes just disconnects for you, and you just gotta keep reconnecting? 25:38 Lauren C.: It shouldn&#39;t. Mine doesn&#39;t normally do that. 25:42 Michael H.: Oh, okay, so… Let&#39;s see… 25:57 Lauren C.: Maybe Maria will catch that. Perfect, she did. Awesome. Okay, let me check… 26:18 Lauren C.: Go ahead, go to his account for me real quick. 26:21 Michael H.: Okay. In the servers? 26:25 Lauren C.: Uh, no, go on WebEDI. On the portal. 26:27 Michael H.: Okay. Okay, yes. Let me sign back in. Could find him. Okay. 26:40 Lauren C.: And then, once you sign in, hit his draft folder. I want to say something. In his draft folder. Is there anything for DSG? 27:02 Michael H.: Yes, I&#39;m still opening in this draft folder. Yes. 27:07 Lauren C.: I don&#39;t think they auto-send. It wouldn&#39;t go… to his draw folder. Um… 27:16 Michael H.: Yes, it&#39;s… his last created one was May 13th. 27:23 Lauren C.: Okay, let me look at… That may be a manually created one. Okay, let me go look at something now… 27:34 Michael H.: Ms. Lauren, um, how come you asked me to look at his draft? To see if it was, like, automatically sending, or was it manual? 27:42 Lauren C.: So, if it&#39;s in draft. Now, we&#39;ll go on the actual server. 27:47 Michael H.: Yes. 27:49 Lauren C.: And we&#39;ll see if it&#39;s something that he sent. If it is a file that he sent from the server and it&#39;s in his draft, it does not auto-send. So… who is he?
Oh, nope. Do you know what his channel was? Hold on. It&#39;s okay. Let me check with his account number for 7175… Ew, 7176, okay. Okay, I&#39;m going to locate his details, hold on. 28:32 Michael H.: Yes. 28:39 Lauren C.: Okay, input… 29:03 Lauren C.: Oh my goodness. Cannot make it easy. That&#39;s okay. Giant bicycle, let me look at anything he sent for this month. Let&#39;s see if it gives me a source. Has not sent anything this month. Okay… Or his inbound might actually be on the other one. I know sometimes they, um… bounce between. So, okay, so his O2, for some reason, I don&#39;t see anything processed there, which is fine. I&#39;m gonna open the other one. 29:48 Lauren C.: And… Okay. Here we go. So, he does have stuff on O2, but it looks like 03 is actually where he is. Coming into… let&#39;s grab that and check as step one. Nothing. Cool. Um… March? Okay, there we go. Okay, yeah, so heat is on 03 for his inbound. The D drive is only located on that server, so if it&#39;s T, it can be located on either one. 30:44 Michael H.: Okay. 30:49 Lauren C.: Be specifically to where you&#39;re at. 30:52 Michael H.: Okay. 30:57 Lauren C.: I show on 317, there&#39;s one that says test. Um… I&#39;m trying to think… 31:11 Michael H.: So you said T-Drive is always on 3. Oh, okay. Are these, um… Okay. 31:16 Lauren C.: T is anywhere. So, T is universal. Um, D is specific to the server that you&#39;re on, so they both have a D drive? 31:26 Michael H.: Okay. 31:27 Lauren C.: So it&#39;s kind of like your seat, but the D drive is, um, hard drive specific, so… In this case, you know, this server has its own D drive and ECSO2 has its own D drive. But they share tea. 32:01 Lauren C.: Um, I was hoping it was just a partner config. I don&#39;t see anything for that. Let me do something else. Biggs, config name last. 32:37 Lauren C.: I don&#39;t show this customer under AutoSun. 32:44 Michael H.: Yeah, his own manual, I believe.
32:47 Lauren C.: Okay, then… unless he hits send in the portal, it&#39;s not gonna go. 32:53 Michael H.: Yeah. 32:54 Lauren C.: Nope, that&#39;s my customer. Yours is this one, 7175. So what I did is I went to Partner Configs. And admin, and I hit config name list. And this will show you every configuration. Um, we don&#39;t need all that. You can search, I did auto. 33:21 Michael H.: Okay, such are all… 33:21 Lauren C.: And there&#39;s an… actual auto-send feature. You can hit Show Usage. And under search, put your customer&#39;s number. He&#39;s not in here. These are the only two with 7-1. In their number that shows auto-send as configured. So his shouldn&#39;t be made, uh, automatically going out unless he&#39;s manually hitting send on it. That&#39;s what I thought, but I wasn&#39;t sure. So when I hit partner config, I can pull him right here, 7175… this guy, so WebID… Hit edit. Within that, you can see… anything, if it&#39;s enabled. Um, if it has a 1 next to it, that means it&#39;s on. If there&#39;s nothing next to it, it&#39;s most likely not enabled. So, for him, I did auto. I don&#39;t have anything. Nothing is set up automatically for him. 34:25 Michael H.: Yes. 34:34 Lauren C.: I mean, I can… 34:34 Michael H.: Yes, I… I think he just… he wanted to disable everything, correct? 34:40 Lauren C.: Yeah… I just don&#39;t know if we need to add in the auto-send and set it to zero. If that&#39;ll… Um, anything that says zero is turned off. 34:52 Michael H.: Yes. 34:52 Lauren C.: Let&#39;s do that. Here. Add config… Of course. Okay, let me go back and pull it. I was hoping I could just search it. It does not work that way. 35:05 Michael H.: But yeah, I was like… I thought you remembered it and everything. 35:09 Lauren C.: No. I… I&#39;m not that magical, unfortunately. Okay, so here is the list, we&#39;ll do auto. Um. I wonder what those do. But I think it&#39;s just literally auto-sun. Copy, who does this? Let me look up… Oh goodness, I can&#39;t think of her company name. I don&#39;t know, most of these people. I&#39;m terrible. I know customer names. Okay, hold on. Oh, yes, I do remember hers, actually. Um… Oh, lovely. That&#39;s why hers doesn&#39;t work. I&#39;m scrolling through hers. Hers? Should be set up
for that. Okay, that&#39;s check anything that says send. Uh, send notification inbox. That is literally notifications in the inbox, they get emailed for it. That&#39;s what that one means. Um… So, auto-send is actually disabled for this customer. And it should not be. But… I&#39;m not changing that. 36:51 Lauren C.: So, let&#39;s do… auto-send… Back here… We&#39;ll go ahead and… Add it, leave it as 0, so we&#39;re adding it. 37:04 Michael H.: Yes. 37:08 Lauren C.: To not work. Um, so email him back, let him know that you disabled, um… I think that does it for everybody, though. That&#39;s what makes me nervous. Are those the only two partners he&#39;s using that for? Uh, using the portal for, do you know? Because I&#39;d hate to turn that on if he&#39;s using it. The portal for anyone else. 37:38 Michael H.: Yes, I… That was the only… Miss Sandy, if I wrote it down in my notes right here, that… That was his two partners. 37:48 Lauren C.: Okay, so just those two. Oh, perfect. Um, so if he had, like, 5 and he&#39;s only disabling for 2, then that would be a little more tricky. Okay, so AI sports, perfect. Um, so… We can try that. See if that helps anything. Um… So, screenshot… let me go to this one… Got it, config… to disable auto send. His concern is auto-sending to the partner, right? 38:33 Michael H.: Yes, um, because he was getting a lot of charges. And, um… like, oh, yeah, right there, it says a chargeback errors, so that&#39;s why he wants to do everything manually. Until we could confirm it, and today he confirmed everything, and just… wanted us to create the event rules. 38:45 Lauren C.: Oh, Carmel. So that should disable his account altogether will not send anywhere. Um, so just let him know that we&#39;ve… configure the account to no longer auto-send to either. 38:51 Michael H.: And, um, he will let us know when he wants to… I tried to restart things, I believe. Yeah, farmers, um, people. Yes. 39:17 Lauren C.: Um, and just tag us in once he&#39;s ready or needs to make any further changes, but as of now, you do show that as disabled. 39:27 Michael H.: Yes. And then, um… 39:38 Lauren C.: And I put that note there so they&#39;ll see that I did that. 39:38 Michael H.: Yes.
39:40 Lauren C.: That way, they&#39;re not guessing who did it, it was me. If it&#39;s wrong, it&#39;s on me. Um, what I would say… Oop, where are you? Let me not send that to the whole group. 39:46 Michael H.: Oh, okay, yes. 40:21 Lauren C.: Esg, is that how he labeled it? Esg and… ASG. Um, we use, let us know… 40:52 Lauren C.: You can send him that. 41:10 Michael H.: And for the second part of the event rules, um, Ms. Lauren, what should I say about that? 41:19 Lauren C.: Um, so, since they&#39;re not going out, they will create a draft. So that should cover both. 41:25 Michael H.: Okay, so I should… In case he thinks… should I just mention that? Where the event woke worlds. Since they&#39;re not… being sent out. 41:38 Lauren C.: Here, hello for… These files… To show… 41:39 Michael H.: Okay. 41:52 Lauren C.: There you go. Okay. That way, it just touches that. You&#39;re good, you&#39;re good. 41:53 Michael H.: Okay, thank you so much. 41:59 Lauren C.: Okay, so copy that, shoot it over his way, and then if he encounters any issues, we did put, you know, let us know if you experience any hiccups. And then that way he knows, like, okay, this is… a maybe. So… Um, there&#39;s no way for me to check until he does try. 42:25 Michael H.: Yes. And for a ticket like this, that&#39;s pretty much almost done, um… would I put it still in progress until I hear from him, or would this be considered resolved? 42:42 Lauren C.: Um, I usually leave it open, um, to see if they do respond, but if he doesn&#39;t respond by the end of, you know, today or tomorrow morning, you can close it. 42:51 Michael H.: Okay. 42:51 Lauren C.: But… No, go away, I don&#39;t feel like answering. Mainly because I go in 10 minutes. But if she doesn&#39;t want it, I&#39;ll take it. 43:00 Michael H.: Yes. 43:06 Lauren C.: Um… Okay, so that should be that for that one. Do you have any
others? 43:17 Michael H.: I&#39;m… I&#39;m… no. Those were the three that I was gonna work on, uh, after lunch, which you pretty much helped me with all of them. And, uh, one with Nico. When he comes back. Yeah. 43:27 Lauren C.: Oh, yeah. Um, that one is definitely a fun one. Honestly, I&#39;ve done the same thing. I used to take Glenn&#39;s tickets a lot. Plus, when I started, I was the 7-4 shift. Um, and that&#39;s when Glenn calls, at 7 in the morning. 43:40 Michael H.: Yes. 43:43 Lauren C.: So… Yeah, he kept calling, and I kept getting him, so… it was fun, but I kind of learned to… you know, if he does call, I can help him, but… For the most part, if I see his tickets, I already know that that&#39;s going to somebody else. 44:02 Michael H.: Yes. So I&#39;m looking at the unassigned. So, um, something like, um. Invoice adding extra lines, that… That seems pretty something that I should be able to do, correct? And, like, um… a bras, 8, 10 missing lines. 44:26 Lauren C.: Mm-hmm. 44:26 Michael H.: Those two, and ASN. 44:31 Lauren C.: Ooh, Maria is talking to your guy. I just said, hey, Michael has his ticket. Definitely have him shadow. 44:39 Michael H.: Oh, oh, oh! 44:40 Lauren C.: Yeah, she just said, she&#39;s like, I&#39;m on the… I just finished a call with Milkut. I&#39;ve said, uh, he has his ticket. Okay. Have the glint… Sorry, I had an SFTP call, I&#39;m free… okay, so Nico&#39;s back, and Maria has… she said you can give the ticket to her, actually. Um… 45:11 Michael H.: Yes, sir. Um, just open… assign it to her, then. John Deere, um… 45:17 Lauren C.: Yes. So, open your ticket, click… Here, I can show you on mine. 45:29 Lauren C.: So, assignee, just change that to somebody else. So you can click Maria. 45:34 Michael H.: Okay, I just cooked… I put unassigned. Because I don&#39;t have Maria, I only have you, and, um, Miss Sandy. 45:43 Lauren C.: Oh, I think it&#39;s because you haven&#39;t searched it yet. I think if you search for her, she should come up. 45:43 Michael H.: As a people accident. Oh, okay. Oh, okay, I see it, yes. Thank
you. 45:48 Lauren C.: Should. All right, um… So I guess she&#39;s just gonna take over that one, which is great, that works out for you. Because that, like I said, that one is just a lot. Anything with global shop is just… It&#39;s a lot. There&#39;s a lot going on in their files. 46:12 Michael H.: Yes. Yeah, that was… Yeah, so for us, just Web EDI and everything. 46:13 Lauren C.: Um, for mine, I have no idea. Yes. Anything that says, like, oh, I can&#39;t log in, um… This is showing rejected in the portal, this or that. If they&#39;re saying anything that says portal. 46:23 Michael H.: That&#39;s… 46:33 Lauren C.: The website, any of that is usually a… key indicator that it&#39;s going to the website, our website. Um, that should be a little bit easier to navigate. Um, I know secondary users after a project finishes. They may not have the permissions or access to a trading partner, so they&#39;ll say, like, hey. My inbox says I have messages, but when I go to my inbox, it&#39;s not there. That&#39;s usually a permissions issue, so that one&#39;s also really easy. Um, all you have to do is log in as the admin and double check to make sure they have visibility. 47:09 Michael H.: Ms. Lauren, um, are you in the unassigned tab? Because I just wanted to ask you. 47:15 Michael H.: Um, what these would, um… like, ASN and Baross 810 missing lines, and… invoice adding extra lines, are those considered… like, the 8… the 8, uh… pretty much what we were supposed to do, correct? 47:35 Lauren C.: This one. So, Mr. Andrew, um, they&#39;re integrated. So, theirs is… and they&#39;ll usually put pictures… Ours is pretty lengthy. I would leave that one. Banner is a very hefty one. Usually, Maria and Sandy handled him. 47:39 Michael H.: Oh, okay, so… Okay. 47:52 Lauren C.: Um… Let&#39;s see… Invoices, adding extra lines… Oh, um, seems like the customization was reversed, and now… If you want, you can take that one, and we can look at that one together. Um, that one is going to be an integrator and QuickBooks Um, if you want to grab this one, let Kendall know that you&#39;re reviewing and you&#39;re looking into it. We can go over that one in a little bit. Um, that one… That one would fall underneath us, technically, because… It&#39;s just
QuickBooks. 48:34 Michael H.: I&#39;m just gonna use the button. Okay, yes. 48:46 Lauren C.: I think I know a way to do that. Possibly, but yeah, go ahead, grab that one, we can work on that one together. Um… This guy… Maybe, for now, don&#39;t grab CPRO. Cpro is not a fun one. He will blow your phone up. Well, your phone&#39;s not on yet. But still, um, if you see Devin… Go ahead and leave him there. He can talk to Nico. That&#39;s terrible, but… He&#39;s not very nice. Um, okay, so the one at the very bottom is a failed 856. 49:40 Lauren C.: Hmm… So… ooh, okay. Um… without… of signing this anywhere. Who is this? Hover? Nope, it does not show me her email. Okay, um… I have a few minutes, I&#39;ll do it. Let&#39;s see. So, I usually try to shovel through these a little bit. Um, especially for you, I would go through and open them up in Admin. Or I guess… hold on, the website now has a… So we don&#39;t have to do all of that. 50:25 Michael H.: Or no helps again. 50:26 Lauren C.: Yeah. Um, okay, so… Medicare? Okay, so log in… We&#39;ll see if we can locate an account. Um, if it&#39;s just a little rejection… You may be able to… yep. Here we go. So, Medicare Pharma… 50:46 Michael H.: Yes, medical formula? 50:49 Lauren C.: There&#39;s no rejection in here, so they must have reached out to her directly. Um, okay, so this ASN, let me copy… oop. She even gave us the original ticket. 51:00 Michael H.: Oh, yes. Okay. 51:01 Lauren C.: So let&#39;s go look there. Oh boy, I have a notification. Okay, cool. Um… Who&#39;s Nicole? Oh, I know. Okay, that&#39;s someone that&#39;s helping us. Oh, no. Um, okay, so somebody on Cleo&#39;s side. Oh, no, Marie… or Sandy was helping. 51:39 Lauren C.: Okay, hello, since the original email is not included in this thread, we&#39;re going to need some additional information. Please provide the… Okay, here&#39;s the ASN. Lindsay, please provide. 52:00 Lauren C.: Oh, this is a work in progress. I don&#39;t know why she opened a second ticket. Mm-hmm. 52:06 Michael H.: So she just created… Yeah. 52:29 Lauren C.: That is not her vendor number either, that is incorrect. Um, okay, so that goes with… Nope. I&#39;m giving that to Sandy.
52:39 Lauren C.: So, since somebody&#39;s already working on that, that is not us. 52:44 Michael H.: Okay. 52:57 Lauren C.: Okay, save, assign, not to me. Far away from me. Go to Sandy, and always mark it as in progress. 53:10 Lauren C.: Back here, let&#39;s see if there&#39;s anything else. Edi maps? No. 53:13 Michael H.: Okay. 53:14 Lauren C.: Not Tier 1. Ip changes, also not us. Certificate change, not us. We can look at SkiPro. Our invoices are still calculating GST off of pre-discounted totals. Where should it be based off of? Most discounted totals. Yep, nope, mm-mm. We&#39;re gonna leave him there. Devin is just… Very needy, and the moment you start helping him, he will be up your butt. So, full disclosure. So, sincere apologies if you do end up getting him. Um, yeah, you can grab… so that one, if you didn&#39;t already, grab the 44058. 54:07 Lauren C.: We can look at that. Um… Oh, I think your customer earlier… 54:08 Michael H.: Yes. 54:23 Lauren C.: Responded. Maybe… 54:25 Michael H.: Oh, really? I… Is this a nerd ticket? 54:32 Lauren C.: Um, this is your packing guy. 54:35 Michael H.: Yeah. Uh, that works with DSG and… 54:43 Lauren C.: Um, this one&#39;s the Five Below, and he didn&#39;t fill all of his packs. Please confirm… 54:51 Michael H.: Sure. Right? 54:54 Lauren C.: We&#39;ve been pushing him for a while, so… Please note… Okay, um… 55:19 Lauren C.: Who is… what? Hmm. Oh, they need to reissue the PO. I was on the 15th, okay. I don&#39;t understand who that is. I don&#39;t… I don&#39;t get why people have signatures and they don&#39;t put their company name. If you&#39;re responding from a Gmail, who are you? 55:57 Michael H.: Yeah, why is the… that&#39;s… that&#39;s… does CE work? Of another company, or…? 56:03 Lauren C.: I have no idea, I don&#39;t know who that is. Um, hi, Raina. Okay, so I&#39;m assuming Raina is with Five Below. 56:12 Michael H.: Okay. Okay.
56:12 Lauren C.: Five Below doesn&#39;t have a domain. Interesting. Okay, so for this one, this goes with your slime guy. Um, allegedly, they were waiting on… POs to be issued with a split that actually showed where the DCs were. That was 2 hours ago. I don&#39;t know. 56:41 Michael H.: But was there… didn&#39;t make a ticket for that, or…? 56:46 Lauren C.: Mm-hmm. 56:47 Michael H.: Um, okay. 56:52 Lauren C.: So I don&#39;t know if you want to grab that and work that with him, too, since it&#39;s the same person? 56:57 Michael H.: Okay. 56:58 Lauren C.: I don&#39;t know… That is your guy, right? A1 Toys? 57:02 Michael H.: Yes, yes, that&#39;s Chris. 57:07 Lauren C.: Okay, weird. 57:10 Michael H.: I guess I would respond to that and be like, I&#39;m taking over this ticket as well. I&#39;ll be helping to… 57:16 Lauren C.: Um, you can respond on there and just let them know just… let… just say, hi Chris, let me know how I can help. Or if you need anything from our side. That&#39;s what I would say on that one. 57:30 Michael H.: Okay. 57:31 Lauren C.: Because it may not need us, they may be just having us there for visibility. 57:36 Michael H.: Oh, okay. 57:38 Lauren C.: That&#39;s what I would do. And if they do, then we&#39;ll just ask for more details. But yeah, that was your guy with the… labels. Um, okay, Ms. Candy Rogers, I know she&#39;s integrated. That was yesterday. Okay, so this one is one in the portal that was sent. 58:17 Lauren C.: I do believe that was in the middle of the restart yesterday? Oh no. That&#39;s after work. Oh, that would have been at 4. Um… This one, the way I would work it, I would respond… Say, hi, Ms. Candy. Um, we&#39;re looking into this, I will get back to you. 58:38 Lauren C.: And then we would go through on the server, see if it did send, see if there&#39;s a 997. 58:43 Lauren C.: If not, we can respond back and give her options. So option one
is we can go ahead and resend it if her partner hasn&#39;t confirmed with her, if it&#39;s been received. 58:51 Lauren C.: Or option two, we can reach out to the trading partner. 58:56 Michael H.: Okay. Yes, I do this one. 58:56 Lauren C.: Um, this one&#39;s pretty straightforward. Did you want to try that one? Perfect! So grab it, I can put a note on here. 59:05 Michael H.: Yes. 59:08 Lauren C.: And I&#39;m trying to put it in quotes, um… Hi, Candy. So that&#39;s what I would say to her. Next. Album… 01:00:20 Lauren C.: Sorry. I&#39;m, like, super quiet when I&#39;m diving. Option 1… 01:01:38 Lauren C.: Okay, I&#39;m gonna actually label that as option 1. Option 1, we can reach out. 01:01:45 Lauren C.: Ctrl-copy… 01:02:25 Lauren C.: Okay. So, notes there. Um, anything in quotations goes to the customer, anything outside of that? That&#39;s just a note for you. Let&#39;s see… Okay, so you can do this, the reach out… I&#39;ll do instructions in bold, so that way it makes a little more sense. 01:02:47 Michael H.: Okay. 01:02:49 Lauren C.: And it&#39;ll stand out. So if you accidentally copy it, it&#39;s okay. You know, to take it off. Okay. Um, so first is gonna be just reaching out, letting them know that you&#39;re looking into it, then we can go on the servers, check to see if it did send, and then we can search for any inbound 997s yesterday that she would have received. Um, from there, if it wasn&#39;t, you can send this back. Um, I put, unfortunately, I&#39;m not showing a positive 997 was returned. We have two options for this. Option one, we can reach out to the partner on your behalf to confirm if it was received or not, and retransmit if not. 01:03:24 Lauren C.: Um, option two, we can go ahead and always put, with your permission, with resending the delivery… I&#39;ll just put the document. To trigger the 997 if the partner has already confirmed with you that the document was not received. Please let us know how you&#39;d like to proceed. So, that&#39;s what I would do for that. Make sure that&#39;s internal, yes. 01:03:40 Michael H.: Yes. 01:03:46 Lauren C.: Alrighty. So that should go there, so once you assign it to
yourself, if you haven&#39;t already… You might have. It may not have updated. Um, did you pull it already? 01:03:57 Michael H.: Uh, yes, I should&#39;ve. Oh, no, I didn&#39;t… I assigned it to me. 01:04:00 Lauren C.: Okay. 01:04:02 Michael H.: Try to refresh. 01:04:05 Lauren C.: Okay. Yeah, sometimes mine is slow, so that&#39;s why I&#39;m like, I don&#39;t know. Okay, unassign, I do show that the numbers are going down. Yes, okay, perfect. 01:04:09 Michael H.: Thank you so much. 01:04:13 Lauren C.: So yeah, there&#39;s your note. Um, you can respond to her if you want right now, and just let her know you&#39;re looking into it. She&#39;s not super pushy, so even if we don&#39;t get a response back to her tonight, she&#39;ll be okay. Ms. Candy is also really nice. 01:04:26 Lauren C.: Um, I try not to throw anybody that&#39;s gonna, you know. Be on your… on your neck over anything your way. Um… Of course. Alrighty, so it is 3, so I am about to head to my break. Um… 01:04:46 Michael H.: It&#39;s… I&#39;ve got… I&#39;m going to be working on these tickets, and then probably asking Maria and… Nico, when I, uh, think, uh, if I get stuck and everything. So they could, uh, then go over everything with me. And everything. Yes. 01:05:00 Lauren C.: Perfect. Yep. Go for it, um, play with it, see if you can locate, even for Ms. Candy, see if you can find the stuff on the server, just so you get practice. Um, but if you have any questions, let me know, let us know, we&#39;re here. 01:05:10 Michael H.: Yes. Thank you so much, thank you. 01:05:16 Lauren C.: Of course! Alrighty, well, you have a good day, you too. 01:05:18 Michael H.: Yes, have a nice lunch. Yes. Yes. 01:05:20 Lauren C.: Or if you already had yours, you do have a great rest of your day. 01:05:24 Michael H.: You too, thank you for. 01:05:24 Lauren C.: All right
View original transcript at Tactiq.</p>
