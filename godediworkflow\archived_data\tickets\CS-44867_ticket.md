# CS-44867: AW: CS-44674 Welser Profile North America ASN to trading partner Magna Seating Columbus [#20250411-0088]

## Ticket Information
- **Key**: CS-44867
- **Status**: In Progress
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON><PERSON>@welser.com
- **Created**: 2025-06-03T02:26:11.570-0600
- **Updated**: 2025-06-10T10:17:55.929-0600
- **Customer**: <PERSON><PERSON><PERSON>pitale<PERSON>@welser.com

## Description
Dear Nicole 

  

Thank you for investigating {color:#333333}CTRL# 3318 sent to Magna Seating -Columbus on 5/21. Our intern delivery# is 68005813, which equals your Reference#{color}{color:#333333}{color} 

  

!image005.png|thumbnail!{color:#333333}{color}{color:#333333}{color} 

  

{color:#333333}This ASN based on the Globalshop file AE856SRF.20250521141002_44667546_*68005813* which was uploaded to sftp://<EMAIL>{color}{color:#333333}{color} 

  

{color:#333333}Logfile winscp:{color} 

{color:#333333}. 2025-05-21 14:15:10.128 Transfer done: 'AE856SRF.20250521141002_44667546_68005813' => '/home/<USER>/todts/AE856SRF.20250521141002_44667546_68005813' [16962]{color}{color:#333333}{color} 

  

{color:#333333}But there were even more successful uploads to sftp after 5/21: All timestamps are in CET – 6 hours shift to USA{color}{color:#333333}{color} 

  

!image003.png|thumbnail!{color:#333333}{color}{color:#333333}{color} 

  

{color:#333333}The problem must be located at the datatrans process which converts the files from folder todts and create new CTRL# as draft in WebEDI. Forwarding to partner is triggered manually by completing the ASN data eg. SCAC or trailer as Jim mentioned.{color}{color:#333333}{color} 

  

{color:#333333}This process runs stable since 2 years and worked great till 5/28,{color}{color:#333333}{color} 

  

{color:#333333}Please find out, why last file with reference 68005856 was not processed. The file was uploaded yesterday at 12:55 CET but never appeared in draft folder.{color}{color:#333333}{color} 

  

{color:#333333}Both mentioned files are attached to this email.{color}{color:#333333}{color}{color:#333333}{color} 
| Beste Grüße | 
| | 
|  *Reinhard Spitaler* | 
| IT Entwicklung | 
| | 
| +43 7487 410 - 4437 <tel:+43%207487%20410%20-%204437> | 
| 
!image006.png|thumbnail! |
 

  

  
  

 *Von:* Mason, Jim <<EMAIL>> 
  *Gesendet:* Dienstag, 3. Juni 2025 03:19
  *An:* <EMAIL>
  *Betreff:* RE: CS-44674 Welser Profile North America ASN to trading partner Magna Seating Columbus (never populated in Drafts folder of WebEDI)   

  

Nicole, 

  

Sorry, for the misinformation. The “test” file was a sample 856 ASN to Magna Seating Columbus. I find today’s file in our Smart Folders. I am attaching the raw data. Unfortunately, this is not the actual ASN we are attempting to send to MSC. 

  

I am out of the office this week. I may ask another of our technicians to respond to this to email and include the CS-44674 ticket number. I will ask them to include a copy of the actual ASN document in the Global Shop format we are attempting to upload to Datatrans via SFTP. With that data, you may be able to tell why our file is not either being sent to MSC or show in our WebEDI Drafts Folder, so we can add any missing information (like a SCAC code or trailer number). 

  

Jim 

  

  

!image001.png|thumbnail! 

  
  

 *From:* Nicole Gardner <[<EMAIL>|mailto:<EMAIL>]> 
  *Sent:* Monday, June 2, 2025 12:38 PM
  *To:* Mason, Jim <[<EMAIL>|mailto:<EMAIL>]>
  *Subject:* CS-44674 Welser Profile North America ASN to trading partner Magna Seating Columbus (never populated in Drafts folder of WebEDI)   

   

{color:#999999}—-—-—-—{color}    

{color:#999999}Reply above this line.{color}   
  

{color:#333333}Nicole Gardner commented:{color} 

{color:#333333}Hello,{color} 

{color:#333333}What is the Ctrl number, Doctype, sender and receiver for these test files?{color} 

{color:#333333}Thank you,{color} 

{color:#333333}Nicole Gardner{color}{color:#333333}{color} 

[{color:#333333} +<EMAIL>+{color}|mailto:<EMAIL>]{color:#333333}{color}{color:#333333}{color}   

[{color:#333333}{color}{color:#3572B0}View request{color}{color:#333333}{color}|https://help.desk.datatrans-inc.com/servicedesk/customer/portal/1/CS-44674?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0Z3QiOiJhbm9ueW1vdXMtbGluayIsInFzaCI6IjM3OGEzMjU4MzYzN2JlYzcxOTEwNmI1Y2NjMGFmZWZkMzRjMTE0MzMzZTdiY2MzOGY1OTFmNDZmY2MyOTIwZmMiLCJpc3MiOiJzZXJ2aWNlZGVzay1qd3QtdG9rZW4taXNzdWVyIiwiY29udGV4dCI6eyJ1c2VyIjoiMTM2MDgiLCJpc3N1ZSI6IkNTLTQ0Njc0In0sImV4cCI6MTc1MTMwMTQ4NiwiaWF0IjoxNzQ4ODgyMjg2fQ.J-oQrxJDmjMz672mBPfgusuwzwomO6byyL-ObMLOfBk&sda_source=notification-email]{color:#333333} ·{color} [{color:#333333} {color}{color:#3572B0}Turn off this request's notifications{color}{color:#333333}{color}|https://help.desk.datatrans-inc.com/servicedesk/customer/portal/1/CS-44674/unsubscribe?jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0Z3QiOiJhbm9ueW1vdXMtbGluayIsInFzaCI6ImUxMmViYjNhNDU1Nzk3YzQ0YWE5NzA0OTAxZGQ2MmQzZTQyZGI5OTNhNDRkYzk1ZGZjOTQ0MTAzNGU5NTYyNWQiLCJpc3MiOiJzZXJ2aWNlZGVzay1qd3QtdG9rZW4taXNzdWVyIiwiY29udGV4dCI6eyJ1c2VyIjoicW06ZGI2YWEzMGUtM2E3MS00YjNiLTlkZTMtYTY1MTI3Yzk5ZjQxOjk5MDY3ZDdmLWM4MWUtNDI2OC05ODkxLTU4NTU4MjdmM2Y4ZCIsImlzc3VlIjoiQ1MtNDQ2NzQifSwiZXhwIjoxNzUxMzAxNDg2LCJpYXQiOjE3NDg4ODIyODZ9.1WbMpGlDLLPfvA2RVeiG7jGnh728SFq9HyqcEx5AGsQ]{color:#333333}{color}  

{color:#333333}Sent on June 2, 2025 10:38:06 AM MDT{color}{color:#333333}{color}   

!image002.gif!{color:#333333}{color}{color:#333333}{color}

## Components


## Labels

