<h3 id="meet-with-michael">Meet with <PERSON></h3>
<p>Meeting started: May 28, 2025, 4:10:23 PM Meeting duration: 29 minutes Meeting participants: <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="generated-content">Generated Content</h4>
<p>Subject: Assistance Required: Issue with DOT Foods Pricing on WebEDI
Dear Erica,
Good morning, and thank you for reaching out.
We have reviewed the current situation regarding the DOT Foods pricing on WebEDI. At this time, the orders we checked do display the price per case correctly, and the invoices created match the expected total amounts.
To assist you effectively, could you please provide us with specific examples of orders or invoices where this discrepancy occurs? Including purchase order numbers and screenshots showing the issue would be very helpful. This information will allow us to investigate the problem more thoroughly and identify the cause of the $1 to $4 variance you&#39;ve experienced.
We appreciate your cooperation and look forward to resolving this matter promptly.
Best regards,  [Your Name]  [Your Position]
[Your Contact Information]</p>
<h4 id="generated-content-2">Generated Content</h4>
<h3 id="summary-of-the-two-tickets-discussed-in-the-meeting">Summary of the Two Tickets Discussed in the Meeting</h3>
<h4 id="1-ticket-44388-invoice-rejection-due-to-total-amount-error">1. <strong>Ticket #44388: Invoice Rejection Due to Total Amount Error</strong></h4>
<ul>
<li><strong>Issue:</strong> The invoice was rejected because the <strong>Total Dollar Amount (TDS)</strong> did not match the expected value.     *   <strong>Cause:</strong> The raw data in the invoice was missing a decimal in the charge amount (e.g., 29230 was used instead of 292.30).     *   <strong>Details:</strong>         *   The 810 invoice document TDS total amount was different from what was expected.             *   The error was related to formatting, specifically the <strong>decimal place in the charge value</strong>.             *   The system required decimal placement for charges, but the raw data contained whole numbers without decimals.         *   <strong>Resolution:</strong>         *   The decimal formatting in the database or smart folders had to be corrected.             *   The user had to create a <strong>new invoice</strong> with the corrected decimal format because changes to existing invoices were not recognized.</li>
</ul>
<h4 id="2-ticket-44307-pricing-display-and-invoice-total-mismatch-for-dot-foods-orders">2. <strong>Ticket #44307: Pricing Display and Invoice Total Mismatch for DOT Foods Orders</strong></h4>
<ul>
<li><strong>Issue:</strong>         *   Orders were not displaying the <strong>price per case</strong> correctly.             *   The invoice <strong>total amount appeared less than the actual invoice</strong>, with discrepancies typically between $1 to $4.         *   <strong>Investigation:</strong>         *   The orders for DOT Foods were checked, and the price per case was showing correctly in the system.             *   An invoice created from an order matched the expected price exactly, leading to confusion.         *   <strong>Outcome:</strong>         *   No clear example or data was provided from the requester to replicate the issue.         *   <strong>Next Step:</strong>         *   Request examples of affected orders or invoices and screenshots to investigate further.</li>
</ul>
<h3 id="key-points">Key Points</h3>
<ul>
<li>Ticket #44388 involved a data formatting issue with decimals leading to rejection.     *   Ticket #44307 involved a possible pricing discrepancy with insufficient data to diagnose.
If you want, I can also help you create exam questions or memory aids based on these issues. Would you like me to proceed?</li>
</ul>
<h4 id="generated-content-3">Generated Content</h4>
<p>Subject: Re: 824-Rejection on 810 Invoice - Total Amount Error
Hi Taylor,
Thank you for reaching out. I reviewed the 824 rejection and the 810 invoice data you provided. The rejection was due to a <strong>“total amount error”</strong>, which appears to stem from the formatting of the charge values in your invoice, specifically the <strong>decimal placement</strong> in the raw data.
Here’s what we found:</p>
<ul>
<li>The <strong>total dollar amount for the charge was not formatted correctly</strong>; the raw data lacked the necessary decimal point, causing the system to interpret the amount incorrectly.     *   The total amounts should reflect decimal places to match the expected format. For example, a charge amount like 292.30 must be entered with the decimal and not as 29230 or 2923.     *   Additionally, there was an invalid code in the allowance/charge segment (SC
code), but this is secondary to the decimal formatting issue.     *   The rule to remember is: If IT07 is present with either IT06 or IT08, IT07 takes precedence, and a dollar amount must be correctly entered with the decimal placement.
<strong>To resolve this:</strong></li>
<li>Please create a <strong>new invoice with the corrected decimal formatting</strong> in the charge amount fields.     *   Avoid restaging and resubmitting the same invoice, as the system will not pick up those changes on an existing document.     *   Ensure that the charge and allowance values follow the correct syntax as per the invoice requirements.
If you need assistance creating the new invoice or have additional questions, feel free to reach out.
Best regards,  [Your Name]  [Your Position]</li>
</ul>
<h4 id="transcript">Transcript</h4>
<p>00:00 Michael H.: Hi, Sandy. Oh, how are you doing? 00:01 Sandy K.: Hi, Michael. I&#39;m okay, how are you? 00:07 Michael H.: A good, good, just, uh, just stuck on some tickets. 00:14 Sandy K.: Alright, let me… Sorry, I had to also restart my computer, so…
Okay. 00:18 Michael H.: Oh, yes, no problem. So, um, so the ticket that I was, um, having problem with was… to get 44388. 01:25 Sandy K.: I got an 824 rejection, an 810. Total amount error, cannot seem to locate where. 01:28 Michael H.: Yes. 01:33 Sandy K.: Why this would be rejected him? 01:38 Michael H.: So, I went through all the steps that you usually tell me, and I tried to, um, look at the 824. And everything. And, um, I looked at the 810 invoice document. And, um… This is so… yes. He&#39;s the one that opened 3 tickets. 01:54 Sandy K.: What company is this? Oh, Wolf and Swipper? 01:59 Michael H.: Um, I don&#39;t know why he keeps opening a whole bunch of tickets. 02:00 Sandy K.: All right, so 6393. 02:03 Michael H.: But… it gets… 02:04 Michael H.: Hi, I’m transcribing this call with my Tactiq AI Extension: https:// tactiq.io/r/transcribing 02:30 Sandy K.: I really need my database tool. So what did he do with it? So this is Wolf and Swickard. The only thing in my inbox is this. 02:43 Michael H.: Yes. 02:45 Sandy K.: He… what did he do at the age 24? 02:49 Michael H.: Um, that That&#39;s what I wanted to ask you. I think he did, uh… 03:10 Sandy K.: All right, well, let us do a smartphone. Oh, here&#39;s got one already for A24s. 03:18 Michael H.: Okay. So that&#39;s what you do, you go in smart folders and… 03:22 Sandy K.: It just brings up… I can easily then look for all 824, so he obviously archived it. 03:27 Michael H.: Yes. 03:28 Sandy K.: Okay, so this is the one he&#39;s talking about. So, this is the invoice number. 03:38 Michael H.: Yes. 03:40 Sandy K.: And he&#39;s saying that the TDS total amount is wrong. 03:46 Michael H.: Yes, and the TDS, the discrepancy is that The 810 states that
it&#39;s supposed to be a different amount. I wrote it down. 04:02 Sandy K.: So, the TDS total, I exported this here. So the TDS… It&#39;s 472305, and this is saying here… This is just saying that we&#39;re missing something, and that&#39;s okay, but 472305. 04:25 Michael H.: Is this person… Yes, but there&#39;s a period uh, 5 cents. Does that matter? 04:37 Sandy K.: Oh, and 5 cents, 47.23.05. Around 1975 times… 37… Oh, well, yeah. 04:40 Michael H.: Yes. 04:49 Sandy K.: Oh, and then there&#39;s… Okay. I wonder if that&#39;s why. So, in this, I… TA segment, an allowance or charge. Sc is not valid, they&#39;re saying. So, 119.75 times 37 is… 32… 30… 119… 75 times 3 solid. It was 443075. Okay, and we have… A charge of $7.90… Well, that&#39;s the rate. We have a charge of $292.30. Bus 29230. 472305. I wonder if they&#39;re looking for a decimal here. Because this is not 292.30. Oh yeah, here it is, 292.30. This has a decimal place in it. This does not have a decimal place in it. 06:00 Michael H.: Yes. 06:07 Sandy K.: I wonder if that&#39;s why, because this doesn&#39;t have a decimal. It also says SC is invalid, but… I don&#39;t think that&#39;s… I think it&#39;s… the fact that the raw data doesn&#39;t have a decimal in it. 06:31 Sandy K.: Um, it&#39;s odd, because Maria was asking about this same But I don&#39;t know if it was Wolf and Swickard. No, it wasn&#39;t. Okay. 06:44 Michael H.: Yeah, this is Taylor from Wolf &amp; Swickard. 06:56 Sandy K.: And this is, um… Cat 89. 07:18 Sandy K.: Bullborne. So… Let&#39;s see… ITA… This says… this string… The format says none. This one says two implied decimal. I think we have to change that. 07:41 Michael H.: Okay. 07:43 Sandy K.: I think that&#39;s what we have to make that say none. Because… You say none, and I think that&#39;s probably why it rejected, is because in the raw data. This is… I wonder if that&#39;s why. They&#39;re not looking… this never has a decimal on it, but… I don&#39;t know if they want a decimal in that one. 08:13 Michael H.: Oh, okay. Just because he, um, in the screenshots he sent us
the raw data, so that&#39;s why… That&#39;s why I was con… 09:50 Sandy K.: See, this is a whole number, so there&#39;s no decimals. That is the… IT or 7. If I TO7&#39;s present with IT06 or 8, then 7 takes precedence. 10:35 Sandy K.: Okay. I used to pass one caution. Total dollar amount for the charge. 11:09 Michael H.: Just writing down this in my notes. Right here, we&#39;re just checking to make sure we&#39;re having the right syntax, like decimals and everything, for… the 810. 11:22 Sandy K.: Yeah. So I&#39;m just making sure that however they want it. 11:22 Michael H.: Yes, okay. 11:27 Sandy K.: We have it set up, but here it says… If IT08 is present, then 9 is required. And he used 6 and 7. Well then, I think he just shouldn&#39;t… If IT07 is present with either 6 or 8, then 7 takes precedence. That at least 6, 7, or 8 must be present. So, I say that he doesn&#39;t fill in this dollar amount. He just fills in the rate and the quantity. Leave that blank. I want to guess this is the first time he&#39;s used an allowance or a charge. 13:02 Michael H.: Because, um, the TDS, uh, I was googling it, and of course, using the knowledge base. Um, it says that, uh. The calculated total should be the line item total, which is, um, and plus the metal surcharge. 13:20 Sandy K.: Mm-hmm. 13:21 Michael H.: Yes, so it should be actually $187.65. From, um… Right? 13:27 Sandy K.: No. No. So, this is 119.75. Is the unit price total $37. So here&#39;s the total invoice. 13:35 Michael H.: Yes. Yes. 13:38 Sandy K.: This is the total, $44.3075. Plus, there is a charge, additional charge of $292.30. So this plus this equals that. 13:48 Michael H.: Okay. Yes. Okay. 13:52 Sandy K.: So this is right, but this… It does not like this 292.3 13:58 Michael H.: Yes. 14:03 Sandy K.: So what I think he should do… 14:39 Sandy K.: I&#39;m gonna fill it in, just left it all blank. Okay. I&#39;m surprised it didn&#39;t automatically fill that in. But if I take his invoice… I&#39;m gonna say I have 37… It&#39;s each… 1975… And then I&#39;m gonna have a charge…
15:16 Sandy K.: A surcharge… I don&#39;t know what it said. 37… Each… Say… Paid by customer. 16:05 Sandy K.: Doesn&#39;t change it. Mountain. Okay. So I won&#39;t use this unless it wants this amount. All right. Okay, go back here… 16:34 Michael H.: Sorry, Ms. Sandy, I&#39;m confused about what you just did. 16:36 Sandy K.: I wanted to create a new invoice as a test. To see if I didn&#39;t have this value. 16:41 Michael H.: Yes, okay. 16:43 Sandy K.: Would this still change? I thought it would take the rate times the quantity and calculate the 292 and add it here, but no, you have to actually enter it in. 16:47 Michael H.: Okay. 16:58 Sandy K.: So… I&#39;m gonna go to the DOM. 16:58 Michael H.: Yes. 17:06 Sandy K.: Oh, not IT1, ITA07. And I&#39;m going to say not. All that&#39;s saving, I&#39;ll get rid of my test invoice. 18:12 Sandy K.: Okay. So now let&#39;s refresh this. I&#39;m gonna create a new invoice. I&#39;ll just use this one, I don&#39;t really care. All right, 37… Well, 1975, all around each. I&#39;m going to be so many fun. Now we have a charge… Oh, wait. Oh. Never mind. I have to go back. I have to… I have to do it, because I just changed Cat 89. 19:10 Michael H.: Yes. 19:14 Sandy K.: I need this invoice number. 25, sorry. Okay. I&#39;ll use this E30 respond. Each… 19 is on that. Okay, perfect. This was a charge… I only have one option… He selected service charge… Made by customer… 790… 37… 20:39 Sandy K.: All right, now we&#39;ll see what the wrong data looks like here. 21:04 Sandy K.: 292c, now it has the decimal. I think it needs the decimal. 21:08 Michael H.: Oh, okay. 21:13 Sandy K.: That&#39;s why I think it didn&#39;t… do it right, because I think it looked as that as 29,230. 21:22 Michael H.: Okay. 21:24 Sandy K.: So now, I think that… It&#39;ll be correct. Um, so he probably has to create a new… invoice. He can&#39;t use the one that&#39;s already there. So, let&#39;s see, does it say… Um, yeah, item rejected. So he has to… create a brand new invoice.
21:40 Michael H.: Okay, thank you. For the 824. 21:50 Sandy K.: Yeah, well, no, for this invoice, this one one, this invoice. He has to create a brand new one. 21:57 Michael H.: Okay.
22:00 Sandy K.: So, you can let them know that we fixed the… you know, we fixed the invoice. So that, uh… the values will calculate correctly, and he just has to create a new invoice. 22:12 Michael H.: Okay, yes. 22:14 Sandy K.: He can&#39;t… he can&#39;t restage the one that he had and resend it Because it… the… once you have a document created, it won&#39;t pick up those changes. So he has to re… he has to create a brand new one. 22:33 Michael H.: I wrote this all down. And it was just a decimal place, pretty much. They had to use that. Format. 22:46 Sandy K.: Yeah, it wasn&#39;t… it wasn&#39;t formatted correctly for the… the charge value, so… 22:53 Michael H.: Yes. And my last ticket, um, that I had trouble with was… Um, Ms. Sandy, 44307. 23:08 Sandy K.: 44307. 23:09 Michael H.: Yes. 23:30 Sandy K.: The orders are not displaying the price per case, and when
invoicing, the total amount appears to be less than the actual invoice. I&#39;m assuming any DOT Foods order. Let me refresh this. 23:42 Michael H.: Yes. 23:45 Sandy K.: And who&#39;s the customer here? Broster? 23:49 Michael H.: Awesome. Yes. 23:52 Sandy K.: I don&#39;t know if it&#39;s here. Oh, okay. 7496… Oh, okay, I guess that&#39;s not their username. They must have changed it. 25:12 Sandy K.: So, the dog foods… price, he&#39;s saying. Which we don&#39;t control, but okay, we&#39;ll look at it. Nice. All right. Alright, so I&#39;ll scroll back to the ticket. We&#39;re currently experiencing an issue with DOT food&#39;s pricing. 25:46 Sandy K.: The orders are not displaying the price per case. And when invoicing, the total amount appears less than the actual invoice, typically varying from $1 to $4. No examples, though, right? Nothing. 26:00 Michael H.: Yes. 26:03 Sandy K.: Hasn&#39;t… Provided any… Nothing. It&#39;s always so hopeful. Okay, so… 4607 times… 932. There&#39;s 43 A5864. What&#39;s not right? 26:32 Michael H.: That&#39;s what I was confused about, because when I saw the unit price, it was not showing 1 through 4, so that&#39;s why 26:38 Sandy K.: Well, he&#39;s saying… no, he&#39;s saying that… Oh, I need more detail. He&#39;s saying it&#39;s typically varying from $1 to $4 difference. So he&#39;s saying that the total amount appears to be less than the actual invoice, typically varying from a $1 to $4. 27:01 Michael H.: Oh, okay. 27:02 Sandy K.: But I need an example. Like, can you give me a PO number? That this has happened on, like, as an example, because I can… create… I could take this and create an invoice. So, these are the case prices. So this invoice right here is $61,186.40. Great. I&#39;ll create an invoice. $61,186.40. It&#39;s exactly the same price. So, what… what&#39;s different? 27:42 Michael H.: Yes. 27:50 Sandy K.: So, he is going to have to provide examples. Can you give us an example? 27:58 Michael H.: Yes. 27:58 Sandy K.: Of… an order… Where… Um, when you create the invoice. You
know, they&#39;re not… the orders are not displaying the price per case. So, I just looked at DOT Foods orders. 28:19 Michael H.: That&#39;s… 28:19 Sandy K.: And they have price per case. So… Case price. Case. So, we could… you know, you could tell them that, you know, you looked at the Daw Foods orders, and The orders do have the unit of measure of case. 28:32 Michael H.: Yes, case. 28:43 Sandy K.: And… we&#39;ve created an invoice off of an order to use as an example, and it matched. Could he please give us an example of an order that&#39;s not displaying the correct, you know, like, not displaying a case price. Or were the invoices creating incorrectly. But yeah, we need some details, yeah. 29:06 Michael H.: Yes, and ask them for screenshots and everything. Yes, thank you so much, Ms. Sandy. 29:10 Sandy K.: Yeah. 29:14 Michael H.: That one, I was, uh, confused up about. 29:20 Sandy K.: Okay. 29:20 Michael H.: Yes, these recent tickets, um… was confusing for me. Sorry about that. 29:28 Sandy K.: No, that&#39;s okay. Any others 29:29 Michael H.: Yes.
View original transcript at Tactiq.</p>
