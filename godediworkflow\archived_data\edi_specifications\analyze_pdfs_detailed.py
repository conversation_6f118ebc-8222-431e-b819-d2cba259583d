#!/usr/bin/env python3
import PyPDF2
import sys
import os
from datetime import datetime
import re
import hashlib

def get_file_hash(pdf_path):
    """Calculate MD5 hash of file to check for duplicates."""
    with open(pdf_path, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()

def extract_detailed_info(text):
    """Extract more detailed information from PDF text."""
    info = {
        'trading_partners': [],
        'edi_transactions': [],
        'emails': [],
        'urls': [],
        'has_test_data': False,
        'has_prod_data': False,
        'sensitive_info': []
    }
    
    # Extract trading partner names (common patterns)
    tp_patterns = [
        r'\b(?:Trading Partner|Customer|Vendor):\s*([A-Za-z0-9\s&,.-]+)',
        r'\bISA\*[^*]+\*[^*]+\*[A-Z0-9]{2}\*([A-Z0-9]+)',
        r'\bGS\*[^*]+\*([A-Z0-9]+)'
    ]
    
    for pattern in tp_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        info['trading_partners'].extend(matches[:5])
    
    # Extract EDI transaction types
    trans_pattern = r'\b(810|820|830|832|840|842|843|844|846|847|849|850|852|855|856|860|861|864|865|866|867|869|870|940|943|944|945|947|990|997)\b'
    info['edi_transactions'] = list(set(re.findall(trans_pattern, text)))
    
    # Extract emails (redacted)
    email_pattern = r'\b([A-Za-z0-9._%+-]+)@([A-Za-z0-9.-]+\.[A-Z|a-z]{2,})\b'
    emails = re.findall(email_pattern, text)
    info['emails'] = [f"{e[0][:3]}***@{e[1]}" for e in emails[:5]]  # Redact emails
    
    # Extract URLs
    url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+' 
    info['urls'] = re.findall(url_pattern, text)[:5]
    
    # Check for test vs production indicators
    if re.search(r'\b(test|testing|sandbox|dev)\b', text, re.IGNORECASE):
        info['has_test_data'] = True
    if re.search(r'\b(production|prod|live)\b', text, re.IGNORECASE):
        info['has_prod_data'] = True
    
    # Check for sensitive information patterns
    sensitive_patterns = [
        (r'password\s*[:=]\s*\S+', 'password'),
        (r'api[_\s]?key\s*[:=]\s*\S+', 'api_key'),
        (r'token\s*[:=]\s*\S+', 'token'),
        (r'\b(?:ssn|social)\s*[:=]?\s*\d{3}-?\d{2}-?\d{4}', 'ssn'),
        (r'(?:account|acct)\s*(?:number|#|no\.?)\s*[:=]?\s*\d{6,}', 'account_number')
    ]
    
    for pattern, label in sensitive_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            info['sensitive_info'].append(label)
    
    return info

# Analyze each PDF in detail
pdf_files = [
    '/home/<USER>/edi_knowledge_base/trading_partners/wisebatch.pdf',
    '/home/<USER>/edi_knowledge_base/trading_partners/wisebatch 2.pdf',
    '/home/<USER>/edi_knowledge_base/trading_partners/Symphony_Beauty_Box_Corp_CVS_Production_Checklist-Tier1.pdf',
    '/home/<USER>/edi_knowledge_base/trading_partners/trading partners.pdf',
    '/home/<USER>/edi_knowledge_base/trading_partners/customers.pdf',
    '/home/<USER>/edi_knowledge_base/trading_partners/mckesson.pdf'
]

file_hashes = {}
recommendations = []

print("\nDETAILED TRADING PARTNER PDF ANALYSIS")
print("=" * 80)

for pdf_path in pdf_files:
    if os.path.exists(pdf_path):
        filename = os.path.basename(pdf_path)
        file_hash = get_file_hash(pdf_path)
        
        # Check for duplicates
        is_duplicate = False
        if file_hash in file_hashes:
            is_duplicate = True
            duplicate_of = file_hashes[file_hash]
        else:
            file_hashes[file_hash] = filename
        
        print(f"\n### {filename}")
        print("-" * 40)
        
        if is_duplicate:
            print(f"⚠️  DUPLICATE of {duplicate_of}")
            recommendations.append({
                'file': filename,
                'action': 'REMOVE',
                'reason': f'Duplicate of {duplicate_of}'
            })
            continue
        
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                
                if len(text.strip()) < 20:
                    print("⚠️  File appears to be empty or unreadable")
                    recommendations.append({
                        'file': filename,
                        'action': 'REMOVE',
                        'reason': 'Empty or corrupted file'
                    })
                    continue
                
                # Extract detailed information
                details = extract_detailed_info(text)
                
                print(f"Trading Partners Found: {len(details['trading_partners'])}")
                if details['trading_partners']:
                    print(f"  Examples: {', '.join(details['trading_partners'][:3])}")
                
                print(f"EDI Transactions: {', '.join(details['edi_transactions']) if details['edi_transactions'] else 'None'}")
                
                print(f"Contact Emails: {len(details['emails'])} found")
                if details['emails']:
                    print(f"  Examples: {', '.join(details['emails'][:3])}")
                
                print(f"Environment: ", end="")
                if details['has_test_data'] and details['has_prod_data']:
                    print("Mixed (Test & Production)")
                elif details['has_prod_data']:
                    print("Production")
                elif details['has_test_data']:
                    print("Test")
                else:
                    print("Unknown")
                
                if details['sensitive_info']:
                    print(f"⚠️  SENSITIVE INFO DETECTED: {', '.join(set(details['sensitive_info']))}")
                
                # Make recommendation
                if details['sensitive_info']:
                    recommendations.append({
                        'file': filename,
                        'action': 'REVIEW',
                        'reason': 'Contains sensitive information - needs sanitization'
                    })
                elif details['edi_transactions'] or details['trading_partners']:
                    recommendations.append({
                        'file': filename,
                        'action': 'KEEP',
                        'reason': 'Contains valuable EDI specifications/partner info'
                    })
                else:
                    recommendations.append({
                        'file': filename,
                        'action': 'REVIEW',
                        'reason': 'Limited EDI-specific content'
                    })
                    
        except Exception as e:
            print(f"Error analyzing file: {str(e)}")
            recommendations.append({
                'file': filename,
                'action': 'REVIEW',
                'reason': f'Error during analysis: {str(e)}'
            })

print("\n" + "=" * 80)
print("RECOMMENDATIONS SUMMARY")
print("=" * 80)

for rec in recommendations:
    icon = "✅" if rec['action'] == 'KEEP' else "❌" if rec['action'] == 'REMOVE' else "⚠️"
    print(f"{icon} {rec['file']}: {rec['action']} - {rec['reason']}")

print("\n" + "=" * 80)