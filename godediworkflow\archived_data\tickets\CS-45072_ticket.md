# CS-45072: Data Trans Assistance

## Ticket Information
- **Key**: CS-45072
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <EMAIL>
- **Created**: 2025-06-06T11:17:05.231-0600
- **Updated**: 2025-06-09T06:57:36.992-0600
- **Customer**: <EMAIL>

## Description
*{color:red} *CAUTION: This email originated from outside of the organization. Do not click links or open attachments unless you recognize the sender and know the content is safe.*{color}
 
 
  

Good Afternoon Data Trans Support Team, 
  

My main contact, <PERSON><PERSON>, is out of office until the 9th but I am trying to get my SPS testing done. Could you help me determine the below information? 
  !Testing_and_Certification___SPS_Commerce.png|thumbnail!
 


  

Thank you and have a wonderful day,{adf}{"type":"expand","content":[{"type":"paragraph","content":[{"type":"text","text":"– "}]},{"type":"paragraph","content":[{"type":"text","text":"<PERSON>","marks":[{"type":"strong"}]},{"type":"text","text":" "}]},{"type":"paragraph","content":[{"type":"text","text":"https://www.stickerpack.com","marks":[{"type":"link","attrs":{"href":"https://www.stickerpack.com"}}]},{"type":"text","text":" "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://cdn.shopify.com/s/files/1/0052/4356/8162/files/Emailsig_Sticker_Pack_Logo.png?v=1729612214","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":" <"},{"type":"text","text":"https://www.stickerpack.com","marks":[{"type":"link","attrs":{"href":"https://www.stickerpack.com"}}]},{"type":"text","text":"> "}]},{"type":"paragraph","content":[{"type":"text","text":"Phone: (************* "}]},{"type":"paragraph","content":[{"type":"text","text":"Cell: (************* "}]},{"type":"paragraph","content":[{"type":"text","text":"<EMAIL>","marks":[{"type":"link","attrs":{"href":"mailto:<EMAIL>"}}]},{"type":"text","text":" "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://cdn.shopify.com/s/files/1/0052/4356/8162/files/Emailsig_Instagram.png?v=1729609799","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":" <"},{"type":"text","text":"http://instagram.com/stickerpackcompany","marks":[{"type":"link","attrs":{"href":"http://instagram.com/stickerpackcompany"}}]},{"type":"text","text":"> "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://cdn.shopify.com/s/files/1/0052/4356/8162/files/Emailsig_Facebook.png?v=1729609799","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":" <"},{"type":"text","text":"http://www.facebook.com/stickerpack","marks":[{"type":"link","attrs":{"href":"http://www.facebook.com/stickerpack"}}]},{"type":"text","text":"> "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://cdn.shopify.com/s/files/1/0052/4356/8162/files/Emailsig_Linkedin.png?v=1729609799","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":" <"},{"type":"text","text":"https://www.linkedin.com/company/sticker-pack-sp","marks":[{"type":"link","attrs":{"href":"https://www.linkedin.com/company/sticker-pack-sp"}}]},{"type":"text","text":"> "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://cdn.shopify.com/s/files/1/0052/4356/8162/files/Emailsig_Google.png?v=1729609799","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":" <"},{"type":"text","text":"https://maps.app.goo.gl/ZZSoAhzvicyCWLLc7","marks":[{"type":"link","attrs":{"href":"https://maps.app.goo.gl/ZZSoAhzvicyCWLLc7"}}]},{"type":"text","text":"> "}]},{"type":"mediaSingle","attrs":{"layout":"center"},"content":[{"type":"media","attrs":{"type":"external","url":"https://cdn.shopify.com/s/files/1/0052/4356/8162/files/Emailsig_JudgeMe.png?v=1729609799","height":183,"width":200}}]},{"type":"paragraph","content":[{"type":"text","text":" <"},{"type":"text","text":"https://stickerpack.com/pages/reviews#judgeme","marks":[{"type":"link","attrs":{"href":"https://stickerpack.com/pages/reviews#judgeme"}}]},{"type":"text","text":"> "}]},{"type":"paragraph","content":[{"type":"text","text":"*******************************  "}]},{"type":"paragraph","content":[{"type":"text","text":"We Have Moved!","marks":[{"type":"strong"}]},{"type":"text","text":"  "}]},{"type":"paragraph","content":[{"type":"text","text":"Please update your records to reflect our new address: "}]},{"type":"paragraph","content":[{"type":"text","text":"831 East Glendale Ave "}]},{"type":"paragraph","content":[{"type":"text","text":"Sparks, NV 89431  "}]},{"type":"paragraph","content":[{"type":"text","text":"*******************************"}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

