<h3 id="lauren-cohn-s-zoom-meeting"><PERSON>&#39;s Zoom Meeting</h3>
<p>Meeting started: May 16, 2025, 10:15:52 AM Meeting duration: 29 minutes Meeting participants: <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="generated-content">Generated Content</h4>
<h1 id="ticket-4-overview">Ticket 4 Overview</h1>
<h2 id="customer-information-name-dave-company-dolores-canning">Customer Information - <strong>Name:</strong> Dave - <strong>Company:</strong> <PERSON></h2>
<h2 id="issue-summary-problem-dave-received-an-email-notification-for-an-875-document-but-cannot-locate-it-in-his-account-details-the-email-notification-includes-document-type-875-control-numbers-screenshot-provided-by-dave-but-is-unclear-user-status-dave-is-an-admin-user-which-typically-grants-access-to-all-documents">Issue Summary - <strong>Problem:</strong> Dave received an email notification for an <strong>875 document</strong> but cannot locate it in his account.   ### Details - The email notification includes:   - <strong>Document Type:</strong> 875   - <strong>Control Numbers</strong> - <strong>Screenshot:</strong> Provided by Dave but is unclear. - <strong>User Status:</strong> Dave is an <strong>admin user</strong>, which typically grants access to all documents.</h2>
<h2 id="actions-taken-1-mark-ticket-as-unread-this-action-prompts-dave-to-refresh-his-account-for-updates">Actions Taken 1. <strong>Mark Ticket as Unread:</strong>    - This action prompts Dave to refresh his account for updates.</h2>
<ol start="2">
<li><strong>Sorting Check:</strong>    - Suggested that Dave verify if any sorting settings are hiding the document (e.g., sorting by newest to oldest).</li>
</ol>
<h2 id="next-steps-await-dave-s-response-after-he-refreshes-his-account-to-determine-if-the-document-becomes-visible">Next Steps - Await Dave&#39;s response after he refreshes his account to determine if the document becomes visible.</h2>
<hr>
<h3 id="key-concepts-875-document-a-specific-type-of-document-related-to-data-notifications-admin-user-access-admin-users-generally-have-unrestricted-access-to-all-documents-even-if-they-cannot-open-them">Key Concepts - <strong>875 Document:</strong> A specific type of document related to data notifications. - <strong>Admin User Access:</strong> Admin users generally have unrestricted access to all documents, even if they cannot open them.</h3>
<h3 id="thought-provoking-questions-what-are-the-common-issues-that-admin-users-face-when-accessing-documents-how-can-support-teams-improve-communication-with-users-regarding-document-visibility">Thought-Provoking Questions - What are the common issues that admin users face when accessing documents? - How can support teams improve communication with users regarding document visibility?</h3>
<h3 id="connections-to-other-topics-this-ticket-relates-to-user-permissions-and-document-management-systems-which-may-connect-to-previous-discussions-on-user-roles-and-access-rights">Connections to Other Topics - This ticket relates to user permissions and document management systems, which may connect to previous discussions on user roles and access rights.</h3>
<h3 id="real-world-applications-understanding-document-access-issues-can-help-improve-user-training-and-support-processes-in-organizations-that-rely-on-document-management-systems">Real-World Applications - Understanding document access issues can help improve user training and support processes in organizations that rely on document management systems.</h3>
<h3 id="areas-for-further-research-investigate-common-sorting-issues-in-document-management-systems-and-their-resolutions">Areas for Further Research - Investigate common sorting issues in document management systems and their resolutions.</h3>
<h3 id="potential-exam-questions-1-what-steps-should-be-taken-when-a-user-cannot-find-a-document-they-have-been-notified-about-2-explain-the-significance-of-user-roles-in-document-management-systems">Potential Exam Questions 1. What steps should be taken when a user cannot find a document they have been notified about? 2. Explain the significance of user roles in document management systems.</h3>
<h3 id="glossary-875-document-a-specific-document-type-used-in-data-notifications-admin-user-a-user-with-elevated-permissions-typically-able-to-access-all-documents">Glossary - <strong>875 Document:</strong> A specific document type used in data notifications. - <strong>Admin User:</strong> A user with elevated permissions, typically able to access all documents.</h3>
<h3 id="summary-of-main-takeaways-ensure-users-are-aware-of-sorting-options-that-may-affect-document-visibility-admin-users-should-still-be-guided-through-troubleshooting-steps-to-resolve-access-issues">Summary of Main Takeaways - Ensure users are aware of sorting options that may affect document visibility. - Admin users should still be guided through troubleshooting steps to resolve access issues.</h3>
<h4 id="generated-content-2">Generated Content</h4>
<h1 id="lecture-notes-edi-processes-and-ticket-management">Lecture Notes: EDI Processes and Ticket Management</h1>
<h2 id="1-ticket-management-overview-purpose-efficient-handling-of-user-requests-and-issues-key-actions-refreshing-screens-for-visibility-marking-tickets-as-unread-for-notifications-using-labels-for-categorization-e-g-update-ybdi-user">1. Ticket Management Overview    - <strong>Purpose</strong>: Efficient handling of user requests and issues.    - <strong>Key Actions</strong>:      - Refreshing screens for visibility.      - Marking tickets as unread for notifications.      - Using labels for categorization (e.g., &#34;update YBDI user&#34;).</h2>
<h2 id="2-ticket-5-valerie-javellina-allegheny-health-context-issue-related-to-edi-850-document-type-document-types-855-acknowledgment-of-purchase-orders-not-required-for-blanket-pos">2. Ticket 5: Valerie Javellina - Allegheny Health    - <strong>Context</strong>: Issue related to EDI 850 document type.    - <strong>Document Types</strong>:      - <strong>855</strong>: Acknowledgment of purchase orders (not required for blanket POs).</h2>
<ul>
<li><strong>810</strong>: Invoice document.    - <strong>Blanket PO</strong>:      - Definition: A purchase order covering a period (e.g., 30 days, 3 months).      - Note: If a blanket PO is received, an 855 acknowledgment is not necessary.</li>
</ul>
<h3 id="2-1-actions-taken-verification-steps-check-if-the-document-type-sent-is-valid-875-confirm-user-permissions-and-access-potential-issues-user-may-have-restructured-view-settings-or-permissions">2.1. Actions Taken    - <strong>Verification Steps</strong>:      - Check if the document type sent is valid (875).      - Confirm user permissions and access.    - <strong>Potential Issues</strong>:      - User may have restructured view settings or permissions.</h3>
<h2 id="3-key-concepts-edi-electronic-data-interchange-the-electronic-exchange-of-business-documents-document-types-different-formats-of-data-exchanged-e-g-850-855-810-user-permissions-access-levels-that-determine-what-documents-users-can-view">3. Key Concepts    - <strong>EDI (Electronic Data Interchange)</strong>: The electronic exchange of business documents.    - <strong>Document Types</strong>: Different formats of data exchanged (e.g., 850, 855, 810).    - <strong>User Permissions</strong>: Access levels that determine what documents users can view.</h2>
<h2 id="4-thought-provoking-questions-how-do-different-document-types-impact-the-efficiency-of-edi-transactions-what-are-the-implications-of-blanket-pos-on-supplier-relationships">4. Thought-Provoking Questions    - How do different document types impact the efficiency of EDI transactions?    - What are the implications of blanket POs on supplier relationships?</h2>
<h2 id="5-real-world-applications-healthcare-networks-use-of-edi-for-managing-purchase-orders-and-invoices-construction-projects-implementation-of-blanket-pos-for-ongoing-supplies">5. Real-World Applications    - <strong>Healthcare Networks</strong>: Use of EDI for managing purchase orders and invoices.    - <strong>Construction Projects</strong>: Implementation of blanket POs for ongoing supplies.</h2>
<h2 id="6-areas-for-further-research-best-practices-for-managing-edi-ticketing-systems-impact-of-edi-on-supply-chain-efficiency">6. Areas for Further Research    - Best practices for managing EDI ticketing systems.    - Impact of EDI on supply chain efficiency.</h2>
<h2 id="7-potential-exam-questions-explain-the-difference-between-edi-document-types-850-855-and-810-discuss-the-significance-of-user-permissions-in-edi-systems">7. Potential Exam Questions    - Explain the difference between EDI document types 850, 855, and 810.    - Discuss the significance of user permissions in EDI systems.</h2>
<h2 id="8-glossary-edi-electronic-data-interchange-po-purchase-order-blanket-po-a-purchase-order-that-covers-multiple-deliveries-over-a-specified-period">8. Glossary    - <strong>EDI</strong>: Electronic Data Interchange.    - <strong>PO</strong>: Purchase Order.    - <strong>Blanket PO</strong>: A purchase order that covers multiple deliveries over a specified period.</h2>
<h2 id="9-summary-of-main-takeaways-effective-ticket-management-is-crucial-for-resolving-user-issues-understanding-edi-document-types-and-their-requirements-is-essential-for-smooth-operations-user-permissions-play-a-significant-role-in-document-accessibility">9. Summary of Main Takeaways    - Effective ticket management is crucial for resolving user issues.    - Understanding EDI document types and their requirements is essential for smooth operations.    - User permissions play a significant role in document accessibility.</h2>
<hr>
<p>These notes provide a structured overview of the discussed topics, focusing on ticket management and EDI processes relevant to Allegheny Health.</p>
<h4 id="generated-content-3">Generated Content</h4>
<p><strong>Ticket 5 Overview: Need to Ship an Account Number for EDI 850 for Allegheny Health</strong></p>
<ul>
<li><strong>Context</strong>:   - Involves a customer from Allegheny Health Network.   - The issue pertains to the EDI (Electronic Data Interchange) document type 850.</li>
<li><strong>Key Points</strong>:   - Allegheny Health only sends back two document types: <strong>855</strong> (Order Acknowledgment) and <strong>810</strong> (Invoice).   - The <strong>855</strong> is not required if a <strong>blanket PO</strong> (Purchase Order) is received. A blanket PO is a large order covering a specific period (e.g., 30 days or 3 months).   - The customer is likely inquiring about the <strong>810</strong> document type.</li>
<li><strong>Action Items</strong>:   - Verify which document the customer is referring to (either 810 or 855).   - Confirm the account the customer is logged into and the specific document they are working on.   - <strong>Next Steps</strong>:   - Ask the customer for their account number or company name and the type of document they are looking at to provide further assistance.
<strong>Summary</strong>: Ticket 5 involves a query regarding the EDI 850 document for Allegheny Health, focusing on the need for clarification on document types and customer account verification.</li>
</ul>
<h4 id="generated-content-4">Generated Content</h4>
<h3 id="ticket-5-update-edi-850-for-allegheny-health">Ticket 5 Update: EDI 850 for Allegheny Health</h3>
<p><strong>Problem:</strong> - The customer is trying to submit an account number for EDI 850 related to Allegheny Health. - There is confusion regarding the document types being sent back, specifically the absence of the &#34;ship to&#34; information.
<strong>Steps Taken:</strong></p>
<ol>
<li><strong>Document Type Verification:</strong>    - Confirmed that Allegheny only sends back two document types: <strong>855</strong> (Order Acknowledgment) and <strong>810</strong> (Invoice).    - Noted that the <strong>855</strong> is not required if a <strong>blanket PO</strong> is received.</li>
<li><strong>Customer Inquiry:</strong>    - Reached out to the customer (Valerie) to confirm:      - The account she is logged in under.      - The type of document she is working on (either <strong>810</strong> or <strong>855</strong>).</li>
<li><strong>Internal Review:</strong>    - Reviewed the system to check if the customer is registered and what documents are available.    - Attempted to access the 850 document to verify the details.
<strong>Outcome:</strong> - Awaiting a response from the customer to clarify her account and document type. - Identified that the customer may not be familiar with the document types, which could lead to further confusion.
<strong>Next Steps:</strong> - Follow up with the customer for clarification on her account and document type. - If necessary, reach out to the trading partner for additional support regarding the charge and allowance issue mentioned by the customer. - Monitor for any updates from the customer and prepare to assist further based on her response.</li>
</ol>
<h3 id="citations-document-types-855-order-acknowledgment-810-invoice">Citations: - <strong>Document Types:</strong>   - <strong>855</strong>: Order Acknowledgment   - <strong>810</strong>: Invoice</h3>
<ul>
<li><strong>Blanket PO</strong>: A purchase order that covers multiple deliveries over a specified period.</li>
</ul>
<h3 id="summary-the-ticket-is-currently-pending-customer-clarification-and-further-actions-will-be-determined-based-on-her-response">Summary: The ticket is currently pending customer clarification, and further actions will be determined based on her response.</h3>
<h4 id="generated-content-5">Generated Content</h4>
<h3 id="meeting-summary">Meeting Summary</h3>
<h2 id="date-insert-date-participants-lauren-michael"><strong>Date:</strong> [Insert Date]  <strong>Participants:</strong> Lauren, Michael</h2>
<h4 id="1-ticket-management-and-user-support-user-notification-lauren-marks-emails-as-unread-to-notify-users-of-new-updates-uses-generic-labels-like-update-web-edi-user-for-changes-passwords-permissions-admin-access-admin-users-retain-access-to-documents-even-if-they-cannot-open-them-if-a-secondary-user-cannot-see-a-document-lauren-checks-permissions-and-document-types">1. <strong>Ticket Management and User Support</strong>    - <strong>User Notification:</strong>      - Lauren marks emails as unread to notify users of new updates.      - Uses generic labels like &#34;update web EDI user&#34; for changes (passwords, permissions).    - <strong>Admin Access:</strong>      - Admin users retain access to documents even if they cannot open them.      - If a secondary user cannot see a document, Lauren checks permissions and document types.</h4>
<h4 id="2-allegheny-health-network-edi-issues-document-types-allegheny-only-sends-back-855-acknowledgment-and-810-invoice-blanket-purchase-orders-pos-an-855-is-not-required-for-blanket-pos-only-the-810-is-needed-for-invoicing">2. <strong>Allegheny Health Network EDI Issues</strong>    - <strong>Document Types:</strong>      - Allegheny only sends back <strong>855</strong> (acknowledgment) and <strong>810</strong> (invoice).      - <strong>Blanket Purchase Orders (POs):</strong>        - An <strong>855</strong> is not required for blanket POs; only the <strong>810</strong> is needed for invoicing.</h4>
<ul>
<li><strong>User Inquiry:</strong>      - Valerie from Allegheny Health is experiencing issues with missing <strong>SHIP TO</strong> information in her <strong>EDI 850</strong> submission.      - Lauren is waiting for Valerie to confirm her account information and the document type she is working with.</li>
</ul>
<h4 id="3-communication-and-collaboration-internal-communication-lauren-reached-out-to-rafat-via-slack-regarding-a-current-issue-with-the-system-customer-follow-up-lauren-plans-to-follow-up-with-valerie-for-clarification-on-her-account-and-document-type">3. <strong>Communication and Collaboration</strong>    - <strong>Internal Communication:</strong>      - Lauren reached out to Rafat via Slack regarding a current issue with the system.    - <strong>Customer Follow-Up:</strong>      - Lauren plans to follow up with Valerie for clarification on her account and document type.</h4>
<h4 id="4-system-limitations-and-user-experience-software-issues-discussion-on-the-outdated-nature-of-the-software-and-its-user-interface-concerns-about-the-lack-of-an-auto-save-feature-in-the-dom-editor">4. <strong>System Limitations and User Experience</strong>    - <strong>Software Issues:</strong>      - Discussion on the outdated nature of the software and its user interface.      - Concerns about the lack of an auto-save feature in the DOM editor.</h4>
<hr>
<h3 id="key-concepts-edi-electronic-data-interchange-a-system-for-exchanging-business-documents-in-a-standardized-electronic-format-blanket-po-a-purchase-order-that-covers-multiple-deliveries-over-a-specified-period">Key Concepts - <strong>EDI (Electronic Data Interchange):</strong> A system for exchanging business documents in a standardized electronic format. - <strong>Blanket PO:</strong> A purchase order that covers multiple deliveries over a specified period.</h3>
<h3 id="thought-provoking-questions-1-how-can-the-user-experience-be-improved-in-outdated-software-systems-2-what-best-practices-can-be-implemented-for-managing-edi-document-types-effectively">Thought-Provoking Questions 1. How can the user experience be improved in outdated software systems? 2. What best practices can be implemented for managing EDI document types effectively?</h3>
<h3 id="real-world-applications">Real-World Applications</h3>
<ul>
<li>Understanding EDI processes is crucial for businesses that rely on electronic transactions, particularly in healthcare and supply chain management.</li>
</ul>
<h3 id="areas-for-further-research-explore-best-practices-for-edi-management-and-user-support-in-healthcare-settings-investigate-modern-software-solutions-that-enhance-user-experience-in-edi-systems">Areas for Further Research - Explore best practices for EDI management and user support in healthcare settings. - Investigate modern software solutions that enhance user experience in EDI systems.</h3>
<h3 id="potential-exam-questions-1-what-are-the-differences-between-edi-document-types-810-and-855-2-explain-the-significance-of-blanket-purchase-orders-in-edi-transactions">Potential Exam Questions 1. What are the differences between EDI document types 810 and 855? 2. Explain the significance of blanket purchase orders in EDI transactions.</h3>
<h3 id="glossary-edi-850-purchase-order-edi-855-purchase-order-acknowledgment-edi-810-invoice-blanket-po-a-purchase-order-that-covers-multiple-deliveries-over-a-specified-period">Glossary - <strong>EDI 850:</strong> Purchase Order - <strong>EDI 855:</strong> Purchase Order Acknowledgment - <strong>EDI 810:</strong> Invoice - <strong>Blanket PO:</strong> A purchase order that covers multiple deliveries over a specified period.</h3>
<h3 id="main-takeaways-effective-communication-and-clarification-are-essential-in-resolving-edi-issues-understanding-the-specific-document-types-and-user-roles-can-streamline-support-processes">Main Takeaways - Effective communication and clarification are essential in resolving EDI issues. - Understanding the specific document types and user roles can streamline support processes.</h3>
<hr>
<p><strong>Citations:</strong>  - Lauren&#39;s insights on EDI processes and user support were derived from the discussion during the meeting.  - The definitions of EDI document types are based on standard industry practices.</p>
<h4 id="generated-content-6">Generated Content</h4>
<h3 id="summary-of-ticket-5-edi-850-for-allegheny-health">Summary of Ticket 5: EDI 850 for Allegheny Health</h3>
<h4 id="1-initial-issue-customer-allegheny-health-document-type-edi-850-purchase-order-problem-customer-valerie-is-trying-to-submit-an-account-number-for-the-edi-850-but-reports-that-the-ship-to-information-is-missing">1. <strong>Initial Issue</strong>    - <strong>Customer</strong>: Allegheny Health    - <strong>Document Type</strong>: EDI 850 (Purchase Order)    - <strong>Problem</strong>: Customer (Valerie) is trying to submit an account number for the EDI 850 but reports that the <strong>SHIP TO</strong> information is missing.</h4>
<h4 id="2-document-types-involved-855-acknowledgment-of-the-order-not-required-for-blanket-pos-810-invoice-document-blanket-po-a-single-purchase-order-covering-multiple-deliveries-over-a-specified-period-e-g-30-days">2. <strong>Document Types Involved</strong>    - <strong>855</strong>: Acknowledgment of the order (not required for blanket POs).    - <strong>810</strong>: Invoice document.    - <strong>Blanket PO</strong>: A single purchase order covering multiple deliveries over a specified period (e.g., 30 days).</h4>
<h4 id="3-steps-taken-verification-of-document-types-confirmed-that-allegheny-only-sends-back-two-document-types-855-and-810-noted-that-the-855-is-not-required-if-a-blanket-po-is-received">3. <strong>Steps Taken</strong>    - <strong>Verification of Document Types</strong>:      - Confirmed that Allegheny only sends back two document types: <strong>855</strong> and <strong>810</strong>.      - Noted that the <strong>855</strong> is not required if a blanket PO is received.</h4>
<ul>
<li><strong>Customer Communication</strong>:      - Requested Valerie to confirm:        - Which account she is logged into.        - The specific document type she is working on (either <strong>810</strong> or <strong>855</strong>).      - Emphasized the importance of knowing if she is receiving a blanket PO.</li>
</ul>
<h4 id="4-pending-actions-awaiting-valerie-s-response-to-confirm-the-account-information-the-type-of-document-she-generated">4. <strong>Pending Actions</strong>    - Awaiting Valerie&#39;s response to confirm:      - The account information.      - The type of document she generated.</h4>
<ul>
<li>If Valerie does not understand the term &#34;blanket PO,&#34; it is likely she is not receiving one.</li>
</ul>
<h4 id="5-next-steps-once-valerie-provides-the-necessary-information-further-investigation-can-be-conducted-to-resolve-the-issue-with-the-missing-ship-to-information-if-needed-reach-out-to-the-trading-partner-for-additional-clarification-on-the-document-types-and-their-requirements">5. <strong>Next Steps</strong>    - Once Valerie provides the necessary information, further investigation can be conducted to resolve the issue with the missing <strong>SHIP TO</strong> information.    - If needed, reach out to the trading partner for additional clarification on the document types and their requirements.</h4>
<h4 id="6-key-considerations-ensure-that-valerie-understands-the-document-types-and-their-implications-maintain-clear-communication-to-facilitate-a-quick-resolution">6. <strong>Key Considerations</strong>    - Ensure that Valerie understands the document types and their implications.    - Maintain clear communication to facilitate a quick resolution.</h4>
<h3 id="summary-the-ticket-revolves-around-a-missing-ship-to-information-issue-for-an-edi-850-document-submitted-by-allegheny-health-the-next-steps-involve-confirming-the-account-and-document-type-with-the-customer-to-proceed-with-troubleshooting">Summary The ticket revolves around a missing <strong>SHIP TO</strong> information issue for an EDI 850 document submitted by Allegheny Health. The next steps involve confirming the account and document type with the customer to proceed with troubleshooting.</h3>
<h4 id="generated-content-7">Generated Content</h4>
<p>During the meeting, two tickets were specifically discussed:</p>
<ol>
<li><strong>Ticket 5</strong>: EDI 850 for Allegheny Health, which involved issues with missing SHIP2 information and required confirmation from the customer regarding the account and document type. 2. <strong>Another ticket</strong>: Related to a customer needing assistance with submitting an invoice for an old PO, but details were less clear.
Additional tickets were mentioned, but they did not have specific details or actions taken during the meeting.</li>
</ol>
<h4 id="transcript">Transcript</h4>
<p>00:00 Lauren C.: Okay. I hate that. I don&#39;t know why they have it set like that. Please refresh your screen and let me know if you are able to view this. 00:01 Michael H.: That&#39;s no problem. 00:15 Lauren C.: 875 now. Nope, not that link. That would be fun. Be like, oh, what&#39;s up, guys? Okay. So for him, I really didn&#39;t have to do anything. I just resorted in case on his side, he&#39;s actually sorted anything. And then, like I said, if he hasn&#39;t actually opened it before, I just mark it as unread. So it helps give him a notification and it makes it bold in his inbox. So he&#39;ll be like, oh, hey. There it is. So I&#39;ll hit save. And that reply goes right back to him. 00:55 Michael H.: Yes. 01:00 Lauren C.: For the label. I usually just do update web EDI user if I just go through and make any changes. Whether that&#39;s password change, permission changes, or just reorganizing. It&#39;s an update to that user. So I just use that one. It&#39;s generic, but it&#39;s the closest thing I know. So… That one is there. So for now. That&#39;s really it that was really Nicely done and an easy ticket. So that was great I&#39;ll take that. 01:29 Michael H.: Yes. 01:29 Lauren C.: That&#39;s what I was hoping the other guys would be too. But yeah, no, his is there. If he was a secondary user and wasn&#39;t able to see it. Then I would go through and check admin to make sure that the 875 is a document type sent by that person. By that trading partner? If it is, I would then just double check permissions, disable the 875 from view, and then re-enable. 01:50 Michael H.: Yes. Yes. 01:56 Lauren C.: And ask them again to verify. But he&#39;s the admin. He&#39;ll forever have access to even documents that aren&#39;t set up. He just may not be able to open it. But he&#39;ll still be able to see it. Yeah, I&#39;m thinking he had just maybe… done something. He restructured how it was showing, whether it was received newest to oldest. Or he accidentally I don&#39;t know. He did something. 02:24 Michael H.: Yes. 02:24 Lauren C.: But my login is the same exact login that he has. He should be
able to see it. We&#39;ll see. That one&#39;s not crazy. So we&#39;ll let that one sit back to my assigned. Okay, so this one, this is an Allegheny customer. This is the health network that we also have. It&#39;s a separate instance of our website.
02:56 Lauren C.: It really is a watered down version. Oh, I have a notification. What did she say? It&#39;s odd that they allow for charge and allowance but only have an allowance. You will need to contact the trading partner. That&#39;s what I was thinking too. 03:15 Michael H.: So this is ticket five need to ship an account number for EDI 850 for Allegheny Health. 03:23 Lauren C.: Yes, sorry. So she&#39;s trying to submit Whether it&#39;s in, well. 03:30 Lauren C.: Okay, sorry. So for Allegheny, they only have two document types that they&#39;ll send back. That&#39;s the 855 and the 810. The 855 is not required if they receive what&#39;s called a blanket PO. So a blanket PO is like an overall, if they&#39;re doing like construction. They&#39;ll just have the purchase order and it&#39;ll be like a 30 day or like three month Just a big blanket. So if that came through, they don&#39;t need to send back an 855 acknowledgement. Allegheny doesn&#39;t want it. They only want them to invoice off of that PO periodically. So for this one, it&#39;s most likely the 810 that she&#39;s talking about. 04:07 Michael H.: Yes. 04:14 Lauren C.: Let&#39;s do here. I believe I was signed into Allegheny earlier. Oh,
you know what? We signed out. That&#39;s okay. So 3209. This is the Allegheny side. So when you open up the 850, respond. There&#39;s only two document types.
04:41 Michael H.: 810 and 855 04:41 Lauren C.: So the weird Yes. So the 855 is just an acknowledgement. I can open one for you. Those are pretty much the most basic documents to send back. All it is is just saying, hi, I received your order. I&#39;m either accepting it, holding it. Changing it. Rejecting it, any of that. So you&#39;ll finish filling out the details if needed. Like these white fields aren&#39;t required. And then at the bottom, they just have to go through and mark whether the line is accepted, needs to be changed, rejected, on hold, backordered, any of that. So it just gives a status update for the items that the trading partner is trying to order. So that&#39;s all that that one is. The actual… Hold on. 05:27 Michael H.: Yes. 05:33 Lauren C.: Oh, nope. Ship two is on the 855 as well. Okay, so I need her to verify. Which document she&#39;s looking at. It&#39;s interesting that she&#39;s saying that the ship too isn&#39;t there. Because I&#39;m almost positive Allegheny always submits that. So what I&#39;m actually going to ask her is um which account she&#39;s under. Or if she&#39;s even… a customer. So starting in progress I have no idea what organization. I tried looking her name up in admin earlier. She doesn&#39;t register to anything. So. Hi, Valerie. Nope, nope, nope.
06:20 Lauren C.: I can absolutely assist you. With this, are you able to confirm Which account you are logged Oh my goodness. In under… And…
06:46 Lauren C.: Okay, so… Since she didn&#39;t give us a screenshot, I don&#39;t know what she&#39;s looking at. It could be either of the two documents. So I need to know who she is, like who she&#39;s with and what document she&#39;s looking at. So, hi, Valerie. I told her I can help her with this. Are you able to confirm which account you were logged in under and what type of document you&#39;re working on? So from there, she should respond with either an account number or the company name. And then she should say if it&#39;s an 810 or 855 but Some people that are newer are like, I don&#39;t know what you&#39;re talking about. So it could go either way. 07:22 Michael H.: Yes. 07:38 Lauren C.: Horror thoughts that I feel like I got hit by a truck. I think I&#39;m sick. I was like, that&#39;s crazy. Me too. 07:43 Michael H.: Oh, no. You got to be careful. My sisters are like nurse practitioners and she said like there must be a cold or flu going around. 07:54 Lauren C.: No, no. 07:55 Michael H.: Yeah, she said like a lot of kids are getting sick up near Philadelphia, like teenagers and stuff. 08:04 Lauren C.: And that would be my luck. My husband works at a school district and obviously my daughter goes to school so Even though I don&#39;t leave
the house, it gets brought home to me. 08:14 Michael H.: Yeah, that&#39;s always happening. 08:14 Lauren C.: So, oh my goodness. Right? Oh my gosh. No, I&#39;m not going to put an Allegheny issue. I don&#39;t have a label for her yet. So this one is another one. We don&#39;t really have anything until the customer gives us a little bit more information. 08:35 Michael H.: Yes. 08:37 Lauren C.: The AHM customer. Oh my gosh. I just can&#39;t pipe today. Okay, so that one. We just need to wait on them. Since I made… Oh, I need to send this to Rafat. Let me grab. Copy image. Okay, so I&#39;m opening Slack and just saying hi, Rafat. Or actually, since I&#39;m in the middle of talking to her, I can just say question for you. 09:18 Michael H.: Yes. 09:26 Lauren C.: And he said, why does this customer&#39;s current day show more than just current day? And laughing. Okay. So I sent that to her. Usually the response is, can you create a ticket If so, we can. It basically runs the same as generating our ticket. It&#39;s just in a different area for them to work. 09:55 Michael H.: Yes. 09:56 Lauren C.: Joy. Okay. So, and then same thing here. I put updates, reach out to Rafat via Slack. About current. Tab not pulling. Only the current day. And that way I know that I did do that. Okay, so… Waiting on that one. Waiting on her. We may have to reach out to the partner because Sandy said the same thing. She&#39;s like, it&#39;s weird that they allow a charge, but then they don&#39;t have a charge code. 10:41 Lauren C.: So that would be… Admin, which I… I think is here, yes. Oh no, I&#39;m pretty sure it was SPS. They&#39;re not fun to reach out to. 10:57 Michael H.: What is SPS? 11:00 Lauren C.: It&#39;s another company that people use to process their stuff. So it&#39;s like us, but a much larger scale and I think it&#39;s more automated 11:11 Michael H.: Oh. 11:14 Lauren C.: So this is going to be fun. Um… Yeah, the only contact that they have On here is SPS. Which, unfortunately, it&#39;s the testing email, which isn&#39;t correct. I believe that they have… Another one. Specifically… Yes. So like this email case response That&#39;s SPS that I&#39;m working on something completely
different for. I believe that&#39;s the email I need to reach out to. 11:52 Michael H.: Yes. 11:55 Lauren C.: Fun, fun, fun. So this is one of like 10 emails. When you send them one, their system sends back generated like randomly auto generated one so like this one I don&#39;t actually need it. I just grabbed it so it wasn&#39;t in the queue bothering anybody. 12:13 Michael H.: Yes. 12:13 Lauren C.: Oh, Rafat replied. She said, we can totally talk about it. 12:21 Lauren C.: Do you want to hop in our huddle? Well, no, our Zoom call. Just kidding. Okay, so maybe. Oh. I returned that guy&#39;s voicemail? He never called back. I don&#39;t know what he wants from me. Couldn&#39;t tell you. Okay, so let&#39;s do, do I have… Yeah, he&#39;ll figure that out. So we&#39;ll just wait to see if that guy responds to let us know if he can see that. He should be able to. So log out. I wish there was like just a back button where we could just come back to this screen. But you have to log all the way out. Maybe one day we&#39;ll get there. 13:15 Michael H.: Yes. 13:20 Lauren C.: Because this process is new. 13:20 Michael H.: Yeah, the… that some of the software kind of feels kind of old, like the data servers Yeah, I know. 13:28 Lauren C.: Admin. Oh my gosh, yes. Yes. 13:32 Michael H.: The fact that it doesn&#39;t automatically save the DOM editor and I don&#39;t know. That&#39;s the first Yeah. 13:43 Lauren C.: No, you&#39;re telling me. Same thing. And I said that when I got here, I was like, ew, this is it. What are we doing? But I guess it&#39;s something they&#39;re comfortable with. I have no idea. 13:55 Michael H.: Yeah. 14:00 Lauren C.: Well, she didn&#39;t answer me. 14:01 Michael H.: So Ticket 5 is the EDI 850 for Allegheny Health. When Miss, I think Valerie try to submit an account number for the 850. 14:15 Lauren C.: Yes. 14:18 Michael H.: There was like problems because they&#39;re regarding different document types And the absence of the shipping to information. 14:31 Lauren C.: Yes. So she&#39;s creating and you can put it like this just put that
She&#39;s creating a response document from her 850 and she&#39;s saying that the SHIP2 information is missing. 14:43 Lauren C.: And then you can put in your note, we&#39;re waiting on her to confirm the account information and which document type she generated. 14:43 Michael H.: Yes. Yes, like the 855 or 810. 14:51 Lauren C.: Because… Yes, because she only has those two, thankfully. So that makes it a little easier on us, but I don&#39;t know what she&#39;s looking at. 15:01 Michael H.: By 855 was not required for the blanket PO, you said. 15:01 Lauren C.: Or who she is. Yes. So customers will know too if they&#39;re receiving one. If you ask, hi, are you receiving a blanket PO? And they say, what is that? The answer is probably no. Yeah, so if you ask them and they say that just nine times out of 10, it&#39;s probably no. 15:16 Michael H.: Yes. Yes. 15:27 Lauren C.: Well, she was typing. Rafat, hurry up. No, there&#39;s no rush. We&#39;re hanging. We&#39;re chilling. 15:35 Michael H.: Yeah. 15:38 Lauren C.: But yeah, this is, she&#39;s talking to Dev for a little bit okay Okay, she said she&#39;s talking to her team right now. I&#39;m like, okay, well, let me know when you&#39;re free and you&#39;re welcome to join us. I should… probably turn on my phone for the day. Oops. 16:01 Michael H.: Yes. 16:09 Lauren C.: Oopsie. Bye, babe. Okay, so that is on and reloaded. There probably aren&#39;t any calls. The way to check that would be our voicemails. Okay, yeah, so Rafat&#39;s going to get back to us on that. No. So that&#39;s probably an outbound call that she&#39;s on. When we&#39;re on calls. 16:33 Lauren C.: You can see it. So, and then like if it&#39;s ringing, you can see it flash between each person. 16:35 Michael H.: Yes. 16:42 Lauren C.: So who did they add to you yet? Nope. Lucky? Okay. Okay. Honestly, I didn&#39;t grab these other ones because I genuinely don&#39;t know what to do with them. 17:00 Michael H.: Oh, yeah. 17:00 Lauren C.: Like this this is a tier two issue. I probably could call that one
back. I don&#39;t think they left details and that&#39;s why I didn&#39;t.
17:10 Lauren C.: This is a mapping issue, also not a tier one issue. I didn&#39;t listen to that one yet Oops. Okay. As2, also not us. That&#39;s a completely different team. Turn off EDI transmissions, also not us. 17:28 Lauren C.: But the way he&#39;s asking something, I need to ask Sandia before I touch that. 17:32 Michael H.: Yes. 17:32 Lauren C.: The one that just came through, this is a validation email. I do see that there is a failure on here. Choi. These come into our side. I don&#39;t know why we&#39;re cc&#39;d on it, but if you scroll down all of these people also just received it.
17:55 Lauren C.: So anyone under request participants, those are all CC&#39;d on that. 17:55 Michael H.: Okay. 18:02 Lauren C.: That&#39;s a lot of people. And one of them is a developer. Interesting. Miss Jessica is on the development team. 18:10 Michael H.: Yes. And Ms. Jessica is like a higher support Or… Okay. Okay. 18:19 Lauren C.: She&#39;s on the development side, so Rafat&#39;s team. We don&#39;t usually interact with them directly unless they&#39;re working our development ticket and they I guess on something. Otherwise, they&#39;re just kind of You know, on the other side, we don&#39;t really hear from them. We don&#39;t really know them. 18:37 Lauren C.: Different area. But I don&#39;t think we even have to do anything with these. 18:37 Michael H.: Yes. 18:46 Lauren C.: Let&#39;s see. So if you click on, I guess it could show you instead of just viewing it. That might help. So up here at the top where it says customer This is where this came from. So Web EDI Mailer, that&#39;s the email address if you click on it. 18:58 Michael H.: Yes.
19:05 Lauren C.: It&#39;ll show you all of the requests that are from this email address. And if you hit view all You&#39;ll see everything that&#39;s come through. So for me, I&#39;m looking for If any other validation errors had popped up and if we just hit cancel on it If we did, I&#39;m just going to hit cancel on this one too. So clicking through the list and just scrolling until I see red. Which unfortunately I don&#39;t see any red. These will always be an unassigned. There really isn&#39;t a reason. Yep, canceled. There really isn&#39;t a reason for anyone to assign it. We don&#39;t do anything with it,
yeah. She just canceled it. Okay, perfect. So even though it has an error, not our problem unless the customer reaches out. 19:50 Lauren C.: They know that they need to know that they need to revisit and it gives them everything the document type, the reference number, and who it&#39;s for. So, and the message ID is right here too. So they have everything they need and it&#39;ll tell them like, oh, only specific characters are allowed. 20:00 Michael H.: Yes. 20:09 Lauren C.: They probably put something that wasn&#39;t valid there or some people will put a literally an extra space or like a decimal point and it&#39;s just not supposed to be there so they&#39;ll We&#39;ll figure it out. 20:19 Michael H.: Yes. 20:28 Lauren C.: This guy never answered me either. Yesterday, he emailed in, he&#39;s like, how do I submit an invoice for an old PO that&#39;s not here? Not on our side. 20:43 Lauren C.: Okay. So I showed him, I was like, hi, if it&#39;s not present, you&#39;re still able to generate a standalone document. Here&#39;s how you would do that. So file, new document, and then he would have his list to go from. I didn&#39;t know what account he was in. So this is actually from the test account. So if he comes back and says that&#39;s not how it looks. I can just say I am… This is from our side. This is just something that&#39;s generic so Some people are sticklers like that. Some people don&#39;t care. But I try to make sure to include what I think is in his account, which is this 810. So that&#39;s why my list is cut off because I did scroll down. But… Yeah, I don&#39;t. 21:20 Michael H.: Yes. 21:24 Lauren C.: I don&#39;t know who he is. You know what? We&#39;re here. Let&#39;s go look. I&#39;ll duplicate this tab because I do need to get back to them at some point. Customer. Larry is not a very common name. No, I don&#39;t know who that is. Oh, okay. 6481. Oh, here it is. Oh, so this one You see how his um Email says zollipops. 22:14 Michael H.: Yes. 22:18 Lauren C.: I&#39;m pretty sure when he signed stuff, it actually says that as well. Okay, nope, that was Eric&#39;s ticket. So that doesn&#39;t have anything on it. 22:36 Lauren C.: This is the name of his company. Liquid OTC. So it&#39;s completely different I&#39;m pretty sure his old emails used to even say like Zollipops at the bottom.
22:38 Michael H.: Okay.
22:45 Lauren C.: So this is one of those where he just uses something completely different in his email user But… he goes by something else in WebDI. So liquid. 22:47 Michael H.: Thank you. 23:02 Lauren C.: And then six, four, eight, one. So yeah, now I have a customer on here. And since that was yesterday, I can go ahead and follow up. Hi, Larry. I just wanted To circle back to see if you were able to generate your standalone invoice. 23:43 Lauren C.: And I honestly am going to close this one since he didn&#39;t respond. So basically, I just tell him, hi, circling back. I just wanted to see if you were able to and just input whatever it is. And then I just tell them, please don&#39;t hesitate to reach back out to us if you have any questions or need anything further. I hope you have Okay, wonderful day. Which is so extra, I know. They like it, so. 24:09 Michael H.: Yeah, definitely. 24:14 Lauren C.: So honestly, I&#39;ll let that sit for like five minutes and then I&#39;ll probably just close it. Larry is pretty good at responding. So if he doesn&#39;t need help, he just won&#39;t respond, which is something I&#39;ve learned with that one. 24:19 Michael H.: Yes. 24:29 Lauren C.: So, yeah. And that&#39;s usually how it is. Once they ghost us, they probably don&#39;t need help anymore, but it&#39;s always recommended to go ahead and
send that follow up. Just so you can give them the opportunity. There were a few people that were I don&#39;t remember what the issue was. They had a problem. 24:39 Michael H.: Yes. 24:48 Lauren C.: But they didn&#39;t give us all the information. And… So we reached out and we were like, hey, can you provide this? And I guess they were like. Oh, we weren&#39;t sure if her email was legit. But because they didn&#39;t respond for days, someone had closed the ticket Well, they ended up calling got completely escalated. And their issue was, well, no one followed up with us. 25:12 Lauren C.: So I try to make sure that yes. Sorry, Sandy asked me if I could get into ECSO2. 25:20 Michael H.: Yes. 25:20 Lauren C.: So, yeah, I&#39;m in here just fine.
25:28 Lauren C.: I&#39;m just making sure I can actually search something too. She&#39;s like, well, are you able to look at admin I&#39;m like, well, I can, yeah, admin&#39;s there. Okay. I&#39;m just showing her that I can open things. That&#39;s fun. She may just need to reconnect. Sometimes that happens. 26:09 Michael H.: Yeah. 26:14 Lauren C.: Okay, so for him, I&#39;m just going to close it. He&#39;ll let us know if he needs help. Oh, weird. Okay, she said that her and daniel which I believe Daniel&#39;s tier two on the project side. So like the setup team She said neither one of them
can log in there, which is unfortunate because That&#39;s where he works is on the server. 26:53 Michael H.: Mm-hmm. 27:03 Lauren C.: Alrighty, so. This one. 27:06 Michael H.: Just… Just a typical Friday where things… seems to not be working correctly. 27:12 Lauren C.: Yes. Speaking of, okay, so remember how I told you dev works overnight So their days are before our days. 27:21 Michael H.: Yes. 27:24 Lauren C.: They don&#39;t work tonight. It&#39;s Saturday for them. So nobody&#39;s here for that side tonight. 27:24 Michael H.: Yes. Yeah. 27:32 Lauren C.: So they&#39;ll be Sunday through Thursday. So if you need anything on a Friday, you&#39;re going to have to wait till Monday. Still go ahead and submit your ticket though. But just be aware you will not hear anything back. It won&#39;t get touched until Monday. 27:37 Michael H.: Yeah. 27:47 Lauren C.: That&#39;s always fun. Oh boy, someone&#39;s calling. I hope it&#39;s not a me call. For some reason, I still receive accounting calls. I don&#39;t know why. 27:59 Michael H.: Oh. I wonder why did they not like remove you or remove you 28:05 Lauren C.: When I got hired on, they asked me if I would help with that, which in the beginning I didn&#39;t really care. I was like, yeah, sure. But I still get those calls. Okay, someone&#39;s calling. So I just asked her, I was like, do you want to take over Michael and I&#39;ll take the call or do you want the call? 28:29 Lauren C.: I&#39;m fine either way. She is not responding. That&#39;s cool. That&#39;s fine. Um… Yeah, I don&#39;t know what he wants. I can&#39;t call him. Um… Oh, okay. She said she had stepped away for a minute. Okay, I&#39;ll go ahead and go take that call. Miss Sandy is ready. So if you want to hop over to her. 29:17 Michael H.: Okay, yes. 29:17 Lauren C.: And see what she wants to work on today.
View original transcript at Tactiq.</p>
