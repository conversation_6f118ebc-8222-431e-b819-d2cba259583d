# CS-44538: Missing Costco Orders

## Ticket Information
- **Key**: CS-44538
- **Status**: Resolved
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: <PERSON> H
- **Created**: 2025-05-29T11:30:07.986-0600
- **Updated**: 2025-06-04T14:26:15.796-0600
- **Customer**: Invent Inc

## Description
{color:#172B4D}Hi DataTrans Team,{color}


{color:#172B4D}We’re experiencing a technical issue with our trading partner, Costco. Since Friday, the orders we've received from Costco have not been reflecting in our ShipStation account.{color} 

{color:#172B4D}This has prevented our fulfillment team from shipping the orders, which has also led to late shipment violations on our account.{color}

{color:#172B4D}Could you please assist us in resolving this issue?{color}

{color:#172B4D}Our account number is 6729. Below are the affected orders:{color}

 *{color:#172B4D} *PO Number | Order Date | Ship To*{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/21/2025 | <PERSON><PERSON><PERSON>{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/21/2025 | <PERSON>{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/22/2025 | Samia Arshad{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/22/2025 | Barbara Debusschere{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/22/2025 | Josephine Palasigui{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/23/2025 | Raymond Mai{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/23/2025 | Mary Banks{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/23/2025 | Simon Sarr{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/23/2025 | Fakher Oueslati{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/24/2025 | Suzann Al-Qawasmi{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/24/2025 | Randa Alzin{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/25/2025 | Jose Herrick{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/25/2025 | Justen George{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/26/2025 | Michele Ali{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/26/2025 | Patricia Jetter{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/26/2025 | Rosalie Diop{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/26/2025 | Austin Golden{color}{color:#172B4D}{color}
{color:#172B4D}************** | 05/26/2025 | Aammar Khan{color}{color:#172B4D}{color}
{color:#172B4D}00847001965373 | 05/27/2025 | Jennah Al-Failakawi{color}{color:#172B4D}{color}
{color:#172B4D}00847001967438 | 05/27/2025 | Mohammad Aamir Abbasi{color}{color:#172B4D}{color}
{color:#172B4D}00847001972406 | 05/27/2025 | Andrew Fournier{color}{color:#172B4D}{color}
{color:#172B4D}00847001973945 | 05/27/2025 | David K. Reppucci{color}{color:#172B4D}{color}
{color:#172B4D}00847001979615 | 05/27/2025 | Nawal Boograin{color}{color:#172B4D}{color}
{color:#172B4D}00847002003246 | 05/28/2025 | Saif Mohammed{color}{color:#172B4D}{color}
{color:#172B4D}00847002010004 | 05/28/2025 | Shaharezade Ahmad-Malik{color}{color:#172B4D}{color}
{color:#172B4D}00847002024746 | 05/28/2025 | Aida Khalelova{color}{color:#172B4D}{color}
{color:#172B4D}00847002039805 | 05/28/2025 | Nicole Bowe{color}{adf}{"type":"expand","content":[{"type":"paragraph","content":[{"type":"text","text":"Best,  "},{"type":"hardBreak"},{"type":"text","text":"Melissa "},{"type":"hardBreak"},{"type":"text","text":" "}]},{"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"default"},"content":[{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph"}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":" "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Melissa Henry","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#073763"}}]}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"B2B Retail Manager","marks":[{"type":"textColor","attrs":{"color":"#222222"}}]}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":" "}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Email:","marks":[{"type":"textColor","attrs":{"color":"#222222"}}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"<EMAIL>","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#222222"}},{"type":"link","attrs":{"href":"mailto:<EMAIL>"}}]}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Website:","marks":[{"type":"textColor","attrs":{"color":"#222222"}}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"www.inventinc.io","marks":[{"type":"underline"},{"type":"strong"},{"type":"textColor","attrs":{"color":"#1155cc"}},{"type":"link","attrs":{"href":"http://www.inventinc.io"}}]},{"type":"text","text":" "},{"type":"hardBreak"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Address:","marks":[{"type":"textColor","attrs":{"color":"#222222"}}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Invent Inc, 1030 National Pkwy Schaumburg, IL 60173","marks":[{"type":"strong"},{"type":"textColor","attrs":{"color":"#222222"}}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"   "},{"type":"text","text":"The content of this email is confidential and intended for the recipient specified in message only. It is strictly forbidden to share any part of this message with any third party, without a written consent of the sender. If you received this message by mistake, please reply to this message and follow with its deletion, so that we can ensure such a mistake does not occur in the future","marks":[{"type":"textColor","attrs":{"color":"#222222"}}]},{"type":"text","text":" "}]}]}]}]}],"attrs":{"title":"Signature"}}{adf}

## Components


## Labels

