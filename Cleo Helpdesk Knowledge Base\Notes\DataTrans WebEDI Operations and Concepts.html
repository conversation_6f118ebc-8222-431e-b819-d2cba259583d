<p>{
&#34;name&#34;: &#34;DataTrans WebEDI / EDI Concepts&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;WebEDI Platform&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Features&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Cloud-based&#34;
},
{
&#34;name&#34;: &#34;Easy to Use&#34;
},
{
&#34;name&#34;: &#34;Web Portal&#34;
},
{
&#34;name&#34;: &#34;Integration Capabilities&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Shipping Services&#34;
},
{
&#34;name&#34;: &#34;Accounting Software&#34;
},
{
&#34;name&#34;: &#34;ERP Systems&#34;
},
{
&#34;name&#34;: &#34;Warehouse Management&#34;
},
{
&#34;name&#34;: &#34;API&#34;
}
]
},
{
&#34;name&#34;: &#34;Catalog Management&#34;
},
{
&#34;name&#34;: &#34;Auto Pack ASN&#34;
},
{
&#34;name&#34;: &#34;Document Defaults&#34;
},
{
&#34;name&#34;: &#34;Batch Processing&#34;
},
{
&#34;name&#34;: &#34;Graphical Reports/Dashboards&#34;
},
{
&#34;name&#34;: &#34;User Management (myWebEDI)&#34;
},
{
&#34;name&#34;: &#34;Adding Trading Partners&#34;
},
{
&#34;name&#34;: &#34;Form Validation&#34;
}
]
},
{
&#34;name&#34;: &#34;User Interface&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Inbox (New Documents)&#34;
},
{
&#34;name&#34;: &#34;Draft Folder (Working Documents)&#34;
},
{
&#34;name&#34;: &#34;Sent Folder (Sent Documents)&#34;
},
{
&#34;name&#34;: &#34;Smart Folders (Organized by Criteria)&#34;
},
{
&#34;name&#34;: &#34;Archive (Older Documents)&#34;
},
{
&#34;name&#34;: &#34;Active Messages&#34;
},
{
&#34;name&#34;: &#34;Incoming Message Details&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Message ID&#34;
},
{
&#34;name&#34;: &#34;Document Type&#34;
},
{
&#34;name&#34;: &#34;Trading Partner&#34;
},
{
&#34;name&#34;: &#34;Reference/PO Number&#34;
},
{
&#34;name&#34;: &#34;Received Date&#34;
},
{
&#34;name&#34;: &#34;Expected Ship Date&#34;
},
{
&#34;name&#34;: &#34;Total Amount&#34;
},
{
&#34;name&#34;: &#34;Store Number&#34;
},
{
&#34;name&#34;: &#34;Fulfillment Cycle (Document)&#34;
}
]
},
{
&#34;name&#34;: &#34;Sorting Columns&#34;
},
{
&#34;name&#34;: &#34;Searching Documents&#34;
},
{
&#34;name&#34;: &#34;Priority Flags&#34;
},
{
&#34;name&#34;: &#34;Expandable Rows&#34;
},
{
&#34;name&#34;: &#34;Alert Tabs&#34;
},
{
&#34;name&#34;: &#34;Viewing Documents (Paper-like View)&#34;
}
]
},
{
&#34;name&#34;: &#34;Document Creation/Response&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Respond Button&#34;
},
{
&#34;name&#34;: &#34;Automatic Pre-population&#34;
},
{
&#34;name&#34;: &#34;Editing Quantities/Line Items&#34;
},
{
&#34;name&#34;: &#34;Creating from Source Document (e.g., PO)&#34;
},
{
&#34;name&#34;: &#34;Manually Creating New Document&#34;
}
]
}
]
},
{
&#34;name&#34;: &#34;Common EDI Documents&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Transportation&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;204 Motor Carrier Load Tender&#34;
},
{
&#34;name&#34;: &#34;210 Motor Carrier Freight Details/Invoice&#34;
},
{
&#34;name&#34;: &#34;211 Motor Carrier Bill of Lading&#34;
},
{
&#34;name&#34;: &#34;214 Transportation Carrier Shipment Status&#34;
},
{
&#34;name&#34;: &#34;300 Reservations Booking Request (Ocean)&#34;
},
{
&#34;name&#34;: &#34;301 Booking Confirmation (Ocean)&#34;
},
{
&#34;name&#34;: &#34;310 Freight Receipt/Invoice (Ocean)&#34;
},
{
&#34;name&#34;: &#34;315 Status Details (Ocean)&#34;
}
]
},
{
&#34;name&#34;: &#34;Healthcare&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;270 Eligibility/Benefit Inquiry&#34;
},
{
&#34;name&#34;: &#34;271 Eligibility/Benefit Response&#34;
},
{
&#34;name&#34;: &#34;276 Claim Status Request&#34;
},
{
&#34;name&#34;: &#34;277 Claim Status Notification&#34;
},
{
&#34;name&#34;: &#34;278 Service Review Information&#34;
},
{
&#34;name&#34;: &#34;834 Benefit Enrollment/Maintenance&#34;
},
{
&#34;name&#34;: &#34;835 Claim Payment/Advice&#34;
},
{
&#34;name&#34;: &#34;837 Claim Transaction&#34;
}
]
},
{
&#34;name&#34;: &#34;Supply Chain/General Business&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;753 Request for Routing Instructions&#34;
},
{
&#34;name&#34;: &#34;754 Routing Instructions&#34;
},
{
&#34;name&#34;: &#34;810 Invoice&#34;
},
{
&#34;name&#34;: &#34;811 Consolidated Service Invoice/Statement&#34;
},
{
&#34;name&#34;: &#34;812 Credit/Debit Adjustment&#34;
},
{
&#34;name&#34;: &#34;814 General Request/Response/Confirmation&#34;
},
{
&#34;name&#34;: &#34;816 Organizational Relationships&#34;
},
{
&#34;name&#34;: &#34;819 Joint Interest Billing/Operating Expense&#34;
},
{
&#34;name&#34;: &#34;820 Payment Order/Remittance Advice&#34;
},
{
&#34;name&#34;: &#34;824 Application Advice&#34;
},
{
&#34;name&#34;: &#34;830 Planning Schedule with Release Capability&#34;
},
{
&#34;name&#34;: &#34;832 Price/Sales Catalog&#34;
},
{
&#34;name&#34;: &#34;840 Request for Quotation&#34;
},
{
&#34;name&#34;: &#34;843 Response to Request for Quotation&#34;
},
{
&#34;name&#34;: &#34;844 Product Transfer Account Adjustment&#34;
},
{
&#34;name&#34;: &#34;845 Price Authorization Acknowledgment/Status&#34;
},
{
&#34;name&#34;: &#34;846 Inventory Inquiry/Advice&#34;
},
{
&#34;name&#34;: &#34;849 Response to Product Transfer Adjustment&#34;
},
{
&#34;name&#34;: &#34;850 Purchase Order (Retail)&#34;
},
{
&#34;name&#34;: &#34;852 Product Activity Data&#34;
},
{
&#34;name&#34;: &#34;853 Routing and Carrier Instruction&#34;
},
{
&#34;name&#34;: &#34;855 Purchase Order Acknowledgment&#34;
},
{
&#34;name&#34;: &#34;856 Advanced Shipment Notice (ASN)&#34;
},
{
&#34;name&#34;: &#34;857 Shipment and Billing Notice&#34;
},
{
&#34;name&#34;: &#34;860 Purchase Order Change (Buyer)&#34;
},
{
&#34;name&#34;: &#34;861 Receiving Advice/Acceptance Certificate&#34;
},
{
&#34;name&#34;: &#34;862 Shipping Schedule&#34;
},
{
&#34;name&#34;: &#34;864 Text Message&#34;
},
{
&#34;name&#34;: &#34;865 Purchase Order Change (Seller)&#34;
},
{
&#34;name&#34;: &#34;866 Production Sequence&#34;
},
{
&#34;name&#34;: &#34;867 Product Transfer and Resale Report&#34;
},
{
&#34;name&#34;: &#34;869 Order Status Inquiry&#34;
},
{
&#34;name&#34;: &#34;870 Order Status Report&#34;
},
{
&#34;name&#34;: &#34;875 Grocery Products Purchase Order (Grocery)&#34;
},
{
&#34;name&#34;: &#34;876 Grocery Products Purchase Order Change&#34;
},
{
&#34;name&#34;: &#34;879 Price Information&#34;
},
{
&#34;name&#34;: &#34;880 Grocery Products Invoice&#34;
},
{
&#34;name&#34;: &#34;888 Item Maintenance&#34;
},
{
&#34;name&#34;: &#34;889 Promotion Announcement&#34;
},
{
&#34;name&#34;: &#34;894 Delivery/Return Base Record&#34;
},
{
&#34;name&#34;: &#34;895 Delivery/Return Acknowledgement/Adjustment&#34;
},
{
&#34;name&#34;: &#34;940 Warehouse Shipping Order&#34;
},
{
&#34;name&#34;: &#34;943 Warehouse Stock Transfer Shipment Advice&#34;
},
{
&#34;name&#34;: &#34;944 Warehouse Stock Transfer Receipt Advice&#34;
},
{
&#34;name&#34;: &#34;945 Warehouse Shipping Advice&#34;
},
{
&#34;name&#34;: &#34;947 Warehouse Inventory Adjustment Advice&#34;
},
{
&#34;name&#34;: &#34;990 Response to a Load Tender&#34;
},
{
&#34;name&#34;: &#34;997 Functional Acknowledgment&#34;
},
{
&#34;name&#34;: &#34;APERAK Application Error/Acknowledgement&#34;
},
{
&#34;name&#34;: &#34;DELFOR Delivery Schedule&#34;
},
{
&#34;name&#34;: &#34;DELJIT Delivery Just In Time&#34;
},
{
&#34;name&#34;: &#34;DESADV Despatch Advice&#34;
},
{
&#34;name&#34;: &#34;INVOIC Invoice&#34;
},
{
&#34;name&#34;: &#34;INVRPT Inventory Report&#34;
},
{
&#34;name&#34;: &#34;ORDERS Purchase Order&#34;
},
{
&#34;name&#34;: &#34;ORDERSP Purchase Order Acknowledgment&#34;
},
{
&#34;name&#34;: &#34;RECADV Receiving Advice&#34;
},
{
&#34;name&#34;: &#34;UCC-128 Label (GS1-128)&#34;
}
]
}
]
},
{
&#34;name&#34;: &#34;Troubleshooting/Operations&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Tickets&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Creating Tickets&#34;
},
{
&#34;name&#34;: &#34;Identifying Issue&#34;
},
{
&#34;name&#34;: &#34;Document Details&#34;
},
{
&#34;name&#34;: &#34;Control Number&#34;
},
{
&#34;name&#34;: &#34;Invoice Number&#34;
},
{
&#34;name&#34;: &#34;Who Spoke To&#34;
},
{
&#34;name&#34;: &#34;Attempted Actions&#34;
},
{
&#34;name&#34;: &#34;Customer Communication&#34;
},
{
&#34;name&#34;: &#34;Viewing in Progress&#34;
},
{
&#34;name&#34;: &#34;Assigning Tickets&#34;
},
{
&#34;name&#34;: &#34;Closing Tickets (Resolve)&#34;
},
{
&#34;name&#34;: &#34;Reopening Tickets&#34;
},
{
&#34;name&#34;: &#34;Searching Past Tickets (Account Number)&#34;
},
{
&#34;name&#34;: &#34;Linking/Referencing Tickets&#34;
}
]
},
{
&#34;name&#34;: &#34;WebEDI Portal Issues&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Rejected Invoice Status&#34;
},
{
&#34;name&#34;: &#34;ASN Errors&#34;
},
{
&#34;name&#34;: &#34;Large Quantity Issue (Contact Support)&#34;
},
{
&#34;name&#34;: &#34;Label Issues&#34;
},
{
&#34;name&#34;: &#34;Failed Status Error&#34;
},
{
&#34;name&#34;: &#34;Locating Message ID&#34;
},
{
&#34;name&#34;: &#34;Searching in Portal (Message ID, Message)&#34;
},
{
&#34;name&#34;: &#34;Opening Documents (Double Click)&#34;
},
{
&#34;name&#34;: &#34;Invoice Zero Totals&#34;
},
{
&#34;name&#34;: &#34;Permissions Issues (Inbox/Visibility)&#34;
},
{
&#34;name&#34;: &#34;Admin Login (Check Visibility)&#34;
}
]
},
{
&#34;name&#34;: &#34;EDI Document Issues&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Rejected Invoice&#34;
},
{
&#34;name&#34;: &#34;Failed 856 (ASN)&#34;
},
{
&#34;name&#34;: &#34;Incorrect Packing Details (856)&#34;
},
{
&#34;name&#34;: &#34;Blank Lines in Packing&#34;
},
{
&#34;name&#34;: &#34;Empty Item Details&#34;
},
{
&#34;name&#34;: &#34;Customer Needs to Confirm Packaging/Quantity&#34;
},
{
&#34;name&#34;: &#34;Recreating Document&#34;
},
{
&#34;name&#34;: &#34;Customer Not Filling Required Fields (Blue)&#34;
},
{
&#34;name&#34;: &#34;Editing Packing&#34;
},
{
&#34;name&#34;: &#34;Deleting Packs&#34;
},
{
&#34;name&#34;: &#34;Verifying with Purchase Order (850)&#34;
},
{
&#34;name&#34;: &#34;Confirming Quantity Ordered&#34;
},
{
&#34;name&#34;: &#34;Partial Shipment/ASN&#34;
},
{
&#34;name&#34;: &#34;New ASN Creation&#34;
},
{
&#34;name&#34;: &#34;Pick and Pack&#34;
},
{
&#34;name&#34;: &#34;Number of Cartons/Boxes&#34;
},
{
&#34;name&#34;: &#34;Packing Item/Quantity&#34;
},
{
&#34;name&#34;: &#34;Copying Packs&#34;
},
{
&#34;name&#34;: &#34;Setting Document Defaults&#34;
},
{
&#34;name&#34;: &#34;Printing Labels (UCC-128)&#34;
},
{
&#34;name&#34;: &#34;Label Content/Specs (Varies)&#34;
},
{
&#34;name&#34;: &#34;Accessing Labels (Batches)&#34;
},
{
&#34;name&#34;: &#34;Emailing Labels (User Setup)&#34;
},
{
&#34;name&#34;: &#34;Confirming ASN Details&#34;
},
{
&#34;name&#34;: &#34;Confirming Quantity Packing&#34;
},
{
&#34;name&#34;: &#34;Customer Needs to Save&#34;
}
]
},
{
&#34;name&#34;: &#34;EDI File Processing&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Functional Acknowledgment (997)&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Auto Generated&#34;
},
{
&#34;name&#34;: &#34;Invalid Segment (TA1)&#34;
},
{
&#34;name&#34;: &#34;Unexpected TA1 Segment&#34;
},
{
&#34;name&#34;: &#34;Comparing 997s (Different Clients)&#34;
},
{
&#34;name&#34;: &#34;Response Based on Source (875)&#34;
},
{
&#34;name&#34;: &#34;Comparing Source Documents (875s)&#34;
},
{
&#34;name&#34;: &#34;Identifying Differences (Envelope)&#34;
},
{
&#34;name&#34;: &#34;Acknowledgement Type (ISA14)&#34;
},
{
&#34;name&#34;: &#34;Value of Zero vs One (Acknowledgement)&#34;
},
{
&#34;name&#34;: &#34;Testing with Modified Inbound Document&#34;
},
{
&#34;name&#34;: &#34;Copying to New Batch&#34;
},
{
&#34;name&#34;: &#34;Editing Data (Acknowledgement Value)&#34;
},
{
&#34;name&#34;: &#34;Injecting into WebDI&#34;
},
{
&#34;name&#34;: &#34;Generating 997 (Expected Result)&#34;
},
{
&#34;name&#34;: &#34;Trading Partner Needs to Correct Inbound&#34;
},
{
&#34;name&#34;: &#34;Invalid Interchange Acknowledgement&#34;
},
{
&#34;name&#34;: &#34;Fixing Problem (ISA14)&#34;
}
]
},
{
&#34;name&#34;: &#34;Delimiters&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Segment Terminator&#34;
},
{
&#34;name&#34;: &#34;Element Separator (Asterisk)&#34;
},
{
&#34;name&#34;: &#34;Sub-Element Separator&#34;
},
{
&#34;name&#34;: &#34;Repeat Character&#34;
}
]
},
{
&#34;name&#34;: &#34;Envelope&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;ISA (Interchange Control Header)&#34;
},
{
&#34;name&#34;: &#34;GS (Functional Group Header)&#34;
},
{
&#34;name&#34;: &#34;Who is Sender/Receiver&#34;
},
{
&#34;name&#34;: &#34;Control Number&#34;
},
{
&#34;name&#34;: &#34;Version of Document&#34;
},
{
&#34;name&#34;: &#34;Testing Acknowledgement Type&#34;
}
]
},
{
&#34;name&#34;: &#34;Inbound Files&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Source (Lauren Data)&#34;
},
{
&#34;name&#34;: &#34;Injects into Server&#34;
},
{
&#34;name&#34;: &#34;Target (Rabbit MQ WebDI Injection)&#34;
},
{
&#34;name&#34;: &#34;Triggers Injection to Portal&#34;
},
{
&#34;name&#34;: &#34;Separating Multiple Orders in Batch&#34;
},
{
&#34;name&#34;: &#34;Injected Files&#34;
},
{
&#34;name&#34;: &#34;Deleting Injected Files&#34;
},
{
&#34;name&#34;: &#34;Inbound Working (UM Products Example)&#34;
}
]
},
{
&#34;name&#34;: &#34;Outbound Files&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Auto Send Feature&#34;
},
{
&#34;name&#34;: &#34;Partner Configurations&#34;
},
{
&#34;name&#34;: &#34;Admin Settings (Partner Configs)&#34;
},
{
&#34;name&#34;: &#34;Config Name List&#34;
},
{
&#34;name&#34;: &#34;Show Usage (Search by Customer Number)&#34;
},
{
&#34;name&#34;: &#34;Enabled/Disabled (Value 1/0)&#34;
},
{
&#34;name&#34;: &#34;Adding Config&#34;
},
{
&#34;name&#34;: &#34;Disabling Auto Send (Account Level)&#34;
},
{
&#34;name&#34;: &#34;Impact on Other Trading Partners&#34;
},
{
&#34;name&#34;: &#34;Manual Sending (Portal)&#34;
},
{
&#34;name&#34;: &#34;Creating Drafts (When Auto Send Disabled)&#34;
},
{
&#34;name&#34;: &#34;Configuring Account (Disable Auto Send)&#34;
}
]
},
{
&#34;name&#34;: &#34;Server/File Transfer&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Reconnecting Servers&#34;
},
{
&#34;name&#34;: &#34;Refreshing Server&#34;
},
{
&#34;name&#34;: &#34;Drives (D and T)&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;D Drive (Hard Drive Specific/Server Specific)&#34;
},
{
&#34;name&#34;: &#34;T Drive (Universal/Shared)&#34;
}
]
},
{
&#34;name&#34;: &#34;Locating Inbound Files (Step 1 Folder)&#34;
},
{
&#34;name&#34;: &#34;Checking Processed Files&#34;
},
{
&#34;name&#34;: &#34;Server Migration&#34;
},
{
&#34;name&#34;: &#34;SFTP&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Picking Up/Dropping Files&#34;
},
{
&#34;name&#34;: &#34;Scheduler&#34;
},
{
&#34;name&#34;: &#34;Services (FDTS)&#34;
},
{
&#34;name&#34;: &#34;Disabling Services&#34;
},
{
&#34;name&#34;: &#34;Testing Connection&#34;
},
{
&#34;name&#34;: &#34;Credentials/Script&#34;
},
{
&#34;name&#34;: &#34;From DTS Location&#34;
},
{
&#34;name&#34;: &#34;Checking Folder for Files&#34;
},
{
&#34;name&#34;: &#34;Restaging Files&#34;
},
{
&#34;name&#34;: &#34;Test Files&#34;
},
{
&#34;name&#34;: &#34;Running Scheduler&#34;
},
{
&#34;name&#34;: &#34;Files Not Moving&#34;
},
{
&#34;name&#34;: &#34;Error Files&#34;
},
{
&#34;name&#34;: &#34;WinSCP Client&#34;
},
{
&#34;name&#34;: &#34;File Explorer&#34;
},
{
&#34;name&#34;: &#34;Target Path Issue (Dclient)&#34;
},
{
&#34;name&#34;: &#34;Backup Drive&#34;
},
{
&#34;name&#34;: &#34;Timing Issue&#34;
},
{
&#34;name&#34;: &#34;Schedule Task Issue&#34;
},
{
&#34;name&#34;: &#34;User Setting (System)&#34;
},
{
&#34;name&#34;: &#34;Hitting Play Button (Task Scheduler)&#34;
},
{
&#34;name&#34;: &#34;Overwriting Settings&#34;
},
{
&#34;name&#34;: &#34;Running Schedule Task&#34;
},
{
&#34;name&#34;: &#34;Checking Logs (DTS Logs)&#34;
},
{
&#34;name&#34;: &#34;Permissions Issue (Potential)&#34;
},
{
&#34;name&#34;: &#34;Naming Convention (Potential)&#34;
},
{
&#34;name&#34;: &#34;Checking Inbound/Outbound Status&#34;
},
{
&#34;name&#34;: &#34;Accessing History/Backup Folder&#34;
}
]
}
]
}
]
},
{
&#34;name&#34;: &#34;Mapping/Customization&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;QuickBooks Integration&#34;
},
{
&#34;name&#34;: &#34;Customization Reversed (Blank Lines/Tracking)&#34;
},
{
&#34;name&#34;: &#34;Development Tickets (Jira)&#34;
},
{
&#34;name&#34;: &#34;Searching Jira (Customer Name)&#34;
},
{
&#34;name&#34;: &#34;Customization in Integrator&#34;
},
{
&#34;name&#34;: &#34;Requesting Examples (Screenshot from QB, Message ID/Invoice Number from WebEDI)&#34;
},
{
&#34;name&#34;: &#34;Seeing Segments/Elements Affected&#34;
},
{
&#34;name&#34;: &#34;Raw Data&#34;
},
{
&#34;name&#34;: &#34;Talking to Developer&#34;
},
{
&#34;name&#34;: &#34;Hardcoding Data&#34;
},
{
&#34;name&#34;: &#34;Document Defaults&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Remit To Address&#34;
},
{
&#34;name&#34;: &#34;Company Name&#34;
},
{
&#34;name&#34;: &#34;Dun&#39;s Number&#34;
},
{
&#34;name&#34;: &#34;Address&#34;
},
{
&#34;name&#34;: &#34;Terms of Sale&#34;
},
{
&#34;name&#34;: &#34;Description&#34;
},
{
&#34;name&#34;: &#34;Discount Amount&#34;
},
{
&#34;name&#34;: &#34;Method Payments/Origin Shipping Point&#34;
},
{
&#34;name&#34;: &#34;Invoice Shipment Summary (ISS Segment)&#34;
},
{
&#34;name&#34;: &#34;Unit of Measure&#34;
},
{
&#34;name&#34;: &#34;Weight&#34;
},
{
&#34;name&#34;: &#34;Allow Default Switch (DOM)&#34;
},
{
&#34;name&#34;: &#34;Reflecting in Web Portal&#34;
},
{
&#34;name&#34;: &#34;Keying in Value&#34;
},
{
&#34;name&#34;: &#34;Saving Default&#34;
},
{
&#34;name&#34;: &#34;When Not to Set Default (Varies)&#34;
}
]
},
{
&#34;name&#34;: &#34;DOM (Global Dump)&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Editing Cautions (Affects Multiple Customers)&#34;
},
{
&#34;name&#34;: &#34;Risk of File Failure (Editing Mode)&#34;
},
{
&#34;name&#34;: &#34;Checking with Manager Before Changes&#34;
},
{
&#34;name&#34;: &#34;Integrated Maps (Linked with Links Map)&#34;
},
{
&#34;name&#34;: &#34;Source/Target Maps&#34;
},
{
&#34;name&#34;: &#34;Tedious Maps (Integrated, Global Shop, Milcup)&#34;
},
{
&#34;name&#34;: &#34;Creating Clones (Specific Customizations)&#34;
},
{
&#34;name&#34;: &#34;Work Authorization (Integrated Customers)&#34;
},
{
&#34;name&#34;: &#34;Identifying Segment (Export Document, Check Raw Data)&#34;
},
{
&#34;name&#34;: &#34;Allow Default Switch&#34;
},
{
&#34;name&#34;: &#34;Hardcoding in Global DOM&#34;
},
{
&#34;name&#34;: &#34;Saving Changes&#34;
},
{
&#34;name&#34;: &#34;Refreshing WebEDI After DOM Update&#34;
},
{
&#34;name&#34;: &#34;Recreating Documents After DOM Changes&#34;
}
]
},
{
&#34;name&#34;: &#34;Mapping Rules&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Setting Up Mapping Groups&#34;
},
{
&#34;name&#34;: &#34;Mapping PO Number to Invoice Number&#34;
},
{
&#34;name&#34;: &#34;Populating on Response Document&#34;
},
{
&#34;name&#34;: &#34;Source Document (850/875)&#34;
},
{
&#34;name&#34;: &#34;Target Document (810)&#34;
},
{
&#34;name&#34;: &#34;Autopopulating Fields&#34;
},
{
&#34;name&#34;: &#34;Saving Manual Entry&#34;
},
{
&#34;name&#34;: &#34;Creating Rules (Source/Target Segments)&#34;
},
{
&#34;name&#34;: &#34;Testing Mapping Rule (Create Response)&#34;
},
{
&#34;name&#34;: &#34;Screenshotting Proof&#34;
}
]
},
{
&#34;name&#34;: &#34;Notepad++ (Viewing/Editing Files)&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Making Files Look Pretty&#34;
},
{
&#34;name&#34;: &#34;Converting Files&#34;
},
{
&#34;name&#34;: &#34;Identifying Delimiters/Separators&#34;
},
{
&#34;name&#34;: &#34;Comparing Files&#34;
},
{
&#34;name&#34;: &#34;Checking Envelope/Body&#34;
},
{
&#34;name&#34;: &#34;Finding Differences&#34;
},
{
&#34;name&#34;: &#34;Manipulating Inbound Document (Testing)&#34;
},
{
&#34;name&#34;: &#34;Editing Data&#34;
},
{
&#34;name&#34;: &#34;Working on Desktop (Not Server)&#34;
},
{
&#34;name&#34;: &#34;Converting/Saving Files&#34;
}
]
},
{
&#34;name&#34;: &#34;Labels&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Printing UCC 128&#34;
},
{
&#34;name&#34;: &#34;No Specialty Printer Needed&#34;
},
{
&#34;name&#34;: &#34;Automatically Generated per Pack&#34;
},
{
&#34;name&#34;: &#34;Additional Shipping Documents (Packing Slips, BOL, etc.)&#34;
},
{
&#34;name&#34;: &#34;Accessing Printed Labels&#34;
},
{
&#34;name&#34;: &#34;Scanning Labels (Warehouse)&#34;
},
{
&#34;name&#34;: &#34;Customer Printing In House (vs. DataTrans Setup)&#34;
},
{
&#34;name&#34;: &#34;Charging for Labels (Sales/Development Ticket)&#34;
}
]
}
]
}
]
},
{
&#34;name&#34;: &#34;Customer Interaction&#34;,
&#34;children&#34;: [
{
&#34;name&#34;: &#34;Notified by Email (New Document)&#34;
},
{
&#34;name&#34;: &#34;Calls/Meetings (Zoom, Google Meets, Slack)&#34;
},
{
&#34;name&#34;: &#34;Receiving Information&#34;
},
{
&#34;name&#34;: &#34;Asking for Examples/Screenshots&#34;
},
{
&#34;name&#34;: &#34;Explaining Processes&#34;
},
{
&#34;name&#34;: &#34;Guiding Customer&#34;
},
{
&#34;name&#34;: &#34;Setting Expectations (Ticket Time)&#34;
},
{
&#34;name&#34;: &#34;Identifying Urgency (Holding Production)&#34;
},
{
&#34;name&#34;: &#34;Multitasking&#34;
},
{
&#34;name&#34;: &#34;Working with Specific Contacts&#34;
},
{
&#34;name&#34;: &#34;Handling Demanding Customers&#34;
},
{
&#34;name&#34;: &#34;Requesting Additional Information&#34;
}
]
}
]
}</p>
