# CS-45203: BatchId not Injected

## Ticket Information
- **Key**: CS-45203
- **Status**: Waiting for support
- **Priority**: Medium
- **Assignee**: Unassigned
- **Reporter**: <EMAIL>
- **Created**: 2025-06-10T11:16:04.741-0600
- **Updated**: 2025-06-10T11:16:04.903-0600
- **Customer**: PKid

## Description
| |{color:gray}BatchId not Injected: 856{color}{color:gray}{color} | 
{color:white}
{color}||{color:white}Date{color}{color:white} {color}||{color:white}Time{color}{color:white} {color}||{color:white}Batchid{color}{color:white} {color}||{color:white}Server{color}{color:white} {color}||{color:white}CustomerPKid{color}{color:white} {color}||{color:white}TPPKid{color}{color:white} {color}||{color:white}Control Number{color}{color:white} {color}||{color:white}DOMROOT{color}{color:white} {color}||{color:white}Error{color}{color:white} {color}| 
|06/10/2025 |12:15 PM CDT |856 |DTS-ECS-03 |3369 |175 |10060 |2267453498 |Error Inserting Segment/Element: FOB*PO please verify DOM rootnode: 2267453498 |

## Components


## Labels

