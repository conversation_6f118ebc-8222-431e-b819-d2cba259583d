<h3 id="my-meeting">My Meeting</h3>
<p>Meeting started: May 16, 2025, 10:46:00 AM Meeting duration: 138 minutes Meeting participants: <PERSON>, <PERSON>, <PERSON>
View original transcript at Tactiq.</p>
<h4 id="generated-content">Generated Content</h4>
<h3 id="current-work-overview">Current Work Overview</h3>
<ol>
<li><strong>Ticket Management</strong>    - <strong>Ticket Review Process</strong>      - Five to six tickets currently open.      - Ongoing collaboration with <PERSON> for ticket resolution.</li>
<li><strong>Specific Tickets Addressed</strong>    - <strong>Ticket 1: Mr. <PERSON> (Filtering Issue)</strong>      - Issue: Broken filter logic for current day.      - Action: Escalated to development team for resolution.</li>
</ol>
<ul>
<li><strong>Ticket 2: <PERSON> (Shipping Charge Issue)</strong>      - Issue: Unable to add shipping charge on invoice due to incomplete SAC fields.      - Action: Reviewed invoice details and contacted for SAC code guidance.</li>
<li><strong>Ticket 3: <PERSON> (Missing 875 Document)</strong>      - Issue: Email notification received, but document not visible.      - Action: Suggested client refresh and verify sorting settings.</li>
<li><strong>Ticket 4: <PERSON> (Missing SHIT2 on EDI 850)</strong></li>
<li>Issue: Missing information on generated response.      - Action: Awaiting clarification on account and document type.</li>
<li><strong>Ticket 5: Larry (Invoice Inquiry)</strong>      - Issue: Question about generating a standalone invoice.      - Action: Provided guidance and confirmed account details.</li>
</ul>
<ol start="3">
<li><strong>Server Issues</strong>    - <strong>BCSO Server Down</strong>      - Noted problems with server affecting ticket processing.      - IT department required to restart servers.</li>
<li><strong>Communication and Collaboration</strong>    - Ongoing discussions with team members regarding ticket statuses and server issues.    - Use of Slack for real-time updates and communication.</li>
</ol>
<h3 id="key-concepts-ticket-management-process-of-tracking-and-resolving-customer-issues-sac-fields-specific-fields-required-for-certain-transactions-in-the-system-server-downtime-impact-on-ticket-processing-and-communication-with-clients">Key Concepts - <strong>Ticket Management</strong>: Process of tracking and resolving customer issues. - <strong>SAC Fields</strong>: Specific fields required for certain transactions in the system. - <strong>Server Downtime</strong>: Impact on ticket processing and communication with clients.</h3>
<h3 id="thought-provoking-questions-how-can-we-improve-the-ticket-resolution-process-to-minimize-open-tickets-what-preventive-measures-can-be-implemented-to-avoid-server-downtime">Thought-Provoking Questions - How can we improve the ticket resolution process to minimize open tickets? - What preventive measures can be implemented to avoid server downtime?</h3>
<h3 id="real-world-applications-effective-ticket-management-systems-are-crucial-in-customer-service-industries-to-maintain-client-satisfaction">Real-World Applications - Effective ticket management systems are crucial in customer service industries to maintain client satisfaction.</h3>
<h3 id="areas-for-further-research-best-practices-in-ticket-management-and-resolution">Areas for Further Research - Best practices in ticket management and resolution.</h3>
<ul>
<li>Strategies for server maintenance and uptime optimization.</li>
</ul>
<h3 id="potential-exam-questions-describe-the-steps-taken-to-resolve-a-ticket-related-to-filtering-issues-explain-the-importance-of-sac-fields-in-invoice-processing">Potential Exam Questions - Describe the steps taken to resolve a ticket related to filtering issues. - Explain the importance of SAC fields in invoice processing.</h3>
<h3 id="glossary-sac-fields-specific-fields-required-for-transaction-processing-edi-electronic-data-interchange-a-system-for-exchanging-business-documents">Glossary - <strong>SAC Fields</strong>: Specific fields required for transaction processing. - <strong>EDI</strong>: Electronic Data Interchange, a system for exchanging business documents.</h3>
<h3 id="summary-the-current-focus-is-on-resolving-open-tickets-related-to-customer-inquiries-and-addressing-server-issues-that-impact-processing-collaboration-among-team-members-is-essential-for-effective-resolution">Summary The current focus is on resolving open tickets related to customer inquiries and addressing server issues that impact processing. Collaboration among team members is essential for effective resolution.</h3>
<h4 id="generated-content-2">Generated Content</h4>
<p>Lauren is involved in troubleshooting issues related to the ECSO2 server and the processing of outbound files. She is communicating with Sandy and Michael about problems with the server, specifically that it has not sent any batches for over 90 minutes, which indicates a potential server issue. Lauren is also addressing customer concerns about missing purchase orders (POs) and is trying to assist in stopping an outbound file to prevent penalties for incorrect timing. She is sharing her screen to provide visibility into the situation and checking the input channels for the WebEDI system.</p>
<h4 id="generated-content-3">Generated Content</h4>
<p>Yinching from Ford was mentioned as potentially facing a penalty if the time for the sent documents was incorrect.</p>
<h4 id="generated-content-4">Generated Content</h4>
<p>To respond to Dylan on behalf of Sandy, you can use the following template:
<strong>Subject:</strong> Re: Inquiry About A10 and A856 Transmissions
Hi Dylan,
Thank you for reaching out regarding your A10 and A856 transmissions.
I wanted to clarify that it appears you are currently not using the integration for your A10 and A856 documents, as the last activity we have recorded was on March 25th. If you are creating these documents manually in WebEDI, please note that they will not send automatically; you will need to hit &#34;send&#34; for each document.
If you would like to discuss further or need assistance with anything else, please feel free to reach out.
Best regards,
[Your Name]  [Your Position]  [Your Contact Information]
Feel free to customize the message as needed!</p>
<h4 id="generated-content-5">Generated Content</h4>
<h3 id="meeting-notes">Meeting Notes</h3>
<h4 id="1-ticket-review">1. <strong>Ticket Review</strong></h4>
<ul>
<li><strong>Ticket 1: Mr. Mike</strong>      - <strong>Issue:</strong> Filtering information from May 14th to 16th was broken.      - <strong>Action Taken:</strong> Reproduced the issue; escalated to development team for fixing filter logic.</li>
<li><strong>Ticket 2: Michelle Sandro (Toll Warehouse)</strong>      - <strong>Issue:</strong> Failed to add a shipping charge on 810 invoice due to incomplete SAC fields.      - <strong>Action Taken:</strong> Reviewed 810 invoice; logged details for follow-up; contacted for SAC code guidance.</li>
<li><strong>Ticket 3: Dave (Dolores Canning)</strong>      - <strong>Issue:</strong> Missing 875 document despite email notification.      - <strong>Action Taken:</strong> Marked ticket as unread; advised client to refresh inbox and verify sorting.</li>
<li><strong>Ticket 4: Valerie (Allegheny Health)</strong>      - <strong>Issue:</strong> Missing SHIT2 on EDI 850; issues with 855 and 810 invoices.      - <strong>Action Taken:</strong> Reviewed 850 and 855 lists; awaiting clarification on account and document type.</li>
<li><strong>Ticket 5: Larry (Liquid OTC)</strong>      - <strong>Issue:</strong> Inquiry about generating a standalone invoice.      - <strong>Action Taken:</strong> Provided guidance and screenshots; awaiting confirmation for ticket closure.</li>
</ul>
<h4 id="2-server-issues-current-status-ecso2-server-down-last-batch-sent-over-6-000-seconds-ago-impact-delays-in-processing-documents-potential-fines-for-clients-due-to-late-asn-submissions-action-it-department-needs-to-restart-the-server-ongoing-communication-with-team-members-regarding-the-issue">2. <strong>Server Issues</strong>    - <strong>Current Status:</strong> ECSO2 server down; last batch sent over 6,000 seconds ago.    - <strong>Impact:</strong> Delays in processing documents; potential fines for clients due to late ASN submissions.    - <strong>Action:</strong> IT department needs to restart the server; ongoing communication with team members regarding the issue.</h4>
<h4 id="3-customer-communication-purely-bamboo-inquiry-request-daily-inventory-feed-846-via-webedi-action-provided-documentation-and-resources-for-catalog-creation-and-inventory-reporting">3. <strong>Customer Communication</strong>    - <strong>Purely Bamboo Inquiry:</strong>      - <strong>Request:</strong> Daily inventory feed (846) via WebEDI.      - <strong>Action:</strong> Provided documentation and resources for catalog creation and inventory reporting.</h4>
<ul>
<li><strong>Giant Bicycle Inquiry:</strong>      - <strong>Request:</strong> Turn off automated transmissions for 810 and 856.      - <strong>Action:</strong> Confirmed no current integration activity; advised on manual sending process.</li>
</ul>
<h4 id="4-training-and-shadowing-next-week-s-schedule-shadowing-maria-and-nico-on-tickets-attend-web-edi-training-class-on-monday">4. <strong>Training and Shadowing</strong>    - <strong>Next Week&#39;s Schedule:</strong>      - Shadowing Maria and Nico on tickets.      - Attend Web EDI training class on Monday.</h4>
<h4 id="5-key-concepts-edi-document-types-810-invoice-856-advanced-shipment-notice-asn">5. <strong>Key Concepts</strong>    - <strong>EDI Document Types:</strong>      - <strong>810:</strong> Invoice      - <strong>856:</strong> Advanced Shipment Notice (ASN)</h4>
<h4 id="6-thought-provoking-questions-what-are-the-potential-impacts-of-server-downtime-on-client-relationships-how-can-we-improve-our-response-time-to-customer-inquiries-during-peak-hours">6. <strong>Thought-Provoking Questions</strong>    - What are the potential impacts of server downtime on client relationships?    - How can we improve our response time to customer inquiries during peak hours?</h4>
<h4 id="7-real-world-applications-understanding-edi-processes-is-crucial-for-businesses-relying-on-timely-document-exchanges-to-avoid-fines-and-maintain-customer-satisfaction">7. <strong>Real-World Applications</strong>    - Understanding EDI processes is crucial for businesses relying on timely document exchanges to avoid fines and maintain customer satisfaction.</h4>
<h4 id="8-further-research-areas-investigate-best-practices-for-managing-edi-document-errors-and-rejections">8. <strong>Further Research Areas</strong>    - Investigate best practices for managing EDI document errors and rejections.</h4>
<ul>
<li>Explore strategies for improving server uptime and reliability.</li>
</ul>
<h4 id="9-glossary-edi-electronic-data-interchange-sac-standardized-allowance-code-asn-advanced-shipment-notice">9. <strong>Glossary</strong>    - <strong>EDI:</strong> Electronic Data Interchange    - <strong>SAC:</strong> Standardized Allowance Code    - <strong>ASN:</strong> Advanced Shipment Notice</h4>
<h3 id="summary-the-meeting-focused-on-reviewing-open-tickets-addressing-server-issues-and-discussing-customer-inquiries-related-to-edi-processes-key-actions-included-escalating-unresolved-tickets-providing-customer-support-and-planning-for-upcoming-training-sessions">Summary The meeting focused on reviewing open tickets, addressing server issues, and discussing customer inquiries related to EDI processes. Key actions included escalating unresolved tickets, providing customer support, and planning for upcoming training sessions.</h3>
<h4 id="generated-content-6">Generated Content</h4>
<h3 id="liquid-hub-acadia-ticket-details-and-solution">Liquid Hub Acadia Ticket Details and Solution</h3>
<h4 id="issue-summary-customer-liquid-hub-acadia-problem-discrepancy-in-record-counts-for-order-transactions-between-the-text-file-and-edi-data-specifically-an-item-appeared-twice-in-the-edi-data-leading-to-an-extra-line-in-the-output">Issue Summary: - <strong>Customer:</strong> Liquid Hub Acadia - <strong>Problem:</strong> Discrepancy in record counts for order transactions between the text file and EDI data. Specifically, an item appeared twice in the EDI data, leading to an extra line in the output.</h4>
<h4 id="investigation-steps-1-review-of-provided-data-customer-provided-a-screenshot-showing-eight-line-items-but-the-edi-data-showed-nine-the-extra-line-was-identified-as-a-duplicate-of-an-existing-item">Investigation Steps: 1. <strong>Review of Provided Data:</strong>    - Customer provided a screenshot showing eight line items, but the EDI data showed nine.    - The extra line was identified as a duplicate of an existing item.</h4>
<ol start="2">
<li><strong>Map Analysis:</strong>    - Located the map used for converting EDI to a flat file (Map 140 for Cardinal 867s to Liquid Hub).</li>
</ol>
<ul>
<li>Ran a test using the map to verify the output.</li>
</ul>
<ol start="3">
<li><strong>EDI File Examination:</strong>    - Exported the EDI file to examine the raw data.    - Identified that the item in question was indeed listed twice in the EDI file, with different quantities (34 mg and 10 mg).</li>
<li><strong>Conclusion:</strong>    - The issue was not with the mapping process but with the data sent by the customer. The EDI file contained two separate entries for the item, which were processed correctly according to the data received.</li>
</ol>
<h4 id="solution-communication-with-customer-informed-the-customer-that-the-duplication-was-due-to-the-data-sent-not-a-mapping-error-suggested-the-customer-review-the-edi-file-sent-by-cardinal-to-ensure-it-aligns-with-their-expectations">Solution: - <strong>Communication with Customer:</strong>   - Informed the customer that the duplication was due to the data sent, not a mapping error.   - Suggested the customer review the EDI file sent by Cardinal to ensure it aligns with their expectations.</h4>
<h4 id="key-takeaways-the-mapping-process-functioned-correctly-based-on-the-input-data-the-discrepancy-arose-from-the-structure-of-the-edi-file-provided-by-the-customer-which-included-duplicate-entries-for-an-item">Key Takeaways: - The mapping process functioned correctly based on the input data. - The discrepancy arose from the structure of the EDI file provided by the customer, which included duplicate entries for an item.</h4>
<h4 id="potential-follow-up-actions-customer-should-verify-the-data-source-and-ensure-accurate-data-entry-to-prevent-similar-issues-consider-implementing-data-validation-checks-before-processing-to-catch-such-discrepancies-early">Potential Follow-Up Actions: - Customer should verify the data source and ensure accurate data entry to prevent similar issues. - Consider implementing data validation checks before processing to catch such discrepancies early.</h4>
<h4 id="thought-provoking-questions-how-can-data-validation-be-improved-to-prevent-similar-issues-in-the-future-what-steps-can-be-taken-to-ensure-better-communication-between-trading">Thought-Provoking Questions: - How can data validation be improved to prevent similar issues in the future? - What steps can be taken to ensure better communication between trading</h4>
<p>partners regarding data formats?</p>
<h4 id="real-world-application-this-case-highlights-the-importance-of-accurate-data-entry-and-validation-in-edi-transactions-to-avoid-costly-errors-and-inefficiencies">Real-World Application: - This case highlights the importance of accurate data entry and validation in EDI transactions to avoid costly errors and inefficiencies.</h4>
<h4 id="generated-content-7">Generated Content</h4>
<p>Certainly, here are some steps and considerations for uploading to the FTP site without transmitting files to the DSG site:</p>
<h3 id="steps-to-upload-without-transmission-1-access-ftp-site-log-in-to-the-ftp-site-using-your-credentials">Steps to Upload Without Transmission: 1. <strong>Access FTP Site:</strong>    - Log in to the FTP site using your credentials.</h3>
<ol start="2">
<li><strong>Upload Files:</strong>    - Upload the necessary files to the designated folder on the FTP site.</li>
<li><strong>Disable Automatic Transmission:</strong>    - Ensure that any automatic transmission settings are disabled. This might involve:      - Adjusting settings in your FTP client or server to prevent automatic forwarding.      - Contacting your IT department to temporarily disable any scripts or processes that automatically send files to DSG.</li>
<li><strong>Testing:</strong>    - Conduct your tests to verify that the correct SKU/EAN numbers are being generated.</li>
</ol>
<h3 id="considerations-communication-with-it">Considerations: - <strong>Communication with IT:</strong></h3>
<ul>
<li>Coordinate with your IT team to ensure that the files remain on the FTP server and are not automatically processed or transmitted.</li>
<li><strong>Testing Environment:</strong>   - Confirm that you are working in a testing environment to avoid any accidental data transmission.</li>
<li><strong>Documentation:</strong>   - Keep a record of the changes made to the FTP settings and any tests conducted for future reference.</li>
</ul>
<h3 id="thought-provoking-questions-what-are-the-potential-risks-of-disabling-automatic-transmission-and-how-can-they-be-mitigated-how-will-you-verify-that-the-correct-sku-ean-numbers-are-being-generated">Thought-Provoking Questions: - What are the potential risks of disabling automatic transmission, and how can they be mitigated? - How will you verify that the correct SKU/EAN numbers are being generated?</h3>
<h3 id="potential-exam-questions-explain-the-process-of-uploading-files-to-an-ftp-site-without-automatic-transmission-discuss-the-importance-of-testing-sku-ean-numbers-before-transmission-to-a-partner-site">Potential Exam Questions: - Explain the process of uploading files to an FTP site without automatic transmission. - Discuss the importance of testing SKU/EAN numbers before transmission to a partner site.</h3>
<h3 id="further-research-investigate-best-practices-for-managing-ftp-uploads-in-a-testing-environment-explore-tools-and-software-that-can-help-automate-the-testing-of-sku-ean-numbers">Further Research: - Investigate best practices for managing FTP uploads in a testing environment. - Explore tools and software that can help automate the testing of SKU/EAN numbers.</h3>
<p>If you need more detailed instructions or assistance with specific FTP settings, it might be helpful to consult your IT department or refer to the documentation for your FTP software.</p>
<h4 id="transcript">Transcript</h4>
<p>00:00 Michael H.: Hello, Miss Sandy. 00:02 Sandy K.: Hey, Michael. All right. How are things with Lauren? 00:10 Michael H.: Good. We went through about like five to six tickets And each ticket, we weren&#39;t able to fix right away but is so currently kind of open because we&#39;re, for example, the first ticket we went through was Well, Frederick? 00:32 Michael H.: Customer named Mr. Mike and He was having filtering information on like May 14th to the 16th So we just logged in. Through him as Mr. Mike, and we just reproduced the like filter and sorting uh sort sorting function. 00:56 Michael H.: And um the outcome was pretty much the current day. Filter logic. That was broken. So when we click on current day, you still want it updated. So we just told Mr. Mark that we&#39;re diving deeper and we escalated the ticket to a development team to fixed filter rule for the current day. 01:24 Michael H.: And then the second ticket we did was with Michelle Sandro from Poole. 01:30 Michael H.: Whole warehouse. What we did with that was she was attempting to add a shipping charge on her 810 invoice, but it failed. And um The only allowance discount fields appear, yet the system requires all three SACS fields, if one is used. So what we did was we just reviewed 810 to see the exposed sac elements and segment fields And we logged the invoices and ID and reference for follow-up. And then we check the SAC code. 02:07 Michael H.: To see if there is no alternate changes But then the outcome the system still won&#39;t accept the standalone shipping charge without a fully populated SAC. So what we did was Lauren contacted you. Toll warehouse. To contact you or Lauren contacted you for the SAT code. 02:32 Sandy K.: Yeah. 02:32 Michael H.: Guidance. And the third ticket was with um Dave, Dolores Canning, he was just missing an 875 document The problem was he got an email notification about an 875 but couldn&#39;t see in his inbox. So we just marked the ticket. 02:50 Michael H.: To unread to force the client to refresh and bold notification And we told Dave to just double check and verify that he is sorting from newest to oldest in his case. So his view is actually correct. And now we wrote a follow-up email Asking him let us know from his side once he goes through the steps
and refresh everything. And if he&#39;s still missing anything is to retrieve and resend the A75. From our server. The last one, we have two more the last The fifth one is Allegheny health with valerie She was just a minor cause. She was just missing a shit too on the EDI 850. It looks like she was just missing filling out a lot of things. 03:44 Michael H.: So when she was generating a response for her 850 The report says it was shipped to But it&#39;s not appearing on her side. 03:55 Michael H.: We confirm Allegheny issues was the 855 And 810 invoices. So 855 isn&#39;t sent for her blanket PO so we um review the 850 and 855 lists in Allegheny. And her company&#39;s 3209 instance. So we just noted that the ship who actually lives on the 855 as well. So we asked Valerie which account and whether she&#39;s viewing it a 810 or 855. So right now we are like waiting for a clarification on the account and document type and it&#39;s still open. And so we&#39;re… So the steps that we&#39;re going to take for that is just once she confirms her details, we will investigate pretty much the SHIT2 data. We actually Riff rat riffat. 04:52 Sandy K.: Rafah. 04:54 Michael H.: Yes, to help us out with that. To make sure. And the last thing was with Larry from Liquid OTC He&#39;s… he&#39;s asking just a basic question was, I believe he asks. What the invoice for appeal That is no longer a PR. On his list. So we just showed him the file And then select new document and took screenshots to generate a standalone invoice. So we looked up his liquid OTC account which is 6481 and to ensure the correct context We just follow up with him and confirm that he could generate it. 05:41 Michael H.: Um so Usually, Larry, what Lauren told me was like he she knows them and usually he won&#39;t email back And if everything&#39;s good. 05:52 Michael H.: So hopefully he doesn&#39;t email back and means that the ticket is good to close. 06:01 Sandy K.: Okay. Yeah, she covered a lot with you. 06:03 Michael H.: Oh, yes. Yeah, she&#39;s a great teacher. 06:08 Sandy K.: That&#39;s good. All right. Any questions on anything that you went over? 06:14 Michael H.: Oh, no, no, no. Just looking at all the tickets. It&#39;s a very good experience. And she tagged me in all the tickets as well so I could read it and review it and keep up with the information and how the tickets are going.
06:37 Sandy K.: Yeah, so customers replying back. 06:40 Michael H.: Yes. 06:42 Sandy K.: All right, that&#39;s good. I&#39;ll share my screen. 06:48 Michael H.: Yes. 06:56 Sandy K.: Lauren&#39;s sending me something is wrong. Oh, send me. Yeah, I think there&#39;s something wrong with BCSO. 07:22 Sandy K.: Hold on, let me hold on let 08:04 Sandy K.: I think there&#39;s a problem with one of our servers. 08:08 Michael H.: Me and Lauren were talking about it. She was like um saying I told her it always happens on Fridays. 08:13 Michael H.: She&#39;s like, usually there are tickets that she could close, but all of our tickets are kind of open. And then you were sending her messages about having problem with the O2 segues and everything. 08:27 Sandy K.: Yeah, because Daniel couldn&#39;t get into 02. When I was in 02, but I couldn&#39;t do anything. I couldn&#39;t open data admin. The mouse is just spinning I&#39;ve tried disconnecting, reconnecting. I still can&#39;t do anything. 08:34 Michael H.: Yeah. Yes. 08:41 Sandy K.: Daniel just has a black screen. And now she sent me a screenshot that said. The customers. Their documents are in sent failed status. Instead of scent, which would indicate that possibly the servers down. So you should have access to the web EDI general chat In Slack? Okay. 09:06 Michael H.: Yes. 09:08 Sandy K.: So I just put the comment in there And ECSO2 does have hardly any batches today. Which is not a good sign. 09:30 Michael H.: So what happens when the servers are down? 09:37 Sandy K.: Our IT department has to restart them. Like just try to restart them the last time O2 sent a batch was 6,000 seconds ago 09:41 Michael H.: Oh, that&#39;s… Oh, that&#39;s not good. 09:55 Sandy K.: Yeah, so that&#39;s like 90 minutes. No. Yeah. A little over 90 minutes. 10:01 Michael H.: Yeah. 10:08 Sandy K.: So Chris will probably but he&#39;s I know not at his, I know he&#39;s not available right now. What I&#39;m going to show him this. We&#39;ll give them a screenshot that says All right. I just sent him a screenshot. What do I start a file that is
outbound? I don&#39;t know. Laura&#39;s now asking me how does she stop a file that is outbound? 11:11 Sandy K.: Our needs here. 11:12 Michael H.: Oh, no problem. No problem. I completely understand. 11:38 Sandy K.: I&#39;m going to tell her to join this. Zoom thing. I&#39;m going to give her this link and talk. 11:41 Michael H.: Yes. 11:58 Sandy K.: I don&#39;t too, but okay 12:03 Lauren C.: Hey guys! Hello. 12:03 Sandy K.: Hey, Lauren. All right. So it depends on Hopefully it&#39;s not on 02.02 I think there&#39;s a problem on O2. 12:04 Michael H.: I have a story. 12:16 Sandy K.: I just sent it to Chris because I just sent it to chris the last file on O2 was sent over 6,000 seconds ago. Which is more than an hour and a half ago. We&#39;ve only passed 2000 batches through 02. Today. So there&#39;s definitely something wrong with O2. 12:30 Lauren C.: That&#39;s not good. Yeah, that&#39;s why I messaged you. I was like, okay, something&#39;s wrong. The first guy&#39;s telling me he hasn&#39;t received his POs, which it&#39;s only two. But they should have come through a while ago. And then this lady&#39;s like, yeah, I sent this, but it&#39;s not sending. 12:48 Sandy K.: Okay, so… Do you know what who is it? 12:48 Lauren C.: So. Yinching? Hqn Q. 12:54 Sandy K.: Thank you. To Ford. 12:56 Lauren C.: Yes. She&#39;s like, can you please stop it? They&#39;ll penalize us if the time is wrong now. I&#39;m like, oh, no. 13:05 Sandy K.: Yeah, they will. Okay, you can get into O2? 13:09 Lauren C.: I can, yes. 13:11 Sandy K.: So on ECSO2, Is it… 13:17 Lauren C.: I don&#39;t see it. Do you want me to share my screen? 13:19 Sandy K.: Interesting. Share your screen, yeah. 13:23 Lauren C.: Okay, screen. 13:23 Sandy K.: Because it&#39;s stuck somewhere. Oh, let me stop sharing. The problem is as soon as that server comes back, it&#39;s going to go like
13:29 Lauren C.: Here, here. So I only show their outbound today was the 997 I just don&#39;t know where the file is. 13:42 Sandy K.: Yeah. Right. 13:43 Lauren C.: From YBDI to this. 13:46 Sandy K.: It&#39;s stuck somewhere. Because it first has to come here before it would actually Can you look back at a previous day? What&#39;s the input channel that it would use. 14:11 Lauren C.: Weirdly enough, it is not even running slow or anything for me. Webdi 3.0 outbound. 14:20 Sandy K.: Okay. Can you open acs manager I just want to know, is an HTTP path or is that a like file input. 14:27 Lauren C.: This one? 14:35 Sandy K.: There&#39;s no way once Chris restarts this that you&#39;ll be quick enough to stop it. 14:39 Lauren C.: I know. 14:39 Sandy K.: So go to input channel, yeah. And do, I don&#39;t know if it&#39;s going to be a file. Type in web edi and see if it comes up. 14:52 Lauren C.: There&#39;s YBDI emergency. 14:53 Sandy K.: Just do just do input channel. Just click on input channels. Yeah, it&#39;ll show all of them. Now look for WebEDI. Well, the emergency is. Okay, it&#39;s up. They&#39;re all HTTPP accounts. Okay, so I can&#39;t. Otherwise, I&#39;d say maybe like it would be in the tea drive you know somewhere but You&#39;re not going to be fast enough. Once that server comes up, it&#39;s just going to start processing data. What do they go out? Do they go out, learn data? Do they go out So the only thing you could do You&#39;re not going to know though what it&#39;s going to be. 15:32 Lauren C.: Yes. 15:40 Sandy K.: Is you would have to have The directory for Lauren data from DTS open. On that server. 15:53 Sandy K.: And when whatever file that&#39;s four dots something hits it. You would have to know, though, you could be and you could end up deleting somebody else&#39;s file. 16:05 Lauren C.: Oh my goodness. I wish the directory came up like this. Where we could see it.
16:14 Sandy K.: Because it&#39;s not there yet. And she&#39;s right. She will get fined. It has to be within 30 minutes. The scent, the date on the ASN has to be within 30 minutes of Ford getting the file. And we haven&#39;t processed a file now in. Let me refresh this. I already sent the screen to chris but He&#39;s not. Now it&#39;s up to 6,481 seconds. 16:51 Lauren C.: Oh, no, Cardinals calling out, dude. Someone else is calling now too. 16:56 Sandy K.: I&#39;m sure it&#39;s all about the same thing. So here, if I share my screen. 17:00 Lauren C.: Yep. I don&#39;t know if I knew how to stop sharing mine. I think if you share, it&#39;ll cancel mine. 17:07 Sandy K.: I think it will, yeah. 17:11 Lauren C.: Oh, here we go. Here&#39;s a stop share. 17:20 Sandy K.: So here is Retool. And 6,481 seconds ago, I&#39;m on ECSO2. We&#39;ve only done 2,115 batches today. So… 17:37 Lauren C.: Dang it. See, Michael. It&#39;s just Friday. That&#39;s all. That&#39;s what I told him. I had grabbed tickets thinking, oh, these are easy. And then they all turned into rabbit hole tickets. And I&#39;m like, I&#39;m so sorry. 17:57 Michael H.: And it seems like the problem is just the servers are down, correct? 18:02 Lauren C.: Yes. Something&#39;s not working right. 18:03 Michael H.: Yes. 18:05 Sandy K.: Yeah, ECSO3 is working. That doesn&#39;t have a problem. So see, last sent nine seconds ago we&#39;ve done Oh, maybe not. That&#39;s not a lot of batches either. 18:14 Lauren C.: Yeah, that&#39;s really low. 18:16 Sandy K.: It&#39;s low two degrees. No, six seconds ago. So that one has done a batch last six seconds ago. But let&#39;s see. What is usually Friday, May 9th? So we usually do about, we did Almost 23. Thousand batches last week, Friday, but so far we&#39;ve only got 9,000. Well, 9,000 already. We&#39;ve got 9,000 batches. For the day. It&#39;s only 10 o&#39;clock. So ECSO3 is processing batches but UCSO2 is a problem. 18:51 Lauren C.: O2 isn&#39;t. I can&#39;t open File Explorer. It just doesn&#39;t do anything. So I couldn&#39;t even try. 19:02 Sandy K.: Yeah, I messaged Chris.
19:10 Lauren C.: You know what? I&#39;m just glad it decided to happen today. And not, you know, Wednesday or Monday. 19:20 Michael H.: So just a question. So if they don&#39;t um send the documents on time. For example, like Yincheng. She will get fun. The clients and customers who will get fined 19:35 Sandy K.: So certain trading partners do impose fines. The ASN. The advanced shipment notice. If it&#39;s not returned within a specific window of time. 19:46 Michael H.: Oh, okay. 19:47 Sandy K.: So Ford&#39;s pretty strict. It&#39;s 30 minutes. So if you put your time on your document at 10 a.m. They have to have it by 1030. So there&#39;s a 30 minute window from the date you&#39;ve put on the form to the date they to the time they receive it. 19:58 Michael H.: Oh. Yes. 20:06 Sandy K.: Other training partners are usually a 24-hour window. So if you receive an order. Within 24 hours, you have to send the ASN. The time on it doesn&#39;t matter. But it has to be received within 24 hours, otherwise they will fine you. So some of the training partners impose fines. Some do not and some are really hefty. I know $4 to $75. I know Walmart is $25. And I believe Nordstrom&#39;s is $500. And there was one customer that was $1,000. 20:34 Michael H.: Oh. 20:44 Sandy K.: Trading partners, $1,000. I was able to talk them down to them cutting it in half once. 20:46 Lauren C.: Jeez. 20:50 Sandy K.: $1,000 for one late ASN. That&#39;s how much timing matters. 20:54 Michael H.: Wow. Yes. 20:58 Sandy K.: You know. I had initially just messaged Chris directly but I see he&#39;s offline, so I put the screenshot in the general chat. I don&#39;t know if maybe one of the developers would be able to. 21:01 Michael H.: 1,000. 21:12 Sandy K.: Somebody else could restart that server. I don&#39;t know. I don&#39;t know if Chris is in a meeting but But it&#39;s still down. So anything on ECSO2? There&#39;s probably no inbound or outbound. On ECSO2. And unfortunately, I know where you&#39;re what Yin Cheng&#39;s situation is they are going to get fined because
there&#39;s no way you would be quick enough. 21:42 Lauren C.: Yeah, I feel like we had this one more time before too when the server was down and we just couldn&#39;t get it. 21:42 Sandy K.: To… I am surprised that i am surprised that drive lock he hasn&#39;t called it. 21:57 Lauren C.: Why would you Beetlejuice us? Now they will. 22:00 Sandy K.: Unless they didn&#39;t have any. Unless they didn&#39;t have any Ford today. Which is possible. Okay. 22:05 Lauren C.: That would be great. 22:10 Sandy K.: I&#39;m just going to check that off your past. And Jennifer&#39;s pretty good about calling in so No, they haven&#39;t had any fortune. Probably because they haven&#39;t gotten any for today. It may be why. 22:24 Lauren C.: Thank goodness. 22:34 Sandy K.: Also. For Eggs Unlimited. With Safeway. And Michael, this will just be information for you. 22:46 Sandy K.: We have a training partner, Steve Madden. I&#39;m sure everyone knows who Steve Madden is, the shoe company. 22:46 Michael H.: Yes. Yes. 22:52 Sandy K.: So they would send us hundreds of files in like five, six, 700 files at one time And they would have bad EDI data. They were sending invalid information that was not x12 values and what it was doing is it was getting stuck in like a And it was holding up every other batch that came in after it And I tried to work with Steve Madden. To get them to remove those segments or elements that were bad or at least put valid data in them. And with no response, no communication. So I told the customer, we only have one customer with them. That their files were going to be held now We moved them to 4 p.m. 23:39 Sandy K.: So any file that comes in for Steve Madden, which usually comes in about between 7 and 9 a.m. We&#39;re holding them now till 4 p.m. And they&#39;ll get processed at 4 p.m. 23:47 Michael H.: Oh. 23:49 Sandy K.: And if we start having problems with customers at 4 p.m. Then we&#39;re going to move them to move them 7 or 8 p.m. Customer&#39;s already been told. That their files will not process until 4 p.m. In the afternoon. So what was
happening is every other customer was getting held up. And we have some large customers, Eggs Unlimited. Or large customers and large customers vocal customers, kind of needy ones, drive lock who will call in. If their data isn&#39;t processing like within minutes. Because usually everything&#39;s in real time. So. Okay. All right. Well. Lauren, unfortunately. By the time that server comes up. 24:39 Sandy K.: Those files are probably the process. You&#39;re going to have a hard time catching them. 24:43 Lauren C.: Yeah. And I can&#39;t even access anything else. I can just see what&#39;s been sent. Nothing else is responding now. 24:52 Sandy K.: I know, and I don&#39;t know where Chris is. Because Chris is offline. 25:00 Lauren C.: Oh, no. 25:01 Sandy K.: And um I don&#39;t know if he&#39;s No, he&#39;s not in a meeting. Oh. 25:18 Lauren C.: He ran away. Poor thing, he probably was like, okay, it&#39;s slow. Let me take a quick break. 25:27 Sandy K.: Well, I know he takes kids to school in the morning, but he&#39;s usually back by 10. Oh. Okay. 25:34 Lauren C.: Or he stopped for a coffee. He&#39;s like, it&#39;s Friday. It&#39;s fine. They can handle it. 25:42 Michael H.: It&#39;s always happening on Fridays. 25:42 Sandy K.: Okay. All right. Well, I&#39;m sure he&#39;ll put it in the general Slack once he gets it up. I thought maybe like Emmanuel or Luis see that and see that. 25:45 Lauren C.: Yes. 25:55 Sandy K.: Maybe they would maybe they would be able to restart ECS. I think it&#39;s only gross. I don&#39;t think there&#39;s anybody else that can restart. You see us. Okay. All right. Well… Since you&#39;re in O2, he&#39;s probably going to kick you out though when he has to restart it. If you are able to try and log back in quickly. But that was going to be a lot. You don&#39;t have to open data admin. Find those files then go to the D drive for Lauren data. 26:32 Sandy K.: And then delete those exact names. You can do that, though, out of the directory. Or you could also log into Learn Data. 26:44 Lauren C.: That&#39;s what I was just thinking. Can I stop a file from the EC grid? 26:51 Sandy K.: So… Once it hits e-cigaret, if you&#39;re in transactions I mean, the only way to hit it to stop it from hitting e-cigarette is to delete it out of the D drive
forlorn data. 27:05 Lauren C.: Okay. 27:05 Sandy K.: If you delete it out of the D drive for Lauren data before it gets sent. 27:11 Sandy K.: Then you could then it will stop it. But I don&#39;t even know if you would be quick enough. So it&#39;s sitting in the D drive. And if you delete it, is it still going to get And once it hits transactions. 27:18 Lauren C.: Is… 27:27 Sandy K.: It&#39;s already sent. 27:28 Lauren C.: What if I break something? When I say that, I mean, what if I tell like the event rule not to run for that customer? Could we do that? 27:32 Sandy K.: What if you… 27:38 Lauren C.: Or like the output channel I don&#39;t know. I&#39;m trying to think. 27:42 Sandy K.: Can you, okay, can you open ACS manager 27:47 Lauren C.: Let&#39;s see, let me go back. 27:52 Sandy K.: All right, Chris is back. He&#39;s checking in. All right. You want to share your screen? 27:55 Lauren C.: Thank goodness. Yes. Share. Yes. And screen two. Okay. That&#39;s like the only thing I can think of is can we break something? 28:06 Sandy K.: So if you go to the input channel for the web EDI 3.0, yeah. Is it AS2 Cirrus or is it Which channel is it? 28:11 Lauren C.: And which… It says AS2C generic. 28:17 Sandy K.: Up on Pastor S.2C genera Okay, so that&#39;s the one it&#39;s using. So that one. All right, so if you can open that one and go to the event rules 28:24 Lauren C.: Oh, I don&#39;t know if I can open it. 28:32 Sandy K.: Can you sort the details column? All right, so there&#39;s only three things going to Lauren data. 28:40 Sandy K.: So Ford Motor Company. So you would have to break the Ford Motor Company one. So if you double click that one. And go to condition. Okay, that&#39;s Ford Motor Company Canada. Okay, cancel. That&#39;s not that one. It&#39;s got to be the one above it that says Ford. You could add a rule in here that says trading partner is not Yinching. So go there and say sending trading partner, check the box that says the sending. No, right where you are. Nope, go back.
29:18 Lauren C.: Oh, sorry. I&#39;ve never been in here. 29:19 Sandy K.: Go and check the circle that says the Senate. Yep, select that one. And say, okay. And now is check the box that says not and then find Yin Chang. You just type in Yin. 29:37 Lauren C.: Okay, and then just hit OK. And fingers crossed and Am I able to delete that after when So would the file just fail? 29:39 Sandy K.: And hit OK. Yeah, say okay. It will fail. If you say okay and then say okay, the file will fail. 29:51 Lauren C.: Okay. Okay, so my silly question got us somewhere. That&#39;s cool. Oh no, I don&#39;t think I can. Dang it. Wishful thinking. 30:06 Sandy K.: Double click on four, double click and see if it&#39;s there. Yeah, it is. So it&#39;s just say okay and say okay again. 30:12 Lauren C.: Yeah, it is. Okay. And then if it fails, I don&#39;t need to do anything with it. It&#39;s not going to send or do anything. I would just… 30:25 Sandy K.: It&#39;s just going to fail. You would just have unable to submit refresh requests to the remote server. 30:32 Lauren C.: So we&#39;ll see if it actually went through on 30:35 Sandy K.: Yeah, if you want closed ECS manager and then Open it again, see if it&#39;s there. 30:45 Lauren C.: I hope it&#39;s not just saving locally where I can see it. Maybe it&#39;s being really slow. So I wonder if Chris is doing something right now. Because that opened just fine for me before. 31:02 Sandy K.: He told me he may have to reboot it because he asked if I was in a meeting And I just told him that I was in a meeting with my new hire. But I said, I can leave if you need me. So he said not yet. It&#39;s probably not going to open for you. 31:19 Lauren C.: No, I think he&#39;s playing with something. Which that&#39;s fine. Whatever fixes it, that&#39;ll kind of help. The call in the ticket queue. 31:32 Sandy K.: Okay. At least there&#39;s not I mean, a whole lot of tickets, okay. All right well fingers crossed that it took that. I would say once Chris says that the server restart 32:01 Lauren C.: Track admin to see if it says green or red. 32:06 Sandy K.: Well, I would. Go in the server. I would open up BCS Manager.
And… see if you can update that. Before the file hits. And okay. 32:20 Lauren C.: Okay. Can you speak quick. 32:23 Sandy K.: And get out and he needs you to disconnect from ECSO2. 32:34 Lauren C.: Okay, I&#39;m out of it. 32:39 Sandy K.: Okay. All right. And I don&#39;t have anybody else on my team. He said, let your team know. I said, there&#39;s nobody else here. Everybody else is out. Uh-huh. 32:48 Lauren C.: It&#39;s just us. 32:52 Sandy K.: Oh, Suj might be on. Oh, no, he&#39;s not online yet. He doesn&#39;t start till 11. So surgery is not only. Well, he&#39;s not supposed to be. It says here that he&#39;s usually he works 11 to 8. 32:58 Lauren C.: Oh, perfect. 33:07 Sandy K.: So he&#39;s not supposed to be on. Okay. All right. Well, we&#39;ll see what Chris says. I will keep an eye on the general channel. And see if he puts anything in there that he&#39;s restarted it and that way you could come in and Trying to do something Right away. He&#39;s working on it. 33:35 Lauren C.: Yeah, three is working, so that&#39;s nice. Okay. 33:37 Sandy K.: Three is working, yeah. The batches work going up. So that was good on all three. 33:44 Lauren C.: Perfect. All right. Will do. Have fun, guys. 33:44 Sandy K.: All right. All right. Well… If you need anything, let me know. Thanks. Bye. 33:52 Participant : Of 33:58 Sandy K.: Okay. All right, let me share my screen. So that&#39;s going to limit what I can do. 34:00 Michael H.: Yes. 34:04 Sandy K.: Okay. No, this doesn&#39;t happen often. But it doesn&#39;t. Okay, I do have a ticket that we can um so This customer reached out. Purely bamboo reached out And said their trading partner is requiring a day. Can you see that screen with a ticket on it? 34:36 Michael H.: Yes. 34:36 Sandy K.: Okay. That the training partner is requiring a daily inventory feed.
34:43 Sandy K.: And… 34:48 Michael H.: This ticket is purely bamboo
<a href="mailto:<EMAIL>"><EMAIL></a> Our trading partner is requireing a daily inventory feed sent to them to keep quantities up to date and it looks like the inventory inquiry/ advice 846 within the webedi portal handles that? Is there a training video speficic to that thanks bernie 34:48 Sandy K.: Knees center and you know He&#39;s up to date. It looks like the inventory inquiry advice within WebDI, they&#39;re asking if that can handle it. And is there a specific video on that topic on how to use the function. So, um. I do believe. Has a video on a video inventory and we also have documentation So I will send the customer then the documentation resource. We have one for inventory. We have one for catalog. Create. Creating a catalog from the po catalog, catalog. Not. All right. I will still give them access to the resource page so they know how to do their catalog, but I&#39;m going to see if they do have it. So I&#39;m going to log into their account, so 7635.
35:52 Michael H.: Yes. 36:00 Sandy K.: All right. I&#39;m going to see if they have Cal. Looks like they&#39;ve only got one order here so they may not Okay, so they don&#39;t have a catalog set up. So the first thing this customer is going to have to do is build a catalog. And I&#39;m going to send them the directions from inside JIRA on how to build a catalog as well as that resource page link. 36:21 Sandy K.: And I will also send them a link to the knowledge-based document on how to create a catalog so I&#39;m going to go to the knowledge base So here&#39;s the knowledge base. 36:21 Michael H.: Okay. 36:39 Sandy K.: And I&#39;m going to give them the documents on how to create a catalog. And I&#39;m going to give them the document on creating an E56. So I&#39;m going to click on this and there&#39;s this over here. It&#39;s a shareable link So I&#39;m going to copy that link and I&#39;m going to go to this ticket. 37:01 Sandy K.: I&#39;ll click the link there. And then I&#39;m going to go Back to the articles. I&#39;m going to get I&#39;m controlling. Create an inventory report. And then this is telling them from within the catalog how they can create their inventory report. So I&#39;m going to take this one. Let me give him this one. So now they have now how to create a catalog, how to create Oh, and then I&#39;m going to give them the resource.
Hello Bernie,
Yes, you are able to create a daily 846 inventory report from within the WebEDI portal. I have attached documentation on both creating the catalog and creating the inventory report. I have also attached a link to our resource page where there are tutorial videos to assist with catalog creation. Please note that sending the daily</p>
<ul>
<li>Jira Service Management</li>
<li>Jira Service Management</li>
<li>WebEDI Video Tutorials Resource Page | DataTrans Solutions
Sandy Karidas Cleo <a href="mailto:<EMAIL>"><EMAIL></a> ************ opt. 2 37:57 Sandy K.: So…
40:05 Sandy K.: So because our customers are charged by document type. Our bookkeeper always tells us it&#39;s a good idea to let them know in advance that that report is going to add to their transaction cost. 40:22 Michael H.: Oh. 40:28 Sandy K.: Because they&#39;re usually always surprised when they&#39;re always surprised Why did my transaction fee go up? Because again, they&#39;re charged by document unless that document is over a killer character and then they&#39;re charged by killer character um so And then I will just tell them Okay. And then please contact me. Now, these links are going to take them to Jira service management and if they haven&#39;t already created a portal in order for them to see the articles, they&#39;re going to have to create a portal. So that what it will do. So as soon as they click on it. 41:21 Sandy K.: Is going to bring them to this page. And ask them to either log in But it&#39;ll show them if they&#39;ve already created a portal, I already have a portal. So it took me directly to the document. And if they didn&#39;t, it&#39;s just going to tell them to create their portal. So I provided them with the documentation so they could create it. Macy. Is their account manager and she&#39;s also reached out and had already asked about this customer. 41:51 Sandy K.: And I let her know that they had contacted support. And so that we would be And there&#39;s the one customer that is a squeaky wheel. So Eggs
Unlimited is now calling in. And I don&#39;t know if Lauren picked that one up or if they&#39;re still going to circle through to me. 42:19 Sandy K.: So Eggs Unlimited. Is a big customer. When their data doesn&#39;t flow within minutes, they will call support. Okay. I guess she answered that. All right. So this was easy. This was just, they wanted to know for Cape where we have the ability to send a daily 846. I just let them know that we do. You know directly from within the portal And since they didn&#39;t have a catalog. It was best to show them how to add a catalog as well as then the inventory report so they know they could import their catalog and create it via an import or po They do have to maintain the inventory inside one BDI. 43:10 Sandy K.: Oh, that one is an easy one. We&#39;re probably going to get a lot of calls today for the server being down um let&#39;s see. 43:22 Michael H.: Yeah. How often does the server go down this sandy Oh, yes. 43:26 Sandy K.: It doesn&#39;t. I don&#39;t know, the last time that one of the servers went down, it&#39;s been uh it hasn&#39;t gone down like this in a while. Like if I were to go back through our general chat, there isn&#39;t any. You know… sometimes they say it&#39;s slow we haven&#39;t had like one of the servers stop processing data like this. For a while, so I don&#39;t really know why it isn&#39;t processing data Let&#39;s see if it&#39;s doing now. Still, we&#39;re at 8,121 seconds now. So. Chris is working on it. All right. But I&#39;m sure that&#39;s going to be who&#39;s going to be calling in. Maria also let me know that she will be back on Monday. 44:28 Michael H.: Yes. 44:29 Sandy K.: Maria and Nico will be back on Monday. So on Monday. I&#39;m going to schedule a meet and greet. With the team for Monday. 44:36 Michael H.: Yes. 44:41 Sandy K.: Since everyone will be here. 44:47 Michael H.: Would I get the schedule for next week? 44:53 Sandy K.: Yeah, next week um next week probably going to have you, I&#39;m going to see how Marie is doing on Monday. 45:01 Sandy K.: Because she said she&#39;s feeling better because I&#39;m probably going to have Because, you know, Maria and Nico are going to have to get caught up on their tickets And I&#39;m going to have you shadow them on tickets as well. And that&#39;s what next week will be. I will probably also If they&#39;re simple tickets have you work
them, you know, not necessarily shadow them doing the work, but have you doing the work 45:15 Michael H.: Yes. Yes. 45:35 Sandy K.: So I&#39;m going to see based on Nico&#39;s ability because he&#39;s got Some server, some SFTP migrations he&#39;s doing next week as well. 45:49 Sandy K.: I gotta put together. It&#39;s all going to be shadowing tickets and doing tickets. And because there really isn&#39;t anything else to go over. I just have to do daily who&#39;s going to be handling what and when. 46:04 Michael H.: Yes. 46:04 Sandy K.: And I will have that to you. On Monday morning, but Monday morning. So next week you&#39;ll still do the same hours. 46:17 Michael H.: Yes. 46:17 Sandy K.: And you&#39;ll work with me Monday. We&#39;ll start with me Monday morning. And then… I will probably, oh, because then at 10 30 Monday morning, you&#39;ll be in the training class the actual Web EDI training class, you&#39;ll attend that because you had meetings this past Monday. So you&#39;ll attend that training class on Monday. That usually does run for about an hour. And… And I believe there&#39;ll be there uh customers on that one, because I know I had a couple of customers that I sent the link to for them to attend the training class on Monday. 46:44 Michael H.: Yes. 46:58 Sandy K.: So you&#39;ll kind of get a feel of customers asking questions about, you know, when they&#39;re going through the training. And then… I&#39;ll figure out the rest of the week who you&#39;ll sit with Because Tuesday afternoon I&#39;m off. So I&#39;ll make sure that I have 47:14 Michael H.: Yes. 47:19 Sandy K.: You set up with everybody next week. I just got to put that schedule together. 47:24 Sandy K.: I don&#39;t have a schedule for at what time, what person yet because yet I was going through Maria and Nico&#39;s tickets today to make sure, see what they had because they&#39;re going to have tickets that they&#39;re going to have to follow up on. You&#39;ll work with each one of them all next week. Again, you&#39;ll learn different styles, how they do things. 47:45 Sandy K.: And… Yeah, and just see the various different types of tickets
Obviously, Maria and Nico are tier two So they get involved in a lot more the complex mapping issues Not necessarily going to be showing you those like right off the bat. 48:01 Michael H.: Yes. 48:05 Sandy K.: But, you know, they&#39;ll be showing you their different processes and how they handle tickets. We used to have like system generated tickets that we used for training purposes and I&#39;m probably going to have you also work some of those next week. So you see we have these watchdogs. 48:27 Michael H.: Yes. 48:27 Sandy K.: Oh no, these are all just the batch non-injected watchdogs. What we have is no one&#39;s working these right now.
48:37 Sandy K.: I don&#39;t have the staff to work these But what these are is These are batches that weren&#39;t unable to inject into a cluster&#39;s web EDI account. 48:41 Michael H.: Yes. 48:46 Sandy K.: And there may be times when they don&#39;t need to be 48:58 Michael H.: Yes. 48:58 Sandy K.: But like a lot of these that just get canceled. They don&#39;t have to work. But there are tickets I&#39;m going to add you to the channel because I think I&#39;m actually going to have you work some of these. 49:20 Sandy K.: Because they&#39;re only in a Slack channel right now. We no longer
get them as tickets. Because I didn&#39;t want them. Clogging up my queue. I didn&#39;t want them clogging up my queue. So I&#39;m actually going to I&#39;m actually going to share my slacks Okay. 49:32 Michael H.: Yes. 49:41 Sandy K.: This channel you should have just gotten access to, correct? All right. 49:44 Michael H.: Yes, I got it. 49:49 Sandy K.: These are… documents that were rejected by a training partner. Our customers get notified. Inside Web EDI when a document is rejected. They have a red tab that says acknowledgement errors. So we generally don&#39;t generally let them know a document was rejected because they have visibility. But as a courtesy. We can send them an email. That tells them what was rejected. 50:24 Michael H.: Yes. 50:24 Sandy K.: So this is a ticket that rejected. If I, let me see if I can, can I open this ticket Okay. 50:37 Sandy K.: So it was on server ECSO3. And it was at today. And it was a warning And it told me that it told me that You know, an EDI document was acknowledged with an R meaning rejected This was the sender, this was the receiver. This was the control number. 50:57 Michael H.: Okay. 50:59 Sandy K.: And this here, 144, that is going to be the batch ID.
51:07 Sandy K.: Okay. So if I go to ECSO3. And I have to have a batch off. But if you do control g It allows you to search for batch ID. So long as you have data admin open, you type in control G 51:07 Michael H.: Okay.
51:22 Sandy K.: You can do search by batch id You can either type or control V to put the batch ID in there. But control G opens up this little search box. Okay, so I could see that our school rail Which is our customer. So we don&#39;t really care for
customers sending back a 997 that rejected to their trading partner. But what this is, is the 997 It was rejected. And down here at the end, we&#39;re all the way under would say number of transactions included number that were received. So what the customer is saying to the trading partners really is what we&#39;re saying. We&#39;re saying that we are not going to accept this transmission. 52:10 Sandy K.: Now, it&#39;s because it has something that we don&#39;t like. In the file. So it says copy of bad element So you can see here it&#39;s got a slash. 52:10 Michael H.: Yes. 52:21 Sandy K.: So you can&#39;t have a slash. Inside the data, the system doesn&#39;t like it. And so it tells the training partner I don&#39;t like this character. So we&#39;re rejecting your file. But we really didn&#39;t reject the file. We take in every single file that comes through us. So every file that comes through to us we take in we might have taken in hard time processing it But every single file that comes through we process. 52:52 Michael H.: Yes. 52:52 Sandy K.: So that one is not a very good example because i want one where we are sending it. To where a training partner is sending it to a customer. See, I don&#39;t have… When these used to be tickets I had all kinds of filters because see this is our customer sending it back to a trading partner. So I don&#39;t care about those. So I had all filters that got rid of all of those. I only wanted ones where it was Okay, so here&#39;s one. The sender, our receiver. Anytime you see DTS, that&#39;s going to be our customer. Our customers usually always have DTS. And this… is the concern. Is the batch ID. All right, so let&#39;s go look up that batch. Okay, RADIUS Global Supply. No, that&#39;s our customer too. 53:59 Sandy K.: And circles. Ecso2, I can&#39;t do ECSO2 because I can&#39;t get on ECSO2. I can only get on UCSO4. These are all ECSO2. 54:08 Michael H.: Yes. 54:17 Sandy K.: And I know because ECSO2 will start out one three ECSO 4 starts not one four. These are all 1, 3. I wanted to find one that was good so I could say, okay, here&#39;s what we do this is How we go and find what was rejected. Because then as a courtesy, we would just send an email to the customer and say You know this file rejected Yeah, I know I can do control F. And I guess I can type in. That&#39;s not letting me do control of. Okay. It&#39;s not letting me do control F to search
in here. I did control F, but it&#39;s not bringing anything up for me to do. I&#39;ll just search here. Dts, ECS. On three. Okay. Document rejected. 56:15 Sandy K.: Okay, this one. Okay. So Sephora Tracy Hernan, Actonacre is our customer. Sephora has sent back to Acton Acre a 997. Rejecting. Na55. The problem is this isn&#39;t a good one either. 56:51 Sandy K.: Because usually you&#39;ll see in here why it was rejected. All it&#39;s saying is set rejected.
56:57 Sandy K.: Not telling us why, not giving us any details. All it&#39;s saying is, yeah, we rejected it. But I can&#39;t help the customer to understand why it was rejected because they&#39;re not giving us any segments and or elements as to why it was rejected. So that&#39;s just not going to work. Okay, we&#39;ll just go back to tickets then. What I wanted to have you do, I&#39;m just not going to be able to hear you do. They&#39;re just in a Slack channel and they&#39;re too hard it&#39;s going to be too hard to find ones that are actually valid 997s that you could use for reference so All right, we&#39;re just going to come back here to these tickets. 57:46 Sandy K.: We would like to schedule a call to see the capabilities Cleo can give us. Oh, well, that&#39;s helpful. All right. Our EDI mapping and querying. All right. My name is Lauren. I&#39;m the marketing coordinator at Siffany Beauty. 58:07 Sandy K.: We worked with Krista on our EDI News platform. She&#39;s out of the office. A new buyer of ours is using EVI with open text for PO transmissions.
They&#39;re requesting that an 850 mapping be set up by May 23rd. Would someone be able to help us to ensure a smooth experience? A new buyer of ours is using EDI with open text for PO transmission. Well, let&#39;s go look up because I don&#39;t know what they&#39;re asking. 58:47 Sandy K.: So Symphony beauty I&#39;m going to do that one and then I&#39;m going to pull up their project board to see if they have an open project with Crystal. 59:29 Sandy K.: All right, let me tell. Lauren, ECSO2 is online. 59:35 Michael H.: Yes. 59:42 Sandy K.: All right. So they don&#39;t have any open projects. But they have a few trading partners so i don&#39;t know when they said their buyer, I need to know which trading partner. So they&#39;ve got CVS, Nordstrom&#39;s, Amerisource, Virgin.
59:58 Sandy K.: Harmon stores an HEB. And no open projects. So we need to get a better understanding as to what her needs are. She has a new buyer, but is that a new trading partner? So that&#39;s what we need to know from her. So if we ask her. 01:00:38 Sandy K.: First, I&#39;m just going to ask if she&#39;s looking to add a tree. I&#39;ll certainly. All right, so I&#39;m just going to ask him if she&#39;s looking to add a new trading partner Actually, you know what? I&#39;m going to give this one to you. 01:01:05 Michael H.: Yes. 01:01:09 Sandy K.: Okay. I&#39;m going to give that one to you. And then when she replies back. I&#39;ll let you handle the response to her. So she responds back that
They are looking to enter a trading partner. We&#39;re going to send her the form. To add a new training partner or if she&#39;s saying no it&#39;s It&#39;s an existing one. It&#39;s just a new buyer, you know, not really certain. So I&#39;m going to leave that one with you. 01:01:36 Michael H.: Yes. 01:01:36 Sandy K.: Wow. And actually, let me go back to that ticket. 01:02:03 Sandy K.: Okay. Because I replied. So it will come to me automatically, but I want to make sure it goes to you as well. Okay. All right. Purely Bamboo replied back. About the 846. He&#39;s a great think on review for questions only working with 15 to 20 SKUs, so it should be manageable, but I appreciate the heads up on the transition. 01:02:31 Michael H.: Yes. 01:02:43 Sandy K.: Okay. All right. So that&#39;s great. So he&#39;s going to review that.
01:02:47 Sandy K.: And then if he has any questions, he will get back to me. So I&#39;m going to actually mark this as resolved. 01:02:53 Michael H.: Yes, for purity. 01:02:53 Sandy K.: And he can open a new ticket if he needs to. So that is done. We gave him everything he needed. I just don&#39;t know what to do with that auto ticket. Let&#39;s see if I had any. No, mine, these are all i&#39;m just I have a lot of tickets that I&#39;m waiting on customers to. 01:03:14 Sandy K.: Respond. I don&#39;t have anything i could we could work on them
okay so let&#39;s see what we have here. This I actually have to give to Nico. So let me just let from Barcelona. 01:04:06 Sandy K.: So Nico&#39;s handling our SFTP migration, so I&#39;m actually going to assign this to him. 01:04:13 Michael H.: Okay. 01:04:13 Sandy K.: And I will talk to him on Monday then. She wants to do some sort of testing before they actually migrate. So our old FTP, we&#39;re shutting it down told every single customer and trading partner we&#39;re doing that on June 7th. And that… I want to see if what Lauren did is She stepped away to get some coffee. 01:05:06 Sandy K.: All right, so… She did. The input channel I don&#39;t even know if I can do all this fast enough. Yinching. It was this right? So, so far we only have the one at three in the morning. 01:05:57 Sandy K.: Okay, so it&#39;s there. Perfect. So it&#39;s not going to send that. Okay, so he kept what she did then. All right. I&#39;ll let her know that EC is moved. So now. That should fail. So when that picks it up. It&#39;s going to fail. But I think Chris said that and scott a lot. To do here. So right now it&#39;s processed 3,100 batches. Okay, so when it comes through, that&#39;s going to have a gray box here because it&#39;s not going to be able to deliver it because it&#39;s not going to be able to find an event rule. 01:07:06 Michael H.: Okay. 01:07:19 Sandy K.: Then she can just have customer restage it and send it from within their portal. Okay, this one. And I signed it to Nico. I let Jennifer know that he&#39;ll reach out to her next week when he&#39;s back. 01:07:35 Sandy K.: That one&#39;s done. Oh, that was a voicemail. Oh, they&#39;re otherwise gone. Okay. So this is a customer that called in, this customer that called in. We&#39;re going to have to listen to voicemails and call these people. That I think is from Marianne. Okay. All right. So this is Dylan with Giant Bicycle. We&#39;ve been creating both EDIA 10s and A526 manually. Is it possible to show off transmissions from Both. We&#39;re getting too many chargebacks. From the errors in the automated EDIs and creating them manually is avoiding them all.
01:08:32 Sandy K.: Okay, so giant bicycle. As an integrated customer. And so they&#39;re asking us to turn off the integration I&#39;m guessing because they&#39;re doing them manually. And they&#39;re avoiding them at the moment. We&#39;re looking to create a test PO and check if the correct skew EAN is generated without the PO being sent. Dicks. All right, so let&#39;s see, where is giant bicycle?
01:09:17 Sandy K.: Not yet. So I&#39;m looking so giant bicycle is saying they want to turn off their integration Because they&#39;re getting a lot of errors on the automated documents. So giant bicycles integrated. So they send in their A-10s and their A56s through an FTP channel. So that would not be a sender or receiver. That&#39;s going to be a source. So I type in giant bicycle and I see we have giant bicycle step in step one. 01:09:55 Michael H.: Yeah. 01:10:00 Sandy K.: So they have a step in step one And then I see that it depending on who it&#39;s going to So they have DSG and ASG, A10, A56. So step one brings the file in. So I do giant bicycle in We don&#39;t have anything.
01:10:24 Sandy K.: So did they already stop sending their files to us? Let&#39;s see how far back can I go? 01:10:51 Sandy K.: Here&#39;s those files that Lauren did see no deliveries. So what, remember when Lauren was creating that event rule.
01:10:59 Sandy K.: And I walked her through the event rule. So now those files did not get sent. 01:11:02 Michael H.: Yes.
01:11:10 Sandy K.: You did not send. So the customer now We&#39;ll need to restage. Fix the time. 01:11:47 Sandy K.: Yes, Karen. Lauren, if she&#39;s comfortable to remove the condition. So I want her to go back into this channel where she put the condition in.
01:11:56 Sandy K.: And take the condition out. I asked her if she&#39;s comfortable to do that. 01:12:00 Michael H.: Okay. 01:12:04 Sandy K.: Because making changes to these rules If you delete the wrong thing, you could end up breaking a delivery for a bunch of people. So like if she actually deleted the room. The rule for Ford Every single Ford file would fail. 01:12:20 Michael H.: Oh, man. 01:12:20 Sandy K.: So, correct. Making changes to the channels and the event rules are usually left to tier like tier two people. Because you can make a big mistake. And she&#39;s all right, I&#39;m going to, she told me she just did it. So the rule is still here. Okay, let&#39;s see. That&#39;s correct. 01:13:14 Sandy K.: All right. Okay, so she&#39;s going to take care of that. All right. So what I was looking at. So I can see here that giant bicycle. They haven&#39;t sent us anything through their integration since the end of March. So we would not be sending anything out automatically unless they now have a partner config that
sends it. This file comes in. You&#39;ll see they have Ale&#39;s Sporting Goods, and Dick&#39;s Sporting Goods. So that&#39;s where then step two comes in. The first step calls these two maps. 01:13:56 Sandy K.: If I go to logging in I can see that. This says it&#39;s queuing on data for delivery, giant bicycle And then it pulls two maps. One for Al&#39;s Sporting Good and one for Dick&#39;s Sporting Goods. If I were then to go to step two Because it doesn&#39;t look like they&#39;re sending anything. But I do see that they have files that go to both Dick&#39;s and a ales. 01:14:34 Sandy K.: So if I were to now change my input channel Reads really slow.
01:14:52 Michael H.: Yeah, it seems pretty slow. 01:14:55 Sandy K.: Yeah. Okay. All right, so then that is channels just sending it directly out because we already ran it through the mail. All right. So now I&#39;m going to go pull up there are these pics. I don&#39;t know, you might not have access to partner. So with the partner configurations do is it it&#39;s like special rules for customers so if they want to have files auto send they&#39;ll have this scent as draft.
01:15:29 Sandy K.: And if it&#39;s set to a one. It will automatically send. 01:15:29 Michael H.: Yes. 01:15:32 Sandy K.: So there&#39;s different partner configs that our developers set up on the back end. Could be for ShipStation. Could be for internally within QuickBooks or anything within a document. Sometimes there&#39;s special partner configurations they have to set up for the document. And that&#39;s where these are. But if you make a change on these, you break everything. So sent his draft. Is set to zero. Which means do not automatically send the file if it was a one.
01:16:14 Sandy K.: Then it would automatically send the file. So I can see that they do not have Any other… They don&#39;t have any other Partner configs sending documents automatically. 7175. So I&#39;m going to go into their web edi 01:16:57 Sandy K.: Okay. So they have to be generating these manually because there&#39;s no other way these got in here because I don&#39;t see that they&#39;re using integration any longer. 01:17:19 Michael H.: So. So that&#39;s how you know that they&#39;re generating this manually. 01:17:28 Sandy K.: Right, because if they were using their integration we would see files for today here. Or yesterday or the day before. So this file comes in through their integration. It runs through a map and then it injects into WebEDI. See how this says web edi sent folder? 01:17:48 Michael H.: Yes. 01:17:48 Sandy K.: So this is automatically dropping documents bypassing draft. And it&#39;s dropping them into the sent folder. And then… We have another rule that sends it out to Lauren data. 01:18:04 Sandy K.: So he is asking for us to turn off the automation. It is off because he&#39;s not using the integration. Let me just tell Lauren.
01:18:38 Sandy K.: He was using the integration still, it would drop it in the sent folder and it would go out to learn data. I would be able to kind of what Lauren did break this channel and remove the out to Lauren data and the document would just sit in the sent folder. 01:18:55 Sandy K.: But he&#39;s not using that. He said he&#39;s been creating them manually and is it possible to shut off transmissions for both. When you create a document manually, you have to hit send. They will not send automatically. 01:19:13 Michael H.: Yes. 01:19:15 Sandy K.: So I&#39;m not certain. All right, so I&#39;m going to reply back to Dylan. And I&#39;m going to ask him. No. Yeah, I&#39;ll go and reply back to Dylan. And maybe I&#39;ll have you reply back to Dylan. 01:19:39 Michael H.: Yes. 01:19:39 Sandy K.: Yeah. All right. I&#39;m going to give this to you. I&#39;m going to have you reply back to Dylan. Okay, so I&#39;m going to have you reply back to Dylan. 01:19:47 Michael H.: Okay. 01:19:51 Sandy K.: And you&#39;re going to ask, you know, you&#39;re just going to say that I could see i could see that you&#39;re currently not using that you&#39;re currently using the integration for your A10 and 856. Do you want to do this or you want me to type it? 01:20:09 Michael H.: Oh, no, I would type it. Yes.
01:20:10 Sandy K.: Okay, just let them know that we don&#39;t see that he&#39;s using currently using the integration for his 810s and 856s. The last time we show any activity on that is March 25th. 01:20:18 Michael H.: Yes. 01:20:22 Sandy K.: So currently, if he creates them manually inside Web EDI, if he, you know, there is no auto send feature currently if he doesn&#39;t use the integration. However you want to phrase that. Do you understand that, though, what I&#39;m trying to say? 01:20:38 Michael H.: Yes. 01:20:38 Sandy K.: Okay. 01:20:45 Michael H.: Can I send you what I&#39;m going to say before? Okay. 01:20:45 Sandy K.: Oh, sure. Mm-hmm. Yeah. 01:21:33 Michael H.: This is correct. The A10 and A, you said a10 and a 856 transmission Yes. 01:21:38 Sandy K.: 856, yeah, 810 and 856. Okay. Oh, not A, just 8, 10, and 856. 01:21:55 Michael H.: Oh, yes. So 8A56. 01:22:05 Sandy K.: Yeah, that sounds great. 01:22:08 Michael H.: Okay. Just 856. 01:22:15 Sandy K.: Correct. 8, 10, and 856. Yeah, there&#39;s no A. There&#39;s no alpha characters. 01:22:18 Michael H.: Oh, okay. Yes. 01:22:21 Sandy K.: So… instead like if you didn&#39;t want to say 8 to 10 you could call it an invoice. Or if you didn&#39;t want to say 856, you can call it an ASN. 01:22:29 Michael H.: Okay. 01:22:30 Sandy K.: But since he referred to them as 810s and 856s. So depending on the customer&#39;s EDI knowledge. A lot of them just call it an invoice and an ASN. So you&#39;re going to hear both interchangeably, right? An 810 is an invoice and 856 is an ASN. So you&#39;re going to hear both. And you can use them and people use them interchangeably. But yeah, he&#39;s there&#39;s nothing auto sending We don&#39;t have the auto send configuration turned on. We don&#39;t have, he&#39;s not using his integration 01:23:23 Sandy K.: I was just looking to see if he had another ticket where he asked to have that turned off and it was already turned off, but I don&#39;t see for junk
bicycle. 01:23:32 Sandy K.: Okay, so yeah, you could answer that one. 01:23:36 Michael H.: Okay. I just sent it. 01:23:39 Sandy K.: Mm-hmm. Okay. 01:23:41 Michael H.: Yes. 01:23:44 Sandy K.: These are long. I have to take these. Because I&#39;m actually working with Allegheny on these. 01:23:47 Michael H.: Yeah. Oh, no problem. 01:23:52 Sandy K.: Okay. Somebody put this back. Okay. All right, so let&#39;s see what this one is. All right, for the following five six file acadia This is liquid heart candle. You&#39;d notice a difference between record counts for order trans between the text file and EDI. I don&#39;t understand what she&#39;s asking. For the Found 56, we noticed the difference between record counts for order trans number between the text files and the EDI. 01:24:56 Sandy K.: Let&#39;s see, what do they have here? 01:24:58 Michael H.: For the label, would this just be update web EDI user? 01:25:06 Sandy K.: Oh, the one where he&#39;s asking about sending his files. Or you could just say just web EDI question. No, it&#39;s not updating a user. Updating a user would be like if they wanted their password changed. 01:25:09 Michael H.: Yes. Oh, okay. Okay. Yes. 01:25:17 Sandy K.: The label would just be Web EDI question. I think there&#39;s one there for web media questions or general questions or something. 01:25:25 Michael H.: Yes. 01:25:45 Sandy K.: Okay. All right, so for this one So what they&#39;re sending us here is It looks like they exported something. So one, two, three, four, five, six, seven, eight, nine. One, two, three, four, five, six, seven, eight. So it sounds like they&#39;re saying that they only received eight line items inside the EDI data, but when we mapped it they got nine lines and one of the lines here is here twice. That&#39;s what I think they&#39;re trying to say. 01:26:23 Sandy K.: And she wants she&#39;s asking if I can remap it to see if the error has been resolved.
01:26:30 Sandy K.: Because I think that&#39;s what she said, right? Can you please correct and regenerate the file so I will assign this to Marissa. 01:26:34 Michael H.: Yes. 01:26:43 Sandy K.: Okay, so liquid hubicania and this is and 867. May 6th, this is the batch ID. I&#39;m thinking this is going to be cardinal. Okay, so I&#39;m going to go to 3933. 867. So I&#39;m going to log into their web edi account
01:27:23 Sandy K.: I just want… 867. Toronto Health Pharmaceuticals. This is the one they&#39;re asking about. This 860 song. If I open it up and look at it i should see the lines that they&#39;re talking. Eight lines. Okay. So she&#39;s asking if I can regenerate this file to see if it is correct. Okay. Let me go. Acso3. So I have to find the channel because this is going to be a map This is going to get really confusing for you. 01:28:43 Michael H.: I&#39;m going to confuse her. I&#39;m trying to… Yes. 01:28:45 Sandy K.: I know, because this is not going to be This isn&#39;t something simple. This is something that, you know. Maria would generally. Do. I mean, or I would generally do or Maria would do or Nico would do this wouldn&#39;t be something somebody new would do but Of course, every file that we&#39;ve received every email we&#39;ve received today has been something Nah, I have a question on WebDI. 01:28:58 Michael H.: Yes. 01:29:15 Sandy K.: Everything has been a mapping. Complex question. 01:29:19 Michael H.: Thanks. 01:29:26 Sandy K.: It&#39;s all kept jumping. 01:29:49 Sandy K.: Good hub. Yes, ma&#39;am, 140. Okay. So what this is telling me is I needed to know what map Liquid Hub was running off of. To take that EDI data and put it into their fixed file format. So on our T drive we have where all of the
maps are listed not Not the maps, but the integration. So see, this is the web EDI 3.5 integration for Liquid Hub. Cardinal 867 and right here&#39;s my map number. So it&#39;s map 140. So I know I have to use links map 140. 01:30:33 Sandy K.: So what I&#39;m going to do is I&#39;m going to come back here to web edi admin You can or don&#39;t have to even have to So we have two mappers. We have the Delta map, which lives inside those servers. And then we have the links mapper. Now, even though files that are on the server, like they&#39;ll run out of web edi They&#39;ll run through the links map. And then they&#39;ll post to the server. So this is the link spamper. This either takes files and takes an EDI file and converts it into a fixed file or flat file. Or it takes a fixed or flat file and converts it into a EDI. So either way, it does both depending on the type of mapping. 01:31:43 Sandy K.: But in this case, this is a cardinal You know, 867 to Liquid Hub. And this is, again, the rules. So here we have the EDI file and all our segments and elements And on this side is our flat file with just like header record you know just all of our records fixed file And then here it&#39;s actually saying here take the value from this segment, BPTO3, and store it as the var order date. Because down here, you&#39;re going to see it&#39;s going to say retrieve our order date and put it in this field on the flash file. So that&#39;s what the mapper is doing. So at the top you have your source and target. Again, same thing. This is our source. This is our target. 01:32:34 Sandy K.: And here are our rules. That&#39;s going to tell it what from here to put over here. But I&#39;m going to run a test because they&#39;re saying they said that they got too many lines. So I&#39;m actually going to run a test. Um So… Because I want to see. If it actually does it actually does create it wrong or if there was a problem because we were having a problem But it was. 01:33:10 Sandy K.: Fixed a while back. So I&#39;m going to actually just see if it is working. So I put in my customer name their number I&#39;m doing a get because I want to get a This 867. Which is an EDI file. And my message ID. So it&#39;s going to now pull this file and at the very end is going to be my raw data. And what does it look like? It&#39;s their choice. So there is something wrong in this map. That is pulling that item two times, unless there is something on this file for that item. 01:34:09 Sandy K.: One. Which item is that? So let me buy. Well, there&#39;s definitely something that&#39;s why it&#39;s pulling it twice because I can see all of my others. You see how four and five the spacing So there&#39;s definitely something in here And I
bet it&#39;s in here twice. And that&#39;s why it&#39;s pulling twice. So it&#39;s not that we did it twice. I can tell looking at this Quantity sold one. Oh, quantity sold one. Here we only have quantity sold once. They have sent quantities sold once here. And here. So they actually sent this item two times. So they actually ordered two two units of it, not one. Which is why our math created two lines. Instead of one line that said two. They actually sent One. And then a second one. 01:35:37 Sandy K.: So if I export this as EDI, so I&#39;m going to go back here. So there actually isn&#39;t anything wrong. If I export this as EDI, And look at the raw data. Okay. So here&#39;s my first LIN. Unit amount, PID, PTD. Here&#39;s the second one. The third one, I think. All right, here. This is… This is the one I need. So here&#39;s that item number that ends in 7.3. 01:37:04 Sandy K.: Okay, so this is the first item. And then we have… PTD, the DTM, ARIA, We have our ship to information We have our quantity. Quantity one. We go to our next LIN. The UIT, the amt the PID. And quantity one. So that PTD line is missing from here that would have that number. 01:37:46 Sandy K.: But I can… clearly see on here that for that line item five This is not anything that we did wrong. This is the way they sent it. So what I will do is I&#39;m going to send them a screenshot from within WebEDI. That shows this I know and how they sent it. What&#39;s happening? Your mouse just die or something? Oh, there it is. 01:38:58 Sandy K.: Slow right now. And there isn&#39;t. There&#39;s not anybody in my house working so there&#39;s my son is not here to We use up all the bandwidth. So my son works from home twice a week. And on Mondays and Fridays. And I usually yell at him that he&#39;s using up all the bandwidth when he&#39;s working from home. He slows everything down. Because he doesn&#39;t just work he&#39;s got like he&#39;s streaming a television show while he&#39;s working or he&#39;s got music playing and he&#39;s doing all this stuff and i&#39;m you&#39;re really sucking up all the band. But he&#39;s on vacation this week, so he&#39;s there. We&#39;ve made life too comfortable for him. He won&#39;t move out. Quantity one. Quantity one. So for line item five. They&#39;ve put it through two times. 01:40:28 Michael H.: Yes. 01:40:30 Sandy K.: And so I&#39;m going to give them that we are not And that was not on us. It was on them. They&#39;ve actually put through that item two times. I
don&#39;t know if you were able to follow along with how I know you won&#39;t understand how to run like the test because you know that&#39;s something that really tier two just runs the test but we have the ability to test maps. 01:40:57 Michael H.: Yes. 01:40:57 Sandy K.: So the links map when you test a links map. If it&#39;s an inbound file, like something that has to go into their WebDI, it actually will put it in there, but it puts it in as a test. So the auto send doesn&#39;t work. And delta we can test on the server. At least see the files. But this at least gives us a test so that we can see, okay, is it working is it not And do we need to get development involved? Because if the If the map doesn&#39;t say To do the fifth item twice, then there would be something internally wrong. So I&#39;m just going to reply back to them. 01:42:55 Michael H.: This one, I&#39;m kind of confused. I&#39;m going through my notes to see. 01:42:59 Sandy K.: So I&#39;ll back up and I&#39;ll send this to her and then I&#39;ll go over this whole one again. 01:42:59 Michael H.: I don&#39;t. Okay, yes. 01:43:05 Sandy K.: So, all right. So what she was saying initially is that When they received the file. She gave us a screenshot here. So what her screenshot shows is she didn&#39;t show the full export of an EDI file. Where she received this from, I don&#39;t know. But she&#39;s showing here that they ordered eight line items, one, two, three, four, five, six, seven, eight. But we sent them nine and she highlighted that this one this item we sent two times, which we did. Two times it&#39;s there. So… The first thing I needed to do was If I would have looked at the EDI file, I would have saw that it was there twice but I went and located the map that converts the EDI to the flat file. 01:44:03 Sandy K.: I then have to take that map. And run a test using a test the file from within her file Web EDI account. So I run a test. I use that file. To run my tests. Because again, this is the math So this is the map for cardinal 867s to liquid hope. Sources on my left. Target is on my right and these are all my rules. 01:44:26 Michael H.: Yes. 01:44:33 Sandy K.: And the rules are pretty straightforward. Take this segment, put it there, take this segment, put it there. So when I ran the test, my output did show exactly what she has. So initially I thought, oh yeah, we have a problem we
should never duplicate a line. 01:44:50 Sandy K.: But why would we duplicate a line? You know just one line that doesn&#39;t make sense So then I went and pulled the actual 867 From inside her WebDi account And once I started looking at the individual lines, I can see that This LIN. You know quantity one And within this same line section, I can see there was another quantity one. Which is why we would put two lines on her flat file. Like here this one, we just have one quantity sold one This line, quantity sold one. There isn&#39;t another one below it. Quantity sold. This one&#39;s three. But this one here, they actually put it in instead of saying quantity sold two. They put one and they did another line for one. Now, this may have been an error on their part. 01:45:46 Sandy K.: But… I can see it&#39;s the same drug. Nuclazid, 34 milligrams. The placid 34 milligrams of Oh, no, this is 10. See, this actually is a different item. But they didn&#39;t send it right. This is 34 milligrams. And this is 10 milligrams. This actually should have been on a completely different line. So this is back, this goes back to They clearly didn&#39;t send something over correctly in their PDI file. The EDI file does look bad. When I export it as EDI, Because all segments and elements usually follow a pattern for line items. So if I take this line item here. It starts out with LIN. That&#39;s the first segment. That&#39;s the line item. 01:46:42 Sandy K.: So that&#39;s the line record. It gives me the drug code, the product ID, the purchaser&#39;s item. So this is all the codes that relate to the item. And then I have the unit detail they&#39;re in each is, the unit price, and it&#39;s wholesale And then the amount will be $51.20 because there&#39;s only one The PID is the description. The PTD is the detail So we have it for stock sale. Their invoice number and then their invoice number This is their reference number. The item number that they were using. We have the date. The invoice date. We have an REF segment. The contract number, all the ship to information And then the quantity sold and the quantity shipped. Okay, so quantity is one. Now I have another LIN. 01:47:46 Sandy K.: That&#39;s my next LIN. Everything from here to here, this LAN to this QTY, all pertains to that one item.
01:47:57 Sandy K.: My next one is I should have, you know, LIN. I have my unit detail the amount the description, the quantity And you&#39;ll notice I jump right to jump right the next LIN. I am missing. Ptd, DTM, REF, And all of that because that should come after my quantity. 01:48:25 Sandy K.: No, that should come after PID. I should have all of this before quantity. Do you see that? Okay. 01:48:34 Michael H.: Yes. 01:48:36 Sandy K.: We would automatically pick that up with the previous line because they didn&#39;t send the line information. We didn&#39;t just fail the file. We picked it up and just associated it to that line because Also. It was all part of line five. Each item needs to be on its own line. So when Cardinal sent this file over to us. There is something structurally wrong inside the file. They&#39;re missing a bunch of segments. 01:49:20 Sandy K.: This file doesn&#39;t have a lot of segments. So they need to go back to Cardinal. This is not on us. We mapped it correctly based on the information we were sent. This is something they need to take back to Cardinal and say the state 67 was incorrect. So that&#39;s not anything that&#39;s not anything we can do. So I just let her know that we sent it out correctly. We mapped it correctly. There&#39;s structurally nothing wrong. We followed exactly what was sent. She needs to look at this to see that this was sent wrong.
01:49:52 Michael H.: Yes. 01:49:52 Sandy K.: Oh, okay. I hope that&#39;s a little clearer. 01:49:58 Michael H.: Yes, let me go over my notes and make sure I have it correctly. 01:50:04 Sandy K.: Sure. 01:50:04 Michael H.: Yes. 01:50:37 Michael H.: So the ticket was Liquid Hops Acadia And so there was like problems and discrepancy in their record counts for the order transition between the EDI data and some items appear twice In the EDI. 01:50:57 Michael H.: Leading an extra line in the output you said So what we did first was we went over the provided data, the customer a liquid hub showed us showing us that there&#39;s eight line items, but the EDI data showed nine. And there was an extra line was identified, which was a 01:51:18 Michael H.: Duplicate from an existing item which I didn&#39;t know who was able to write down Next, we did a map analysis. So we located a map used for converting EDI to a flat file. I&#39;m not sure what a flat file was. But, um. We map 140 for the cardinal 867 to LiquidHub and we ran a test using the map to verify the output And then we did an EDI file examination. So we just pretty much exported the edi file to just look at the raw data and 01:51:57 Michael H.: Found out and identify the item was confirmed that it was listed twice in the EDI file. And different quantities I&#39;m not sure if I got the milligram sound, but it&#39;s like 34 milligrams and 10 milligrams. 01:52:14 Sandy K.: Yeah, yeah. 01:52:15 Michael H.: So in the end, the issue was not with the mapping process, but with the data sent by the customer And there&#39;s nothing we could really do. 01:52:25 Michael H.: We could just inform the customers that it was a duplication on their part when the data was sent and not a mapping area and you just pretty much told them that the customer to review the EDI file sent by Cardinal to ensure It&#39;s right. For that and the data is corrected. 01:52:48 Sandy K.: Yes. One day, though, that it&#39;ll make sense, though. 01:52:49 Michael H.: Okay. I took a lot of screenshots. Yes, that&#39;s the thing. I see the summary, but I&#39;m trying to remember how we did all of that. 01:52:58 Sandy K.: Mm-hmm.
01:53:06 Michael H.: Everything. 01:53:10 Sandy K.: Yeah, it&#39;s like I know it doesn&#39;t a lot of it doesn&#39;t make sense And this is something that&#39;s very complex, right? This isn&#39;t anything that you would be doing in the beginning. 01:53:21 Michael H.: Oh, okay. 01:53:23 Sandy K.: Unfortunately. That happened to be the ticket that was there. And that&#39;s why I was trying to look through tickets where it was something like if somebody just had something really simple, like just a web edi question that we could look up inside WebEI rather than having to get into like 01:53:27 Michael H.: Yes. 01:53:42 Sandy K.: Mapping. My computer&#39;s just having some problems right now. 01:53:49 Michael H.: Yeah, Lauren had the same problem earlier too 01:53:55 Sandy K.: Yeah, it doesn&#39;t want to do anything. Like my mouse doesn&#39;t is like when I move it, it disappears. 01:54:02 Michael H.: Yes. 01:54:09 Sandy K.: I don&#39;t know why I&#39;m having such a problem with moving around like 01:54:20 Sandy K.: I didn&#39;t even click on this to open. But okay. Let&#39;s see what we have. Wow, these are all voicemails. So we&#39;re going to have to call Oh, that&#39;s one thing. I wonder if you were set up on the phone system. I bet you won&#39;t. 01:55:00 Michael H.: I don&#39;t think we ever went through that, but I did see the phone app. That you guys that you guys that the team uses and everything. Lauren showed me and so did you and sandy 01:55:21 Sandy K.: Okay, that I have to get set up for you. I just realized that i just realized you probably don&#39;t have phone extensions of them. I wonder if I could just give you somebody else&#39;s. I wonder if he&#39;s stealing. 01:55:45 Michael H.: Oh, Tyler&#39;s? Yes. 01:55:47 Sandy K.: Yeah. Oh, maybe he&#39;s on this page. So, wow. They were to come out. Of course they do. Okay. All right. I&#39;ll have to talk to Chris about getting you on a call. They&#39;d be like, all right, well, we could listen to these phone messages and then we could call the customer back. And see what they wanted. Because this, I think, is Marianne&#39;s project. I don&#39;t know what Greystone Cardinal, I think probably called because they weren&#39;t getting files. And this is probably
called because they weren&#39;t getting files because of ECSO2. Let&#39;s see. All right. I&#39;m going to just open this one, see what they&#39;re saying. 01:57:00 Michael H.: Yes. 01:57:04 Sandy K.: I&#39;m sure you&#39;ll be able to hear how something went wrong. 01:57:08 Michael H.: Should I mute myself? 01:57:10 Sandy K.: I was just going to listen to the voice. I just wanted to listen to the voicemail, but it&#39;s not letting me. Maybe I download it. 01:57:17 Michael H.: Okay. 01:57:27 Sandy K.: I&#39;m as if it&#39;ll let me 01:57:52 Sandy K.: Okay, well, that wasn&#39;t helpful. He didn&#39;t really say what he needed. All right. So we&#39;re just going to have to give him a call back. And see what he needed. All right, let&#39;s see. Well, this one 01:57:59 Michael H.: Yes. 01:58:06 Sandy K.: Usually they&#39;re a little more, this is what I need. This is the problem I&#39;m having All right, that he did not. 01:58:15 Sandy K.: All right, so let&#39;s see what this one is. 01:59:06 Sandy K.: Okay, so we can at least log into her account. Could you hear that? Okay, so the other one was, so the first call, they didn&#39;t really say what they wanted. 01:59:12 Michael H.: Oh, no, I couldn&#39;t. 01:59:20 Sandy K.: They just said, can someone call me back? The second one was Venture Outdoors. Venture Outdoors called and she was saying that When her invoices And I don&#39;t know if Venture Outdoors is integrated. 01:59:59 Sandy K.: Oh, they&#39;re just a web EDI customer. Okay. She said, when the invoices are in their account the dollar amount disappears. Don&#39;t know what she means by that. So I&#39;m going to guess This… I see dollar amounts on all of her ATAPs. 02:00:21 Sandy K.: I&#39;ll create one for Tim. So she said when in her web edi on the dashboard When she has her invoices the dollar amount is zero. And I see some of them have a zero. And if I open it up Oh, this one&#39;s in draft still. Well, that one&#39;s not sent. All right. That one&#39;s in draft. That&#39;s why it would be zero because there&#39;s no information Draft, draft. Well, I guess I&#39;m not understanding what she&#39;s seeing. Because all the ones that are sent and accepted, I have a dollar amount on them.
Let me just open one. Okay, I have a dollar amount. If I go back to my folder. The dollar amount is still there. Okay. So that&#39;s what she was saying, that the dollar amount was zeroing out. 02:01:33 Sandy K.: So we&#39;ll have to give her a call and get some more information from her as well. Because that wasn&#39;t very helpful, what she said. Bad Ausons from Greestone. 02:02:18 Sandy K.: Okay. Please email from cooper regarding ASN issue. Quite a few ASNs from greystone Greystone&#39;s our customer. 02:02:31 Sandy K.: This is Greystone. 257, 10. Error. Oh, they air out due to extra ARIA? 02:02:48 Sandy K.: Rl1. But ASNs to other Cooper Standards Plants such as OSADA, Torian, El Juardo are good. Don&#39;t have RED Are there supposed to be REF-R-O-L? Please correct and use the same ASN minutes for other CS plants. Okay. She&#39;s sending it to us here to see. I see. Oh, there she&#39;s saying this REFR. There&#39;s an extra RFRL. So they do not want this ARIA RL segment. For… this trading partner. I&#39;m going to assume that&#39;s Cooper standard G-U-A-Y-M-A-S. 02:03:44 Sandy K.: All right, so I&#39;m going to first pull up Beautiful. So she&#39;s saying we have an extra segment. So it&#39;s Cooper Standard mexico But she said, Osada. Torian, they don&#39;t have that extra segment. Okay. So… I&#39;m going to pull up the DOM. And see if the Cooper Standard standard Has an extra segment. Cooper standard ASN.
02:04:39 Sandy K.: Because that&#39;s what she&#39;s saying on the ASM. So we have REFRL. According to her, they don&#39;t want that. Let&#39;s see if that trading partner They just have EDI 810 specifications. Because she said osra About all of this things are under here. So Cooper standard A56 document. So if I go down to all those REF segments here decay. Reflf. R-e-f-r-l. So this says this is use, it says it&#39;s optional here But if you use it, you have to use that in it. 02:06:04 Sandy K.: Okay. And do we have this set as required? Oh, we do. Okay. Let&#39;s go look at… Let&#39;s look.
02:06:20 Sandy K.: Cooper Standard Torian. Let&#39;s see what that one looks like. We don&#39;t have it there. You see that? We just have decking. We&#39;re going to go back. 02:06:35 Michael H.: Yes.
02:06:39 Sandy K.: So I&#39;m just, I&#39;m gonna take it out. Since the other ones don&#39;t have it. And they all follow the same Cooper standard math I&#39;m going to edit it. Well, you know, let me just look at something here first. Is this… if she&#39;s the only
customer with this one? Yeah, they are. Then I have no problem taking it out. 02:07:07 Sandy K.: Okay, so I&#39;m going to come down here to the REFRL. And I&#39;m going to delete. Segment. I&#39;m going to take a screenshot. So I can have my documentation Anytime we make edits to DOMS, we should always make sure we have screenshots. So that we have documentation. For it. So I deleted the RL. That&#39;s what she asked to have it removed. Her ASNs We&#39;re having errors, so I&#39;m going to make an internal note. Okay. There you go. All right. 02:07:53 Michael H.: Miss Sandy, can I use the bathroom real quick? Yes. 02:07:57 Sandy K.: Sure. 02:12:00 Michael H.: Yes, same. 02:12:01 Sandy K.: Okay. All right. So what I did, so you saw me, I deleted that segment from the dom so it&#39;s gone now Because none of the other Cooper standards had it. And she was getting errors. From Cooper Standard because it was there for that one plant. So I just let her i put my screenshot here showing that I deleted it other Cooper Standard DOMs don&#39;t have this segment. And then I let her know that I deleted it. And that&#39;s no, so it&#39;s been deleted. 02:12:38 Michael H.: Yes so you agree with me. She was getting errors from Kupa Standard. 02:12:46 Sandy K.: Right. Do you want the ticket? 02:12:50 Michael H.: Oh, sure. I could do that too. Yes. 02:12:53 Sandy K.: Yeah. So if you want to like read through it or anything like that. 02:13:08 Michael H.: Oh, yes. Yes, this will be very helpful for reference in the future. 02:13:08 Sandy K.: Use it. Mm-hmm. Yeah. Oh, I should have put it as in progress. And assign it to myself. 02:13:20 Sandy K.: And yeah, so that was easy. That&#39;s kind of like an easy one you know just Now, removing a segment from a DOM is easy to do. But we have to make sure that what we&#39;re removing isn&#39;t going to impact anybody else. So in this case she&#39;s Greystone is the only customer partnered with this trading partner. 02:13:37 Michael H.: Yes. 02:13:45 Sandy K.: So I had no issue deleting it. If there were other customers partnered with that trading partner. 02:13:49 Michael H.: Yes.
02:13:54 Sandy K.: That we&#39;re using it I would make it not required So that you could use it or not use it. But… 02:14:02 Michael H.: Yes. 02:14:24 Sandy K.: No, because they were the only one partnered with them. Right. And they&#39;re getting errors. So it&#39;s not like the customer said. 02:14:25 Michael H.: So the um So you had no issues deleting it because there weren&#39;t Okay, yes. 02:14:30 Sandy K.: Hey, I want you to just take this out. I don&#39;t want to use it, right? It&#39;s the trading partner giving them errors for having it. So since the trading partner has made it clear that they don&#39;t want it and they&#39;re giving them errors. 02:14:44 Sandy K.: I will delete it. 02:14:53 Michael H.: Yes. 02:15:39 Michael H.: When I get like different tickets just to go through like the past tickets as a reference I can do that. Yes, okay. 02:15:46 Sandy K.: Yes, correct. Yeah. So when we switched to Jira, we kind of lost our history but we always It&#39;s always helpful to have, that&#39;s why it&#39;s always best to have as many notes on their screenshots as possible Because you can always use it as reference. 02:16:00 Michael H.: Yes. 02:18:12 Michael H.: Yep, yes. 02:18:16 Sandy K.: Okay. All right. I have to go meet with Rafat to talk about a development ticket. If you want to go to lunch I have a meeting from 1230 to 1. So then we can meet back up at One o&#39;clock. 02:18:25 Michael H.: Yes. Yes. 02:18:32 Sandy K.: Either me or Lauren, I don&#39;t know. Either she&#39;s going to take lunch from one to two and I&#39;ll go from two to three or Or I&#39;ll do one to two and she&#39;ll do two to three and then you&#39;ll meet with one of us in between there. 02:18:42 Sandy K.: Whichever one okay All right. All right. I&#39;ll 02:18:43 Michael H.: Yes. Yes, thank you, Ms. Sandy.
View original transcript at Tactiq.</li>
</ul>
