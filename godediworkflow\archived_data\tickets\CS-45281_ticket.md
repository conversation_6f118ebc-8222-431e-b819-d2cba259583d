# CS-45281:  (7213) APP Impex EDI Portal Smart Folder / Archive Not Functioning Correctly 

## Ticket Information
- **Key**: CS-45281
- **Status**: In Progress
- **Priority**: Medium
- **Assignee**: <PERSON>
- **Reporter**: r<PERSON><PERSON>.<EMAIL>
- **Created**: 2025-06-11T12:14:53.509-0600
- **Updated**: 2025-06-16T10:46:17.258-0600
- **Customer**: ID

## Description
* *Caller:* <PERSON>
* *Company:* APP Inc.
* *Company ID:* 7213


Rachel reports that her Smart Folder and Archive feature is not functioning as expected. The intended behavior is that when she selects documents and archives them, they should automatically be moved into a corresponding smart folder.

She provided a specific, recent example:

* She received a batch of EDI 850 documents from her trading partner, *Chrysler*.
* She selected all of them and used the archive function.
* The documents were moved to the general archive but were *not* pushed to the designated smart folder as they normally would be. The core issue is that the workflow connecting the "archive" action to the "smart folder" destination is broken.

## Components


## Labels

